﻿<%@ Page Title="" Language="C#" MasterPageFile="~/masterpage/MasterPage.master" AutoEventWireup="true"
    CodeFile="accountvalidate_compdatachange.aspx.cs" Inherits="accountmt_accountvalidate_compdatachange" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script type="text/javascript">
        function ismaxlength(obj) {
            var mlength = obj.getAttribute ? parseInt(obj.getAttribute("maxlength")) : "";
            if (obj.getAttribute && obj.value.length > mlength)
                obj.value = obj.value.substring(0, mlength);
        }
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="fullheader">
        <table width="100%" cellspacing="0" cellpadding="0" class="fullheaderinfo">
            <tr>
                <td valign="top">
                    <b>帳號申請審核-公司資料異動</b>
                </td>
                <td align="right" valign="top" class="font_loginInfo">
                </td>
            </tr>
        </table>
    </div>
    <!--{* fullheader end *}-->
    <div class="fullcontent">
        <div class="formstyle">
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="font_fullcontent">
                <tr>
                    <td align="right">
                        申請種類：
                    </td>
                    <td>
                        公司資料異動
                    </td>
                </tr>
                <tr>
                    <td colspan="4">
                        <span class="font_fulltitle"><b>公司基本資料</b></span>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        統一編號：
                    </td>
                    <td>
                        <asp:Label ID="lbl_compidno" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        已申請帳號數：
                    </td>
                    <td>
                        <asp:Label ID="lbl_appyliedcount" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        公司名稱：
                    </td>
                    <td >
                        <asp:Label ID="lbl_compname" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        公司英文名稱：
                    </td>
                    <td>
                        <asp:Label ID="lbl_compename" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        公司簡稱：
                    </td>
                    <td>
                        <asp:TextBox ID="txt_compshortname" runat="server" MaxLength="60"></asp:TextBox>
                    </td>
                    <td align="right">
                        負責人姓名：
                    </td>
                    <td>
                        <asp:Label ID="lbl_chargeman" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        公司電話：
                    </td>
                    <td>
                        <asp:Label ID="lbl_comptel" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        公司傳真：
                    </td>
                    <td>
                        <asp:Label ID="lbl_compfax" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        公司地址：
                    </td>
                    <td colspan="3">
                        <asp:Label ID="lbl_compaddr" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right" width="120">
                        合格證寄發地址：
                    </td>
                    <td colspan="3">
                        <asp:Label ID="lbl_certaddr" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        廠商代碼：
                    </td>
                    <td colspan="3">
                        汽：<asp:TextBox ID="txt_stampcompno_g" runat="server" Width="60px" MaxLength="4"></asp:TextBox>
                        &nbsp;&nbsp; 機：<asp:TextBox ID="txt_stampcompno_m" runat="server" Width="60px" MaxLength="4"></asp:TextBox>
                        &nbsp;&nbsp; 柴：<asp:TextBox ID="txt_stampcompno_d" runat="server" Width="60px" MaxLength="4"></asp:TextBox>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        上傳文件：
                    </td>
                    <td colspan="3">
                        <asp:Repeater ID="rpt_fileList" runat="server" OnItemDataBound="rpt_fileList_ItemDataBound">
                            <ItemTemplate>
                                <asp:HyperLink ID="hlk_file" runat="server" Target="_blank"></asp:HyperLink>、
                            </ItemTemplate>
                        </asp:Repeater>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        退件原因：
                    </td>
                    <td colspan="3">
                        <asp:TextBox ID="txt_denybackreason" runat="server" TextMode="MultiLine" Height="50px"
                            Width="371px" CssClass="inputTypeM" MaxLength="120"></asp:TextBox>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        審核人員：
                    </td>
                    <td>
                        <asp:Label ID="lbl_loginname" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        審核時間：
                    </td>
                    <td>
                        <asp:Label ID="lbl_logindate" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td colspan="4" align="center">
                        <asp:Button ID="btn_OK" runat="server" Text="核可" OnClick="btn_OK_Click" CssClass="btn_mouseout"
                            onmouseover="this.className='btn_mouseover'" onmouseout="this.className='btn_mouseout'" />&nbsp;&nbsp;<asp:Button
                                ID="btn_NO" runat="server" Text="退件" OnClick="btn_NO_Click" CssClass="btn_mouseout"
                                onmouseover="this.className='btn_mouseover'" onmouseout="this.className='btn_mouseout'" />&nbsp;&nbsp;<asp:Button
                                    ID="btn_Cancel" runat="server" Text="取消" OnClick="btn_Cancel_Click" CssClass="btn_mouseout"
                                    onmouseover="this.className='btn_mouseover'" onmouseout="this.className='btn_mouseout'" />
                    </td>
                </tr>
            </table>
        </div>
    </div>
    <!--{* fullcontent end *}-->
    <div class="fullfooter">
    </div>
    <!--{* content end *}-->
    <asp:Label ID="lab_compename" runat="server" Visible="False"></asp:Label>
    <asp:Label ID="lab_chargeman" runat="server" Visible="False"></asp:Label>
    <asp:Label ID="lab_compaddrcity" runat="server" Visible="False"></asp:Label>
    <asp:Label ID="lab_compaddrlocalarea" runat="server" Visible="False"></asp:Label>
    <asp:Label ID="lab_compaddrpostno" runat="server" Visible="False"></asp:Label>
    <asp:Label ID="lab_compaddr" runat="server" Visible="False"></asp:Label>
</asp:Content>
