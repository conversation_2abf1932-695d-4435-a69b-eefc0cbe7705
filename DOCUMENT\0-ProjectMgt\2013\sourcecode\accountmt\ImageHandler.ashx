﻿<%@ WebHandler Language="C#" Class="ImageHandler" %>

using System.Linq;
using System;
using System.Web;
using System.IO;
using System.Drawing;
using System.Drawing.Imaging;
using System.Drawing.Drawing2D;
using System.Data.SqlClient;

public class ImageHandler : IHttpHandler
{
    public void ProcessRequest(HttpContext context)
    {
        context.Response.Clear();
        context.Response.ClearContent();
        context.Response.Expires = 0;
        context.Response.ContentType = "image/JPG";

        try
        {          
           
            Cryptography crypt = new Cryptography();
            string FileID = crypt.DeCode(context.Request["File"].ToString());

            Byte[] imgbyte = (Byte[])getimage(FileID);
                        
            if (imgbyte != null)
            {
                context.Response.BinaryWrite(imgbyte);
            }
            context.Response.Flush();
        }
        catch
        {
            HttpContext.Current.Response.Write("Error");
        }
    }

    private object getimage(string fileid)
    {
        object imgstream = null;
        try
        {
            string pDBRole = string.Empty;
            if (Convert.ToBoolean(System.Configuration.ConfigurationManager.AppSettings["DEBUGMode"].ToString()))
                pDBRole = "noise_t";
            else pDBRole = "noise_o";
            SqlConnection usrcn = new SqlConnection();
            usrcn.ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings[pDBRole].ConnectionString;
            usrcn.Open();
            string sql = @"select * from nv_applybasedata 
            where abd_applyno=@FileApplyno";
            SqlCommand cmd_file = new SqlCommand(sql, usrcn);
            cmd_file.Parameters.AddWithValue("@FileApplyno", fileid);

            SqlDataReader dr = cmd_file.ExecuteReader();
            dr.Read();
            imgstream = dr["abd_barcodeimg"];
            usrcn.Close();
        }
        catch
        {
            HttpContext.Current.Response.Write("Error");
        }
        return imgstream;
    }

    public bool IsReusable
    {
        get
        {
            return false;
        }
    }
}