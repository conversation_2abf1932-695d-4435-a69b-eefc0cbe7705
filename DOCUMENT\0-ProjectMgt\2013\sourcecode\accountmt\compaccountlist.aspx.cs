﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;

public partial class accountmt_compaccountlist : BaseAdminPage
{
    wsGroupInfo wsgroup = new wsGroupInfo();
    wsAccountInfo wsaccount = new wsAccountInfo();
   
    protected void Page_Load(object sender, EventArgs e)
    {
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = 'index.aspx';</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" && (GetAgentAccountGrpList().Contains("cam_adm") || GetAgentAccountGrpList().Contains("pg_adm")))
            {
                if (!IsPostBack)
                {   
                    BindData();
                }
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }
    }
    private void BindData()
    {
        string[] radom_a = wsAccountValidate(getRadNum());
        string compidnoE = Session["compidno"].ToString();
        DataSet ds_Get = wsaccount.getCompaccountlist(compidnoE, radom_a[0].ToString(), radom_a[1].ToString());
        gv_body.DataSource = ds_Get;
        gv_body.DataBind();
    }

    protected void btn_Save_Click(object sender, EventArgs e)
    {
        string[] radom_a = wsAccountValidate(getRadNum());
        foreach (GridViewRow grv in gv_body.Rows)
        {
            if (grv.RowType == DataControlRowType.DataRow)
            {
                CheckBox cb_usesystemP = (CheckBox)grv.FindControl("cb_usesystemP");
                Label lbl_account = (Label)grv.FindControl("lbl_account");
                if (cb_usesystemP.Enabled)
                {
                    wsaccount.updateAccountUseGMSystem(radom_a[0].ToString(), radom_a[1].ToString(), lbl_account.Text.Trim(), (cb_usesystemP.Checked) ? "1" : "0");
                }
            }
        }
        MessageBox.Show("存檔成功");
        BindData();
    }
    protected void gv_body_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            CheckBox cb_usesystemP = (CheckBox)e.Row.FindControl("cb_usesystemP");
            string ai_use_gm_system = DataBinder.Eval(e.Row.DataItem, "ai_use_gm_system").ToString();
            if (ai_use_gm_system == "1")
                cb_usesystemP.Checked = true;

            Button btn_Stop = (Button)e.Row.FindControl("btn_Stop");
            string accountstatus = DataBinder.Eval(e.Row.DataItem, "ai_accountstatus").ToString();
            string account = DataBinder.Eval(e.Row.DataItem, "ai_account").ToString();
            string username = DataBinder.Eval(e.Row.DataItem, "ai_username").ToString();

            //已核發:可編輯「停用」「權限」
            if ((accountstatus == "1" || accountstatus == "2") && account != DeCode(Session["account"].ToString()))
            {
                cb_usesystemP.Enabled = true;
                btn_Stop.Visible = true;
                btn_Stop.Attributes["account"] = account;
                btn_Stop.Attributes["use_gm_system"] = ai_use_gm_system;
            }
            else
            {
                cb_usesystemP.Enabled = false;
                btn_Stop.Visible = false;
            }

            //送件中：只可編輯「確認結果」
            Button btn_OK = (Button)e.Row.FindControl("btn_OK");
            Button btn_Error = (Button)e.Row.FindControl("btn_Error");
            if (accountstatus == "0")
            {
                btn_OK.Visible = true;
                btn_Error.Visible = true;
                btn_OK.Attributes["account"] = account;
                btn_OK.Attributes["username"] = username;
                btn_Error.OnClientClick = string.Format("OpenCheckNG('{0}','{1}');", account, username);
            }
            else
            {
                btn_OK.Visible = false;
                btn_Error.Visible = false;
            }
        }
    }

    protected void NG_Click(object sender, EventArgs e)
    {
        BindData();
    }

    protected void OK_Click(object sender, EventArgs e)
    {
        Button btn_OK = (Button)sender;
        string account = btn_OK.Attributes["account"].ToString();
        string username = btn_OK.Attributes["username"].ToString();
        wsAccountInfo wsaccount = new wsAccountInfo();
        string[] radom_A = wsAccountValidate(getRadNum());
        string[] radom_G = wsGroupValidate(getRadNum());
        DataTable dtFirstAdmList = wsgroup.getFirstAdmList(radom_G[0].ToString(), radom_G[1].ToString());
        if (dtFirstAdmList.Rows.Count > 0)
        {
            string certpersonlist = string.Empty;
            foreach (DataRow dr in dtFirstAdmList.Rows)
            {
                certpersonlist += dr["ai_email"].ToString() + ",";
            }
            certpersonlist = certpersonlist.Substring(0, certpersonlist.Length - 1);

            //email
            string[] _mailTo = new string[1];
            _mailTo[0] = certpersonlist;//收件人                
            string[] _mailCc = new string[1];//副本
            _mailCc[0] = "";
            string[] _mailBcc = new string[1];//密件
            _mailBcc[0] = "";
            if (Convert.ToBoolean(ConfigurationManager.AppSettings["DEBUGMode"].ToString()))
            {
                //測試
                _mailTo[0] = ConfigurationManager.AppSettings["MailTestTo"].ToString();
            }
            SendMail sendmail = new SendMail();
            if (sendmail.Mail_CheckAccountResult(_mailTo, _mailCc, _mailBcc, GetAgentIDName()))
            {
                wsaccount.UpdateStatusAndCheckData(account, GetAgentAccount(), "4", "", radom_A[0].ToString(), radom_A[1].ToString());
                ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('新增帳號確認完成');</script>");

                BindData();
            }
            else
            {
                MessageBox.Show("寄件失敗");
            }
        }
        else
        {
            MessageBox.Show("尚未有審驗人員資料");
        }
    }

    protected void Stop_Click(object sender, EventArgs e)
    {
        Button btn_Stop = (Button)sender;
        string account = btn_Stop.Attributes["account"].ToString();        
        string[] radom_A = wsAccountValidate(getRadNum());
        wsAccountInfo wsaccount = new wsAccountInfo();
        wsaccount.UpdateStatusToStop(account, "3", GetAgentAccount(), GetAgentName(), radom_A[0].ToString(), radom_A[1].ToString());
        BindData();
        //如果該帳號有兩個系統權限，需通知污染停用該帳號--未處理
        string use_gm_system = btn_Stop.Attributes["use_gm_system"].ToString();
    }
}