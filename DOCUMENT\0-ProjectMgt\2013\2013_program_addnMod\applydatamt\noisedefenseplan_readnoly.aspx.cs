﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Xml;
using Common;
using System.IO;


using System.Web.Security;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls.WebParts;
using System.Xml.Linq;
public partial class applydatamt_noisedefenseplan_readnoly : BaseAdminPage
{
    public DataTable dt_list
    {
        get { return (DataTable)ViewState["_dt_list"]; }
        set { ViewState["_dt_list"] = value; }
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = '../accountmt/index.aspx';</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" &&
                (
                (
                GetAgentAccountGrpList().Contains("epa_adm") || GetAgentAccountGrpList().Contains("ac_adm") || GetAgentAccountGrpList().Contains("tc_adm") || GetAgentAccountGrpList().Contains("rv_adm") || GetAgentAccountGrpList().Contains("rs_adm") || GetAgentAccountGrpList().Contains("fis_adm") || GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("pg_adm")
                )
                ||
                (
                  (GetAgentAccountGrpList().Contains("cga_adm") || GetAgentAccountGrpList().Contains("cam_adm")) && CheckSingleApplyDataAccessRight(DeCode(Request["applyno"].ToString()))
                )
                )
                )
            {
                if (!IsPostBack)
                {

                    if (Request["applyno"].ToString() != "" && Request["carcompomodelno"].ToString() != "")
                    {
                        Cryptography crypy = new Cryptography();
                        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
                        int keynum = crypy.getRadNum();
                        string[] arr = crypy.wsApplybasedataDetailValid(keynum);

                        DataTable dt_applydatadetail = wsApplyData.get_applybasedate_detail(crypy.DeCode(Request["applyno"].ToString()), arr[0].ToString(), arr[1].ToString());
                        if (dt_applydatadetail.Rows.Count > 0)
                        {
                            //if (dt_applydatadetail.Rows[0]["abd_databelongcompidno"].ToString() == Session["compidno"].ToString())
                            //{
                            BindData();
                            dt_applydatadetail.Dispose();
                            //}
                            //else
                            //{
                            //    dt_applydatadetail.Dispose();
                            //    Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                            //    Response.End();
                            //}
                        }
                        else
                        {
                            dt_applydatadetail.Dispose();
                            Response.Write("發生取不到申請編號錯誤，系統將導回首頁！！");
                            Response.Redirect("../index.aspx");
                        }
                    }
                    else
                    {
                        Response.Write("發生取不到申請編號錯誤，系統將導回首頁！！");
                        Response.Redirect("../index.aspx");
                    }
                }
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }
    }
    #region 更改的麵包屑傳入的參數
    protected void Page_LoadComplete(object sender, EventArgs e)
    {
        ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text = ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text.Replace("applydatamt/applybasedata_detail.aspx", "applydatamt/applybasedata_detail.aspx?applyno=" + Server.UrlEncode(Request["applyno"].ToString()));
    }
    #endregion

    public void BindData()
    {

        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        Cryptography crypy = new Cryptography();
        int keynum = crypy.getRadNum();
        string[] arr = crypy.wsApplybasedataDetailValid(keynum);
        string applyno = DeCode(Request["applyno"].ToString());
        string carcompomodelno = DeCode(Request["carcompomodelno"].ToString());
        lbl_applyno.Text = applyno;
        lbl_carcompomodelno.Text = carcompomodelno;
        DataTable dt_applystatus = wsApplyData.get_applystatus(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
        if (dt_applystatus.Rows.Count > 0)
        {
            lbl_applystatus.Text = dt_applystatus.Rows[0]["ct_aps_desc"].ToString();
        }

        dt_list = wsApplyData.get_Nv_noisedefenseplan_lists(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
        gv_data.DataSource = dt_list;
        gv_data.DataBind();

        DataTable dt_carmodelnames = wsApplyData.get_Nv_carmodelnames(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
        if (dt_carmodelnames.Rows.Count > 0)
        {
            rpt_representvehicle.DataSource = dt_carmodelnames;
            rpt_representvehicle.DataBind();
        }


    }

    protected void gv_data_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {

            //Label lbl_position = (Label)e.Row.FindControl("lbl_position");
            //lbl_position.Text = DataBinder.Eval(e.Row.DataItem, "ndp_position").ToString().Trim();
            //Label lbl_defenseeqpname = (Label)e.Row.FindControl("lbl_defenseeqpname");
            //lbl_defenseeqpname.Text = DataBinder.Eval(e.Row.DataItem, "ndp_defenseeqpname").ToString().Trim();
            //Label lbl_eqpmaterial = (Label)e.Row.FindControl("lbl_eqpmaterial");
            //lbl_eqpmaterial.Text = DataBinder.Eval(e.Row.DataItem, "ndp_eqpmaterial").ToString().Trim();
            //Label lbl_eqpheight = (Label)e.Row.FindControl("lbl_eqpheight");
            //lbl_eqpheight.Text = DataBinder.Eval(e.Row.DataItem, "ndp_eqpheight").ToString().Trim();
            //Label lbl_equremark = (Label)e.Row.FindControl("lbl_equremark");
            //lbl_equremark.Text = DataBinder.Eval(e.Row.DataItem, "ndp_equremark").ToString().Trim();

            Repeater rpt_file = (Repeater)e.Row.FindControl("rpt_file");

            wsOtherFileupload oOFD = new wsOtherFileupload();
            Cryptography crypy = new Cryptography();
            int keynum = crypy.getRadNum();
            string[] Carr = crypy.wsOtherFileuploadValid(keynum);
            string applyno = DeCode(Request["applyno"].ToString());
            string carcompomodelno = DeCode(Request["carcompomodelno"].ToString());
            DataTable dtList = oOFD.get_uploadfenseplanfiles(applyno, carcompomodelno, DataBinder.Eval(e.Row.DataItem, "ndp_defenseplanid").ToString().Trim(), Carr[0].ToString(), Carr[1].ToString());
            rpt_file.DataSource = dtList;
            rpt_file.DataBind();


        }
    }

    protected void btn_Print_Click(object sender, EventArgs e)
    {
        //到列印畫面      
        string scriptString = "";
        scriptString = string.Format(@"var newwin=window.open('noisedefenseplan_rpt.aspx?applyno={0}&carcompomodelno={1}','winFP','scrollbars=yes');newwin.resizeTo(screen.width-20,screen.height-20) ;newwin.moveTo(0,0); ", Server.UrlEncode(Request["applyno"].ToString()), Server.UrlEncode(Request["carcompomodelno"].ToString()));
        this.ClientScript.RegisterStartupScript(this.GetType(), "", scriptString, true);

    }

    protected void rpt_representvehicle_ItemDataBound(object sender, RepeaterItemEventArgs e)
    {
        Label lbl_carengmodelname = (Label)e.Item.FindControl("lbl_carengmodelname");

        if (e.Item.ItemType == ListItemType.Item || e.Item.ItemType == ListItemType.AlternatingItem)
        {
            //判斷是否為代表車
            if (DataBinder.Eval(e.Item.DataItem, "cmdg_berepresentativevehicle").ToString().Trim().Equals("Y", StringComparison.CurrentCultureIgnoreCase) || DataBinder.Eval(e.Item.DataItem, "cmdg_berepresentativevehicle").ToString() == "1")
            {
                lbl_carengmodelname.Text = DataBinder.Eval(e.Item.DataItem, "fullcarstylename").ToString().Trim() + "(代表車)";
            }
            else
            {
                lbl_carengmodelname.Text = DataBinder.Eval(e.Item.DataItem, "fullcarstylename").ToString().Trim();
            }

        }

    }
    protected void rpt_file_ItemDataBound(object sender, RepeaterItemEventArgs e)
    {
        LinkButton lbl_file = (LinkButton)e.Item.FindControl("lbl_file");
        if (e.Item.ItemType == ListItemType.Item || e.Item.ItemType == ListItemType.AlternatingItem)
        {
            lbl_file.Text = DataBinder.Eval(e.Item.DataItem, "afi_userfilename").ToString().Trim();
            lbl_file.Attributes["afi_fileid"] = DataBinder.Eval(e.Item.DataItem, "afi_fileid").ToString().Trim();
            lbl_file.Attributes.Add("onclick", "javascript:window.open('../FileDownloadSeal.aspx?File=" + Server.UrlEncode(EnCode(DataBinder.Eval(e.Item.DataItem, "afi_fileid").ToString().Trim())) + "','_blank');");
        }
    }
}