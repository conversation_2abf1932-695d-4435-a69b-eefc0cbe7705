﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Xml;
using Common;
using System.IO;


using System.Web.Security;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls.WebParts;
using System.Xml.Linq;
public partial class applydatamt_noisedefenseplan : BaseAdminPage
{

    public DataTable dt_list
    {
        get { return (DataTable)ViewState["_dt_list"]; }
        set { ViewState["_dt_list"] = value; }
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        #region TextBox在Textmode=MultiLine狀態下MaxLength的字數限制無效
        txt_eqpmaterial_e.Attributes.Add("maxlength", "300");
        txt_eqpmaterial_e.Attributes.Add("onkeyup", "return ismaxlength(this)");

        txt_eqpheight_e.Attributes.Add("maxlength", "300");
        txt_eqpheight_e.Attributes.Add("onkeyup", "return ismaxlength(this)");

        txt_equremark_e.Attributes.Add("maxlength", "300");
        txt_equremark_e.Attributes.Add("onkeyup", "return ismaxlength(this)");
        #endregion
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = '../accountmt/index.aspx';</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" &&
                (
                (
                 GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("pg_adm") || GetAgentAccountGrpList().Contains("rs_adm") || GetAgentAccountGrpList().Contains("fis_adm") || GetAgentAccountGrpList().Contains("rv_adm")
                )
                ||
                (
                  (GetAgentAccountGrpList().Contains("cga_adm") || GetAgentAccountGrpList().Contains("cam_adm")) && CheckSingleApplyDataAccessRight(DeCode(Request["applyno"].ToString()))
                )
                )
                )
            {
                if (!IsPostBack)
                {

                    if (Request["applyno"].ToString() != "" && Request["carcompomodelno"].ToString() != "")
                    {

                        Cryptography crypy = new Cryptography();
                        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
                        int keynum = crypy.getRadNum();
                        string[] arr = crypy.wsApplybasedataDetailValid(keynum);
                        DataTable dt_applydatadetail = wsApplyData.get_applybasedate_detail(crypy.DeCode(Request["applyno"].ToString()), arr[0].ToString(), arr[1].ToString());
                        if (dt_applydatadetail.Rows.Count > 0)
                        {
                            //if (dt_applydatadetail.Rows[0]["abd_databelongcompidno"].ToString() == Session["compidno"].ToString())
                            //{
                            BindData();
                            dt_applydatadetail.Dispose();
                            //}
                            //else
                            //{
                            //    dt_applydatadetail.Dispose();
                            //    Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                            //    Response.End();
                            //}
                        }
                        else
                        {
                            dt_applydatadetail.Dispose();
                            Response.Write("<script languge='javascript'>alert('發生取不到申請編號錯誤，系統將導回首頁！！');window.location.href='../index.aspx'</script>");
                            Response.End();

                        }

                    }
                    else
                    {
                        Response.Write("<script languge='javascript'>alert('發生取不到申請編號錯誤，系統將導回首頁！！');window.location.href='../index.aspx'</script>");
                        Response.End();
                    }
                }
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }
    }

    protected void Page_LoadComplete(object sender, EventArgs e)
    {
        string url = HttpContext.Current.Request.Url.PathAndQuery.ToString();//
        string[] urlArray = url.Split('?');
        int count = urlArray.Length;
        string pageurl = urlArray[count - 1].ToString();
        ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text = ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text.Replace("applydatamt/carcompomodelList.aspx", "applydatamt/carcompomodelList.aspx?applyno=" + Server.UrlEncode(Request["applyno"].ToString()));
    }


    #region 申請型式為沿用、不異動、撤件，則不可填寫並導回查詢頁面
    public void CheckApplyType(string sApplyType)
    {
        string compidnoE = Session["compidno"].ToString(); //公司統編
        DataView dv = new MailParaControl().GetApplyBaseData(lbl_applyno.Text);
        string carcompomodelno = DeCode(Request["carcompomodelno"].ToString());//車型組編號
        string EngineFamily = dv[0]["EngineFamily"].ToString(); //引擎族
        Cryptography crypy = new Cryptography();
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        int keynum = getRadNum();
        string[] arr = wsApplybasedataDetailValid(keynum);
        //沿用(非第一筆)、不異動、撤銷不可編輯         
        if (wsApplyData.IsDateoneExist(compidnoE, carcompomodelno, EngineFamily, arr[0].ToString(), arr[1].ToString()))
        {
            Response.Write("<script languge='javascript'>alert('申請型式為「" + sApplyType + "」，不可編輯！！');window.location.href='noisedefenseplan_readnoly.aspx?applyno=" + Server.UrlEncode(Request["applyno"].ToString()) + "&carcompomodelno=" + Server.UrlEncode(Request["carcompomodelno"].ToString()) + "'</script>");       
            Response.End();
        }       
    }
    #endregion

    #region 判斷資料狀態不為填寫中、補件、拒件，則不可填寫並導回查詢頁面
    public void CheckApplyStatus(string sStatusCode, string sStatusDesc)
    {
        if (!(sStatusCode.Equals("10") || sStatusCode.Equals("40") || sStatusCode.Equals("90")))
        {
            Response.Write("<script languge='javascript'>alert('資料狀態為「" + sStatusDesc + "」，不可編輯！！');window.location.href='noisedefenseplan_readnoly.aspx?applyno=" + Server.UrlEncode(Request["applyno"].ToString()) + "&carcompomodelno=" + Server.UrlEncode(Request["carcompomodelno"].ToString()) + "'</script>");           
            Response.End();
        }
    }
    #endregion

    public void BindData()
    {

        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        Cryptography crypy = new Cryptography();
        int keynum = crypy.getRadNum();
        string[] arr = crypy.wsApplybasedataDetailValid(keynum);
        string applyno = DeCode(Request["applyno"].ToString());
        string carcompomodelno = DeCode(Request["carcompomodelno"].ToString());
        lbl_applyno.Text = applyno;
        lbl_carcompomodelno.Text = carcompomodelno;
        string strStatusCode = "";
        string strStatusDesc = "";
        string strApplyType = "";
        dt_list = wsApplyData.get_Nv_noisedefenseplan_lists(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
        gv_data.DataSource = dt_list;
        gv_data.DataBind();

        if (dt_list.Rows.Count > 0)
        {
            //取的修改人資料
            DataTable dt_modadate = wsApplyData.get_fenseplanmodadate(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
            lbl_modaccount.Text = dt_modadate.Rows[0]["ndp_modaccount"].ToString();
            lbl_moddate.Text = dt_modadate.Rows[0]["ndp_moddate"].ToString();

        }
        else
        {
            lbl_modaccount.Text = "尚無填寫人";
            lbl_moddate.Text = "尚無填寫日期";
        }
        //申請型式
        DataTable dt_applyno = wsApplyData.get_applyno(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
        if (dt_applyno.Rows.Count > 0)
        {
            strApplyType = dt_applyno.Rows[0]["ct_at_desc"].ToString();
            CheckApplyType(strApplyType);
        }
        //資料狀態
        DataTable dt_applystatus = wsApplyData.get_applystatus(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
        if (dt_applystatus.Rows.Count > 0)
        {
            lbl_applystatus.Text = dt_applystatus.Rows[0]["ct_aps_desc"].ToString();
            strStatusDesc = dt_applystatus.Rows[0]["ct_aps_desc"].ToString();
            strStatusCode = dt_applystatus.Rows[0]["abd_applystatus"].ToString();
            CheckApplyStatus(strStatusCode, strStatusDesc);
        }
        DataTable dt_carmodelnames = wsApplyData.get_Nv_carmodelnames(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
        rpt_representvehicle.DataSource = dt_carmodelnames;
        rpt_representvehicle.DataBind();



    }

    protected void gv_data_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {

            Button btn_Edit = (Button)e.Row.FindControl("btn_Edit");
            btn_Edit.Attributes["ndp_defenseplanid"] = DataBinder.Eval(e.Row.DataItem, "ndp_defenseplanid").ToString().Trim();
            btn_Edit.Attributes["ndp_position"] = DataBinder.Eval(e.Row.DataItem, "ndp_position").ToString().Trim();
            btn_Edit.Attributes["ndp_defenseeqpname"] = DataBinder.Eval(e.Row.DataItem, "ndp_defenseeqpname").ToString().Trim();
            btn_Edit.Attributes["ndp_eqpmaterial"] = DataBinder.Eval(e.Row.DataItem, "ndp_eqpmaterial").ToString().Trim();
            btn_Edit.Attributes["ndp_eqpheight"] = DataBinder.Eval(e.Row.DataItem, "ndp_eqpheight").ToString().Trim();
            btn_Edit.Attributes["ndp_equremark"] = DataBinder.Eval(e.Row.DataItem, "ndp_equremark").ToString().Trim();

            Button btn_Delete = (Button)e.Row.FindControl("btn_Delete");
            btn_Delete.Attributes["ndp_defenseplanid"] = DataBinder.Eval(e.Row.DataItem, "ndp_defenseplanid").ToString().Trim();
            btn_Delete.Attributes["onclick"] = @"if (!confirm('是否確定要刪除?'))return false;";
            HyperLink hlk_file = (HyperLink)e.Row.FindControl("hlk_file");
            hlk_file.Attributes["ndp_defenseplanid"] = DataBinder.Eval(e.Row.DataItem, "ndp_defenseplanid").ToString().Trim();

            Cryptography crypy = new Cryptography();
            wsOtherFileupload oOFD = new wsOtherFileupload();
            int keynum = crypy.getRadNum();
            string[] Oarr = crypy.wsOtherFileuploadValid(keynum);

            int isFileSum = oOFD.get_CountFenseplanSum(hlk_file.Attributes["ndp_defenseplanid"].ToString().Trim(), Oarr[0].ToString(), Oarr[1].ToString());



            hlk_file.NavigateUrl = "fenseplanfilesupload.aspx?applyno=" + Server.UrlEncode(Request["applyno"].ToString()) + "&carcompomodelno=" + Server.UrlEncode(Request["carcompomodelno"].ToString()) + "&filetype=" + Server.UrlEncode(EnCode("07")) + "&defenseplanid=" + Server.UrlEncode(EnCode(hlk_file.Attributes["ndp_defenseplanid"].ToString().Trim()));

            hlk_file.Target = "_blank";
            hlk_file.Text = "檔案上傳";
            if (isFileSum > 0)
                hlk_file.ImageUrl = "../images/show.GIF";


            //Label lbl_position = (Label)e.Row.FindControl("lbl_position");
            //lbl_position.Text = DataBinder.Eval(e.Row.DataItem, "ndp_position").ToString().Trim();
            //Label lbl_defenseeqpname = (Label)e.Row.FindControl("lbl_defenseeqpname");
            //lbl_defenseeqpname.Text = DataBinder.Eval(e.Row.DataItem, "ndp_defenseeqpname").ToString().Trim();
            //Label lbl_eqpmaterial = (Label)e.Row.FindControl("lbl_eqpmaterial");
            //lbl_eqpmaterial.Text = DataBinder.Eval(e.Row.DataItem, "ndp_eqpmaterial").ToString().Trim();
            //Label lbl_eqpheight = (Label)e.Row.FindControl("lbl_eqpheight");
            //lbl_eqpheight.Text = DataBinder.Eval(e.Row.DataItem, "ndp_eqpheight").ToString().Trim();
            //Label lbl_equremark = (Label)e.Row.FindControl("lbl_equremark");
            //lbl_equremark.Text = DataBinder.Eval(e.Row.DataItem, "ndp_equremark").ToString().Trim();

        }
    }


    #region 新增按鈕
    protected void btn_Add_Click(object sender, EventArgs e)
    {
        ViewState["div_Edit"] = "ins";
        ClearData();
        btn_Add.Visible = false;
        btn_Save.Visible = false;
        div_Edit.Style.Add("display", "inline");
    }
    #endregion

    #region 存檔後結束
    protected void btn_Ins_Click(object sender, EventArgs e)
    {
        Cryptography crypy = new Cryptography();
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        int keynum = crypy.getRadNum();
        string[] arr = crypy.wsApplybasedataDetailValid(keynum);
        string applyno = DeCode(Request["applyno"].ToString());
        string carcompomodelno = DeCode(Request["carcompomodelno"].ToString());
        lbl_applyno.Text = applyno;
        lbl_carcompomodelno.Text = carcompomodelno;

        #region 特殊字元判斷-有MasterPage
        ContentPlaceHolder mpContentPlaceHolder;
        mpContentPlaceHolder = (ContentPlaceHolder)Master.FindControl("ContentPlaceHolder1");
        foreach (object ctrl in mpContentPlaceHolder.Controls)
        {
            Control content = (Control)ctrl;
            foreach (Control c in content.Controls)
            {
                if (c is TextBox)
                {
                    TextBox objTextBox = default(TextBox);
                    objTextBox = (TextBox)c;
                    if ((tbWord(objTextBox.Text)))
                    {
                        MessageBox.Show(objTextBox.Text + ":含有系統不允許的特殊字元");
                        return;
                    }
                }
            }

        }
        #endregion

        try
        {



            if (ViewState["div_Edit"].ToString() == "ins")
            {
                if (txt_position_e.Text.Trim() == "")
                {
                    MessageBox.Show("位置未填寫");
                    txt_position_e.Focus();
                    return;
                }
                wsApplyData.Insert_Nv_noisedefenseplan(applyno, carcompomodelno, txt_position_e.Text.Trim(), txt_defenseeqpname_e.Text.Trim(), txt_eqpmaterial_e.Text.Trim(), txt_eqpheight_e.Text.Trim(), txt_equremark_e.Text.Trim(), GetAgentAccount(), arr[0].ToString(), arr[1].ToString());
            }
            else if (ViewState["div_Edit"].ToString() == "edit")
            {

                wsApplyData.Update_Nv_noisedefenseplan(lbl_no.Text.ToString().Trim(), txt_position_e.Text.Trim(), txt_defenseeqpname_e.Text.Trim(), txt_eqpmaterial_e.Text.Trim(), txt_eqpheight_e.Text.Trim(), txt_equremark_e.Text.Trim(), GetAgentAccount(), arr[0].ToString(), arr[1].ToString());
            }
            BindData();
            ClearData();
            ViewState["div_Edit"] = "as";
            div_Edit.Style.Add("display", "none");
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('存檔成功');window.location = 'noisedefenseplan.aspx?applyno=" + Server.UrlEncode(crypy.EnCode(applyno)) + "&carcompomodelno=" + Server.UrlEncode(crypy.EnCode(carcompomodelno)) + "';</script>"); return;

        }
        catch (Exception ex)
        {
            ex.Message.ToString();
            MessageBox.Show("存檔失敗");

        }
    }
    #endregion

    #region 存檔後繼續新增
    protected void btn_ReIns_Click(object sender, EventArgs e)
    {
        Cryptography crypy = new Cryptography();
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        int keynum = crypy.getRadNum();
        string[] arr = crypy.wsApplybasedataDetailValid(keynum);
        string applyno = DeCode(Request["applyno"].ToString());
        string carcompomodelno = DeCode(Request["carcompomodelno"].ToString());
        lbl_applyno.Text = applyno;
        lbl_carcompomodelno.Text = carcompomodelno;

        #region 特殊字元判斷-有MasterPage
        ContentPlaceHolder mpContentPlaceHolder;
        mpContentPlaceHolder = (ContentPlaceHolder)Master.FindControl("ContentPlaceHolder1");
        foreach (object ctrl in mpContentPlaceHolder.Controls)
        {
            Control content = (Control)ctrl;
            foreach (Control c in content.Controls)
            {
                if (c is TextBox)
                {
                    TextBox objTextBox = default(TextBox);
                    objTextBox = (TextBox)c;
                    if ((tbWord(objTextBox.Text)))
                    {
                        MessageBox.Show(objTextBox.Text + ":含有系統不允許的特殊字元");
                        return;
                    }
                }
            }

        }
        #endregion

        try
        {



            if (ViewState["div_Edit"].ToString() == "ins")
            {
                if (txt_position_e.Text.Trim() == "")
                {
                    MessageBox.Show("位置未填寫");
                    txt_position_e.Focus();
                    return;
                }
                wsApplyData.Insert_Nv_noisedefenseplan(applyno, carcompomodelno, txt_position_e.Text.Trim(), txt_defenseeqpname_e.Text.Trim(), txt_eqpmaterial_e.Text.Trim(), txt_eqpheight_e.Text.Trim(), txt_equremark_e.Text.Trim(), GetAgentAccount(), arr[0].ToString(), arr[1].ToString());
            }
            else if (ViewState["div_Edit"].ToString() == "edit")
            {

                wsApplyData.Update_Nv_noisedefenseplan(lbl_no.Text.ToString().Trim(), txt_position_e.Text.Trim(), txt_defenseeqpname_e.Text.Trim(), txt_eqpmaterial_e.Text.Trim(), txt_eqpheight_e.Text.Trim(), txt_equremark_e.Text.Trim(), GetAgentAccount(), arr[0].ToString(), arr[1].ToString());
            }
            BindData();
            ViewState["div_Edit"] = "ins";
            btn_Add.Visible = false;
            btn_Save.Visible = false;
            div_Edit.Style.Add("display", "inline");
        }
        catch (Exception ex)
        {
            ex.Message.ToString();
            MessageBox.Show("存檔失敗");

        }
    }
    #endregion

    protected void btn_Canl_Click(object sender, EventArgs e)
    {
        ClearData();
        div_Edit.Style.Add("display", "none");
        ViewState["div_Edit"] = "";

    }
    private void ClearData()
    {
        txt_position_e.Text = "";
        txt_defenseeqpname_e.Text = "";
        txt_eqpmaterial_e.Text = "";
        txt_eqpheight_e.Text = "";
        txt_equremark_e.Text = "";
        btn_Add.Visible = true;
        btn_Save.Visible = true;
    }
    protected void btn_Edit_Click(object sender, EventArgs e)
    {
        Button btn_Edit = (Button)sender;
        Button btn_Delete = (Button)sender;
        btn_Add.Visible = false;
        btn_Save.Visible = false;

        lbl_no.Text = btn_Edit.Attributes["ndp_defenseplanid"].ToString().Trim();
        txt_position_e.Text = btn_Edit.Attributes["ndp_position"].ToString().Trim();
        txt_defenseeqpname_e.Text = btn_Edit.Attributes["ndp_defenseeqpname"].ToString().Trim();
        txt_eqpmaterial_e.Text = btn_Edit.Attributes["ndp_eqpmaterial"].ToString().Trim();
        txt_eqpheight_e.Text = btn_Edit.Attributes["ndp_eqpheight"].ToString().Trim();
        txt_equremark_e.Text = btn_Edit.Attributes["ndp_equremark"].ToString().Trim();
        ViewState["div_Edit"] = "edit";

        div_Edit.Style.Add("display", "inline");
    }
    protected void btn_Delete_Click(object sender, EventArgs e)
    {
        Button btn_Delete = (Button)sender;

        try
        {

            Cryptography crypy = new Cryptography();
            wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
            int keynum = crypy.getRadNum();
            string[] arr = crypy.wsApplybasedataDetailValid(keynum);

            wsApplyData.Del_Nv_noisedefenseplan(btn_Delete.Attributes["ndp_defenseplanid"].ToString().Trim(), arr[0].ToString(), arr[1].ToString());

            MessageBox.Show("刪除成功");
            ClearData();
            div_Edit.Style.Add("display", "none");
            ViewState["div_Edit"] = "";
            BindData();
        }
        catch (Exception ex)
        {
            ex.Message.ToString();
            MessageBox.Show("刪除失敗");
        }
    }

    protected void btn_Save_Click(object sender, EventArgs e)
    {
        Response.Redirect("carcompomodelList.aspx?applyno=" + Server.UrlEncode(Request["applyno"].ToString()), false);
    }

    protected void rpt_representvehicle_ItemDataBound(object sender, RepeaterItemEventArgs e)
    {
        Label lbl_carengmodelname = (Label)e.Item.FindControl("lbl_carengmodelname");

        if (e.Item.ItemType == ListItemType.Item || e.Item.ItemType == ListItemType.AlternatingItem)
        {
            //判斷是否為代表車
            if (DataBinder.Eval(e.Item.DataItem, "cmdg_berepresentativevehicle").ToString().Trim().Equals("Y", StringComparison.CurrentCultureIgnoreCase) || DataBinder.Eval(e.Item.DataItem, "cmdg_berepresentativevehicle").ToString() == "1")
            {
                lbl_carengmodelname.Text = DataBinder.Eval(e.Item.DataItem, "fullcarstylename").ToString().Trim() + "(代表車)";
            }
            else
            {
                lbl_carengmodelname.Text = DataBinder.Eval(e.Item.DataItem, "fullcarstylename").ToString().Trim();
            }

        }

    }
    protected void btnReflash_Click(object sender, EventArgs e)
    {
        BindData();
    }

}