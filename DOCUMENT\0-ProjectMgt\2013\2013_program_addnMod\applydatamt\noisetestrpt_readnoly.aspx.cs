﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Xml;
using Common;
using System.IO;

public partial class applydatamt_noisetestrpt_readnoly : BaseAdminPage
{

    public DataTable dt_list
    {
        get { return (DataTable)ViewState["_dt_list"]; }
        set { ViewState["_dt_list"] = value; }
    }

    protected void Page_Load(object sender, EventArgs e)
    {
 

        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = '../accountmt/index.aspx';</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" &&
                (
                (
                GetAgentAccountGrpList().Contains("epa_adm") || GetAgentAccountGrpList().Contains("ac_adm") || GetAgentAccountGrpList().Contains("tc_adm") || GetAgentAccountGrpList().Contains("rv_adm") || GetAgentAccountGrpList().Contains("rs_adm") || GetAgentAccountGrpList().Contains("fis_adm") || GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("pg_adm")
                )
                ||
                (
                  (GetAgentAccountGrpList().Contains("cga_adm") || GetAgentAccountGrpList().Contains("cam_adm")) && CheckSingleApplyDataAccessRight(DeCode(Request["applyno"].ToString()))
                )
                )
                )
            {
                if (!IsPostBack)
                {

                    if (Request["applyno"].ToString() != "" && Request["carcompomodelno"].ToString() != "")
                    {
                        Cryptography crypy = new Cryptography();
                        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
                        int keynum = crypy.getRadNum();
                        string[] arr = crypy.wsApplybasedataDetailValid(keynum);
                        DataTable dt_applydatadetail = wsApplyData.get_applybasedate_detail(crypy.DeCode(Request["applyno"].ToString()), arr[0].ToString(), arr[1].ToString());
                        if (dt_applydatadetail.Rows.Count > 0)
                        {
                            //if (dt_applydatadetail.Rows[0]["abd_databelongcompidno"].ToString() == Session["compidno"].ToString())
                            //{
                                BindData();
                                dt_applydatadetail.Dispose();
                            //}
                            //else
                            //{
                            //    dt_applydatadetail.Dispose();
                            //    Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                            //    Response.End();
                            //}
                        }
                        else
                        {
                            dt_applydatadetail.Dispose();
                            Response.Write("發生取不到申請編號錯誤，系統將導回首頁！！");
                            Response.Redirect("../index.aspx");
                        }
                    }
                    else
                    {
                        Response.Write("發生取不到申請編號錯誤，系統將導回首頁！！");
                        Response.Redirect("../index.aspx");
                    }
                }
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }


    }

    #region 更改的麵包屑傳入的參數
    protected void Page_LoadComplete(object sender, EventArgs e)
    {
        ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text = ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text.Replace("applydatamt/applybasedata_detail.aspx", "applydatamt/applybasedata_detail.aspx?applyno=" + Server.UrlEncode(Request["applyno"].ToString()));
        ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text = ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text.Replace("applydatamt/carcompomodel_readnoly.aspx", "applydatamt/carcompomodel_readnoly.aspx?applyno=" + Server.UrlEncode(Request["applyno"].ToString()) + "&carcompomodelno=" + Server.UrlEncode(Request["carcompomodelno"].ToString()));
    }
    #endregion

    public void BindData()
    {
        Cryptography crypy = new Cryptography();
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        int keynum = crypy.getRadNum();
        string[] arr = crypy.wsApplybasedataDetailValid(keynum);
        string applyno = DeCode(Request["applyno"].ToString());
        string carcompomodelno = DeCode(Request["carcompomodelno"].ToString());
        string carmodelno = DeCode(Request["carmodelno"].ToString());
        lbl_applyno.Text = applyno;
        lbl_carcompomodelno.Text = carcompomodelno;

        dt_list = wsApplyData.get_Nv_cardetails(applyno, carcompomodelno, carmodelno, arr[0].ToString(), arr[1].ToString());
       
        if (dt_list.Rows.Count > 0)
        {
            lbl_databelongcompname.Text = dt_list.Rows[0]["abd_databelongcompname"].ToString();
            lbl_testfactory.Text = dt_list.Rows[0]["ct_tf_desc"].ToString();
            lbl_carstyleyear.Text = dt_list.Rows[0]["abd_carstyleyear"].ToString();
        }
        else
        {
            lbl_databelongcompname.Text = "-";
            lbl_testfactory.Text = "-";
            lbl_carstyleyear.Text = "-";         
        }
        //取得車型名稱
        DataTable dt_carmodelnames = wsApplyData.get_Nv_carmodelnames(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
        if (dt_carmodelnames.Rows.Count > 0)
        {
            rpt_representvehicle.DataSource = dt_carmodelnames;
            rpt_representvehicle.DataBind();
        }
      
        #region 資料狀態-當申請型式 不為新申請 且有 50,55資料 取得上次申請代表車資料 否 擇讀取自己本身填寫資料
        DataTable dt_applystatus = wsApplyData.get_applystatus(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
        bool b_IsApplied = wsApplyData.IsApplied(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
        if (dt_applystatus.Rows.Count > 0 && !(dt_applystatus.Rows[0]["ct_aps_desc"].Equals("新申請")) && b_IsApplied)
        {

            DataTable dt_unNewApplyData = wsApplyData.get_unNewApplyData(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
            if (dt_unNewApplyData.Rows.Count > 0)
            {
                applyno = dt_unNewApplyData.Rows[0]["cmdg_applyno"].ToString();
                carcompomodelno = dt_unNewApplyData.Rows[0]["cmdg_carcompomodelno"].ToString();
                carmodelno = dt_unNewApplyData.Rows[0]["cmdg_carmodelno"].ToString();
            }
        }
        #endregion
        //取得噪音標準值
        DataTable dt_noisestandarddata = wsApplyData.get_Nv_noisestandarddata(applyno, carcompomodelno, carmodelno, arr[0].ToString(), arr[1].ToString());
        if (dt_noisestandarddata.Rows.Count > 0)
        {
            lbl_speedupnoisestandardvalue.Text = dt_noisestandarddata.Rows[0]["nsd_speedupstandardvalue"].ToString();
            lbl_staynoisestandardvalue.Text = dt_noisestandarddata.Rows[0]["nsd_stationarystandardvalue"].ToString();
          
        }
        else
        {
            lbl_speedupnoisestandardvalue.Text = "-";
            lbl_staynoisestandardvalue.Text = "-";
        }
        
        //取得單筆測定報告
        DataTable dt_noisetestrpt = wsApplyData.get_Nv_noisetestrpt(carcompomodelno, carmodelno, arr[0].ToString(), arr[1].ToString());
        if (dt_noisetestrpt.Rows.Count > 0)
        {
            lbl_engineno.Text = dt_noisetestrpt.Rows[0]["ntr_engineno"].ToString();
            lbl_carbodyno.Text = dt_noisetestrpt.Rows[0]["ntr_carbodyno"].ToString();
            lbl_testrptno.Text = dt_noisetestrpt.Rows[0]["ntr_testrptno"].ToString();
            lbl_speedupcondi.Text = dt_noisetestrpt.Rows[0]["ntr_speedupcondi"].ToString();
            lbl_stayrpmparam.Text = dt_noisetestrpt.Rows[0]["ntr_stayrpmparam"].ToString();
            lbl_stayrpmcondi.Text = dt_noisetestrpt.Rows[0]["ntr_stayrpmcondi"].ToString();
            lbl_speedupnoise.Text = dt_noisetestrpt.Rows[0]["ntr_speedupnoise"].ToString();
            lbl_staynoise.Text = dt_noisetestrpt.Rows[0]["ntr_staynoise"].ToString();
            lbl_modaccount.Text = dt_noisetestrpt.Rows[0]["ntr_modaccount"].ToString();
            lbl_moddate.Text = dt_noisetestrpt.Rows[0]["ntr_moddate"].ToString();
            lbl_producercountry.Text = dt_noisetestrpt.Rows[0]["ntr_producercountry"].ToString();

            //取得備註資料
            DataTable dt_remark = wsApplyData.get_Nv_noisetestrpt_remark(carcompomodelno, carmodelno, dt_noisetestrpt.Rows[0]["ntr_testrptno"].ToString(), arr[0].ToString(), arr[1].ToString());
            if (dt_remark.Rows.Count > 0)
            {
                rpt_remark.DataSource = dt_remark;
                rpt_remark.DataBind();
            }
            //取得上傳檔案名稱
            DataTable dt_rptfilename = wsApplyData.get_Nv_rptfilename(dt_noisetestrpt.Rows[0]["ntr_rptfileid"].ToString(), arr[0].ToString(), arr[1].ToString());
            if (dt_rptfilename.Rows.Count > 0)
            {
                hlk_rptfileid.Text = dt_rptfilename.Rows[0]["afi_userfilename"].ToString();
                hlk_rptfileid.NavigateUrl = "../FileDownloadSeal.aspx?File=" + Server.UrlEncode(crypy.EnCode(dt_rptfilename.Rows[0]["afi_fileid"].ToString()));              
            }
            //取得加速測定數值
            DataTable dt_speedupinfo = wsApplyData.get_Nv_speedupinfo(carcompomodelno, carmodelno, dt_noisetestrpt.Rows[0]["ntr_testrptno"].ToString(), arr[0].ToString(), arr[1].ToString());
            if (dt_speedupinfo.Rows.Count > 0)
            {
                rpt_speedupgear.DataSource = dt_speedupinfo;
                rpt_speedupgear.DataBind();

                rpt_speed.DataSource = dt_speedupinfo;
                rpt_speed.DataBind();

            }
        }
        else
        {
            lbl_testrptno.Text = "-";
            lbl_engineno.Text = "-";
            lbl_carbodyno.Text = "-";
            lbl_speedupcondi.Text = "-";
            lbl_stayrpmparam.Text = "-";
            lbl_stayrpmcondi.Text = "-";
            lbl_speedupnoise.Text = "-";
            lbl_staynoise.Text = "-";
            lbl_modaccount.Text = "-";
            lbl_moddate.Text = "-";
        }       
    }
    protected void rpt_representvehicle_ItemDataBound(object sender, RepeaterItemEventArgs e)
    {

        Label lbl_carengmodelname = (Label)e.Item.FindControl("lbl_carengmodelname");

        if (e.Item.ItemType == ListItemType.Item || e.Item.ItemType == ListItemType.AlternatingItem)
        {
            //判斷是否為代表車
            if (DataBinder.Eval(e.Item.DataItem, "cmdg_berepresentativevehicle").ToString().Trim().Equals("Y", StringComparison.CurrentCultureIgnoreCase) || DataBinder.Eval(e.Item.DataItem, "cmdg_berepresentativevehicle").ToString() == "1")
            {
                lbl_carengmodelname.Text = DataBinder.Eval(e.Item.DataItem, "fullcarstylename").ToString().Trim() + "(代表車)";
            }
            else
            {
                lbl_carengmodelname.Text = DataBinder.Eval(e.Item.DataItem, "fullcarstylename").ToString().Trim();
            }

        }
    }
    protected void btn_Print_Click(object sender, EventArgs e)
    {
        Response.Write("<script>window.print();</script>");
    }
}