﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Configuration;

public partial class index : JSCommon
{
    Cryptography crypt = new Cryptography();
    private string Msg = string.Empty;
    Library.ExamineProcedure ep = new Library.ExamineProcedure();
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            if (Convert.ToBoolean(ConfigurationManager.AppSettings["DEBUGMode"].ToString()))
            {
                //測試
                txt_ID.Text = "U1234567";
                txt_account.Text = "U1234567";
            }

            #region 註冊轉成大寫的 javascript
            //統一編號	
            txt_ID.Attributes.Add("onkeypress", "javascript:this.value = this.value.toUpperCase();");
            txt_ID.Attributes.Add("onblur", "javascript:this.value = this.value.toUpperCase();");
            #endregion

            if (Request["Error"] != null)
            {
                if (Request["Error"] == "SessionError")
                {
                    Msg = "由於您已超過一小時沒有作業, 系統連線已中斷.若需繼續使用, 請重新登入";
                }
                else if (Request["Error"] == "NoRight")
                {
                    Msg = "您沒有檢視該網頁的權限";
                }
                else if (Request["Error"] == "NULL")
                {
                    if (crypt.CryptographyVerify())
                    {
                        wsAccountInfo wsaccount = new wsAccountInfo();
                        string[] radom_A = crypt.wsAccountValidate(crypt.getRadNum());
                        DataSet ds_account = wsaccount.getAccountBase(crypt.DeCode(Session["account"].ToString()), radom_A[0].ToString(), radom_A[1].ToString());
                        if (ds_account.Tables[0].Rows[0]["ai_accountstatus"].ToString() == "1")
                        {
                            ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('系統登入成功，請先變更密碼後方能使用!');
                                                    window.location = 'changepwd.aspx';</script>");
                        }
                        else
                        {
                            Msg = "登入系統完成";
                        }
                    }
                    else
                    {
                        Msg = "您沒有檢視該網頁的權限";
                    }
                }
                else if (Request["Error"] == "Out")
                {
                    Msg = "登出成功";
                }
                if (Msg.Length > 0)
                    Alert(Msg);
            }
            int keynum = crypt.getRadNum();
            string[] array = crypt.wsNewsValidate(keynum);
            wsNewsInfo wsnews = new wsNewsInfo();
            //取得News
            rpt_News.DataSource = wsnews.getNewsList(true, false, false, "", "", array[0].ToString(), array[1].ToString());
            rpt_News.DataBind();
            //取得參考資料
            rpt_Brief.DataSource = wsnews.getBrief(true, false, false, "", "", array[0].ToString(), array[1].ToString());
            rpt_Brief.DataBind();
        }
    }
    protected void btn_login_Click(object sender, EventArgs e)
    {
        try
        {
            wsLoginCheck wslogin = new wsLoginCheck();
            string[] radom_L = crypt.wsCheckLogin(crypt.getRadNum());

            #region 特殊字元判斷-有MasterPage
            ContentPlaceHolder mpContentPlaceHolder;
            mpContentPlaceHolder = (ContentPlaceHolder)Master.FindControl("ContentPlaceHolder1");
            BaseAdminPage egpadmin = new BaseAdminPage();
            foreach (object ctrl in mpContentPlaceHolder.Controls)
            {
                Control content = (Control)ctrl;
                foreach (Control c in content.Controls)
                {
                    if (c is TextBox)
                    {
                        TextBox objTextBox = default(TextBox);
                        objTextBox = (TextBox)c;
                        if ((egpadmin.tbWord(objTextBox.Text)))
                        {
                            MessageBox.Show(objTextBox.Text + ":含有系統不允許的特殊字元");
                            return;
                        }
                    }
                }

            }
            #endregion

            if (Session["compidno"] != null || Session["account"] != null)
            {
                MessageBox.Show("請先進行帳號登出作業");
                return;
            }
            if (!(wslogin.FailAccountLog(txt_account.Text.Trim(), radom_L[0].ToString(), radom_L[1].ToString())))
            {
                MessageBox.Show("您登入失敗次數過多，請連繫系統管理員");
                return;
            }
            Session.RemoveAll();	//結束目前 Session.
            string compidno = txt_ID.Text.Trim();
            string account = txt_account.Text.Trim();

            if (ep.danger_word(compidno) || ep.danger_word(account) || ep.danger_word(txt_password.Text.Trim()))
            {
                MessageBox.Show("不可輸入 ' -- / * 等字元");
                txt_account.Text = "";
                txt_ID.Text = "";
                txt_password.Text = "";
                return;
            }

            if (account != "" && compidno != "" && txt_password.Text.Trim() != "")
            {
                string password = crypt.GetPAWDHash(txt_password.Text.Trim());

                if (wslogin.AccountCheck(account, password, crypt.EnCode(compidno), radom_L[0].ToString(), radom_L[1].ToString()))
                {
                    //紀錄登入成功
                    wslogin.InsertAccountLog(account, "1", radom_L[0].ToString(), radom_L[1].ToString()); // 1 表示成功   

 
                    Session["encryfunc"] = crypt.GetEnCodeMode();
                    switch (Session["encryfunc"].ToString())
                    {
                        case "M":
                            Session["Vkey"] = crypt.GetMD5(account + crypt.GetIP());
                            break;
                        case "H":
                            Session["Vkey"] = crypt.GetHtmlEnCode(account + crypt.GetIP());
                            break;
                        case "B":
                            Session["Vkey"] = crypt.GetBig(account + crypt.GetIP());
                            break;
                        case "U":
                            Session["Vkey"] = crypt.GetUTF(account + crypt.GetIP());
                            break;
                        default:
                            break;
                    }
                    Session["compidno"] = crypt.EnCode(compidno);
                    Session["account"] = crypt.EnCode(account);
                    //MessageBox.Show("OK");

                    //判斷是否已填寫個資同意
                    wsAccountInfo wsaccount = new wsAccountInfo();
                    string[] radom_A = crypt.wsAccountValidate(crypt.getRadNum());
                    if (!wsaccount.Isdeclareinfo(account, radom_A[0].ToString(), radom_A[1].ToString()))
                    {
                        Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script> window.location = 'personalinfo_declare.aspx?account=" + Server.UrlEncode(crypt.EnCode(account)) + "';</script>"); return;
                    }  
                    Response.Redirect("index.aspx?Error=NULL", false);

                }
                else
                {
                    Session.RemoveAll();
                    //非已核發的帳號
                    //取得帳號目前狀態
                    wsAccountInfo wsaccount = new wsAccountInfo();
                    string[] radom_A = crypt.wsAccountValidate(crypt.getRadNum());
                    DataSet ds_account = wsaccount.getAccountBase(txt_account.Text.Trim(), radom_A[0].ToString(), radom_A[1].ToString());
                    if (ds_account.Tables[0].Rows.Count > 0)
                    {
                        //判斷帳號目前狀態 ai_accountstatus
                        string accountstatus = ds_account.Tables[0].Rows[0]["ai_accountstatus"].ToString();
                        if (accountstatus == "0" || accountstatus == "4")
                            MessageBox.Show("您的帳號尚未核發，不可進行登入作業");
                        else if (accountstatus == "3")
                        {
                            //若該帳號已被停用，錯誤訊息應顯示 “ 該帳號已被停用，請洽系統管理人!”
                            MessageBox.Show("該帳號已被停用，請洽系統管理人!");
                        }
                        else
                        {
                            //紀錄登入失敗
                            wslogin.InsertAccountLog(txt_account.Text.Trim(), "0", radom_L[0].ToString(), radom_L[1].ToString()); // 0 表示失敗
                            //統編+帳號+密碼 若不正確，錯誤訊息請改為“【統一編號】或【帳號】或【密碼】不正確，請重新輸入!”
                            MessageBox.Show("【統一編號】或【帳號】或【密碼】不正確，請重新輸入!");
                        }
                    }
                    else
                    {
                        //紀錄登入失敗
                        wslogin.InsertAccountLog(txt_account.Text.Trim(), "0", radom_L[0].ToString(), radom_L[1].ToString()); // 0 表示失敗
                        //統編+帳號+密碼 若不正確，錯誤訊息請改為“【統一編號】或【帳號】或【密碼】不正確，請重新輸入!”
                        MessageBox.Show("【統一編號】或【帳號】或【密碼】不正確，請重新輸入!");
                    }

                    txt_ID.Text = "";
                    txt_account.Text = "";
                    txt_password.Text = "";

                }
            }
            else
            {
                //統一編號或帳號或密碼錯誤
                ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('請輸入統一編號或帳號及密碼');
                                                    window.location = 'index.aspx';</script>");
            }
        }
        catch (Exception ex)
        {
            baseFunc func = new baseFunc();
            func.InsertLogMsg("", "Login", ex.Message.ToString() + "*" + ex.Source, "");
        }
    }
    protected void rpt_News_ItemDataBound(object sender, RepeaterItemEventArgs e)
    {
        //最新消息
        if (e.Item.ItemIndex < 0)
        {
            return;
        }

        HyperLink hlk_title = (HyperLink)e.Item.FindControl("hlk_title");
        string ED_TITLE = DataBinder.Eval(e.Item.DataItem, "ed_title").ToString();
        string ED_ID = DataBinder.Eval(e.Item.DataItem, "ed_id").ToString();
        hlk_title.Text = ED_TITLE;
        //if (ED_ID.Equals(""))
        //{
        //    hlk_title.NavigateUrl = "../FileDownloadSeal.aspx?File=" + Server.UrlEncode(crypt.EnCode("hSWFY0cTCus%3d"));
        //}
        //else
        //{
            hlk_title.NavigateUrl = string.Format("newscontent.aspx?datatype={0}&EDID={1}", Server.UrlEncode(crypt.EnCode("01")), Server.UrlEncode(crypt.EnCode(ED_ID)));
        //}
        Label lbl_newsdate = (Label)e.Item.FindControl("lbl_newsdate");
        string newsdate = Convert.ToDateTime(DataBinder.Eval(e.Item.DataItem, "ed_newsdate").ToString()).ToString("yyyy-MM-dd");
        lbl_newsdate.Text = newsdate;
    }
    protected void rpt_Brief_ItemDataBound(object sender, RepeaterItemEventArgs e)
    {
        //取得參考資料

        if (e.Item.ItemIndex < 0)
        {
            return;
        }
        HyperLink hlk_title = (HyperLink)e.Item.FindControl("hlk_title");
        string ED_TITLE = DataBinder.Eval(e.Item.DataItem, "afi_userfilename").ToString();
        string FileID = DataBinder.Eval(e.Item.DataItem, "ed_afi_fileid").ToString();
        string FilePath = DataBinder.Eval(e.Item.DataItem, "afi_filepath").ToString();
        string FileName = DataBinder.Eval(e.Item.DataItem, "afi_sysfilename").ToString();
        hlk_title.Text = ED_TITLE;
        if (File.Exists(FilePath + FileName))
        {
            hlk_title.NavigateUrl = "../FileDownloadSeal.aspx?File=" + Server.UrlEncode(crypt.EnCode(FileID));
        }

        Label lbl_newsdate = (Label)e.Item.FindControl("lbl_newsdate");
        string newsdate = Convert.ToDateTime(DataBinder.Eval(e.Item.DataItem, "ed_newsdate").ToString()).ToString("yyyy-MM-dd");
        lbl_newsdate.Text = newsdate;
    }
    protected void imgbtn_newsmore_Click(object sender, ImageClickEventArgs e)
    {
        Response.Redirect("newslist.aspx?datatype=" + Server.UrlEncode(crypt.EnCode("01")), false);
    }
    protected void imgbtn_briefmore_Click(object sender, ImageClickEventArgs e)
    {
        Response.Redirect("newslist.aspx?datatype=" + Server.UrlEncode(crypt.EnCode("03")), false);
    }
}