﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

using System.Configuration;
using System.IO;
using System.Text;
using System.Web.UI.HtmlControls;
using System.Data;
using System.Data.SqlClient;
using System.Globalization;
using System.Xml;
using Common;





public partial class applydatamt_AlterCarcompomodelno : BaseAdminPage
{

    protected void Page_Load(object sender, EventArgs e)
    {
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.close;</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" &&
                (
                (
                GetAgentAccountGrpList().Contains("pg_adm") || GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("rs_adm") || GetAgentAccountGrpList().Contains("fis_adm") || GetAgentAccountGrpList().Contains("rv_adm")
                )
                ||
                (
                  (GetAgentAccountGrpList().Contains("cga_adm") || GetAgentAccountGrpList().Contains("cam_adm")) && CheckSingleApplyDataAccessRight(DeCode(Request["applyno"].ToString()))
                )
                )
                )
            {
                if (!IsPostBack)
                {
                    if (Request["applyno"].ToString() != "")
                    {
                        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
                        Cryptography crypy = new Cryptography();
                        int keynum = crypy.getRadNum();
                        string[] arr = crypy.wsApplybasedataDetailValid(keynum);
                        DataTable dt_applydatadetail = wsApplyData.get_applybasedate_detail(crypy.DeCode(Request["applyno"].ToString()), arr[0].ToString(), arr[1].ToString());
                        if (dt_applydatadetail.Rows.Count > 0)
                        {
                            //if (dt_applydatadetail.Rows[0]["abd_databelongcompidno"].ToString() == Session["compidno"].ToString())
                            //{
                                BindData();
                                dt_applydatadetail.Dispose();
                            //}
                            //else
                            //{
                            //    dt_applydatadetail.Dispose();
                            //    Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                            //    Response.End();
                            //}
                        }
                        else
                        {
                            dt_applydatadetail.Dispose();
                            Response.Write("發生取不到申請編號錯誤，系統將導回首頁！！");
                            Response.Redirect("../index.aspx");
                        }
                    }
                    else
                    {
                        Response.Write("發生取不到申請編號錯誤，系統將導回首頁！！");
                        Response.Redirect("../index.aspx");
                    }
                }
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }

        }
    }

    //#region 申請型式不為新車型，則不可填寫並導回查詢頁面
    //public void CheckApplyType(string sApplyType)
    //{
    //    if (!(sApplyType.Equals("沿用") || sApplyType.Equals("新車型")))
    //    {          
    //        Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('申請型式為「" + sApplyType + "」，不可編輯！！');window.close();</script>");     
    //    }
    //}
    //#endregion

    #region 判斷資料狀態不為填寫中、補件、拒件，則不可填寫並導回查詢頁面
    public void CheckApplyStatus(string sStatusCode, string sStatusDesc)
    {
        if (!(sStatusCode.Equals("10") || sStatusCode.Equals("40") || sStatusCode.Equals("90")))
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('資料狀態為「" + sStatusDesc + "」，不可編輯！！');window.close();</script>");     
        }
    }
    #endregion
    public void BindData()
    {

        Cryptography crypy = new Cryptography();
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        int keynum = crypy.getRadNum();
        string[] arr = crypy.wsApplybasedataDetailValid(keynum);
        string applyno = DeCode(Request["applyno"].ToString());
        string carcompomodelno = DeCode(Request["carcompomodelno"].ToString());
        string strStatusCode = "";
        string strStatusDesc = "";
        string strApplyType = "";
        lab_carcompomodelno.Text = carcompomodelno;
        //申請型式
        DataTable dt_applyno = wsApplyData.get_applyno(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
        if (dt_applyno.Rows.Count > 0)
        {
            strApplyType = dt_applyno.Rows[0]["ct_at_desc"].ToString();
            //CheckApplyType(strApplyType);
        }
        //資料狀態
        DataTable dt_applystatus = wsApplyData.get_applystatus(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
        if (dt_applystatus.Rows.Count > 0)
        {
            strStatusDesc = dt_applystatus.Rows[0]["ct_aps_desc"].ToString();
            strStatusCode = dt_applystatus.Rows[0]["abd_applystatus"].ToString();
            CheckApplyStatus(strStatusCode, strStatusDesc);
        }

    }
    private bool CheckData()
    {
        StringBuilder sb = new StringBuilder();
        Cryptography crypy = new Cryptography();
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        string applyno = DeCode(Request["applyno"].ToString());
        string carcompomodelno = DeCode(Request["carcompomodelno"].ToString());

        int keynum = crypy.getRadNum();
        string[] arr_apply = crypy.wsApplybasedataDetailValid(keynum);

        string compidnoE = Session["compidno"].ToString(); //公司統編
        DataView dv = new MailParaControl().GetApplyBaseData(applyno);
        string EngineFamily = dv[0]["EngineFamily"].ToString(); //引擎族

       
        #region 沿用的車名稱修改 前10碼不能與上次申請的不一樣
        DataTable dt_applystatus = wsApplyData.get_applystatus(applyno, carcompomodelno, arr_apply[0].ToString(), arr_apply[1].ToString());
        DataTable dt_unNewApplyData = new DataTable();
        bool b_IsApplied = wsApplyData.IsApplied(applyno, carcompomodelno, arr_apply[0].ToString(), arr_apply[1].ToString());
        if (dt_applystatus.Rows.Count > 0 && !(dt_applystatus.Rows[0]["ct_aps_desc"].Equals("新申請")) && b_IsApplied)
        {
                if (!wsApplyData.CheckCarnoLeft10(applyno, lab_carcompomodelno.Text.Trim(), txt_new_carcompomodelno.Text.Trim(), arr_apply[0].ToString(), arr_apply[1].ToString()))
                {
                    sb.Append(string.Format("{0}：非新申請，前10碼需與上次申請的一樣!\n", txt_new_carcompomodelno.Text.Trim()));
                    txt_new_carcompomodelno.Text = "";
                }
        }
        #endregion

        bool isRecarcompomodelno = wsApplyData.CheckhReCarcompomodelno(txt_new_carcompomodelno.Text.Trim(), arr_apply[0].ToString(), arr_apply[1].ToString());
        if (isRecarcompomodelno)
        {
            sb.Append(string.Format("{0}：此車型代碼已存在，請重新輸入!\n", txt_new_carcompomodelno.Text.Trim()));
            txt_new_carcompomodelno.Text = "";
        }


        if (sb.Length > 0)
        {
            MessageBox.Show(sb.ToString());
            return false;
        }
        else
        {
            return true;
        }
    }
    protected void btn_Alter_Click(object sender, EventArgs e)
    {
        #region 特殊字元判斷-沒有MasterPage
        foreach (Control ctl in Page.Form.Controls)
        {
            Control content = (Control)ctl;
            foreach (Control c in content.Controls)
            {
                if (c is TextBox)
                {
                    TextBox objTextBox = default(TextBox);
                    objTextBox = (TextBox)c;
                    if ((tbWord(objTextBox.Text)))
                    {
                        MessageBox.Show(objTextBox.Text + ":含有系統不允許的特殊字元");
                        return;
                    }
                }
            }
        }
        #endregion
        try
        {
            if (CheckData())
            {
                //更新所有的carcompomodelno
                StringBuilder sb = new StringBuilder();
                Cryptography crypy = new Cryptography();
                wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
                int keynum = crypy.getRadNum();
                string[] arrA = crypy.wsApplybasedataDetailValid(keynum);
                string applyno = DeCode(Request["applyno"].ToString());
                string carcompomodelno = DeCode(Request["carcompomodelno"].ToString());
                string strApplyTypes = "";
                //申請型式
                DataTable dt_applyno = wsApplyData.get_applyno(applyno, carcompomodelno, arrA[0].ToString(), arrA[1].ToString());
                if (dt_applyno.Rows.Count > 0)
                {
                    strApplyTypes = dt_applyno.Rows[0]["ct_at_desc"].ToString();

                }
                bool return_result = wsApplyData.AlterCarcompomodelNo(applyno, carcompomodelno, txt_new_carcompomodelno.Text.Trim(), strApplyTypes, arrA[0].ToString(), arrA[1].ToString());
                if (return_result)
                {
                    String scriptString = "";
                    scriptString = @"<script language='javascript' type='text/javascript'>alert('變更型組代號完成!');window.opener.raiseAlterCarcompomodelnoback();window.close();</script>";
                    this.RegisterStartupScript("s", scriptString);
                }
                else
                {
                    MessageBox.Show("異動失敗");
                }
            }
        }
        catch (Exception ex)
        {
            ex.Message.ToString();
            MessageBox.Show("異動失敗!\n");
        }
    }
}