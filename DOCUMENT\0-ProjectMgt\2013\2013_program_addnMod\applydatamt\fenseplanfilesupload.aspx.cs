﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.IO;
using System.Text;
using System.Drawing;

using System.Xml;
using Common;

public partial class applydatamt_fenseplanfilesupload : BaseAdminPage
{
    public DataTable dtList
    {
        get { return (DataTable)ViewState["dtList"]; }
        set { ViewState["dtList"] = value; }
    }

    protected void Page_Load(object sender, EventArgs e)
    {

        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = '../accountmt/index.aspx';</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" &&
                (
                (
                 GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("pg_adm") || GetAgentAccountGrpList().Contains("rs_adm") || GetAgentAccountGrpList().Contains("fis_adm") || GetAgentAccountGrpList().Contains("rv_adm")
                )
                ||
                (
                  (GetAgentAccountGrpList().Contains("cga_adm") || GetAgentAccountGrpList().Contains("cam_adm")) && CheckSingleApplyDataAccessRight(DeCode(Request["applyno"].ToString()))
                )
                )
                )
            {
                if (!IsPostBack)
                {
                    if (Request["applyno"].ToString() != "")
                    {

                        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
                        Cryptography crypy = new Cryptography();
                        int keynum = crypy.getRadNum();
                        string[] arr = crypy.wsApplybasedataDetailValid(keynum);

                        DataTable dt_applydatadetail = wsApplyData.get_applybasedate_detail(crypy.DeCode(Request["applyno"].ToString()), arr[0].ToString(), arr[1].ToString());
                        if (dt_applydatadetail.Rows.Count > 0)
                        {
                            //if (dt_applydatadetail.Rows[0]["abd_databelongcompidno"].ToString() == Session["compidno"].ToString())
                            //{
                                BindData();
                                dt_applydatadetail.Dispose();
                            //}
                            //else
                            //{
                            //    dt_applydatadetail.Dispose();
                            //    Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                            //    Response.End();
                            //}
                        }
                        else
                        {
                            dt_applydatadetail.Dispose();
                            Response.Write("<script languge='javascript'>alert('發生取不到申請編號錯誤，系統將導回首頁！！');window.location.href='../index.aspx'</script>");
                            Response.End();
                        }
                    }
                    else
                    {
                        Response.Write("<script languge='javascript'>alert('發生取不到申請編號錯誤，系統將導回首頁！！');window.location.href='../index.aspx'</script>");
                        Response.End();
                    }
                }
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }
    }

    #region 判斷資料狀態不為填寫中、補件、拒件，則不可填寫並導回查詢頁面
    public void CheckApplyStatus(string sStatusCode, string sStatusDesc)
    {
        if (!(sStatusCode.Equals("10") || sStatusCode.Equals("40") || sStatusCode.Equals("90")))
        {
            Response.Write("<script languge='javascript'>alert('資料狀態為「" + sStatusDesc + "」，不可編輯，系統將導回查詢頁面！！');window.location.href='nv_applylistqry.aspx'</script>");
            Response.End();
        }
    }
    #endregion

    public void BindData()
    {
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        Cryptography crypy = new Cryptography();
        wsOtherFileupload oOFD = new wsOtherFileupload();
        int keynum = crypy.getRadNum();
        string[] arr = crypy.wsOtherFileuploadValid(keynum);
        string[] Warr = crypy.wsApplybasedataDetailValid(keynum);
        string[] radom_C = wsCompanyValidate(getRadNum());
        string SelectedTextList = string.Empty;
        string applyno = DeCode(Request["applyno"].ToString());
        string carcompomodelno = DeCode(Request["carcompomodelno"].ToString());
        string filetype = DeCode(Request["filetype"].ToString());
        string defenseplanid = DeCode(Request["defenseplanid"].ToString());
        string strStatusCode = "";
        string strStatusDesc = "";
        //資料狀態
        DataTable dt_applystatus = wsApplyData.get_applystatus(applyno, carcompomodelno, Warr[0].ToString(), Warr[1].ToString());
        if (dt_applystatus.Rows.Count > 0)
        {          
            strStatusDesc = dt_applystatus.Rows[0]["ct_aps_desc"].ToString();
            strStatusCode = dt_applystatus.Rows[0]["abd_applystatus"].ToString();
            CheckApplyStatus(strStatusCode, strStatusDesc);
        }

        dtList = oOFD.get_uploadfenseplanfiles(applyno, carcompomodelno, defenseplanid, arr[0].ToString(), arr[1].ToString());
        gv_data.DataSource = dtList;
        gv_data.DataBind();

    }

    protected void gv_data_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            CheckBox cbl_select = (CheckBox)e.Row.FindControl("cbl_select");
            LinkButton lbnt_userfilename = (LinkButton)e.Row.FindControl("lbnt_userfilename");
            string userfilename = DataBinder.Eval(e.Row.DataItem, "afi_userfilename").ToString();
            string sAfi_fileid = DataBinder.Eval(e.Row.DataItem, "afi_fileid").ToString().Trim();
                           
            cbl_select.DataBind();
            lbnt_userfilename.Attributes["afi_fileid"] = sAfi_fileid;
            lbnt_userfilename.Text = userfilename;
            lbnt_userfilename.Attributes.Add("onclick", "javascript:window.open('../FileDownloadSeal.aspx?File=" + Server.UrlEncode(EnCode(sAfi_fileid)) + "','_blank');");

        }
    }
    protected void btn_Add_Click(object sender, EventArgs e)
    {

        try
        {
            if (fupload_companfile.FileName.Length > 0)
            {
                string[] strFileType = fupload_companfile.PostedFile.FileName.Split('.');
                string subFileName = strFileType[strFileType.GetUpperBound(0)];
                if (subFileName.ToUpper() != "pdf" && subFileName.ToUpper() != "PDF")
                {
                    MessageBox.Show("您上傳的檔案為" + subFileName + "，請改上傳PDF檔\n");
                    return;
                }

            }
            else if (fupload_companfile.FileName.Length == 0)
            {
                MessageBox.Show("您尚未選擇欲上傳的檔案");
                return;
            }

            
            wsCompany wscompany = new wsCompany();
            wsOtherFileupload oOFD = new wsOtherFileupload();
            Cryptography crypy = new Cryptography();
            wsFileInfo wsfile = new wsFileInfo();
            string applyno = DeCode(Request["applyno"].ToString());
            string carcompomodelno = DeCode(Request["carcompomodelno"].ToString());
            string filetype = DeCode(Request["filetype"].ToString());
            string[] radom_C = crypy.wsCompanyValidate(getRadNum());
            string defenseplanid = DeCode(Request["defenseplanid"].ToString());
            string[] radom_F = crypy.wsFileValidate(crypy.getRadNum());
            string strFileId = "";
            DataSet ds_company = wscompany.getCompanyBase(Session["compidno"].ToString(), radom_C[0].ToString(), radom_C[1].ToString());

            #region 檔案上傳、新增審驗資料
            string filepath = ConfigurationManager.AppSettings["FilePath"].ToString();
            string strUpLoadPath = filepath + DateTime.Now.ToString("yyyy") + "\\" + applyno + "\\";
            //如果上傳路徑中沒有該目錄，則自動新增

            if (!Directory.Exists(strUpLoadPath.Substring(0, strUpLoadPath.LastIndexOf("\\"))))
            {
                Directory.CreateDirectory(strUpLoadPath.Substring(0, strUpLoadPath.LastIndexOf("\\")));
            }
            //原始檔名
            string[] strFileNm = fupload_companfile.PostedFile.FileName.Split('\\');
            string filename1 = strFileNm[strFileNm.GetUpperBound(0)];

            //系統名稱          
            string filename2 = DateTime.Now.ToString("yyyyMMddHHmmss") + "_" + strFileNm[strFileNm.GetUpperBound(0)];
            string strDescr = "噪音防治對策";
            strFileId = wsfile.insertapplydatafile(filetype, "1", filename1, filename2, strUpLoadPath, strDescr, GetAgentAccount(), radom_F[0].ToString(), radom_F[1].ToString());
            if (strFileId != "-1")
            {
                //新增審驗資料            
                string[] radom_E = crypy.wsOtherFileuploadValid(crypy.getRadNum());
                oOFD.Insert_Nv_noisedefenseplanfiles(applyno, carcompomodelno, defenseplanid, strFileId, radom_E[0].ToString(), radom_E[1].ToString()); /*table 的欄位不夠長，所以不加密儲存*/
            }
            //實體檔案上傳
            string newfile = strUpLoadPath + filename2;
            fupload_companfile.PostedFile.SaveAs(newfile);

            #endregion

            MessageBox.Show("檔案上傳成功");

            BindData();
        }
        catch (Exception ex)
        {
            ex.Message.ToString();
            MessageBox.Show("檔案上傳失敗");
        }
    }
    protected void btn_Delet_Click(object sender, EventArgs e)
    {
        int selcount = 0;
        foreach (GridViewRow grv in gv_data.Rows)
        {

            if (grv.RowType == DataControlRowType.DataRow)
            {
                CheckBox cbl_select = (CheckBox)grv.FindControl("cbl_select");
                if (cbl_select.Checked)
                {
                    selcount += 1;
                }
            }
        }

        if (selcount > 0)
        {
            string s_afinostring = string.Empty;

            foreach (GridViewRow grv in gv_data.Rows)
            {
                if (grv.RowType == DataControlRowType.DataRow)
                {
                    CheckBox cbl_select = (CheckBox)grv.FindControl("cbl_select");
                    Label lbl_afino = (Label)grv.FindControl("lbl_afino");

                    if (cbl_select.Checked)
                    {
                       // s_afinostring += lbl_afino.Text.ToString() + ",";
                        s_afinostring = lbl_afino.Text.ToString() ;
                        string[] radom_E = wsOtherFileuploadValid(getRadNum());
                        wsOtherFileupload oOFD = new wsOtherFileupload();
                        string defenseplanid = DeCode(Request["defenseplanid"].ToString());
                       // oOFD.Del_Nv_noisedefenseplanfiles(s_afinostring.TrimEnd(new char[] { ',' }), radom_E[0].ToString(), radom_E[1].ToString());
                        oOFD.Del_Nv_noisedefenseplanfiles(s_afinostring, radom_E[0].ToString(), radom_E[1].ToString());
                       
                    }
                }
            }

            BindData();
            ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已成功刪除檔案!');</script>");
        }
        else
        {
            MessageBox.Show("請先勾選欲刪除的檔案");
            return;
        }
    }


    protected void gv_data_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        gv_data.PageIndex = e.NewPageIndex;
        BindData();
    }
    protected void btn_Close_Click(object sender, EventArgs e)
    {
        String scriptString = "";
        scriptString = @"<script language='javascript' type='text/javascript'> window.opener.raiseAsyncPostback();window.close();</script>";
        this.RegisterStartupScript("s", scriptString);
    }
}