﻿<%@ Page Title="" Language="C#" MasterPageFile="~/masterpage/MasterDialog.master"
    AutoEventWireup="true" CodeFile="personalinfo_declare.aspx.cs" Inherits="accountmt_personalinfo_declare" %>

<%@ Register TagPrefix="uc_pdp" TagName="dtprotection" Src="~/usercontrol/uc_dtprotection.ascx" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div id="header">
        <div class="logo">
            <a href="../accountmt/index.aspx" target="_self">
                <img src="../images/logo.png" border="0" /></a></div>
        <br />
        <!--{* info end *}-->
        <br />
        <br />
        <br />
        <br />
        <br />
        <br />
        <br />
        <br />
    </div>
    <table width="100%" border="0" cellspacing="0" cellpadding="0" class="font_fullcontent">
        <tr>
            <td colspan="4" align="center">
                <span style="font-size: medium"><b>個人資料使用告知事項</b></span>
            </td>
        </tr>
        <tr>
            <td colspan="4">
                <uc_pdp:dtprotection ID="uc_PersonalDataProtection" runat="server" />
            </td>
        </tr>
        <tr>
            <td colspan="4" align="center">
                <asp:Button ID="btn_Agree" runat="server" Text="我同意" OnClick="btn_Agree_Click" CssClass="btn_mouseout"
                    onmouseover="this.className='btn_mouseover'" onmouseout="this.className='btn_mouseout'" />
                &nbsp;&nbsp;
                <asp:Button ID="btn_Disagree" runat="server" Text="我不同意" OnClick="btn_Disagree_Click"
                    CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'" onmouseout="this.className='btn_mouseout'" />
            </td>
        </tr>
    </table>
</asp:Content>
