﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Services;
using System.Data;
using System.Data.SqlClient;

/// <summary>
/// Summary description for wsApplyBaseDataInfo
/// </summary>
[WebService(Namespace = "http://tempuri.org/")]
[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
// To allow this Web Service to be called from script, using ASP.NET AJAX, uncomment the following line. 
// [System.Web.Script.Services.ScriptService]
public class wsApplyBaseDataInfo : System.Web.Services.WebService
{

    public wsApplyBaseDataInfo()
    {

        //Uncomment the following line if using designed components 
        //InitializeComponent(); 
    }

    //取出申請資料By申請編號
    [WebMethod]
    public DataSet getApplyBaseDataById(string strApplyNo, string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsCheckApplyBaseDataValid(ID, PD))
        {
            //SqlCommand cmd = new SqlCommand(@"select * from nv_applybasedata ad left outer join nv_paydata pd on ad.abd_applyno = pd.pd_applyno left join nv_codetbl_applystatus on ct_aps_id =abd_applystatus  where abd_applyno=@applyno");
            SqlCommand cmd = new SqlCommand(@"select * from nv_applybasedata where abd_applyno=@applyno");
            cmd.Parameters.AddWithValue("@applyno", strApplyNo);
            ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
        }
        return ds;
    }
    //取出申請資料交費單By申請編號
    [WebMethod]
    public DataTable getApplyBasePaydata(string applyno, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsCheckApplyBaseDataValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select nv_paydata.*,afi_userfilename,afi_fileid from nv_paydata 
                                               left join nv_attachfileinfo on afi_fileid=pd_attachfileid where pd_applyno=@applyno ");
            cmd.Parameters.AddWithValue("@applyno", applyno);
            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;
    }

    //取得該統一編號號所屬的公司(廠商)汽車編號,機車編號,柴油車編號
    [WebMethod]
    public DataSet getStampCompNoAll(string strCompIdNo, string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsCheckApplyBaseDataValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select case ISNULL(ci_stampcompno_g,'') when '' then '' else 'G' end as stampcompno_g,
                                                     case ISNULL(ci_stampcompno_m,'') when '' then '' else 'M' end as stampcompno_m,
                                                     case ISNULL(ci_stampcompno_d,'') when '' then '' else 'D' end as stampcompno_d
                                                from nv_companyinfo
                                               where ci_compidno=@CompIdNo");

            cmd.Parameters.AddWithValue("@CompIdNo", strCompIdNo);
            ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
        }
        return ds;
    }

    //檢查該帳號所屬的公司(廠商)是否有汽車編號
    [WebMethod]
    public Boolean isExistCarNo(string account, string ID, string PD)
    {
        if (wsCheckApplyBaseDataValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select ai_account ,ai_username ,ci_stampcompno_g from nv_accountinfo join nv_companyinfo
                                                  on ai_accountbelongcomp = ci_compidno 
                                              where ci_stampcompno_g is not null and ai_account=@account");
            cmd.Parameters.AddWithValue("@account", account);
            DataSet ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
            if (ds.Tables[0].Rows.Count > 0)
                return true;
            else return false;
        } return false;
    }

    //get該帳號所屬的公司合格證寄發地址
    [WebMethod]
    public string get_companycertmailaddr(string compidno, string ID, string PD)
    {
        if (wsCheckApplyBaseDataValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select replace(ct_zipcode_id+' '+ct_city_desc+ct_zipcode_desc+ ci_certaddr,'新竹市新竹市','新竹市') 'fullcertaddr' from dbo.nv_companyinfo
                join dbo.nv_codetbl_city on ci_certaddrcity=ct_city_id
                join dbo.nv_codetbl_zipcode  on ci_certaddrlocalarea=ct_zipcode_id
                where ci_compidno=@compidno");
            cmd.Parameters.AddWithValue("@compidno", compidno);
            DataSet ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
            if (ds.Tables[0].Rows.Count > 0)
                return ds.Tables[0].Rows[0][0].ToString();
            else
                return "";
        }
        else
        {
            return "";
        }
    }

    //新增一筆資料(nv_applybasedata, pay_data)
    [WebMethod]
    public string InsertApplyBaseData(string[] arrData, string ID, string PD)
    {
        if (wsCheckApplyBaseDataValid(ID, PD))
        {
            string strYear = (DateTime.Now.Year - 1911).ToString();
            string strMonth = (DateTime.Now.Month).ToString("00");
            string strNo = "";
            SqlCommand cmd = new SqlCommand(@"select cast(substring(max(abd_applyno),6,4) as int) as newApplyNo from nv_applybasedata where substring(abd_applyno,1,5)=@yearmonth ");
            cmd.Parameters.AddWithValue("@yearmonth", strYear + strMonth);
            DataSet ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
            if (ds.Tables[0].Rows.Count > 0)
            {
                if (ds.Tables[0].Rows[0][0].ToString().Trim() != "")
                {
                    strNo = ds.Tables[0].Rows[0][0].ToString().Trim();
                    strNo = (Convert.ToInt32(strNo) + 1).ToString("0000");  //4位數流水號
                }
                else
                    strNo = "0001";  //第一筆
            }

            //申請編號(民國年月+流水號)
            string strApplyNo = strYear + strMonth + strNo;

            SqlCommand cmd_ins = new SqlCommand(@"Insert into nv_applybasedata
                                                  ( abd_applyno ,abd_applystatus ,abd_contactname ,abd_contacttel ,abd_contactemail
                                                   ,abd_vechiletype ,abd_enginefamily ,abd_adaptstandarddate ,abd_twocertmethod ,abd_factoryname 
                                                   ,abd_carstyleyear ,abd_powerfrom ,abd_officedocno ,abd_officedocdate ,abd_mailcertidocaddr 
                                                   ,abd_userdocfileid ,abd_databelongcompidno ,abd_steps,abd_databelongcompname,abd_contactaddr
                                                   ,abd_contactfax,abd_databelongcomptel,abd_databelongcompaddr,abd_isimport
                                                   ,abd_createaccount,abd_createdate,abd_modaccount,abd_moddate)
                                                  Values
                                                  ( @applyno, @applystatus, @contactname, @contacttel, @contactemail
                                                   ,@vechiletype, @enginefamily, @adaptstandarddate, @twocertmethod, @factoryname
                                                   ,@carstyleyear, @powerfrom, @officedocno, @officedocdate, @mailcertidocaddr
                                                   ,@userdocfileid, @databelongcompidno,@steps,@databelongcompname,@contactfax,@contactfax,@databelongcomptel,@databelongcompaddr,@isimport,@createaccount,getdate(),@modaccount,getdate())");
            cmd_ins.Parameters.AddWithValue("@applyno", strApplyNo);
            cmd_ins.Parameters.AddWithValue("@applystatus", arrData[0]);
            cmd_ins.Parameters.AddWithValue("@contactname", arrData[1]);
            cmd_ins.Parameters.AddWithValue("@contacttel", arrData[2]);
            cmd_ins.Parameters.AddWithValue("@contactemail", arrData[3]);
            cmd_ins.Parameters.AddWithValue("@vechiletype", arrData[4]);
            cmd_ins.Parameters.AddWithValue("@enginefamily", arrData[5]);
            cmd_ins.Parameters.AddWithValue("@adaptstandarddate", arrData[6]);
            cmd_ins.Parameters.AddWithValue("@twocertmethod", arrData[7]);
            cmd_ins.Parameters.AddWithValue("@factoryname", arrData[8]);
            cmd_ins.Parameters.AddWithValue("@carstyleyear", arrData[9]);
            cmd_ins.Parameters.AddWithValue("@powerfrom", arrData[10]);
            cmd_ins.Parameters.AddWithValue("@officedocno", arrData[11]);
            cmd_ins.Parameters.AddWithValue("@officedocdate", arrData[12]);
            cmd_ins.Parameters.AddWithValue("@mailcertidocaddr", arrData[13]);
            cmd_ins.Parameters.AddWithValue("@userdocfileid", arrData[14]);
            cmd_ins.Parameters.AddWithValue("@databelongcompidno", arrData[15]); //公司統一編號
            cmd_ins.Parameters.AddWithValue("@steps", arrData[16]);
            cmd_ins.Parameters.AddWithValue("@databelongcompname", arrData[17]);
            cmd_ins.Parameters.AddWithValue("@contactfax", arrData[18]);
            cmd_ins.Parameters.AddWithValue("@databelongcomptel", arrData[19]);
            cmd_ins.Parameters.AddWithValue("@databelongcompaddr", arrData[20]);
            cmd_ins.Parameters.AddWithValue("@isimport", arrData[21]);
            cmd_ins.Parameters.AddWithValue("@createaccount", arrData[22]);
            cmd_ins.Parameters.AddWithValue("@modaccount", arrData[23]);
            Common.Data_noisevalidation.runParaCmd1(cmd_ins);

            return strApplyNo;  //傳回剛剛產生的申請編號
        }
        return "";
    }

    //更新一筆資料(nv_applybasedata )
    [WebMethod]
    public void UpdateApplyBaseData(string strApplyNo, string[] arrData, string ID, string PD)
    {
        if (wsCheckApplyBaseDataValid(ID, PD))
        {
            SqlCommand cmd_upd = new SqlCommand(@"update nv_applybasedata
                                                   set abd_applystatus=@applystatus ,abd_contactname=@contactname ,abd_contacttel=@contacttel ,abd_contactemail=@contactemail
                                                      ,abd_vechiletype=@vechiletype ,abd_enginefamily=@enginefamily ,abd_adaptstandarddate=@adaptstandarddate ,abd_twocertmethod=@twocertmethod ,abd_factoryname=@factoryname 
                                                      ,abd_carstyleyear=@carstyleyear ,abd_powerfrom=@powerfrom ,abd_officedocno=@officedocno ,abd_officedocdate=@officedocdate ,abd_mailcertidocaddr=@mailcertidocaddr 
                                                      ,abd_userdocfileid=@userdocfileid,abd_steps=@steps,abd_moddate=getdate(),abd_modaccount=@modaccount,abd_isimport=@isimport
                                                    where abd_applyno=@applyno");
            cmd_upd.Parameters.AddWithValue("@applyno", strApplyNo);
            cmd_upd.Parameters.AddWithValue("@applystatus", arrData[0]);
            cmd_upd.Parameters.AddWithValue("@contactname", arrData[1]);
            cmd_upd.Parameters.AddWithValue("@contacttel", arrData[2]);
            cmd_upd.Parameters.AddWithValue("@contactemail", arrData[3]);
            cmd_upd.Parameters.AddWithValue("@vechiletype", arrData[4]);
            cmd_upd.Parameters.AddWithValue("@enginefamily", arrData[5]);
            cmd_upd.Parameters.AddWithValue("@adaptstandarddate", arrData[6]);
            cmd_upd.Parameters.AddWithValue("@twocertmethod", arrData[7]);
            cmd_upd.Parameters.AddWithValue("@factoryname", arrData[8]);
            cmd_upd.Parameters.AddWithValue("@carstyleyear", arrData[9]);
            cmd_upd.Parameters.AddWithValue("@powerfrom", arrData[10]);
            cmd_upd.Parameters.AddWithValue("@officedocno", arrData[11]);
            cmd_upd.Parameters.AddWithValue("@officedocdate", arrData[12]);
            cmd_upd.Parameters.AddWithValue("@mailcertidocaddr", arrData[13]);
            cmd_upd.Parameters.AddWithValue("@userdocfileid", arrData[14]);
            cmd_upd.Parameters.AddWithValue("@steps", arrData[15]);
            cmd_upd.Parameters.AddWithValue("@modaccount", arrData[16]);
            cmd_upd.Parameters.AddWithValue("@isimport", arrData[17]);

            Common.Data_noisevalidation.runParaCmd1(cmd_upd);
        }
    }

    //更新申請函檔案ID資料(nv_applybasedata )
    [WebMethod]
    public void UpdateApplyBaseData_userdocfileid(string strApplyNo, string fileid_new, string ID, string PD)
    {
        if (wsCheckApplyBaseDataValid(ID, PD))
        {
            SqlCommand cmd_upd = new SqlCommand(@"update nv_applybasedata
                                                   set abd_userdocfileid=@userdocfileid
                                                    where abd_applyno=@applyno");
            cmd_upd.Parameters.AddWithValue("@applyno", strApplyNo);
            cmd_upd.Parameters.AddWithValue("@userdocfileid", fileid_new);
            Common.Data_noisevalidation.runParaCmd1(cmd_upd);
        }

    }

    private bool insert_paymentdata(string strApplyNo, string[] arrData_pay, string ID, string PD)
    {
        //繳費資料--新增
        string[] arrFields = { "pd_bankaccount", "pd_paydate", "pd_paysn", "pd_paymoney", "pd_attachfileid" };
        string[] arrValues = { "@bankaccount", "@paydate", "@paysn", "@paymoney", "@attachfileid" };
        string strSql_Fields = "";
        string strSql_Values = "";
        for (int i = 0; i < arrData_pay.Length; i++)
            if (arrData_pay[i] != null)
            {
                strSql_Fields += arrFields[i] + ",";
                strSql_Values += arrValues[i] + ",";
            }

        if (strSql_Fields.Length > 0)
            strSql_Fields = "pd_applyno," + strSql_Fields.Substring(0, strSql_Fields.Length - 1);
        else
            strSql_Fields = "pd_applyno";

        if (strSql_Values.Length > 0)
            strSql_Values = "@applyno," + strSql_Values.Substring(0, strSql_Values.Length - 1);
        else
            strSql_Values = "@applyno";

        //如果繳費資料都沒填的話,也會先在nv_paydata增加一筆至少有一個申請編號,可讓日後update用
        SqlCommand cmd_ins_2 = new SqlCommand("Insert into nv_paydata(" + strSql_Fields + ") Values (" + strSql_Values + ")");
        cmd_ins_2.Parameters.AddWithValue("@applyno", strApplyNo);

        for (int i = 0; i < arrData_pay.Length; i++)
            if (arrData_pay[i] != null)
            {
                cmd_ins_2.Parameters.AddWithValue(arrValues[i], arrData_pay[i]);
            }
        Common.Data_noisevalidation.runParaCmd1(cmd_ins_2);
        return false;
    }

    private bool update_paymentdata(string strApplyNo, string[] arrData_pay, string ID, string PD)
    {
        //繳費資料-更新
        string[] arrFields = { "pd_bankaccount", "pd_paydate", "pd_paysn", "pd_paymoney", "pd_attachfileid" };
        string[] arrValues = { "@bankaccount", "@paydate", "@paysn", "@paymoney", "@attachfileid" };
        string strTable = "nv_paydata";
        string strSql_Fields = "";
        string strSql_Where = " Where pd_applyno=" + arrValues[2];

        for (int i = 0; i < arrData_pay.Length; i++)
            if (arrData_pay[i] != null)
            {
                strSql_Fields += arrFields[i] + "=" + arrValues[i] + ",";
            }

        if (strSql_Fields.Length > 0)
            strSql_Fields = strSql_Fields.Substring(0, strSql_Fields.Length - 1);

        if (strSql_Fields.Length > 0)
        {
            SqlCommand cmd_upd_2 = new SqlCommand("Update " + strTable + " set " + strSql_Fields + strSql_Where);

            for (int i = 0; i < arrData_pay.Length; i++)
                if (arrData_pay[i] != null)
                {
                    cmd_upd_2.Parameters.AddWithValue(arrValues[i], arrData_pay[i]);
                }
            Common.Data_noisevalidation.runParaCmd1(cmd_upd_2);
        }
        return false;
    }

    [WebMethod]
    public string copy_existsapplydata(string applyno, string enginefamily, string account, string compidno, string ID, string PD)
    {
        if (wsCheckApplyBaseDataValid(ID, PD))
        {
            SqlCommand cmd_copy_existsapplydata = new SqlCommand();
            //cmd_copy_existsapplydata.CommandType = CommandType.StoredProcedure;
            cmd_copy_existsapplydata.CommandText = "exec pr_copy_existapplydata @applyno,@enginefamily,@account,@compidno";
            cmd_copy_existsapplydata.Parameters.AddWithValue("@applyno", applyno);
            cmd_copy_existsapplydata.Parameters.AddWithValue("@enginefamily", enginefamily);
            cmd_copy_existsapplydata.Parameters.AddWithValue("@account", account);
            cmd_copy_existsapplydata.Parameters.AddWithValue("@compidno", compidno);
            string run_result = "";
            run_result = (string)Common.Data_noisevalidation.runScalar(cmd_copy_existsapplydata);
            return run_result;
        }
        else
            return "";
    }

    [WebMethod]
    public int check_carcompomodelno_been_applied(string carcompomodelno, string compidno, string ID, string PD)
    {
        if (wsCheckApplyBaseDataValid(ID, PD))
        {
            SqlCommand cmd_carcompomodelno_been_applied = new SqlCommand();
            //cmd_copy_existsapplydata.CommandType = CommandType.StoredProcedure;
            cmd_carcompomodelno_been_applied.CommandText = "select count(*) from nv_carcompomodel join nv_applybasedata on cpm_applyno=abd_applyno where cpm_carcompomodelno=@carcompomodelno and abd_databelongcompidno=@compidno and abd_applystatus in ('20','21','30','31','32','33','34','35','40','50','55','70','80','91','92','93')";
            cmd_carcompomodelno_been_applied.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
            cmd_carcompomodelno_been_applied.Parameters.AddWithValue("@compidno", compidno);
            int run_result = 0;
            run_result = (int)Common.Data_noisevalidation.runScalar(cmd_carcompomodelno_been_applied);
            return run_result;
        }
        else
            return 0;
    }
    //繳費資料by繳費ID
    [WebMethod]
    public DataTable get_Nv_paydataList(string id,string fileid, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsCheckApplyBaseDataValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select nv_paydata.*,afi_userfilename,afi_fileid from nv_paydata 
                                              left join nv_attachfileinfo on afi_fileid=pd_attachfileid
                                              where pd_id=@id and pd_attachfileid=@fileid");

            cmd.Parameters.AddWithValue("@id", id);
            cmd.Parameters.AddWithValue("@fileid", fileid);
           
            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;
    }
    //新增繳費資料
    [WebMethod]
    public void Insert_Nv_paydata(string applyno, string bankaccount, string paydate, string paysn, string paymoney, string attachfileid, string ID, string PD)
    {

        if (wsCheckApplyBaseDataValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"INSERT INTO nv_paydata
           (pd_applyno
           ,pd_bankaccount
           ,pd_paydate
           ,pd_paysn
           ,pd_paymoney
           ,pd_attachfileid           
            )
     VALUES
           (@applyno
           ,@bankaccount
           ,@paydate
           ,@paysn 
           ,@paymoney
           ,@attachfileid
            )");

            cmd.Parameters.AddWithValue("@applyno", applyno);
            cmd.Parameters.AddWithValue("@bankaccount", bankaccount);
            cmd.Parameters.AddWithValue("@paydate", paydate);
            cmd.Parameters.AddWithValue("@paysn", paysn);
            cmd.Parameters.AddWithValue("@paymoney", paymoney);
            cmd.Parameters.AddWithValue("@attachfileid", attachfileid);
            Common.Data_noisevalidation.runParaCmd1(cmd);
        }
    }

    //修改繳費資料
    [WebMethod]
    public void Update_Nv_paydata(string id, string bankaccount, string paydate, string paysn, string paymoney, string attachfileid, string ID, string PD)
    {
        if (wsCheckApplyBaseDataValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"UPDATE  nv_paydata
                            SET pd_bankaccount = @bankaccount
                               ,pd_paydate =  @paydate
                               ,pd_paysn =  @paysn
                               ,pd_paymoney =  @paymoney
                               ,pd_attachfileid = @attachfileid
                          WHERE pd_id=@id");

            cmd.Parameters.AddWithValue("@id", id);
            cmd.Parameters.AddWithValue("@bankaccount", bankaccount);
            cmd.Parameters.AddWithValue("@paydate", paydate);
            cmd.Parameters.AddWithValue("@paysn", paysn);
            cmd.Parameters.AddWithValue("@paymoney", paymoney);
            cmd.Parameters.AddWithValue("@attachfileid", attachfileid);

            Common.Data_noisevalidation.runParaCmd1(cmd);
        }
    }

    //刪除繳費資料
    [WebMethod]
    public void Del_Nv_fixcatalog(string id,string ID, string PD)
    {
        if (wsCheckApplyBaseDataValid(ID, PD))
        {
            SqlCommand cmd_del = new SqlCommand(@"
                SET XACT_ABORT ON
                begin transaction

               delete from nv_attachfileinfo where afi_fileid=(select pd_attachfileid from nv_paydata where pd_id=@id)
               delete from nv_paydata where pd_id=@id

            if (@@ERROR<>0)
            rollback tran;
            else
            commit tran;
            ");
            cmd_del.Parameters.AddWithValue("@id", id);
            Common.Data_noisevalidation.runParaCmd1(cmd_del);
        }
    }
    //判斷申請表是否存在check_testrptspeedupinfo
    [WebMethod]
    public void Check_Nv_paydata(string applyno, string ID, string PD)
    {
        if (wsCheckApplyBaseDataValid(ID, PD))
        {
            SqlCommand cmd_del = new SqlCommand(@"
            if(not exists(select abd_applyno from nv_applybasedata where abd_applyno = @applyno ))
                begin 
                    delete  nv_paydata                           
                    WHERE pd_applyno=@applyno
                end
            ");
            cmd_del.Parameters.AddWithValue("@applyno", applyno);
            Common.Data_noisevalidation.runParaCmd1(cmd_del);
        }
    }
    //判斷此引擎族是否已有填單中或申請中的資料
    public bool IsEnginefamilyExist(string strApplyNo, string enginefamily, string carstyleyear, string ID, string PD)
    {
        DataSet ds_SystemReminder = new DataSet();
        if (wsCheckApplyBaseDataValid(ID, PD))
        {
            SqlCommand cmd_no = new SqlCommand(@"
                                    select count(abd_enginefamily) as EGFno from nv_applybasedata where abd_applyno<>@strApplyNo and abd_enginefamily=@enginefamily and 
                                    abd_applystatus in('10','20','21','30','31','32','33','34','35','40','80','90','91','92','93') and abd_carstyleyear=@carstyleyear");
            cmd_no.Parameters.AddWithValue("@carstyleyear", carstyleyear);
            cmd_no.Parameters.AddWithValue("@enginefamily", enginefamily);
            cmd_no.Parameters.AddWithValue("@strApplyNo", strApplyNo);
            ds_SystemReminder = Common.Data_noisevalidation.runParaCmdDS(cmd_no);
            if (ds_SystemReminder.Tables[0].Rows[0]["EGFno"].Equals(0))
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        else
        {
            return false;
        }
    }

    private bool wsCheckApplyBaseDataValid(string ID, string PD)
    {
        string[][] x = new string[7][];
        x[0] = new string[2] { "APBD1", "uy9Gpbx" };
        x[1] = new string[2] { "APBD2", "pksd51cv" };
        x[2] = new string[2] { "APBD3", "tkjf2HS" };
        x[3] = new string[2] { "APBD4", "etwU5Sxf" };
        x[4] = new string[2] { "APBD5", "plids23MDz" };
        x[5] = new string[2] { "APBD6", "Wd06Khnbvx" };
        x[6] = new string[2] { "APBD7", "lkMdd75j" };

        for (int i = 0; i < 7; i++)
        {
            if (x[i][0].ToString() == ID && x[i][1].ToString() == PD)
            {
                return true;
            }
        }
        return false;
    }

}
