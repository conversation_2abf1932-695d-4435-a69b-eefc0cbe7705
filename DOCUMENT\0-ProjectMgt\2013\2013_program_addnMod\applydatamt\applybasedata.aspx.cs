﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.IO;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Globalization;

public partial class applydatamt_applybasedata : BaseAdminPage
{
    Cryptography crypy = new Cryptography();
    wsApplyBaseDataInfo wsApplyData = new wsApplyBaseDataInfo();
    wsCodetbl wsCodeTbl = new wsCodetbl();
    wsFileInfo wsfile = new wsFileInfo();
    private string strApplyNo = "";
    private string strStatusCode = "";
    private string strStatusDesc = "";
//    private string strSteps = "1";

    protected void Page_Load(object sender, EventArgs e)
    {
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = 'index.aspx';</script>");
            return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" && (GetAgentAccountGrpList().Contains("cam_adm") || GetAgentAccountGrpList().Contains("cga_adm") || GetAgentAccountGrpList().Contains("pg_adm") || GetAgentAccountGrpList().Contains("sys_adm")))
            {
                if (!IsPostBack)
                {
                    if (Request["applyno"] != null && Request["applyno"] != "")
                    {
                        strApplyNo = crypy.DeCode(Request["applyno"].ToString());
                        hidden_flag.Value = "2";
                        lbn_addpaydata.Enabled = true;
                    }
                    else
                        hidden_flag.Value = "1";

                    if (hidden_flag.Value == "1")
                    {
                        BindBaseData();
                    }
                    else if (hidden_flag.Value == "2")
                    {
                        BindBaseData();
                        int keynum = crypy.getRadNum();
                        string[] arr_apd = crypy.wsApplyBaseDataValidate(keynum);
                        DataSet dsApplyData = wsApplyData.getApplyBaseDataById(strApplyNo, arr_apd[0].ToString(), arr_apd[1].ToString());
                        if (dsApplyData.Tables[0].Rows.Count > 0)
                        {
                            if (dsApplyData.Tables[0].Rows[0]["abd_databelongcompidno"].ToString() == Session["compidno"].ToString())
                                chkItem_All(dsApplyData);
                            else
                            {
                                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                                Response.End();
                                return;
                            }

                        }
                    }

                }
                applybase.NavigateUrl = "applybasedata.aspx?applyno=" + Server.UrlEncode(Request["applyno"]);
                if (steps.Text == "2")
                    carcompomodel.NavigateUrl = "carcompomodelList.aspx?applyno=" + Server.UrlEncode(Request["applyno"]);
                else
                    carcompomodel.NavigateUrl = "";
                docreview.NavigateUrl = "";
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }
    }

    #region 更改的麵包屑傳入的參數
    protected void Page_LoadComplete(object sender, EventArgs e)
    {
        ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text = ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text.Replace("applydatamt/carcompomodelList.aspx", "applydatamt/carcompomodelList.aspx?applyno=" + Server.UrlEncode(Request["applyno"].ToString()));
    }
    #endregion

    //依照取回的申請資料,套在要顯示在畫面各項Item基本相關資料
    private void chkItem_All(DataSet dsApplyData)
    {
        int keynum = crypy.getRadNum();
        string[] arr_ct = crypy.wsCodetblValidate(keynum);
        string[] arr_apd = crypy.wsApplyBaseDataValidate(keynum);
        strStatusCode = dsApplyData.Tables[0].Rows[0]["abd_applystatus"].ToString();  //申請狀態(代碼)        
        lbl_statuscode.Text = strStatusCode;

        lbl_applyusername.Text = dsApplyData.Tables[0].Rows[0]["abd_databelongcompname"].ToString();
        lbl_modifyuser.Text = GetAgentName();
        lbl_modifydate.Text = DateTime.Now.ToString();

        strStatusDesc = wsCodeTbl.getApplyStatusById(strStatusCode, arr_ct[0].ToString(), arr_ct[1].ToString());
        steps.Text = dsApplyData.Tables[0].Rows[0]["abd_steps"].ToString();
        lbl_applyno.Text = dsApplyData.Tables[0].Rows[0]["abd_applyno"].ToString();  //申請編號
        lbl_staus.Text = strStatusDesc;  //申請狀態
        lbl_contactname.Text = dsApplyData.Tables[0].Rows[0]["abd_contactname"].ToString();
        lbl_contacttel.Text = dsApplyData.Tables[0].Rows[0]["abd_contacttel"].ToString();
        lbl_contactemail.Text = dsApplyData.Tables[0].Rows[0]["abd_contactemail"].ToString();
        rdBtnLst_cartype.SelectedValue = dsApplyData.Tables[0].Rows[0]["abd_vechiletype"].ToString();  //交通工具種類
        txt_enginefamily.Text = dsApplyData.Tables[0].Rows[0]["abd_enginefamily"].ToString();  //引擎族
        DrpDnList_adaptstandarddate.SelectedValue = dsApplyData.Tables[0].Rows[0]["abd_adaptstandarddate"].ToString();  //適用期別
        rdBtnLst_twocerttype.SelectedValue = dsApplyData.Tables[0].Rows[0]["abd_twocertmethod"].ToString();  //兩證合一申請方式*
        rdBtnLst_isimport.SelectedValue = dsApplyData.Tables[0].Rows[0]["abd_isimport"].ToString();  //國產/進口
        txt_factoryname.Text = dsApplyData.Tables[0].Rows[0]["abd_factoryname"].ToString();  //製造廠
        txt_carstyleyear.Text = dsApplyData.Tables[0].Rows[0]["abd_carstyleyear"].ToString();  //車型年
        rdBtnLst_powerfrom.SelectedValue = dsApplyData.Tables[0].Rows[0]["abd_powerfrom"].ToString();  //動力來源  
        txt_enginesn.Text = dsApplyData.Tables[0].Rows[0]["abd_enginesn"].ToString();  //引擎流水編號
        if (dsApplyData.Tables[0].Rows[0]["abd_officedocdate"].ToString() != "")  //申請函發文日期
            officedocdate.GetDate = DateTime.ParseExact(dsApplyData.Tables[0].Rows[0]["abd_officedocdate"].ToString(), "yyyyMMdd", CultureInfo.CurrentCulture).ToString("yyyy/MM/dd");
        txt_officedocno.Text = dsApplyData.Tables[0].Rows[0]["abd_officedocno"].ToString();  //申請函發文字號
        txt_mailcertidocaddr.Text = dsApplyData.Tables[0].Rows[0]["abd_mailcertidocaddr"].ToString();  //合格證郵寄地址
        if (txt_mailcertidocaddr.Text == "")
        {
            txt_mailcertidocaddr.Text = wsApplyData.get_companycertmailaddr(Session["compidno"].ToString(), arr_apd[0].ToString(), arr_apd[1].ToString());
        }
        hidden_uplfileid_apply.Value = dsApplyData.Tables[0].Rows[0]["abd_userdocfileid"].ToString();  //上傳申請函的檔案編號
        userdocfile.NavigateUrl = "../FileDownloadSeal.aspx?File=" + Server.UrlEncode(crypy.EnCode(hidden_uplfileid_apply.Value));
        userdocfile.Text = "";
        DataSet ds_fileinfo = new DataSet();
        string[] radom_F = crypy.wsFileValidate(crypy.getRadNum());
        ds_fileinfo = wsfile.getFileBase(hidden_uplfileid_apply.Value, radom_F[0].ToString(), radom_F[1].ToString());
        if (ds_fileinfo.Tables[0].Rows.Count > 0)
            userdocfile.Text = ds_fileinfo.Tables[0].Rows[0]["afi_userfilename"].ToString();
        //--------繳費資料----------------
        //txt_bankaccount.Text = dsApplyData.Tables[0].Rows[0]["pd_bankaccount"].ToString();  //局號
        //if (dsApplyData.Tables[0].Rows[0]["pd_paydate"].ToString() != "")  //日期
        //  txt_paydate.Text = DateTime.ParseExact(dsApplyData.Tables[0].Rows[0]["pd_paydate"].ToString(), "yyyyMMdd", CultureInfo.CurrentCulture).ToString("yyyy/MM/dd");
        //txt_paysn.Text = dsApplyData.Tables[0].Rows[0]["pd_paysn"].ToString();  //序號
        //txt_paymoney.Text = dsApplyData.Tables[0].Rows[0]["pd_paymoney"].ToString();  //金額
        //hidden_uplfileid_pay.Value = dsApplyData.Tables[0].Rows[0]["pd_attachfileid"].ToString();  ////上傳繳費單的檔案編號

        if (strStatusCode == "40")
        {
            hl_RePairProcess.Visible = true;
            hl_RePairProcess.NavigateUrl = "../certwork/cw_resupplyprocesslog.aspx?p=" + Server.UrlEncode(Request["applyno"].ToString());
        }

        lbn_addpaydata.Attributes.Add("onClick", string.Format(@"javascript:var Mleft = (screen.width - 550) / 2;var Mtop = (screen.height - 650) / 2;window.open('addPayData.aspx?command=add&applyno={0}',10,'height=150,width=550,top='+Mtop+',left='+Mleft+'');", Server.UrlEncode(Request["applyno"].ToString())));

        BindPayData();


    }
    private void BindPayData()
    {
        int keynum = crypy.getRadNum();
        string[] arr_apd = crypy.wsApplyBaseDataValidate(keynum);
        DataTable dt_paydata = new DataTable();
        dt_paydata = wsApplyData.getApplyBasePaydata(lbl_applyno.Text, arr_apd[0].ToString(), arr_apd[1].ToString());
        gv_paydata.DataSource = dt_paydata;
        gv_paydata.DataBind();
    }
    //取出要顯示在畫面各項Item基本相關資料
    private void BindBaseData()
    {
        int keynum = crypy.getRadNum();

        if (strApplyNo == "") //若為新增時,則聯絡人相關資料用login者的資料
        {
            lbl_contactname.Text = GetAgentName();
            lbl_contacttel.Text = GetAgentTel();
            lbl_contactemail.Text = GetAgentEmail();

            lbl_applyusername.Text = GetAgentIDName();
            lbl_modifyuser.Text = GetAgentName();
            lbl_modifydate.Text = DateTime.Now.ToString();
        }

        #region 交通工具種類
        //取CarType
        string[] arr_ct = crypy.wsCodetblValidate(keynum);
        DataSet dsCarType = wsCodeTbl.getCarType(arr_ct[0].ToString(), arr_ct[1].ToString());

        //取廠商編號(汽,柴,機)
        string[] arr_apd = crypy.wsApplyBaseDataValidate(keynum);
        DataSet dsStampCompNoAll = wsApplyData.getStampCompNoAll(Session["compidno"].ToString(), arr_apd[0].ToString(), arr_apd[1].ToString());
        DataSet dsCarType_cmpr = new DataSet();

        if (txt_mailcertidocaddr.Text == "")
        {
            txt_mailcertidocaddr.Text = wsApplyData.get_companycertmailaddr(Session["compidno"].ToString(), arr_apd[0].ToString(), arr_apd[1].ToString());
        }


        //用chkCarType()將CarType與廠商編號(汽,柴,機)兩堆資料,整理出可顯示的Item
        dsCarType_cmpr.Tables.Add(chkCarType(dsStampCompNoAll, dsCarType));
        //交通工具種類
        rdBtnLst_cartype.DataSource = dsCarType_cmpr;
        rdBtnLst_cartype.DataTextField = "ct_desc";
        rdBtnLst_cartype.DataValueField = "ct_id";
        rdBtnLst_cartype.DataBind();
        #endregion

        #region 適用期別
        string[] arr_std = crypy.wsCodetblValidate(keynum);
        DataSet dsStandardDate = wsCodeTbl.getStandardDate(arr_std[0].ToString(), arr_std[1].ToString());
        DrpDnList_adaptstandarddate.DataSource = dsStandardDate;
        DrpDnList_adaptstandarddate.DataTextField = "ct_sd_desc";
        DrpDnList_adaptstandarddate.DataValueField = "ct_sd_id";
        DrpDnList_adaptstandarddate.SelectedValue = "4";
        DrpDnList_adaptstandarddate.DataBind();
        #endregion

        #region 兩證合一申請方式
        string[] arr_twct = crypy.wsCodetblValidate(keynum);
        DataSet dsTwoCertType = wsCodeTbl.getTwoCertType(arr_twct[0].ToString(), arr_twct[1].ToString());
        rdBtnLst_twocerttype.DataSource = dsTwoCertType;
        rdBtnLst_twocerttype.DataTextField = "ct_twct_desc";
        rdBtnLst_twocerttype.DataValueField = "ct_twct_id";
        rdBtnLst_twocerttype.DataBind();
        #endregion

        #region 動力來源
        string[] arr_pf = crypy.wsCodetblValidate(keynum);
        DataSet dsPowerFrom = wsCodeTbl.getPowerFrom(arr_pf[0].ToString(), arr_pf[1].ToString());
        rdBtnLst_powerfrom.DataSource = dsPowerFrom;
        rdBtnLst_powerfrom.DataTextField = "ct_pf_desc";
        rdBtnLst_powerfrom.DataValueField = "ct_pf_id";
        rdBtnLst_powerfrom.DataBind();
        #endregion

        #region 國產/進口
        string[] arr_im = crypy.wsCodetblValidate(keynum);
        DataSet dsIsImport = wsCodeTbl.getIsImport(arr_im[0].ToString(), arr_im[1].ToString());

        rdBtnLst_isimport.DataSource = dsIsImport;
        rdBtnLst_isimport.DataTextField = "ct_im_desc";
        rdBtnLst_isimport.DataValueField = "ct_im_id";
        rdBtnLst_isimport.DataBind();
        #endregion

    }

    //取出聯絡人相關資料
    //private void BindContacter()
    //{
    //    int keynum = crypy.getRadNum();
    //    string[] arr = crypy.wsApplyBaseDataValidate(keynum);
    //    wsApplyBaseDataInfo wsa = new wsApplyBaseDataInfo();
    //    lbl_contactname.Text = GetAgentName();
    //    lbl_contacttel.Text = GetAgentTel();
    //    lbl_contactemail.Text = GetAgentEmail();
    //}

    #region Update
    private void updateApplyData(string strStatus, string steps)
    {
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = 'index.aspx';</script>");
            Response.End();
        }
        string[] arrData = new string[18];
        arrData[0] = strStatus; //申請狀態
        arrData[1] = GetAgentName();//lbl_contactname.Text; //聯絡人姓名
        arrData[2] = GetAgentTel();// lbl_contacttel.Text; //聯絡人電話
        arrData[3] = GetAgentEmail();//lbl_contactemail.Text; //聯絡人E-Mail
        arrData[4] = rdBtnLst_cartype.SelectedValue.ToString(); //交通工具種類
        arrData[5] = txt_enginefamily.Text; //引擎族
        arrData[6] = DrpDnList_adaptstandarddate.SelectedValue.ToString(); //適用期別
        arrData[7] = rdBtnLst_twocerttype.SelectedValue.ToString(); //兩證合一申請方式*
        arrData[8] = txt_factoryname.Text; //製造廠
        arrData[9] = txt_carstyleyear.Text; //車型年
        arrData[10] = rdBtnLst_powerfrom.SelectedValue.ToString(); //動力來源
        arrData[11] = txt_officedocno.Text; //申請函發文字號
        arrData[12] = DateTime.Parse(officedocdate.GetDate.ToString()).ToString("yyyyMMdd"); //申請函發文日期
        arrData[13] = txt_mailcertidocaddr.Text.ToString().Trim().Replace(" ",""); //合格證郵寄地址
        arrData[14] = hidden_uplfileid_apply.Value; //上傳申請函檔案編號       
        arrData[15] = steps; //上傳申請函檔案編號       
        arrData[16] = Session["account"].ToString(); //上傳申請函檔案編號       
        arrData[17] = rdBtnLst_isimport.SelectedValue.ToString(); //國產/進口       

        // //繳費資料
        //string[] arrData_pay = new string[5];
        //if (txt_bankaccount.Text != "")
        //    arrData_pay[0] = txt_bankaccount.Text;  //局號        

        //if(txt_paydate.Text!="")
        //  arrData_pay[1] = DateTime.Parse(txt_paydate.Text).ToString("yyyyMMdd");  //日期

        //if(txt_paysn.Text != "")
        //  arrData_pay[2] = txt_paysn.Text;  //序號

        //if(txt_paymoney.Text !="")
        //  arrData_pay[3] = txt_paymoney.Text;  //金額

        //if (hidden_uplfileid_pay.Value != "")
        //  arrData_pay[4] = hidden_uplfileid_pay.Value; //繳費單檔案編號

        int keynum = crypy.getRadNum();
        string[] arr_apd = crypy.wsApplyBaseDataValidate(keynum);
        wsApplyData.UpdateApplyBaseData(strApplyNo, arrData, arr_apd[0].ToString(), arr_apd[1].ToString());
        //判斷申請編號是否存在，如不存在刪除Paydata
        wsApplyData.Check_Nv_paydata(strApplyNo, arr_apd[0].ToString(), arr_apd[1].ToString());
    }
    #endregion

    #region Insert
    private string insApplyData(string strStatus, string steps)
    {
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = 'index.aspx';</script>");
            Response.End();
        }
        string[] arrData = new string[24];
        arrData[0] = strStatus; //申請狀態
        arrData[1] = lbl_contactname.Text; //聯絡人姓名
        arrData[2] = lbl_contacttel.Text; //聯絡人電話
        arrData[3] = lbl_contactemail.Text; //聯絡人E-Mail
        arrData[4] = rdBtnLst_cartype.SelectedValue.ToString(); //交通工具種類
        arrData[5] = txt_enginefamily.Text; //引擎族
        arrData[6] = DrpDnList_adaptstandarddate.SelectedValue.ToString(); //適用期別
        arrData[7] = rdBtnLst_twocerttype.SelectedValue.ToString(); //兩證合一申請方式*
        arrData[8] = txt_factoryname.Text; //製造廠
        arrData[9] = txt_carstyleyear.Text; //車型年
        arrData[10] = rdBtnLst_powerfrom.SelectedValue.ToString(); //動力來源
        arrData[11] = txt_officedocno.Text; //申請函發文字號
        if (officedocdate.GetDate.ToString() != "")
            arrData[12] = DateTime.Parse(officedocdate.GetDate.ToString()).ToString("yyyyMMdd"); //申請函發文日期        
        arrData[13] = txt_mailcertidocaddr.Text.Trim().Replace(" ",""); //合格證郵寄地址
        arrData[14] = hidden_uplfileid_apply.Value; //上傳申請函檔案編號       
        arrData[15] = Session["compidno"].ToString();  //公司統一編號
        arrData[16] = steps;  //執行步驟
        arrData[17] = lbl_applyusername.Text;  //公司名稱
        arrData[18] = GetAgentFax();  //連絡人傳真

        arrData[19] = "";  //公司電話
        arrData[20] = "";  //公司傳真

        arrData[21] = rdBtnLst_isimport.SelectedValue.ToString();  //國產/進口
        arrData[22] = crypy.DeCode(Session["account"].ToString());  //建檔人
        arrData[23] = crypy.DeCode(Session["account"].ToString());  //修改人

        wsCompany wscompany = new wsCompany();
        string[] radom_C = wsCompanyValidate(getRadNum());
        DataSet ds_company = wscompany.getCompanyBase(Session["compidno"].ToString(), radom_C[0].ToString(), radom_C[1].ToString());
        if (ds_company.Tables[0].Rows.Count > 0)
        {
            arrData[19] = ds_company.Tables[0].Rows[0]["ci_comptel"].ToString();
            arrData[20] = (ds_company.Tables[0].Rows[0]["comp_citydesc"].ToString() + ds_company.Tables[0].Rows[0]["comp_zipdesc"].ToString() + ds_company.Tables[0].Rows[0]["ci_compaddr"].ToString()).Replace("新竹市新竹市", "新竹市");
        }
        ds_company.Dispose();
        wscompany.Dispose();


        //繳費資料
        //string[] arrData_pay = new string[5];
        //if (txt_bankaccount.Text != "")
        //    arrData_pay[0] = txt_bankaccount.Text;  //局號        

        //if(txt_paydate.Text!="")
        //  arrData_pay[1] = DateTime.Parse(txt_paydate.Text).ToString("yyyyMMdd");  //日期

        //if(txt_paysn.Text != "")
        //  arrData_pay[2] = txt_paysn.Text;  //序號

        //if(txt_paymoney.Text !="")
        //  arrData_pay[3] = txt_paymoney.Text;  //金額

        //if (hidden_uplfileid_pay.Value != "")
        //  arrData_pay[4] = hidden_uplfileid_pay.Value; //繳費單檔案編號

        int keynum = crypy.getRadNum();
        string[] arr_apd = crypy.wsApplyBaseDataValidate(keynum);
        string strApplyNo = wsApplyData.InsertApplyBaseData(arrData, arr_apd[0].ToString(), arr_apd[1].ToString());
        return strApplyNo;
    }
    #endregion

    #region 暫存
    protected void btn_tmpsave_Click(object sender, EventArgs e)
    {
        #region 特殊字元判斷-日期
        if (tbWord(officedocdate.GetDate))
        {
            MessageBox.Show("日期選項含有系統不允許的特殊字元");
            return;
        }
        #endregion
        if (txt_enginefamily.Text.Trim() == "")
        {
            MessageBox.Show("引擎族不可空白");
            txt_enginefamily.Focus();
            return;
        }
        #region 特殊字元判斷-沒有MasterPage
        foreach (Control ctl in Page.Form.Controls)
        {
            Control content = (Control)ctl;
            foreach (Control c in content.Controls)
            {
                if (c is TextBox)
                {
                    TextBox objTextBox = default(TextBox);
                    objTextBox = (TextBox)c;
                    if ((tbWord(objTextBox.Text)))
                    {
                        MessageBox.Show(objTextBox.Text + ":含有系統不允許的特殊字元");
                        return;
                    }
                }
            }
        }
        #endregion
        if (officedocdate.GetDate == "")
        {
            MessageBox.Show("申請函發文日期不可空白");
            return;
        }
        #region 辨識文件類型
        if (fu_importersDoc.FileName.Length > 0)
        {
            string subFileName = System.IO.Path.GetExtension(fu_importersDoc.PostedFile.FileName).Substring(1);
            if (!subFileName.Equals("pdf", StringComparison.CurrentCultureIgnoreCase))
            {
                MessageBox.Show("您上傳的檔案為" + subFileName + "，請改上傳PDF檔\n");
                return;
            }

        }
        #endregion
        string[] arr_apd = crypy.wsApplyBaseDataValidate(crypy.getRadNum());
        if (hidden_flag.Value == "1")
        {
            if (!wsApplyData.IsEnginefamilyExist(strApplyNo, txt_enginefamily.Text, txt_carstyleyear.Text.Trim(), arr_apd[0].ToString(), arr_apd[1].ToString()))
            {
                MessageBox.Show("此引擎族為填單中或申請中，不可重複申請");
                return;
            }
            strApplyNo = insApplyData("10", "1"); //10:填單中(nv_codetbl_applystatus.ct_aps_id)            
        }
        else if (hidden_flag.Value == "2")
        {
            strApplyNo = lbl_applyno.Text;
            updateApplyData(lbl_statuscode.Text, steps.Text); //10:填單中(nv_codetbl_applystatus.ct_aps_id)
        }

        if (fu_importersDoc.FileName != "")
            hidden_uplfileid_apply.Value = uplFile(strApplyNo);
        MessageBox.Show("暫存完成!");
        lbl_applyno.Text = strApplyNo;
        Server.Transfer("applybasedata.aspx?applyno=" + Server.UrlEncode(crypy.EnCode(strApplyNo)));
    }
    #endregion

    #region 完成填表
    protected void btn_apply_Click(object sender, EventArgs e)
    {
        int keynum = crypy.getRadNum();
        string[] arr_apd = crypy.wsApplyBaseDataValidate(keynum);

        #region 特殊字元判斷-日期
        if (tbWord(officedocdate.GetDate))
        {
            MessageBox.Show("日期選項含有系統不允許的特殊字元");
            return;
        }
        #endregion

        #region 特殊字元判斷-沒有MasterPage
        foreach (Control ctl in Page.Form.Controls)
        {
            Control content = (Control)ctl;
            foreach (Control c in content.Controls)
            {
                if (c is TextBox)
                {
                    TextBox objTextBox = default(TextBox);
                    objTextBox = (TextBox)c;
                    if ((tbWord(objTextBox.Text)))
                    {
                        MessageBox.Show(objTextBox.Text + ":含有系統不允許的特殊字元");
                        return;
                    }
                }
            }
        }
        #endregion

        #region 辨識文件類型
        if (fu_importersDoc.FileName.Length > 0)
        {
            string subFileName = System.IO.Path.GetExtension(fu_importersDoc.PostedFile.FileName).Substring(1);
            if (!subFileName.Equals("pdf", StringComparison.CurrentCultureIgnoreCase))
            {
                MessageBox.Show("您上傳的檔案為" + subFileName + "，請改上傳PDF檔\n");
                return;
            }

        }
        #endregion
        if (officedocdate.GetDate == "")
        {
            MessageBox.Show("申請函發文日期不可空白");
            return;
        }
        if (hidden_flag.Value == "1")
        {
            if (!wsApplyData.IsEnginefamilyExist(strApplyNo, txt_enginefamily.Text, txt_carstyleyear.Text.Trim(), arr_apd[0].ToString(), arr_apd[1].ToString()))
            {
                MessageBox.Show("此引擎族為填單中或申請中，不可重複申請");
                return;
            }
            strApplyNo = insApplyData("10", "2"); //10:填單中(nv_codetbl_applystatus.ct_aps_id)
        }
        else if (hidden_flag.Value == "2")
        {
            strApplyNo = lbl_applyno.Text;
            updateApplyData(lbl_statuscode.Text, "2"); //10:填單中(nv_codetbl_applystatus.ct_aps_id)
        }
        if (steps.Text != "2")
        {
            //copy old data

            string copyresult = wsApplyData.copy_existsapplydata(strApplyNo, txt_enginefamily.Text, GetAgentAccount(), Session["compidno"].ToString(), arr_apd[0].ToString(), arr_apd[1].ToString());
            if (copyresult == "1")
                MessageBox.Show("複製原車型組資料失敗！");
        }
        if (fu_importersDoc.FileName != "")
            hidden_uplfileid_apply.Value = uplFile(strApplyNo);

        lbl_applyno.Text = strApplyNo;
        MessageBox.Show("完成填表!");
        //如果引擎族曾經申請過資料，必須要負制相關資料

        //轉到車型組清單
        Response.Redirect(string.Format("carcompomodelList.aspx?applyno={0}&applystatus={1}", Server.UrlEncode(EnCode(strApplyNo)), Server.UrlEncode(EnCode(strStatusCode)), false));
    }
    #endregion

    #region 比對出交通工具種類(Function)
    private DataTable chkCarType(DataSet ds1, DataSet ds2)
    {
        DataTable table1 = ds1.Tables[0]; // Get the data table.
        DataTable table2 = ds2.Tables[0];
        DataTable table3 = new DataTable();
        table3.Columns.Add("ct_id", typeof(string));
        table3.Columns.Add("ct_desc", typeof(string));

        string strResult = "";
        foreach (DataRow row in table1.Rows) // Loop over the rows.
        {
            foreach (var item in row.ItemArray) // Loop over the items.
            {
                if (item.ToString() != "")
                {
                    strResult = searchCarType(table2, item.ToString());
                    if (strResult != "")
                    {
                        string[] arrCarType = new string[2];
                        arrCarType[0] = item.ToString();
                        arrCarType[1] = strResult;
                        table3.Rows.Add(arrCarType);
                    }
                }
            }
        }
        return table3;
    }

    private string searchCarType(DataTable tb, string str1)
    {
        foreach (DataRow row in tb.Rows) // Loop over the rows.
        {

            if (row[0].ToString() == str1)
                return row[1].ToString();
        }
        return "";
    }
    #endregion

    #region 上傳檔案(申請函)
    private string uplFile(string applyno)
    {
        string strFileId = "";
        if (Session["compidno"] != null && Session["account"] != null && fu_importersDoc.FileName.Length > 0)
        {
            string strCompIdNo = crypy.DeCode(Session["compidno"].ToString().Trim());
            string strLoginUserid = crypy.DeCode(Session["account"].ToString().Trim());
            string[] radom_F = crypy.wsFileValidate(crypy.getRadNum());

            string filepath = ConfigurationManager.AppSettings["FilePath"].ToString();
            string this_year = DateTime.Now.Year.ToString();
            string strUpLoadPath = filepath + this_year + "\\" + applyno + "\\";
            //如果上傳路徑中沒有該目錄，則自動新增
            if (!Directory.Exists(strUpLoadPath.Substring(0, strUpLoadPath.LastIndexOf("\\"))))
            {
                Directory.CreateDirectory(strUpLoadPath.Substring(0, strUpLoadPath.LastIndexOf("\\")));
            }
            string[] strFileNm = null;
            string filename1 = "";
            string filename2 = "";
            string newfile = "";
            //申請函
            if (fu_importersDoc.FileName.Length > 0)
            {
                //原始檔名
                strFileNm = fu_importersDoc.PostedFile.FileName.Split('\\');
                filename1 = strFileNm[strFileNm.GetUpperBound(0)];
                //系統名稱
                filename2 = strCompIdNo + "_" + DateTime.Now.ToString("yyyyMMddHHmmss") + "_" + filename1;
                string strDescr = "申請函";
                strFileId = wsfile.insertapplydatafile("05", "1", filename1, filename2, strUpLoadPath, strDescr, strLoginUserid, radom_F[0].ToString(), radom_F[1].ToString());
                if (strFileId != "-1")
                {
                    int keynum = crypy.getRadNum();
                    string[] arr_apd = crypy.wsApplyBaseDataValidate(keynum);
                    wsApplyData.UpdateApplyBaseData_userdocfileid(applyno, strFileId, arr_apd[0].ToString(), arr_apd[1].ToString());
                }
                //實體檔案上傳
                newfile = strUpLoadPath + filename2;
                fu_importersDoc.PostedFile.SaveAs(newfile);
            }
        }
        return strFileId;
    }
    #endregion

    protected void gv_paydata_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {

            LinkButton lbtn_paydata_edit = (LinkButton)e.Row.FindControl("lbtn_paydata_edit");
            LinkButton lbtn_paydata_delete = (LinkButton)e.Row.FindControl("lbtn_paydata_delete");
            lbtn_paydata_edit.Attributes["pd_id"] = DataBinder.Eval(e.Row.DataItem, "pd_id").ToString().Trim();
            lbtn_paydata_edit.Attributes["pd_attachfileid"] = DataBinder.Eval(e.Row.DataItem, "pd_attachfileid").ToString().Trim();
            lbtn_paydata_edit.Attributes.Add("onClick", string.Format(@"javascript:var Mleft = (screen.width - 500) / 2;var Mtop = (screen.height - 250) / 2;window.open('addPayData.aspx?command=edit&id={0}&applyno={1}&fileid={2}',10,'height=150,width=550,top='+Mtop+',left='+Mleft+'');", Server.UrlEncode(crypy.EnCode(lbtn_paydata_edit.Attributes["pd_id"].ToString().Trim())), Server.UrlEncode(Request["applyno"].ToString()), Server.UrlEncode(crypy.EnCode(lbtn_paydata_edit.Attributes["pd_attachfileid"].ToString().Trim()))));

            lbtn_paydata_delete.Attributes["pd_id"] = DataBinder.Eval(e.Row.DataItem, "pd_id").ToString().Trim();
            LinkButton lbnt_userfilename = (LinkButton)e.Row.FindControl("lbnt_userfilename");
            string userfilename = DataBinder.Eval(e.Row.DataItem, "afi_userfilename").ToString();
            lbnt_userfilename.Attributes["afi_fileid"] = DataBinder.Eval(e.Row.DataItem, "afi_fileid").ToString().Trim();
            lbnt_userfilename.Text = userfilename;
            lbnt_userfilename.Attributes.Add("onclick", "javascript:window.open('../FileDownloadSeal.aspx?File=" + Server.UrlEncode(EnCode(DataBinder.Eval(e.Row.DataItem, "afi_fileid").ToString().Trim())) + "','_blank');");

        }
    }

    protected void lbtn_paydata_delete_Click(object sender, EventArgs e)
    {
        LinkButton lbtn_paydata_delete = (LinkButton)sender;

        try
        {

            Cryptography crypy = new Cryptography();
            wsApplyBaseDataInfo wsApplyBaseData = new wsApplyBaseDataInfo();
            int keynum = crypy.getRadNum();

            string[] arr_apd = crypy.wsApplyBaseDataValidate(keynum);
            wsApplyBaseData.Del_Nv_fixcatalog(lbtn_paydata_delete.Attributes["pd_id"].ToString().Trim(), arr_apd[0].ToString(), arr_apd[1].ToString());
            // ScriptManager.RegisterStartupScript(this.Page, this.Page.GetType(), "Success", "alert('刪除成功');", true);

            BindPayData();
        }
        catch (Exception ex)
        {
            ex.Message.ToString();
            ScriptManager.RegisterStartupScript(this.Page, this.Page.GetType(), "Fail", "alert('刪除失敗');", true);
        }

    }

    protected void lbn_addpaydata_Click(object sender, EventArgs e)
    {
        BindPayData();
        updp_paydata.Update();
    }
}