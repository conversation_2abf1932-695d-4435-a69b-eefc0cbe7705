﻿<%@ Page Title="" Language="C#" MasterPageFile="~/masterpage/MasterOutset.master"
    AutoEventWireup="true" CodeFile="applybasedata.aspx.cs" Inherits="applydatamt_applybasedata" %>

<%@ Register Src="../usercontrol/GetCalender.ascx" TagName="GetCalender" TagPrefix="uc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script language="javascript" type="text/javascript">
        function raisePayDataPostback() { __doPostBack('ctl00$ContentPlaceHolder1$lbn_addpaydata', ''); }
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <asp:ScriptManager ID="ScriptManager1" runat="server">
    </asp:ScriptManager>
    <div class="fullheader">
        <table width="100%" cellspacing="0" cellpadding="0" class="fullheaderinfo">
            <tr>
                <td valign="top">
                    <b>機動車輛噪音合格證申請-申請表</b>
                </td>
                <td align="right" class="font_loginInfo">
                </td>
            </tr>
        </table>
    </div>
    <!--{* fullheader end *}-->
    <div class="fullcontent">
        <div class="formstyle">
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="font_fullcontent">
                <tr>
                    <td align="right">
                        申請編號：
                    </td>
                    <td>
                        <asp:Label ID="lbl_applyno" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        申請狀態：
                    </td>
                    <td>
                        <div id="div_applied2" runat="server" style="display: none;">
                            <asp:Label ID="lbl_statuscode" runat="server"></asp:Label></div>
                        <asp:Label ID="lbl_staus" runat="server"></asp:Label>
                        <asp:HyperLink ID="hl_RePairProcess" runat="server" Target="_blank" Visible="false">補件通知歷程</asp:HyperLink>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        申請者名稱：
                    </td>
                    <td>
                        <asp:Label ID="lbl_applyusername" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        聯絡人：
                    </td>
                    <td>
                        <div id="div2" runat="server" style="display: none;">
                            <asp:Label ID="Label1" runat="server"></asp:Label></div>
                        <asp:Label ID="lbl_contactname" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        聯絡人電話：
                    </td>
                    <td>
                        <asp:Label ID="lbl_contacttel" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        聯絡人Email：
                    </td>
                    <td>
                        <div id="div1" runat="server" style="display: none;">
                            <asp:Label ID="Label3" runat="server"></asp:Label></div>
                        <asp:Label ID="lbl_contactemail" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        <span style="color: Red;">*</span>交通工具種類：
                    </td>
                    <td>
                        <asp:RadioButtonList ID="rdBtnLst_cartype" runat="server" RepeatDirection="Horizontal"
                            RepeatLayout="Flow">
                        </asp:RadioButtonList>
                        <asp:RequiredFieldValidator ID="rf_cartype" runat="server" ControlToValidate="rdBtnLst_cartype"
                            Display="None" ErrorMessage="交通工具種類至少要選一項"></asp:RequiredFieldValidator>
                    </td>
                    <td align="right">
                        <span style="color: Red;">*</span>引擎族：
                    </td>
                    <td>
                        <asp:TextBox ID="txt_enginefamily" runat="server" Width="152px" CssClass="inputTypeS"
                            MaxLength="30"></asp:TextBox>
                        <asp:RequiredFieldValidator ID="rfenginefamily" runat="server" Display="None" ErrorMessage="引擎族不可空白"
                            ControlToValidate="txt_enginefamily"></asp:RequiredFieldValidator>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        <span style="color: Red;">*</span>適用期別：
                    </td>
                    <td>
                        <asp:DropDownList ID="DrpDnList_adaptstandarddate" runat="server">
                        </asp:DropDownList>
                        <asp:RequiredFieldValidator ID="rf_adaptstandarddate" runat="server" Display="None"
                            ErrorMessage="適用期別至少要選一項" ControlToValidate="DrpDnList_adaptstandarddate"></asp:RequiredFieldValidator>
                    </td>
                    <td align="right">
                        二證合一申請方式：
                    </td>
                    <td>
                        <asp:RadioButtonList ID="rdBtnLst_twocerttype" runat="server" RepeatDirection="Horizontal">
                        </asp:RadioButtonList>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        <span style="color: Red;">*</span>國產/進口：
                    </td>
                    <td>
                        <asp:RadioButtonList ID="rdBtnLst_isimport" runat="server" AutoPostBack="True" RepeatDirection="Horizontal"
                            RepeatLayout="Flow">
                        </asp:RadioButtonList>
                        <asp:RequiredFieldValidator ID="rfv_isimport" runat="server" ControlToValidate="rdBtnLst_isimport"
                            Display="None" ErrorMessage="國產/進口至少要選一項"></asp:RequiredFieldValidator>
                    </td>
                    <td>
                    </td>
                    <td>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        <span style="color: Red;">*</span>製造廠：
                    </td>
                    <td>
                        <asp:TextBox ID="txt_factoryname" runat="server" CssClass="inputTypeS" MaxLength="50"></asp:TextBox>
                        <asp:RequiredFieldValidator ID="rffactoryname" runat="server" Display="None" ErrorMessage="製造廠不可空白"
                            ControlToValidate="txt_factoryname"></asp:RequiredFieldValidator>
                    </td>
                    <td align="right">
                        車型年：
                    </td>
                    <td>
                        <asp:TextBox ID="txt_carstyleyear" runat="server" CssClass="inputTypeS" MaxLength="4"
                            CausesValidation="True"></asp:TextBox>&nbsp;&nbsp;<span
                            style="color: #A9A9A9;">(西元年)</span>
                        <asp:RangeValidator ID="RV_carstyleyear" runat="server" ErrorMessage="須為四碼數字"
                            MaximumValue="9999" MinimumValue="2000" Type="Integer" ControlToValidate="txt_carstyleyear"></asp:RangeValidator>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        <span style="color: Red;">*</span>動力來源：
                    </td>
                    <td>
                        <asp:RadioButtonList ID="rdBtnLst_powerfrom" runat="server" RepeatDirection="Horizontal">
                        </asp:RadioButtonList>
                        <asp:RequiredFieldValidator ID="rfv_rdBtnLst_powerfrom" runat="server" ControlToValidate="rdBtnLst_powerfrom"
                            Display="None" ErrorMessage="動力來源至少要選一項"></asp:RequiredFieldValidator>
                    </td>
                    <td align="right">
                        引擎流水碼：
                    </td>
                    <td>
                        <asp:TextBox ID="txt_enginesn" runat="server" CssClass="inputTypeS" ReadOnly="True"
                            MaxLength="20"></asp:TextBox><br />
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        <span style="color: Red;">*</span>申請函發文日期：
                    </td>
                    <td>
                        <uc1:GetCalender ID="officedocdate" runat="server" DateFormat="yyyy/MM/dd" />
                    </td>
                    <td align="right">
                        申請函發文字號：
                    </td>
                    <td>
                        <asp:TextBox ID="txt_officedocno" runat="server" CssClass="inputTypeS" MaxLength="30"></asp:TextBox>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        <span style="color: Red;">*</span>合格證郵寄地址：
                    </td>
                    <td colspan="3">
                        <asp:TextBox ID="txt_mailcertidocaddr" runat="server" CssClass="inputTypeS" Width="518px"
                            MaxLength="120"></asp:TextBox>
                        <asp:RequiredFieldValidator ID="rf_mailcertidocaddr" runat="server" ControlToValidate="txt_mailcertidocaddr"
                            Display="None" ErrorMessage="合格證郵寄地址不可空白"></asp:RequiredFieldValidator>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        上傳申請函：
                    </td>
                    <td colspan="3">
                        <table>
                            <tr>
                                <td>
                                    <asp:Panel ID="p_fileUpload" runat="server">
                                        <asp:FileUpload ID="fu_importersDoc" runat="server" />
                                    </asp:Panel>
                                </td>
                                <td>
                                    <asp:HyperLink ID="userdocfile" runat="server"></asp:HyperLink>&nbsp;&nbsp;
                                    <span style="color: #A9A9A9;">按下<b>暫存</b>或<b>完成填寫</b>檔案才會上傳 </span>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        <span style="color: Red;">*</span>繳費資料：
                    </td>
                    <td colspan="3">
                        <asp:UpdatePanel ID="updp_paydata" runat="server" UpdateMode="Conditional">
                            <ContentTemplate>
                                <asp:LinkButton ID="lbn_addpaydata" runat="server" Text='新增繳費資料' OnClick="lbn_addpaydata_Click"
                                    CausesValidation="False" Enabled="False"></asp:LinkButton>
                                <asp:GridView ID="gv_paydata" runat="server" AutoGenerateColumns="False" OnRowDataBound="gv_paydata_RowDataBound"
                                    BackColor="White" BorderColor="#CC9966" BorderWidth="1px" CellPadding="4" Width="550px">
                                    <Columns>
                                        <asp:TemplateField ItemStyle-CssClass="lineleft" HeaderStyle-CssClass="header" ItemStyle-HorizontalAlign="Center"
                                            HeaderStyle-HorizontalAlign="Center">
                                            <ItemTemplate>
                                                <asp:LinkButton ID="lbtn_paydata_edit" runat="server" Text='編輯' CausesValidation="False"></asp:LinkButton>
                                                <asp:LinkButton ID="lbtn_paydata_delete" runat="server" OnClick="lbtn_paydata_delete_Click"
                                                    Text='刪除'></asp:LinkButton>
                                            </ItemTemplate>
                                            <HeaderStyle CssClass="lineright"></HeaderStyle>
                                            <ItemStyle CssClass="lineright"></ItemStyle>
                                        </asp:TemplateField>
                                        <asp:BoundField HeaderText="局號" DataField="pd_bankaccount" />
                                        <asp:BoundField HeaderText="日期" DataField="pd_paydate" DataFormatString="{0:d}" />
                                        <asp:BoundField HeaderText="序號" DataField="pd_paysn" />
                                        <asp:BoundField HeaderText="金額" DataField="pd_paymoney" />
                                        <asp:TemplateField HeaderText="繳費單" ItemStyle-CssClass="lineleft" HeaderStyle-CssClass="header">
                                            <ItemTemplate>
                                                <asp:LinkButton ID="lbnt_userfilename" runat="server"></asp:LinkButton>
                                            </ItemTemplate>
                                            <HeaderStyle CssClass="header"></HeaderStyle>
                                            <ItemStyle CssClass="lineleft"></ItemStyle>
                                        </asp:TemplateField>
                                    </Columns>
                                </asp:GridView>
                            </ContentTemplate>
                        </asp:UpdatePanel>
                    </td>
                </tr>
                <tr>
                    <td align="right" width="150px">
                        資料最後修改人：
                    </td>
                    <td>
                        <asp:Label ID="lbl_modifyuser" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        資料最後修改日期：
                    </td>
                    <td>
                        <asp:Label ID="lbl_modifydate" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td colspan="4" align="center">
                        <asp:Button ID="btn_tmpsave" runat="server" Text="暫存" OnClick="btn_tmpsave_Click"
                            CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'" onmouseout="this.className='btn_mouseout'"
                            CausesValidation="False" />
                        <asp:Button ID="btn_apply" runat="server" Text="完成填單" OnClick="btn_apply_Click" CssClass="btn_mouseout"
                            onmouseover="this.className='btn_mouseover'" onmouseout="this.className='btn_mouseout'"
                            Enabled="True" />
                        <asp:Label ID="steps" runat="server" Visible="false"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td colspan="4" align="center">
                        流程：
                        <asp:HyperLink ID="applybase" runat="server" Text="申請表"></asp:HyperLink>
                        &nbsp;＞＞&nbsp;
                        <asp:HyperLink ID="carcompomodel" runat="server" Text="車型組清單"></asp:HyperLink>
                        &nbsp;＞＞&nbsp;
                        <asp:HyperLink ID="docreview" runat="server"></asp:HyperLink>
                    </td>
                </tr>
            </table>
        </div>
    </div>
    <!--{* fullcontent end *}-->
    <div class="fullfooter">
    </div>
    <!--{* content end *}-->
    <asp:HiddenField ID="hidden_uplfileid_pay" runat="server" />
    <asp:HiddenField ID="hidden_uplfileid_apply" runat="server" />
    <asp:HiddenField ID="hidden_flag" runat="server" />
    <asp:HiddenField ID="hidden_uplfilepath_apply" runat="server" />
    <asp:ValidationSummary ID="ValidationSummary1" runat="server" ShowMessageBox="True"
        ShowSummary="False" />
</asp:Content>
