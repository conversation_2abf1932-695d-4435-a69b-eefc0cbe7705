﻿<%@ Page Title="" Language="C#" MasterPageFile="~/masterpage/MasterPage.master" AutoEventWireup="true"
    CodeFile="companydatamt.aspx.cs" Inherits="accountmt_companydatamt" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script language="javascript" type="text/javascript">
        function OpenPostNo() {
            window.open('http://www.post.gov.tw/post/internet/f_searchzone/index.jsp?ID=190102', '', 'dialogWidth:400px;dialogHeight:400px;');
            return false;
        }
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <asp:ScriptManager ID="ScriptManager1" runat="server">
    </asp:ScriptManager>
    <div class="fullheader">
        <table width="100%" cellspacing="0" cellpadding="0" class="fullheaderinfo">
            <tr>
                <td valign="top">
                    <b>公司基本資料維護</b>
                </td>
                <td align="right" valign="top" class="font_loginInfo">
                </td>
            </tr>
        </table>
    </div>
    <!--{* fullheader end *}-->
    <div class="fullcontent">
        <div class="formstyle">
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="font_fullcontent">
                <tr>
                    <td colspan="4">
                        <span class="font_fulltitle"><b>公司基本資料</b></span>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        統一編號：
                    </td>
                    <td>
                        <asp:Label ID="lbl_compidno" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        已申請帳號數：
                    </td>
                    <td>
                        <asp:Label ID="lbl_applycount" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right" class="style1">
                        <span style="color: Red;">*</span>公司名稱：
                    </td>
                    <td>
                        <asp:TextBox ID="txt_compname" runat="server" MaxLength="120" Width="310px" CssClass="inputTypeS"></asp:TextBox>
                        <asp:RequiredFieldValidator ID="rfcompname" runat="server" Display="None" ErrorMessage="公司名稱不能空白"
                            ControlToValidate="txt_compname"></asp:RequiredFieldValidator>
                    </td>
                    <td colspan="2">
                        <asp:Button ID="btn_Sync" runat="server" Text="申請同步" OnClick="btn_Sync_Click" 
                            Enabled="False" />
                    </td>
                </tr>
                <tr>
                    <td align="right" class="style1">
                        公司英文名稱 ：
                    </td>
                    <td>
                        <asp:TextBox ID="txt_compename" runat="server" MaxLength="60" Width="237px" CssClass="inputTypeS"></asp:TextBox>
                    </td>
                    <td align="right">
                        <span style="color: Red;">*</span>負責人姓名：
                    </td>
                    <td>
                        <asp:TextBox ID="txt_chargeman" runat="server" Width="152px" MaxLength="30" CssClass="inputTypeS"></asp:TextBox>
                        <asp:RequiredFieldValidator ID="rfchargeman" runat="server" Display="None" ErrorMessage="負責人姓名不能空白"
                            ControlToValidate="txt_chargeman"></asp:RequiredFieldValidator>
                    </td>
                </tr>
                <tr>
                    <td align="right" class="style1">
                        <span style="color: Red;">*</span>公司電話：
                    </td>
                    <td>
                        <asp:TextBox ID="txt_comptel" runat="server" Width="152px" MaxLength="50" CssClass="inputTypeS"></asp:TextBox>
                        <asp:RequiredFieldValidator ID="rfcomptel" runat="server" Display="None" ErrorMessage="公司電話不能空白"
                            ControlToValidate="txt_comptel"></asp:RequiredFieldValidator>
                    </td>
                    <td align="right">
                        公司傳真：
                    </td>
                    <td>
                        <asp:TextBox ID="txt_compfax" runat="server" Width="152px" MaxLength="30" CssClass="inputTypeS"></asp:TextBox>
                    </td>
                </tr>
                <tr>
                    <td align="right" class="style1">
                        <span style="color: Red;">*</span>公司地址：
                    </td>
                    <td colspan="3">
                        <asp:UpdatePanel ID="UpdatePanel2" runat="server">
                            <ContentTemplate>
                                縣市別：<asp:DropDownList ID="ddl_compaddrcity" runat="server" AutoPostBack="True" OnSelectedIndexChanged="ddl_compaddrcity_SelectedIndexChanged"
                                    class="inputType">
                                </asp:DropDownList>
                                鄉鎮市區別：<asp:DropDownList ID="ddl_compaddrlocalarea" runat="server" AutoPostBack="True"
                                    OnSelectedIndexChanged="ddl_compaddrlocalarea_SelectedIndexChanged" CssClass="inputType">
                                </asp:DropDownList>
                                郵遞區號：
                                <asp:Label ID="lbl_compaddrpostno" runat="server"></asp:Label>
                                <asp:LinkButton ID="lbtn_comppostno" runat="server" Text="查詢" CausesValidation="False"
                                    OnClientClick="OpenPostNo();"></asp:LinkButton><br />
                                <asp:TextBox ID="txt_compaddr" runat="server" Width="449px" MaxLength="120" CssClass="inputTypeS"></asp:TextBox>(請輸入路巷弄號)
                                <asp:RequiredFieldValidator ID="rfcompaddr" runat="server" Display="None" ErrorMessage="公司地址不能空白"
                                    ControlToValidate="txt_compaddr"></asp:RequiredFieldValidator></ContentTemplate>
                        </asp:UpdatePanel>
                    </td>
                </tr>
                <tr>
                    <td align="right" class="style1">
                        <span style="color: Red;">*合格證寄發地址：</span>
                    </td>
                    <td colspan="3">
                        <asp:UpdatePanel ID="UpdatePanel3" runat="server">
                            <ContentTemplate>
                                縣市別：<asp:DropDownList ID="ddl_certaddrCity" runat="server" AutoPostBack="True" CssClass="inputType"
                                    OnSelectedIndexChanged="ddl_certaddrCity_SelectedIndexChanged">
                                </asp:DropDownList>
                                鄉鎮市區別：<asp:DropDownList ID="ddl_certaddrlocalarea" runat="server" AutoPostBack="True"
                                    CssClass="inputType" OnSelectedIndexChanged="ddl_certaddrlocalarea_SelectedIndexChanged">
                                </asp:DropDownList>
                                郵遞區號：
                                <asp:Label ID="lbl_certaddrpostno" runat="server"></asp:Label>
                                <asp:LinkButton ID="lbtn_certaddrpostno" runat="server" Text="查詢" CausesValidation="False"
                                    OnClientClick="OpenPostNo();"></asp:LinkButton><br />
                                <asp:TextBox ID="txt_certaddr" runat="server" Width="449px" MaxLength="120" CssClass="inputTypeS"></asp:TextBox>(請輸入路巷弄號)
                                <asp:RequiredFieldValidator ID="rfcertaddr" runat="server" Display="None" ErrorMessage="合格證寄發地址不能空白"
                                    ControlToValidate="txt_certaddr"></asp:RequiredFieldValidator>
                            </ContentTemplate>
                        </asp:UpdatePanel>
                    </td>
                </tr>
                <tr>
                    <td align="right" class="style1">
                        相關檔案上傳：
                    <td colspan="3">
                        <table>
                            <tr>
                                <td>
                                    公司證明文件：<asp:FileUpload ID="fu_compDoc" runat="server" />
                                </td>
                                <td>
                                    <asp:HyperLink ID="hlk_comDoc" runat="server" Target="_blank"></asp:HyperLink>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    工廠登記證：<asp:FileUpload ID="fu_facCert" runat="server" />
                                </td>
                                <td>
                                    <asp:HyperLink ID="hlk_facCert" runat="server" Target="_blank"></asp:HyperLink>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    代理授權證明函：<asp:FileUpload ID="fu_authLetter" runat="server" />
                                </td>
                                <td>
                                    <asp:HyperLink ID="hlk_authLetter" runat="server" Target="_blank"></asp:HyperLink>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    進口商證明：<asp:FileUpload ID="fu_importCert" runat="server" />
                                </td>
                                <td>
                                    <asp:HyperLink ID="hlk_importCert" runat="server" Target="_blank"></asp:HyperLink>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        最後修改人員：
                    </td>
                    <td>
                        <asp:Label ID="lbl_loginname" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        最後修改時間：
                    </td>
                    <td>
                        <asp:Label ID="lbl_logindate" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td colspan="4" align="center">
                        <asp:Button ID="btn_Save" runat="server" Text="存檔" OnClick="btn_Save_Click" CssClass="btn_mouseout"
                            onmouseover="this.className='btn_mouseover'" onmouseout="this.className='btn_mouseout'" />
                    </td>
                </tr>
            </table>
        </div>
    </div>
    <!--{* fullcontent end *}-->
    <div class="fullfooter">
    </div>
    <!--{* content end *}-->
    <asp:ValidationSummary ID="ValidationSummary1" runat="server" ShowMessageBox="True"
        ShowSummary="False" />
</asp:Content>
