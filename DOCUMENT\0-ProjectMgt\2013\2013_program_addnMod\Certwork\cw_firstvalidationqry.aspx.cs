﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Configuration;

public partial class Certwork_cw_firstvalidationqry : BaseAdminPage
{
	protected void Page_Load(object sender, EventArgs e)
	{
		if (Session["account"] == null)
		{
			Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = '../accountmt/index.aspx';</script>"); return;
		}
		else
		{
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" && (GetAgentAccountGrpList().Contains("fis_adm") || GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("pg_adm")))
			{
				if (!IsPostBack)
				{
					InitData();
				}
			}
			else
			{
				Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
				Response.End();
				return;
			}
		}
	}

	private void InitData()
	{
		DataUtils.BindList(ddl_firstman, new FirstQueryControl().LisFirstMan(), "");		
		reLoadData();
	}

	private void reLoadData()
	{
		string ApplyNo = txt_applyno.Text;
		string CompanyName = txt_compname.Text;
        string ChargeAccount = ddl_firstman.SelectedValue.Trim();
		string inApplyStatus = inStatus();
		gv_data.DataSource = new FirstQueryControl().ListFirstData(ApplyNo, CompanyName, ChargeAccount, inApplyStatus);
		gv_data.DataBind();
	}

	private string inStatus()
	{
		string str = string.Empty;
		switch (rbl_datastatus.SelectedValue)
		{
			case "初審中":
				str = "ApplyStatus='31'";
				break;
			case "補件中":
				str = "ApplyStatus='40'";
				break;
			case "核閱退回":
				str = "ApplyStatus in('91','92','93')";
				break;
			case "核閱完成":
				str = "ApplyStatus='35'";
				break;
			case "審查完成":
                str = "ApplyStatus in('36','50')";
				break;
            case "全部":
                str = "ApplyStatus in('31','40','35','36','50','91','92','93')";
                break;
		}
		return str;
	}

	//查詢
	protected void btn_Search_Click(object sender, EventArgs e)
	{
        #region 特殊字元判斷-有MasterPage
        ContentPlaceHolder mpContentPlaceHolder;
        mpContentPlaceHolder = (ContentPlaceHolder)Master.FindControl("ContentPlaceHolder1");
        foreach (object ctrl in mpContentPlaceHolder.Controls)
        {
            Control content = (Control)ctrl;
            foreach (Control c in content.Controls)
            {
                if (c is TextBox)
                {
                    TextBox objTextBox = default(TextBox);
                    objTextBox = (TextBox)c;
                    if ((tbWord(objTextBox.Text)))
                    {
                        MessageBox.Show(objTextBox.Text + ":含有系統不允許的特殊字元");
                        return;
                    }
                }
            }

        }
        #endregion
		reLoadData();
	}

	//案件認領
	protected void ibt_charge_Click(object sender, ImageClickEventArgs e)
	{
		int i = Int32.Parse(((ImageButton)sender).CommandArgument);
		string ApplyNo = gv_data.DataKeys[i].Values["ApplyNo"].ToString();
		new FirstQueryControl().SetChargeMan(ApplyNo);
		reLoadData();
	}

	//申請編號
	protected void lbt_ApplyNo_Click(object sender, EventArgs e)
	{
		string applyNo = ((LinkButton)sender).Text;
		Response.Redirect("cw_firstvalidationprocess.aspx?p=" + Server.UrlEncode(EnCode(applyNo)));
                                                              
	}
}