﻿<%@ Page Title="" Language="C#" MasterPageFile="~/masterpage/MasterPage.master" AutoEventWireup="true"
    CodeFile="noisedefenseplan.aspx.cs" Inherits="applydatamt_noisedefenseplan" EnableEventValidation="false" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script language="javascript" type="text/javascript">
        function ismaxlength(obj) {
            var mlength = obj.getAttribute ? parseInt(obj.getAttribute("maxlength")) : "";
            if (obj.getAttribute && obj.value.length > mlength)
                obj.value = obj.value.substring(0, mlength);
        }

        function raiseAsyncPostback() { __doPostBack('ctl00$ContentPlaceHolder1$btnReflash', ''); }
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="fullheader">
        <table width="100%" cellspacing="0" cellpadding="0" class="fullheaderinfo">
            <tr>
                <td valign="top">
                    <b>車型組(<asp:Label ID="lbl_carcompomodelno" runat="server"></asp:Label>)-噪音防制對策</b>
                </td>
                <td align="right" valign="top" class="font_loginInfo">
                </td>
            </tr>
        </table>
    </div>
    <div class="fullcontent">
        <div id="div_maintain" runat="server" class="formstyle">
            <table border="0" cellspacing="0" cellpadding="0" width="100%" class="font_fullcontent">
                <tr>
                    <td align="right">
                        申請編號：
                    </td>
                    <td colspan="2">
                        <asp:Label ID="lbl_applyno" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        資料狀態：
                    </td>
                    <td>
                        <asp:Label ID="lbl_applystatus" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right" width="120px">
                        車型名稱：
                    </td>
                    <td colspan="4">
                        <asp:Repeater ID="rpt_representvehicle" runat="server" OnItemDataBound="rpt_representvehicle_ItemDataBound">
                            <ItemTemplate>
                                <asp:Label runat="server" ID="lbl_carengmodelname"></asp:Label>
                            </ItemTemplate>
                            <SeparatorTemplate>
                                <asp:Label ID="lbl_dot" runat="server" Text=";"></asp:Label><br>
                            </SeparatorTemplate>
                        </asp:Repeater>
                    </td>
                    
                </tr>
                <tr>
                <td align="right" colspan="5">
                        <asp:Button ID="btn_Add" runat="server" Text="新增防治對策" CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'"
                            onmouseout="this.className='btn_mouseout'" OnClick="btn_Add_Click" />
                    </td>
                </tr>
            </table>
        </div>
        <div id="div_Edit" runat="server" class="formstyle" style="display: none;">
            <br>
            <table border="0" cellspacing="0" cellpadding="0" width="100%" 
                class="font_fullcontent" >
                <tr>
                    <td align="right">
                        <asp:Label ID="lbl_no" runat="server" Visible="False"></asp:Label>
                        位置：
                    </td>
                    <td>
                        <asp:TextBox ID="txt_position_e" runat="server" MaxLength="50" Width="228px"></asp:TextBox>
                    </td>
                    <td align="right">
                        設施(品名)：
                    </td>
                    <td colspan="3">
                        <asp:TextBox ID="txt_defenseeqpname_e" runat="server" MaxLength="50" 
                            Width="228"></asp:TextBox>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        材質：
                    </td>
                    <td>
                        <asp:TextBox ID="txt_eqpmaterial_e" runat="server" TextMode="MultiLine" Columns="20"
                            MaxLength="300" Rows="5" Width="228px" Height="213"></asp:TextBox>
                    </td>
                    <td align="right">
                        厚度：
                    </td>
                    <td>
                        <asp:TextBox ID="txt_eqpheight_e" runat="server" TextMode="MultiLine" MaxLength="300"
                            Columns="20" Rows="5" Width="228px" Height="213px"></asp:TextBox>
                    </td>
                    <td align="right">
                        備註：
                    </td>
                    <td>
                        <asp:TextBox ID="txt_equremark_e" runat="server" TextMode="MultiLine" MaxLength="300"
                            Columns="20" Rows="5" CausesValidation="True" Height="213px" Width="228px"></asp:TextBox>
                    </td>
                </tr>
                <tr>
                    <td align="center" colspan="10">
                        <asp:Button ID="btn_Ins" runat="server" Text="存檔後結束" CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'"
                            onmouseout="this.className='btn_mouseout'" OnClick="btn_Ins_Click" />&nbsp;&nbsp;&nbsp;&nbsp;
                            <asp:Button ID="btn_ReIns" runat="server" Text="存檔後繼續新增" CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'"
                            onmouseout="this.className='btn_mouseout'" OnClick="btn_ReIns_Click" />&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:Button ID="btn_Canl" runat="server" Text="取消" CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'"
                            onmouseout="this.className='btn_mouseout'" OnClick="btn_Canl_Click" CausesValidation="False" />
                    </td>
                </tr>
            </table>
        </div>
        <div id="div_gvdata" runat="server" class="tableoutcome" 
            contenteditable="inherit">
            <asp:GridView ID="gv_data" runat="server" AutoGenerateColumns="false" Width="100%"
                CellSpacing="0" CellPadding="0" BorderWidth="0" GridLines="None" CssClass="font_fullcontent"
                EmptyDataText="查無資料" OnRowDataBound="gv_data_RowDataBound">
                <Columns>
                    <asp:TemplateField HeaderText="" ItemStyle-CssClass="header" HeaderStyle-CssClass="lineright"
                        ItemStyle-HorizontalAlign="Center" HeaderStyle-HorizontalAlign="Center" HeaderStyle-Width="50px">
                        <ItemTemplate>
                            <asp:Button ID="btn_Delete" runat="server" Text="刪除" CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'"
                                onmouseout="this.className='btn_mouseout'" OnClick="btn_Delete_Click" />
                            <asp:Button ID="btn_Edit" runat="server" Text="修改" CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'"
                                onmouseout="this.className='btn_mouseout'" OnClick="btn_Edit_Click" />
                        </ItemTemplate>
                        <HeaderStyle HorizontalAlign="Center" CssClass="lineright"></HeaderStyle>
                        <ItemStyle HorizontalAlign="Center" CssClass="lineright"></ItemStyle>
                    </asp:TemplateField>
                    <asp:BoundField HeaderText="位置" DataField="ndp_position" ItemStyle-CssClass="lineleft"
                        HeaderStyle-CssClass="header" ItemStyle-HorizontalAlign="Center" HeaderStyle-HorizontalAlign="Center">
                        <HeaderStyle CssClass="header"></HeaderStyle>
                        <ItemStyle CssClass="lineleft"></ItemStyle>
                    </asp:BoundField>
                    <asp:BoundField HeaderText="設施(品名)" DataField="ndp_defenseeqpname" ItemStyle-CssClass="lineleft"
                        HeaderStyle-CssClass="header" ItemStyle-HorizontalAlign="Center" HeaderStyle-HorizontalAlign="Center">
                        <HeaderStyle CssClass="header"></HeaderStyle>
                        <ItemStyle CssClass="lineleft"></ItemStyle>
                    </asp:BoundField>
                    <asp:BoundField HeaderText="材質" DataField="ndp_eqpmaterial" ItemStyle-CssClass="lineleft"
                        HeaderStyle-CssClass="header" ItemStyle-HorizontalAlign="Center" HeaderStyle-HorizontalAlign="Center">
                        <HeaderStyle CssClass="header"></HeaderStyle>
                        <ItemStyle CssClass="lineleft"></ItemStyle>
                    </asp:BoundField>
                    <asp:BoundField HeaderText="厚度" DataField="ndp_eqpheight" ItemStyle-CssClass="lineleft"
                        HeaderStyle-CssClass="header" ItemStyle-HorizontalAlign="Center" HeaderStyle-HorizontalAlign="Center">
                        <HeaderStyle CssClass="header"></HeaderStyle>
                        <ItemStyle CssClass="lineleft"></ItemStyle>
                    </asp:BoundField>
                    <asp:BoundField HeaderText="備註" DataField="ndp_equremark" ItemStyle-CssClass="lineleft"
                        HeaderStyle-CssClass="header" ItemStyle-HorizontalAlign="Center" HeaderStyle-HorizontalAlign="Center">
                        <HeaderStyle CssClass="header"></HeaderStyle>
                        <ItemStyle CssClass="lineleft"></ItemStyle>
                    </asp:BoundField>
                    <asp:TemplateField HeaderText="檔案" ItemStyle-CssClass="lineleft" HeaderStyle-CssClass="header" ItemStyle-HorizontalAlign="Center"
                        HeaderStyle-HorizontalAlign="Center" HeaderStyle-Width="70px">
                        <ItemTemplate>
                            <asp:HyperLink ID="hlk_file" CausesValidation="false" runat="server"></asp:HyperLink>
                        </ItemTemplate>
                        <HeaderStyle CssClass="lineright"></HeaderStyle>
                        <ItemStyle CssClass="lineright"></ItemStyle>
                    </asp:TemplateField>
                </Columns>
                <EmptyDataRowStyle ForeColor="Red" />
            </asp:GridView>
            <asp:Button ID="btnReflash" runat="server" OnClick="btnReflash_Click" Style="width: 0px;
                padding: 0; border-width: 0px" />
        </div>
        <div id="div_subcontent" runat="server" class="formstyle">
            <table border="0" cellspacing="0" cellpadding="0" width="100%" class="font_fullcontent">
                <tr>
                    <td align="left" colspan="4">
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        資料最後修改人：
                    </td>
                    <td>
                        <asp:Label ID="lbl_modaccount" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        資料最後修改日期：
                    </td>
                    <td>
                        <asp:Label ID="lbl_moddate" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="center" colspan="4">
                        <%--  <asp:Button ID="btn_tmpsave" runat="server" Text="暫存" CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'"
                        onmouseout="this.className='btn_mouseout'" CausesValidation="False" />
                    &nbsp;&nbsp;&nbsp;--%>
                        <asp:Button ID="btn_Save" runat="server" Text="完成填寫" CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'"
                            onmouseout="this.className='btn_mouseout'" CausesValidation="False" OnClick="btn_Save_Click" />
                    </td>
                </tr>
            </table>
        </div>
    </div>
    <!--{* fullcontent end *}-->
    <div class="fullfooter">
    </div>
    <!--{* content end *}-->
</asp:Content>
