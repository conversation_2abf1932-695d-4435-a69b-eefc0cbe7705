﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;

public partial class applydatamt_carmodeldata_readonly : BaseAdminPage
{
    COMEMYFAMILY.Validator.ValidatorHelper ValidatorHelper = new COMEMYFAMILY.Validator.ValidatorHelper();
    JSCommon JS = new JSCommon();
    //   int limtLength = 30;
    string Variable_speed_system_TextBox = "Variable_speed_system_TextBox";
    string carmodelno = "";
    wsCarCompoModel wsCarCmpModel = new wsCarCompoModel();
    Cryptography crypy = new Cryptography();
    noisevalidation.db.nv_db_carcompomodel_carmodellist.nv_db_select db_nv = new noisevalidation.db.nv_db_carcompomodel_carmodellist.nv_db_select();
    protected void Page_Load(object sender, EventArgs e)
    {
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = '../index.aspx';</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" &&
                (
                (
                GetAgentAccountGrpList().Contains("epa_adm") || GetAgentAccountGrpList().Contains("ac_adm") || GetAgentAccountGrpList().Contains("tc_adm") || GetAgentAccountGrpList().Contains("rv_adm") || GetAgentAccountGrpList().Contains("rs_adm") || GetAgentAccountGrpList().Contains("fis_adm") || GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("pg_adm")
                )
                ||
                (
                  (GetAgentAccountGrpList().Contains("cga_adm") || GetAgentAccountGrpList().Contains("cam_adm")) && CheckSingleApplyDataAccessRight(DeCode(Request["applyno"].ToString()))
                )
                )
                )
            {
                if (!IsPostBack)
                {
                    if (Request["applyno"] != null && Request["carcompomodelno"] != "" && !string.IsNullOrEmpty(Request_mode()))
                    {
                        //strApplyno = Request["applyno"].ToString();

                        wsCodetbl wsCodeTbl = new wsCodetbl();
                        Cryptography crypy = new Cryptography();
                        int keynum = crypy.getRadNum();
                        string[] arr_as = crypy.wsCodetblValidate(keynum);
                        //

                        string strApplyno = crypy.DeCode(Request["applyno"].ToString());
                        string carcompomodelno = crypy.DeCode(Request["carcompomodelno"].ToString());
                        carmodelno = crypy.DeCode(Request["carmodelno"].ToString());
                        lbl_carmodelno.Text = carmodelno;
                        lbl_carcompomodelno.Text = carcompomodelno;

                        string[] arr = crypy.wsCarCompoModelValidate(keynum);
                       
                        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
                        string[] arrApply = crypy.wsApplybasedataDetailValid(keynum);

                        DataSet dsCarCmpModel = wsCarCmpModel.getDataByApplyNo_CarCompoModelNo(strApplyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());

                        if (dsCarCmpModel.Tables[0].Rows.Count > 0)
                        {
                            //不是系統管理員 要比對公司是否相同
                            if ((GetAgentAccountGrpList().Contains("cam_adm") || GetAgentAccountGrpList().Contains("cga_adm")) && !(GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("pg_adm") || GetAgentAccountGrpList().Contains("epa_adm") || GetAgentAccountGrpList().Contains("ac_adm") || GetAgentAccountGrpList().Contains("tc_adm") || GetAgentAccountGrpList().Contains("rv_adm") || GetAgentAccountGrpList().Contains("rs_adm") || GetAgentAccountGrpList().Contains("fis_adm") ))
                            {  //需檢查是否為登入USER公司的資料

                                if (Session["compidno"].ToString() == dsCarCmpModel.Tables[0].Rows[0]["abd_databelongcompidno"].ToString())
                                {
                                    
                                    doGetSourceData(strApplyno, carcompomodelno, dsCarCmpModel);
                                }
                                else
                                {
                                    wsCarCmpModel.Dispose();
                                    wsCodeTbl.Dispose();
                                    crypy.Dispose();
                                    Response.Write("<script languge='javascript'>alert('統編比對錯誤，畫面將轉回首頁！');window.location.href='../index.aspx'</script>");
                                    Response.End();
                                }
                            }
                            else
                            {
                                doGetSourceData(strApplyno, carcompomodelno, dsCarCmpModel);
                            }
                        }
                        else
                        {
                            //取不到申請編號畫面轉走
                            wsCarCmpModel.Dispose();
                            wsCodeTbl.Dispose();
                            crypy.Dispose();
                            Response.Write("<script languge='javascript'>alert('查無申請資料，系統將導回首頁！');window.location.href='../index.aspx'</script>");
                            Response.End();
                        }
                    }
                    else
                    {
                        //取不到申請編號畫面轉走                        
                        Response.Write("<script languge='javascript'>alert('參數不足，系統將導回首頁！');window.location.href='../index.aspx'</script>");
                        Response.End();
                    }
                }
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }
    }

    #region 更改的麵包屑傳入的參數
    protected void Page_LoadComplete(object sender, EventArgs e)
    {
        ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text = ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text.Replace("applydatamt/applybasedata_detail.aspx", "applydatamt/applybasedata_detail.aspx?applyno=" + Server.UrlEncode(Request["applyno"].ToString()));
        ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text = ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text.Replace("applydatamt/carcompomodel_readnoly.aspx", "applydatamt/carcompomodel_readnoly.aspx?applyno=" + Server.UrlEncode(Request["applyno"].ToString()) + "&carcompomodelno=" + Server.UrlEncode(Request["carcompomodelno"].ToString()));
    }
    #endregion

    private void doGetSourceData(string strApplyno, string carcompomodelno, DataSet dsCarCmpModel)
    {

        wsCodetbl wsCodeTbl = new wsCodetbl();
        Cryptography crypy = new Cryptography();
        int keynum = crypy.getRadNum();
        string[] arr_as = crypy.wsCodetblValidate(keynum);
        //檢查是否有車型編號  Request["carmodelno"].ToString()，如果沒有->新增
        lbl_applyno.Text = strApplyno;
        lbl_carcompomodelno.Text = carcompomodelno;
        lbl_applytype.Text = dsCarCmpModel.Tables[0].Rows[0]["cpm_applytype"].ToString();
        lbl_compname.Text = dsCarCmpModel.Tables[0].Rows[0]["abd_databelongcompname"].ToString();
        lbl_carstyleyear.Text = dsCarCmpModel.Tables[0].Rows[0]["abd_carstyleyear"].ToString();
        lbl_setuppos.Text = dsCarCmpModel.Tables[0].Rows[0]["ct_ep_desc_g"].ToString();
        lbl_exhaust.Text = dsCarCmpModel.Tables[0].Rows[0]["cpm_exhuast"].ToString().Replace(".00", "");
        lbl_exhaust2.Text = dsCarCmpModel.Tables[0].Rows[0]["cpm_exhuast"].ToString().Replace(".00", "");
        if (dsCarCmpModel.Tables[0].Rows[0]["abd_carstyleyear"].ToString() == "G")
        {
            lbl_cylindernums.Text = dsCarCmpModel.Tables[0].Rows[0]["cpm_cylindernums"].ToString();
        }
        else
        {
            //取得排列方式
            DataSet ds_cylindertype = wsCodeTbl.getCylinderPos_M(arr_as[0].ToString(), arr_as[1].ToString());
            if (ds_cylindertype.Tables[0].Rows.Count > 0)
            {
                foreach (DataRow dr in ds_cylindertype.Tables[0].Rows)
                {
                    if (dr["ct_cp_id_m"].ToString() == dsCarCmpModel.Tables[0].Rows[0]["cpm_cylindertype"].ToString())
                        lbl_cylindernums.Text = dr["ct_cp_desc_m"].ToString();
                }
            }
            lbl_cylindernums.Text = dsCarCmpModel.Tables[0].Rows[0]["cpm_cylindernums"].ToString() + "缸 " + lbl_cylindernums.Text;
        }
        lbl_coolsystem.Text = dsCarCmpModel.Tables[0].Rows[0]["ct_cs_desc"].ToString();
        lbl_provideoilmethod.Text = dsCarCmpModel.Tables[0].Rows[0]["ct_le_desc"].ToString();
        lbl_transmissiontype.Text = dsCarCmpModel.Tables[0].Rows[0]["ct_gm_desc"].ToString();
        //   lbl_drivetraintype.Text = dsCarCmpModel.Tables[0].Rows[0]["ct_dt_desc"].ToString();
        lbl_Installation_location_manner.Text = dsCarCmpModel.Tables[0].Rows[0]["cpm_enginesetupposnmethod"].ToString();

        
        if (Request["carmodelno"].ToString() != null && Request["carmodelno"].ToString() != "")
        {
            string[] arr_carmodel = crypy.wsCarCompoModelValidate(crypy.getRadNum());
            DataTable dtCarData = wsCarCmpModel.Get_single_carspectabledata(strApplyno, carcompomodelno, carmodelno, Request_mode().ToUpper(), arr_carmodel[0].ToString(), arr_carmodel[1].ToString());
            if (dtCarData.Rows.Count > 0)
            {
                BindDBData(dtCarData);
                //DataTable dtCar_GearRatioData = wsCarCmpModel.Get_single_carmodel_GearRatio(strApplyno, carcompomodelno, carmodelno, arr_carmodel[0].ToString(), arr_carmodel[1].ToString());
                //BindGearRatio(dtCar_GearRatioData);
            }
            else
            {
                wsCarCmpModel.Dispose();
                wsCodeTbl.Dispose();
                crypy.Dispose();
                Response.Write("<script languge='javascript'>alert('查無申請車型規格表資料，系統將導回首頁！');window.location.href='../index.aspx'</script>");
                Response.End();
            }

        }
        else
        {
            //參數不足
            Response.Write("<script languge='javascript'>alert('無此車型代碼，系統將導回首頁！');window.location.href='../index.aspx'</script>");
            Response.End();

        }
        ControlObject();
        wsCarCmpModel.Dispose();
        wsCodeTbl.Dispose();
        crypy.Dispose();
    }

    private void BindDBData(DataTable dt_cardata)
    {
        string mode = Request_mode();
        lbl_applyno.Text = dt_cardata.Rows[0]["cmdg_applyno"].ToString();
        lbl_carcompomodelno.Text = dt_cardata.Rows[0]["cmdg_carcompomodelno"].ToString();

        tb_carbodyshapedesc.Text = dt_cardata.Rows[0]["ct_cbs_desc"].ToString().Trim() == "其他" ? dt_cardata.Rows[0]["ct_cbs_desc"].ToString().Replace("其他", "") + dt_cardata.Rows[0]["cmdg_carbodyshapedesc"].ToString().Trim() : dt_cardata.Rows[0]["ct_cbs_desc"].ToString();
        if (dt_cardata.Rows[0]["cmdg_berepresentativevehicle"].ToString().Equals("Y", StringComparison.CurrentCultureIgnoreCase))
            lbl_berepresentativevehicle.Text = "是";
        else
            lbl_berepresentativevehicle.Text = "否";
        lbl_producercountry.Text = dt_cardata.Rows[0]["cmdg_producercountry"].ToString(); //製造地區
        lbl_cartype.Text = dt_cardata.Rows[0]["ct_nvc_desc"].ToString();
        tb_baseenginename.Text = dt_cardata.Rows[0]["cmdg_baseenginename"].ToString();
        lbl_baseenginename.Text = dt_cardata.Rows[0]["cmdg_baseenginename"].ToString();
        lbl_brand.Text = dt_cardata.Rows[0]["ct_bd_desc"].ToString();
        tb_carchnmodelname.Text = dt_cardata.Rows[0]["cmdg_carchnmodelname"].ToString();
        tb_carengmodelname.Text = dt_cardata.Rows[0]["cmdg_carengmodelname"].ToString();
        tb_carmodelcode.Text = dt_cardata.Rows[0]["cmdg_carmodelcode"].ToString();
        lbl_transmissiontype.Text = dt_cardata.Rows[0]["ct_gm_desc"].ToString();
        lbl_transmissionnum.Text = dt_cardata.Rows[0]["cmdg_transmissionnum"].ToString();
        lbl_cardoorcount.Text = dt_cardata.Rows[0]["ct_vr_desc"].ToString();

        tb_carmodeladd.Text = dt_cardata.Rows[0]["cmdg_carmodeladd"].ToString();
        tb_carbodylenth.Text = dt_cardata.Rows[0]["cmdg_carbodylenth"].ToString();
        tb_carbodywidth.Text = dt_cardata.Rows[0]["cmdg_carbodywidth"].ToString();
        tb_carbodyhieght.Text = dt_cardata.Rows[0]["cmdg_carbodyhieght"].ToString();
        tb_carwheelbase.Text = dt_cardata.Rows[0]["cmdg_carwheelbase"].ToString();
        tb_carwheelbase_f.Text = dt_cardata.Rows[0]["cmdg_carwheelbase_f"].ToString();
        tb_carwheelbase_b.Text = dt_cardata.Rows[0]["cmdg_carwheelbase_b"].ToString();
        tb_caremptyweight.Text = dt_cardata.Rows[0]["cmdg_caremptyweight"].ToString();
        tb_cartotalweight.Text = dt_cardata.Rows[0]["cmdg_cartotalweight"].ToString();
        tb_passengers.Text = dt_cardata.Rows[0]["cmdg_passengers"].ToString();
        tb_cylindermeters.Text = dt_cardata.Rows[0]["cmdg_cylindermeters"].ToString().Replace(".00", "");
        tb_cylinderstroke.Text = dt_cardata.Rows[0]["cmdg_cylinderstroke"].ToString().Replace(".00", "");
        tb_compressionratio.Text = dt_cardata.Rows[0]["cmdg_compressionratio"].ToString();
        tb_maxhorsepower.Text = dt_cardata.Rows[0]["cmdg_maxhorsepower"].ToString();
        tb_maxHorsepowerspeed.Text = dt_cardata.Rows[0]["cmdg_maxHorsepowerspeed"].ToString();
        tb_torque.Text = dt_cardata.Rows[0]["cmdg_torque"].ToString();
        tb_torquespeed.Text = dt_cardata.Rows[0]["cmdg_torquespeed"].ToString();
        lbl_turbo_cycles.Text = dt_cardata.Rows[0]["ct_htm_desc"].ToString();
        //"Y";//渦輪增壓器：";
        lbl_use_fuel.Text = dt_cardata.Rows[0]["ct_ft_desc"].ToString();
        tb_oilcapacity.Text = dt_cardata.Rows[0]["cmdg_oilcapacity"].ToString();
        tb_929598.Text = dt_cardata.Rows[0]["cmdg_929598"].ToString();
        tb_exhaustsystem.Text = dt_cardata.Rows[0]["cmdg_exhaustsystem"].ToString();
        tb_pcv.Text = dt_cardata.Rows[0]["cmdg_pcv"].ToString();
        tb_eec.Text = dt_cardata.Rows[0]["cmdg_eec"].ToString();
        tb_differentialmodel.Text = dt_cardata.Rows[0]["cmdg_differentialmodel"].ToString();
        tb_differentialgearratio.Text = dt_cardata.Rows[0]["cmdg_differentialgearratio"].ToString();
        tb_transmissionmodel.Text = dt_cardata.Rows[0]["cmdg_transmissionmodel"].ToString();
        tb_transmissionbackgearratio.Text = dt_cardata.Rows[0]["cmdg_transmissionbackgearratio"].ToString();
        tb_highestspeed.Text = dt_cardata.Rows[0]["cmdg_highestspeed"].ToString().Replace(".00", "");
        tb_suspensionsystem_f.Text = dt_cardata.Rows[0]["cmdg_suspensionsystem_f"].ToString();
        tb_suspensionsystem_b.Text = dt_cardata.Rows[0]["cmdg_suspensionsystem_b"].ToString();
        tb_tirespec_std_f.Text = dt_cardata.Rows[0]["cmdg_tirespec_std_f"].ToString();
        tb_tirespec_std_b.Text = dt_cardata.Rows[0]["cmdg_tirespec_std_b"].ToString();
        tb_tirespec_cho_f.Text = dt_cardata.Rows[0]["cmdg_tirespec_cho_f"].ToString();
        tb_tirespec_cho_b.Text = dt_cardata.Rows[0]["cmdg_tirespec_cho_b"].ToString();
        tb_caremptyweight.Text = dt_cardata.Rows[0]["cmdg_caremptyweight"].ToString().Replace(".00", "");
        tb_testgearreductionratio.Text = dt_cardata.Rows[0]["cmdg_testgearreductionratio"].ToString();
        tb_coolingdrivermethod.Text = dt_cardata.Rows[0]["cmdg_coolingdrivermethod"].ToString();
        tb_tiresnum.Text = dt_cardata.Rows[0]["cmdg_tiresnum"].ToString();
        tb_exhaustpipenum.Text = dt_cardata.Rows[0]["cmdg_exhaustpipenum"].ToString();
        tb_tirewidth.Text = dt_cardata.Rows[0]["cmdg_tirewidth"].ToString();
        tb_inletmode.Text = dt_cardata.Rows[0]["ct_im_desc"].ToString();
        //機車的欄位 ,"cmdg_loadingweight","cmdg_firemethod","cmdg_strokeposdirection","cmdg_PM","cmdg_CO","cmdg_HC","cmdg_1stReductionratio","cmdg_2ndReductionratio"

        Load_weight_TextBox1.Text = dt_cardata.Rows[0]["cmdg_loadingweight"].ToString().Replace(".00", "");
        Start_up_mode_TextBox2.Text = dt_cardata.Rows[0]["cmdg_firemethod"].ToString();
        The_location_and_direction_of_the_exhaust_port_TextBox1.Text = dt_cardata.Rows[0]["cmdg_strokeposdirection"].ToString();
        tb_pm.Text = dt_cardata.Rows[0]["cmdg_PM"].ToString();
        tb_co.Text = dt_cardata.Rows[0]["cmdg_CO"].ToString();
        tb_hc.Text = dt_cardata.Rows[0]["cmdg_HC"].ToString();
        Primary_reduction_ratio_TextBox3.Text = dt_cardata.Rows[0]["cmdg_1stReductionratio"].ToString();
        Secondary_reduction_ratio_TextBox4.Text = dt_cardata.Rows[0]["cmdg_2ndReductionratio"].ToString();
       
        //柴油車
        if (mode.ToString().ToLower() == "d") 
        {
            lbl_backgearnums.Text = dt_cardata.Rows[0]["cmdg_backgearnums"].ToString();
            lbl_forwardgearnums.Text = dt_cardata.Rows[0]["cmdg_forwardgearnums"].ToString();
            lbl_D_highlowgear.Text = dt_cardata.Rows[0]["highlowgear"].ToString();
            lbl_carbodyweight.Text = dt_cardata.Rows[0]["cmdg_carbodyweight"].ToString();

            lbl_maxhorsepower_hb.Text = dt_cardata.Rows[0]["cmdg_maxhorsepower_hb"].ToString();
            lbl_maxhorsepowerspeed_hb.Text = dt_cardata.Rows[0]["cmdg_maxhorsepowerspeed_hb"].ToString();
            lbl_torque_hb.Text = dt_cardata.Rows[0]["cmdg_torque_hb"].ToString();
            lbl_torquespeed_hb.Text = dt_cardata.Rows[0]["cmdg_torquespeed_hb"].ToString();

            lbl_numofaxes_f.Text = dt_cardata.Rows[0]["cmdg_numofaxes_f"].ToString() == "O" ? "單" : "雙";
            lbl_numofaxes_b.Text = dt_cardata.Rows[0]["cmdg_numofaxes_b"].ToString() == "O" ? "單" : "雙";

            lbl_increasepowerstyle.Text = dt_cardata.Rows[0]["cmdg_increasepowerstyle"].ToString();
            lbl_increasepowerratio.Text = dt_cardata.Rows[0]["cmdg_increasepowerratio"].ToString();
            lbl_climbratio.Text = dt_cardata.Rows[0]["cmdg_climbratio"].ToString();
            lbl_suspensionsystem_sub_content.Text = dt_cardata.Rows[0]["cmdg_suspensionsystem_supplement"].ToString();
        }
        last_modaccount.Text = dt_cardata.Rows[0]["ai_username"].ToString();
        last_moddate.Text = dt_cardata.Rows[0]["cmdg_moddate"].ToString();
       
        //GetAgentAccount();
        lbl_drivetype.Text = dt_cardata.Rows[0]["ct_dt_desc"].ToString();

        lbl_testweight.Text = dt_cardata.Rows[0]["cmdg_testweight"].ToString().Replace(".00", "");

        txt_remark.Text = dt_cardata.Rows[0]["cmdg_remark"].ToString();

        string[] arr_carmodel = crypy.wsCarCompoModelValidate(crypy.getRadNum());
        DataTable dtCar_GearRatioData = wsCarCmpModel.Get_single_carmodel_GearRatio(lbl_applyno.Text, lbl_carcompomodelno.Text, lbl_carmodelno.Text, arr_carmodel[0].ToString(), arr_carmodel[1].ToString());


        //如果排檔方式是CVT 就不用顯示排檔數
        if (lbl_transmissiontype.Text == "CVT")
        {
            lbl_transmissionnum.Text = "2";
            div_Ctv.Visible = false;
        }
        else {
            div_Ctv.Visible = true;
        }

        Create_GearRatio(Variable_speed_system, int.Parse(lbl_transmissionnum.Text), dtCar_GearRatioData);
        dtCar_GearRatioData.Dispose();
    }

    private void Create_GearRatio(System.Web.UI.HtmlControls.HtmlGenericControl div, int count, DataTable dt_gearratio)
    {
        int i;

        div.InnerHtml = "";
        if (lbl_transmissiontype.Text != "CVT")
        {
            for (i = 1; i <= count; i++)
            {
                Label LabelVirtual = new Label();
                LabelVirtual.ID = "Variable_speed_system_Label" + i;
                LabelVirtual.Text = i + "檔：";

                Label LabelVirtual_value = new Label();
                LabelVirtual_value.ID = Variable_speed_system_TextBox + i;
                if (dt_gearratio.Rows.Count > 0 && dt_gearratio.Rows.Count >= i)
                {
                    LabelVirtual_value.Text = dt_gearratio.Rows[i - 1]["gr_gearratio"].ToString();
                }
                else
                {
                    LabelVirtual_value.Text = "";
                }

                System.Web.UI.HtmlControls.HtmlGenericControl br = new System.Web.UI.HtmlControls.HtmlGenericControl();

                br.TagName = "div";
                br.Controls.Add(LabelVirtual);
                br.Controls.Add(LabelVirtual_value);
                div.Controls.Add(br);
            }
        }
        else
        {
            for (i = 1; i <= 2; i++)
            {
                Label LabelVirtual = new Label();
                LabelVirtual.ID = "Variable_speed_system_Label" + i;
                if (i == 1)
                {
                    LabelVirtual.Text = "UD檔：";
                }
                else
                {
                    LabelVirtual.Text = "OD檔：";
                }

                Label LabelVirtual_value = new Label();
                LabelVirtual_value.ID = Variable_speed_system_TextBox + i;
                if (dt_gearratio.Rows.Count > 0 && dt_gearratio.Rows.Count >= i)
                {
                    LabelVirtual_value.Text = dt_gearratio.Rows[i - 1]["gr_gearratio"].ToString();
                }
                else
                {
                    LabelVirtual_value.Text = "";
                }

                System.Web.UI.HtmlControls.HtmlGenericControl br = new System.Web.UI.HtmlControls.HtmlGenericControl();

                br.TagName = "div";
                br.Controls.Add(LabelVirtual);
                br.Controls.Add(LabelVirtual_value);
                div.Controls.Add(br);
            }
        }
        int n = Variable_speed_system.Controls.Count;
    }

    private void ControlObject()
    {
        if (!string.IsNullOrEmpty(Request_mode()))
        {
            string mode = Request_mode();

            if (mode.ToString().ToLower() == "g") //汽車
            {
                Label1_Title.Text = "噪音合格證明申請-汽油車(";
                lbl_powerfrom.Text = "電動汽車、複合動力電動車";
                Label2_Title.Text = ")車輛規格表資料填寫";
                Classification_of_vehicle_types.Visible = true;//車種分類
                Gate_Count.Visible = true; //門數

                div_D_highlowgear.Visible = false; //後退檔
                D_CarbodyWeight_title.Visible = false; //車體總重量
                D_maxhorsepower_Title.Visible = false; //馬達最大馬力
                D_torque_Title.Visible = false; //馬達最大扭力
                D_numofaxes_Title.Visible = false; //驅動軸數
                D_increasepower_title.Visible = false; //加力箱
                D_climbratio_title.Visible = false; //爬坡檔
                lbl_suspensionsystem_sub_title.Visible = false; //懸吊補
                lbl_suspensionsystem_sub_content.Visible = false; //懸吊補

                Car_driven_approach_title.Visible = true;//驅動方式
                Car_driven_approach_Dr.Visible = true;//驅動方式
                Automotive_fuel_title.Visible = true;//油料
                Automotive_fuel_title_content.Visible = true;//油料內容

                lbl_haveturbo.Text = "渦輪增壓器：";
                lbl_turbo_cycles.Visible = true;

                Automotive_Exhaust_System_title.Visible = true; //排氣系統
                Automotive_Exhaust_System_content.Visible = true;//排氣系統
                PCV_title.Visible = true; //PCV
                PCV_content.Visible = true;//PCV

                EEC_title.Visible = true; //EEC
                EEC_content.Visible = true;//EEC

                Automobile_differential_type_title.Visible = true; //差速器-型式
                Automobile_differential_type_content.Visible = true;//差速器-型式

                Automobile_differential_gear_ratio_title.Visible = true;//差速器-齒比
                Automobile_differential_gear_ratio_content.Visible = true;//差速器-齒比

                Transmission_system_reverse_gear_ratio_title.Visible = true;//變速系統-倒檔齒比
                Transmission_system_reverse_gear_ratio_content.Visible = true;//變速系統-倒檔齒比
                Installation_location_title.Visible = true;
                Installation_location_content.Visible = true;
                ///////////////////////////以上汽車////////////////////////////
                ///////////////////////////以下機車////////////////////////////
                The_location_and_direction_of_the_exhaust_port_title.Visible = false; //排氣口位置及方向
                The_location_and_direction_of_the_exhaust_port_content.Visible = false;//排氣口位置及方向
                Load_weight_title.Visible = false; //負重
                Load_weight_content.Visible = false; //負重
                Start_up_mode_title.Visible = false;//啟動方式
                Start_up_mode_content.Visible = false;//啟動方式
                Concentration_of_emissions.Visible = false;//碳氧化濃度
                Reduction_ratio.Visible = false; //二次減速比
                use_fuel_01.Visible = false;

                lbl_colheader_cylinder.Text = "汽缸數：";
                /////////////////////////修改名字////////////////////////////

                Label1_Bore_and_Inner_diameter01.Text = "缸徑";
                Stroke_stroke_Label1_title.Text = "衝程";
            }
            else if (mode.ToString().ToLower() == "m")
            {
                Label1_Title.Text = "噪音合格證明申請-機器腳踏車(";
                lbl_powerfrom.Text = "複合動力電動機車";
                Label2_Title.Text = ")規格表資料填寫";
                Carwheelbase_f_title.Visible = false;//輪距-前
                Carwheelbase_f_content.Visible = false;//輪距-前
                Carwheelbase_b_title.Visible = false;//輪距-後
                Carwheelbase_b_content.Visible = false;//輪距-後
                Classification_of_vehicle_types.Visible = false;//車種分類
                Gate_Count.Visible = false;//門數

                div_D_highlowgear.Visible = false; //後退檔
                D_CarbodyWeight_title.Visible = false; //車體總重量
                D_maxhorsepower_Title.Visible = false; //馬達最大馬力
                D_torque_Title.Visible = false; //馬達最大扭力
                D_numofaxes_Title.Visible = false; //驅動軸數
                D_increasepower_title.Visible = false; //加力箱
                D_climbratio_title.Visible = false; //爬坡檔
                lbl_suspensionsystem_sub_title.Visible = false; //懸吊補
                lbl_suspensionsystem_sub_content.Visible = false; //懸吊補

                Car_driven_approach_title.Visible = false; //驅動方式
                Car_driven_approach_Dr.Visible = false;//驅動方式
                Automotive_fuel_title.Visible = false;//油料
                Automotive_fuel_title_content.Visible = false;//油料內容

                PollutionPreventionControlSystem.Visible = false;
                Automotive_Exhaust_System_title.Visible = false; //排氣系統
                Automotive_Exhaust_System_content.Visible = false;//排氣系統
                PCV_title.Visible = false;//PCV
                PCV_content.Visible = false;//PCV

                EEC_title.Visible = false; //EEC
                EEC_content.Visible = false;//EEC

                Automobile_differential_type_title.Visible = false; //差速器-型式
                Automobile_differential_type_content.Visible = false;//差速器-型式


                Automobile_differential_gear_ratio_title.Visible = false;//差速器-齒比
                Automobile_differential_gear_ratio_content.Visible = false;//差速器-齒比

                Transmission_system_reverse_gear_ratio_title.Visible = false;//變速系統-倒檔齒比
                Transmission_system_reverse_gear_ratio_content.Visible = false;//變速系統-倒檔齒比
                Installation_location_title.Visible = false;
                Installation_location_content.Visible = false;
                ///////////////////////////以上汽車////////////////////////////
                ///////////////////////////以下機車////////////////////////////
                The_location_and_direction_of_the_exhaust_port_title.Visible = true; //排氣口位置及方向
                The_location_and_direction_of_the_exhaust_port_content.Visible = true;//排氣口位置及方向
                Load_weight_title.Visible = true;//負重
                Load_weight_content.Visible = true; //負重
                Start_up_mode_title.Visible = true;//啟動方式
                Start_up_mode_content.Visible = true;//啟動方式
                Concentration_of_emissions.Visible = true;//碳氧化濃度
                Reduction_ratio.Visible = true; //一次減速比
                use_fuel_01.Visible = true;
                lbl_colheader_cylinder.Text = "汽缸缸數及排列：";

                lbl_haveturbo.Text = "循環數：";
                lbl_turbo_cycles.Visible = false;
                /////////////////////////修改名字////////////////////////////

                Label1_Bore_and_Inner_diameter01.Text = "內徑";
                Stroke_stroke_Label1_title.Text = "行程";
            }
            else //柴油車
            {
                Label1_Title.Text = "噪音合格證明申請-柴油車(";
                lbl_powerfrom.Text = "複合動力電動車";
                Label2_Title.Text = ")車輛規格表資料填寫";
                Classification_of_vehicle_types.Visible = true;//車種分類
                Gate_Count.Visible = true; //門數
                Passengers_title.Visible = false; //乘坐人數
                Passengers_content.Visible = false; //乘坐人數
                Cartotal_weight_title.Visible = false; //總重
                Cartotal_weight_content.Visible = false; //總重
                div_D_highlowgear.Visible = true; //後退檔
                D_CarbodyWeight_title.Visible = true; //車體總重量
                D_maxhorsepower_Title.Visible = true; //馬達最大馬力
                D_torque_Title.Visible = true; //馬達最大扭力
                D_numofaxes_Title.Visible = true; //驅動軸數
                D_increasepower_title.Visible = true; //加力箱
                D_climbratio_title.Visible = true; //爬坡檔
                lbl_suspensionsystem_sub_title.Visible = true; //懸吊補
                lbl_suspensionsystem_sub_content.Visible = true; //懸吊補

                Car_driven_approach_title.Visible = true;//驅動方式
                Car_driven_approach_Dr.Visible = true;//驅動方式
                Automotive_fuel_title.Visible = true;//油料
                Automotive_fuel_title_content.Visible = true;//油料內容

                lbl_haveturbo.Text = "增壓器：";
                lbl_turbo_cycles.Visible = true;


                //防治污染系統
                PollutionPreventionControlSystem.Visible = false;

                Automotive_Exhaust_System_title.Visible = false; //排氣系統
                Automotive_Exhaust_System_content.Visible = false;//排氣系統

                PCV_title.Visible = false; //PCV
                PCV_content.Visible = false;//PCV

                EEC_title.Visible = false; //EEC
                EEC_content.Visible = false;//EEC

                Automobile_differential_type_title.Visible = true; //差速器-型式
                Automobile_differential_type_content.Visible = true;//差速器-型式

                Automobile_differential_gear_ratio_title.Visible = true;//差速器-齒比
                Automobile_differential_gear_ratio_content.Visible = true;//差速器-齒比

                Transmission_system_reverse_gear_ratio_title.Visible = true;//變速系統-倒檔齒比
                Transmission_system_reverse_gear_ratio_content.Visible = true;//變速系統-倒檔齒比
                Installation_location_title.Visible = true;
                Installation_location_content.Visible = true;
                ///////////////////////////以上汽車////////////////////////////
                ///////////////////////////以下機車////////////////////////////
                The_location_and_direction_of_the_exhaust_port_title.Visible = false; //排氣口位置及方向
                The_location_and_direction_of_the_exhaust_port_content.Visible = false;//排氣口位置及方向
                Load_weight_title.Visible = true; //負重
                Load_weight_content.Visible = true; //負重
                Start_up_mode_title.Visible = false;//啟動方式
                Start_up_mode_content.Visible = false;//啟動方式
                Concentration_of_emissions.Visible = false;//碳氧化濃度
                Reduction_ratio.Visible = false; //二次減速比
                use_fuel_01.Visible = false;

                lbl_colheader_cylinder.Text = "汽缸數：";
                /////////////////////////修改名字////////////////////////////

                Label1_Bore_and_Inner_diameter01.Text = "缸徑";
                Stroke_stroke_Label1_title.Text = "衝程";
            }


        }
    }

    private string Request_mode()
    {
        string value = string.Empty;

        if (Request.QueryString["mode"] != null)
        {
            if (!string.IsNullOrEmpty(Request.QueryString["mode"].ToString()))
            {
                try
                {
                    value = crypy.DeCode(Request.QueryString["mode"].ToString());
                }
                catch { }
            }
        }

        return value.Trim().ToLower();
    }
    protected void btn_print_Click(object sender, EventArgs e)
    {
        string mode = Request_mode();

        string scriptString = "";
        if (mode.ToString().ToLower() == "g") //汽車
        {
            //到列印畫面
            scriptString = string.Format(@"var newwin=window.open('carmodeldatamt_g_rpt.aspx?applyno={0}&carcompomodelno={1}&carmodelno={2}','winG','scrollbars=yes');newwin.resizeTo(screen.width-20,screen.height-20) ;newwin.moveTo(0,0); ", Server.UrlEncode(Request["applyno"].ToString()), Server.UrlEncode(Request["carcompomodelno"].ToString()), Server.UrlEncode(Request["carmodelno"].ToString()));
            this.ClientScript.RegisterStartupScript(this.GetType(), "", scriptString, true);

        }
        else if (mode.ToString().ToLower() == "m")
        {
            //到列印畫面
            scriptString = string.Format(@"var newwin=window.open('carmodeldatamt_m_rpt.aspx?applyno={0}&carcompomodelno={1}&carmodelno={2}','winM','scrollbars=yes');newwin.resizeTo(screen.width-20,screen.height-20) ;newwin.moveTo(0,0); ", Server.UrlEncode(Request["applyno"].ToString()), Server.UrlEncode(Request["carcompomodelno"].ToString()), Server.UrlEncode(Request["carmodelno"].ToString()));
            this.ClientScript.RegisterStartupScript(this.GetType(), "", scriptString, true);
        }
        else
        {
            //到列印畫面
            scriptString = string.Format(@"var newwin=window.open('carmodeldatamt_d_rpt.aspx?applyno={0}&carcompomodelno={1}&carmodelno={2}','winM','scrollbars=yes');newwin.resizeTo(screen.width-20,screen.height-20) ;newwin.moveTo(0,0); ", Server.UrlEncode(Request["applyno"].ToString()), Server.UrlEncode(Request["carcompomodelno"].ToString()), Server.UrlEncode(Request["carmodelno"].ToString()));
            this.ClientScript.RegisterStartupScript(this.GetType(), "", scriptString, true);
        }

        string[] arr_carmodel = crypy.wsCarCompoModelValidate(crypy.getRadNum());
        string strApplyno = crypy.DeCode(Request["applyno"].ToString());
        string carcompomodelno = crypy.DeCode(Request["carcompomodelno"].ToString());
        string carmodelno = crypy.DeCode(Request["carmodelno"].ToString());
        DataTable dtCarData = wsCarCmpModel.Get_single_carspectabledata(strApplyno, carcompomodelno, carmodelno, Request_mode().ToUpper(), arr_carmodel[0].ToString(), arr_carmodel[1].ToString());
        if (dtCarData.Rows.Count > 0)
        {
            BindDBData(dtCarData);
        }

    }
}