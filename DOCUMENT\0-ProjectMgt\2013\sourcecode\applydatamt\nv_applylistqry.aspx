﻿<%@ Page Title="" Language="C#" MasterPageFile="~/masterpage/MasterPage.master" AutoEventWireup="true"
    CodeFile="nv_applylistqry.aspx.cs" Inherits="accountmt_nv_applylistqry" %>

<%@ Register Src="../usercontrol/GetCalender.ascx" TagName="GetCalender" TagPrefix="uc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="fullheader">
        <table width="100%" cellspacing="0" cellpadding="0" class="fullheaderinfo">
            <tr>
                <td valign="top">
                    <b>申請資料查詢</b>
                </td>
                <td align="right" valign="top" class="font_loginInfo">
                </td>
            </tr>
        </table>
    </div>
    <!--{* fullheader end *}-->
    <div class="fullcontent">
        <table border="0" cellspacing="0" cellpadding="0" width="100%" class="font_fullcontent">
            <tr>
                <td align="right">
                    公司名稱：
                </td>
                <td>
                    <asp:Label ID="lbl_compname" runat="server"></asp:Label>
                </td>
                <td align="right">
                    申請編號：
                </td>
                <td>
                    <asp:TextBox ID="txt_applyno" runat="server"></asp:TextBox>
                </td>
                <td align="right">
                    資料狀態：
                </td>
                <td>
                    <asp:DropDownList ID="ddl_applystatus" runat="server" RepeatDirection="Horizontal"
                        RepeatLayout="Flow">
                    </asp:DropDownList>
                </td>
            </tr>
            <tr>
                <td align="right">
                    引擎族：
                </td>
                <td>
                    <asp:TextBox ID="txt_enginefamily" runat="server"></asp:TextBox>
                </td>
                <td align="right">
                    廠牌：
                </td>
                <td>
                    <asp:TextBox ID="txt_factoryname" runat="server"></asp:TextBox>
                </td>
                <td align="right">
                    車型組代號：
                </td>
                <td>
                    <asp:TextBox ID="txt_carstyleno" runat="server" Width="75px"></asp:TextBox>
                </td>
            </tr>
            <tr>
                <td align="right">
                    交通工具種類：
                </td>
                <td>
                    <asp:DropDownList ID="ddl_cartype" runat="server" RepeatDirection="Horizontal" RepeatLayout="Flow"
                        AutoPostBack="True" OnSelectedIndexChanged="ddl_cartype_SelectedIndexChanged">
                    </asp:DropDownList>
                </td>
                <td align="right">
                    車型分類：
                </td>
                <td colspan="3">
                    <asp:DropDownList ID="ddl_transporttype" runat="server" RepeatDirection="Horizontal"
                        RepeatLayout="Flow">
                    </asp:DropDownList>
                </td>
            </tr>
            <tr>
                <td align="right">
                    適用期別：
                </td>
                <td colspan="5">
                    <asp:DropDownList ID="ddl_standarddate" runat="server" RepeatDirection="Horizontal"
                        RepeatLayout="Flow">
                    </asp:DropDownList>
                </td>
            </tr>
            <tr>
                <td align="right">
                    送件日期：
                </td>
                <td colspan="2">
                    <uc1:GetCalender ID="cal_applydate_S" runat="server" DateFormat="yyyyMMdd" />
                    ~<uc1:GetCalender ID="cal_applydate_E" runat="server" DateFormat="yyyyMMdd" />
                </td>
                <td align="right">
                    核發日期：
                </td>
                <td colspan="2">
                    <uc1:GetCalender ID="cal_validdate_S" runat="server" DateFormat="yyyyMMdd" />
                    ~<uc1:GetCalender ID="cal_validdate_E" runat="server" DateFormat="yyyyMMdd" />
                </td>
            </tr>
            <tr>
                <td align="right" colspan="6">
                    <asp:Button ID="btn_Search" runat="server" Text="查詢" CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'"
                        onmouseout="this.className='btn_mouseout'" OnClick="btn_Search_Click" />
                    &nbsp;&nbsp;<asp:Button ID="btn_AddApply" runat="server" Text="新申請" CssClass="btn_mouseout"
                        onmouseover="this.className='btn_mouseover'" onmouseout="this.className='btn_mouseout'"
                        OnClick="btn_AddApply_Click" />
                </td>
            </tr>
        </table>
        <div class="tableoutcome">
            <asp:GridView ID="gv_data" runat="server" AutoGenerateColumns="false" AllowPaging="True"
                PageSize="20" Width="100%" CellSpacing="0" CellPadding="0" BorderWidth="0" GridLines="None"
                CssClass="font_fullcontent" OnRowDataBound="gv_data_RowDataBound" EmptyDataText="查無資料"
                OnPageIndexChanging="gv_data_PageIndexChanging">
                <Columns>
                    <asp:TemplateField HeaderText="申請編號" HeaderStyle-Width="100px" ItemStyle-CssClass="lineleft"
                        HeaderStyle-CssClass="header" ItemStyle-HorizontalAlign="Center" HeaderStyle-HorizontalAlign="Center">
                        <ItemTemplate>
                            <asp:HyperLink ID="hlk_abd_applyno" CausesValidation="false" runat="server"></asp:HyperLink>
                        </ItemTemplate>
                        <HeaderStyle CssClass="header"></HeaderStyle>
                        <ItemStyle CssClass="lineleft"></ItemStyle>
                    </asp:TemplateField>
                    <asp:BoundField HeaderText="引擎族" DataField="abd_enginefamily" ItemStyle-CssClass="lineleft"
                        HeaderStyle-CssClass="header" HeaderStyle-Width="100px" />
                    <asp:TemplateField HeaderText="車型組清單" HeaderStyle-Width="100px" ItemStyle-CssClass="lineleft"
                        HeaderStyle-CssClass="header" ItemStyle-HorizontalAlign="Center" HeaderStyle-HorizontalAlign="Center">
                        <ItemTemplate>
                            <asp:HyperLink ID="hlk_carcompomodellist" CausesValidation="false" runat="server"></asp:HyperLink>
                        </ItemTemplate>
                        <HeaderStyle CssClass="header"></HeaderStyle>
                        <ItemStyle CssClass="lineleft"></ItemStyle>
                    </asp:TemplateField>
                    <asp:TemplateField HeaderText="適用期別" ItemStyle-CssClass="lineleft" HeaderStyle-CssClass="header"
                        ItemStyle-HorizontalAlign="Center" HeaderStyle-HorizontalAlign="Center" HeaderStyle-Width="80px">
                        <ItemTemplate>
                            <asp:Label ID="lbl_adaptstandarddate" runat="server"></asp:Label>
                        </ItemTemplate>
                    </asp:TemplateField>
                    <asp:BoundField HeaderText="引擎流水號" DataField="abd_enginesn" ItemStyle-CssClass="lineleft"
                        HeaderStyle-CssClass="header" HeaderStyle-Width="100px" ItemStyle-HorizontalAlign="Center"
                        HeaderStyle-HorizontalAlign="Center" />
                    <asp:TemplateField HeaderText="交通工具種類" ItemStyle-CssClass="lineleft" HeaderStyle-CssClass="header"
                        ItemStyle-HorizontalAlign="Center" HeaderStyle-HorizontalAlign="Center" HeaderStyle-Width="110px">
                        <ItemTemplate>
                            <asp:Label ID="lbl_vechiletype" runat="server"></asp:Label>
                        </ItemTemplate>
                    </asp:TemplateField>
                    <asp:TemplateField HeaderText="申請狀態" ItemStyle-CssClass="lineleft" HeaderStyle-CssClass="header"
                        ItemStyle-HorizontalAlign="Center" HeaderStyle-HorizontalAlign="Center" HeaderStyle-Width="80px">
                        <ItemTemplate>
                            <asp:Label ID="lbl_applystatus" runat="server"></asp:Label>
                        </ItemTemplate>
                        <HeaderStyle CssClass="lineright"></HeaderStyle>
                        <ItemStyle CssClass="lineright"></ItemStyle>
                    </asp:TemplateField>
                </Columns>
                <EmptyDataRowStyle ForeColor="Red" />
                <PagerStyle HorizontalAlign="Center" Font-Bold="true" CssClass="S_PageCss" />
            </asp:GridView>
        </div>
    </div>
    <!--{* fullcontent end *}-->
    <div class="fullfooter">
    </div>
    <!--{* content end *}-->
</asp:Content>
