﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Configuration;
using System.Data;
using System.Text;

public partial class Certwork_cw_doublevalidationprocess : BaseAdminPage
{
	protected void Page_Load(object sender, EventArgs e)
	{
		if (Session["account"] == null)
		{
			Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = '../accountmt/index.aspx';</script>"); return;
		}
		else
		{
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" && (GetAgentAccountGrpList().Contains("rv_adm") || GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("pg_adm")))
			{
				if (!IsPostBack)
				{
					InitData();
				}
			}
			else
			{
				Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
				Response.End();
				return;
			}
		}
	}

	private string ApplyNo
	{
		get
		{
			return Request["p"] == null ? string.Empty : DeCode(Request["p"].ToString());
		}
	}

	private void InitData()
	{
		ltr_HeaderData.Text = new FirstProcessControl().TagFirstProcess(ApplyNo);
		DataUtils.BindList(ddl_CarCompoModel, new FirstProcessControl().LisCarCompoModel(ApplyNo));
        hl_ValidationProcess.NavigateUrl = "cw_validationprocesslog.aspx?p=" + Server.UrlEncode(Request["p"]);
        hl_RePairProcess.NavigateUrl = "cw_resupplyprocesslog.aspx?p=" + Server.UrlEncode(Request["p"]);
	}

	//取得車型組資料
	protected void btn_GetCarCompoModelInfo_Click(object sender, EventArgs e)
	{
		hf_CarCompoModelNo.Value = ddl_CarCompoModel.SelectedValue;
		gv_data.DataSource = new FirstProcessControl().ListCarCompoModelInfo(ApplyNo, hf_CarCompoModelNo.Value);
		gv_data.DataBind();
	}

	private TableCell GetCell(string txt, HorizontalAlign HAlign)
	{
		TableCell tc = new TableCell();
		tc.Text = txt;
		tc.CssClass = "lineleft";
		tc.HorizontalAlign = HAlign;
		return tc;
	}

	private TableCell GetCell()
	{
		TableCell tc = new TableCell();
		tc.CssClass = "lineright";
		TextBox txt = new TextBox();
		txt.ID = "txt_Opinion";
		txt.TextMode = TextBoxMode.MultiLine;
		txt.Rows = 4;
		txt.Width = Unit.Pixel(450);
		tc.Controls.Add(txt);
		return tc;
	}

	private bool chkOpinion(string Opinion)
	{
		StringBuilder sb = new StringBuilder();
		if (string.IsNullOrEmpty(Opinion)) { sb.Append("複審意見未填寫！"); }
		if (sb.Length.Equals(0))
		{
			return true;
		}
		else
		{
			MessageBox.Show(sb.ToString());
			return false;
		}
	}

    //完成複審
    protected void btn_DoubleComplete_Click(object sender, EventArgs e)
    {
        //記錄複審意見
        int i = gv_data.Controls[0].Controls.Count - 1;
        string Opinion = ((TextBox)gv_data.Controls[0].Controls[i].FindControl("txt_Opinion")).Text;
        if (chkOpinion(Opinion))
        {
            new FirstProcessControl().ChangeApplyStatus(ApplyNo, "34");//二級主管核閱
            new FirstProcessControl().LogValidationProcess(ApplyNo, Opinion, "34");//Log
            MailChiefNotify();//SendMail
            RegisterStartupScript("script", @"
<script>alert('已完成複審！'); window.location='cw_doublevalidationqry.aspx';</script>
");
        }
    }

	private void MailChiefNotify()
	{
		DataView dv = new MailParaControl().GetApplyBaseData(ApplyNo);
		string CompName = dv[0]["CompName"].ToString();
		string EngineFamily = dv[0]["EngineFamily"].ToString();
		DataView GropuAccount = new MailParaControl().GetGroupAccount("04");
		for (int i = 0; i < GropuAccount.Count; i++)
		{
			string AccountName = GropuAccount[i]["AccountName"].ToString();
			string AccountMail = GropuAccount[i]["AccountMail"].ToString();
			string[] mailTo = new string[] { AccountMail };
            string[] mailCc = new string[1];//副本
            mailCc[0] = "";
            string[] mailBcc = new string[1];//密件
            mailBcc[0] = "";
			string Float = "二級主管";
			new SendMail().Mail_ChiefNotify(mailTo, mailCc, mailBcc, AccountName, ApplyNo, CompName, EngineFamily, Float);
		}
	}

    //退回初審
    protected void btn_BackToFirst_Click(object sender, EventArgs e)
    {
        //記錄複審意見
        int i = gv_data.Controls[0].Controls.Count - 1;
        string Opinion = ((TextBox)gv_data.Controls[0].Controls[i].FindControl("txt_Opinion")).Text;
        if (chkOpinion(Opinion))
        {
            new FirstProcessControl().ChangeApplyStatus(ApplyNo, "91");//複審核閱退回
            new FirstProcessControl().LogValidationProcess(ApplyNo, Opinion, "91");//Log			
            MailBackToFirst();//SendMail
            RegisterStartupScript("script", @"
<script>alert('已退回初審！'); window.location='cw_doublevalidationqry.aspx';</script>
");
        }
    }

	private void MailBackToFirst()
	{
		DataView dv = new MailParaControl().GetApplyBaseData(ApplyNo);
		string[] mailTo = new string[] { dv[0]["AccontMail"].ToString() };
        string[] mailCc = new string[1];//副本
        mailCc[0] = "";
        string[] mailBcc = new string[1];//密件
        mailBcc[0] = "";
		string AccountName = dv[0]["AccountName"].ToString();
		string CompName = dv[0]["CompName"].ToString();
		string EngineFamily = dv[0]["EngineFamily"].ToString();
		new SendMail().Mail_BackToFirst(mailTo, mailCc, mailBcc, AccountName, ApplyNo, CompName, EngineFamily);
	}

	private void BindOpinionRow()
	{
		if (!gv_data.Rows.Count.Equals(0))
		{
			int i = gv_data.Controls[0].Controls.Count - 1;
			GridViewRow gr = new GridViewRow(i, 0, DataControlRowType.DataRow, DataControlRowState.Normal);
			gr.Controls.Add(GetCell(i.ToString(), HorizontalAlign.Center));
			gr.Controls.Add(GetCell("審核意見", HorizontalAlign.Left));
			gr.Controls.Add(GetCell());
			gv_data.Controls[0].Controls.Add(gr);
		}
	}
	protected void gv_data_Load(object sender, EventArgs e)
	{
		BindOpinionRow();
	}
	protected void gv_data_DataBound(object sender, EventArgs e)
	{
		BindOpinionRow();
	}
}