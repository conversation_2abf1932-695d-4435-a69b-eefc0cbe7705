﻿<%@ Page Title="" Language="C#" MasterPageFile="~/masterpage/MasterPage.master" AutoEventWireup="true"
    CodeFile="carmodeldatamt_g.aspx.cs" Inherits="applydatamt_carmodeldatamt_g" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script type="text/javascript">
        function ismaxlength(obj) {
            var mlength = obj.getAttribute ? parseInt(obj.getAttribute("maxlength")) : "";
            if (obj.getAttribute && obj.value.length > mlength)
                obj.value = obj.value.substring(0, mlength);
        }
    </script>
    <style type="text/css">
        .style1
        {
            height: 23px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <asp:ScriptManager ID="ScriptManager1" runat="server">
    </asp:ScriptManager>
    <div class="fullheader">
        <table width="100%" cellspacing="0" cellpadding="0" class="fullheaderinfo">
            <tr>
                <td valign="top">
                    <b>
                        <asp:Label ID="Label1_Title" runat="server" Text="噪音合格證明申請-"></asp:Label>
                        <asp:Label ID="lbl_powerfrom" runat="server"></asp:Label>
                        <asp:Label ID="Label2_Title" runat="server" Text="車輛規格表資料填寫"></asp:Label>
                    </b>
                </td>
                <td align="right" class="font_loginInfo">
                </td>
            </tr>
        </table>
    </div>
    <div class="fullcontent">
        <div class="formstyle">
            <asp:Label ID="lbl_isimport" Visible="false" runat="server"></asp:Label>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="font_fullcontent">
                <tr>
                    <td align="right">
                        申請編號：
                    </td>
                    <td align="left">
                        <asp:Label ID="lbl_applyno" runat="server" Text="Label"></asp:Label>
                    </td>
                    <td align="right">
                        車型組代號：
                    </td>
                    <td align="left">
                        <asp:Label ID="lbl_carcompomodelno" runat="server"></asp:Label>
                        <asp:Label ID="lbl_applytype" runat="server" Visible="false"></asp:Label>
                        <asp:Label ID="lbl_carmodelno" runat="server" Visible="false"></asp:Label>
                        <asp:Label ID="lbl_copycarmodelno" runat="server" Visible="false"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        廠商名稱：
                    </td>
                    <td align="left">
                        <asp:Label ID="lbl_compname" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        車型年：
                    </td>
                    <td align="left">
                        <asp:Label ID="lbl_carstyleyear" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        代表車：
                    </td>
                    <td align="left" colspan="3">
                        <asp:CheckBox ID="cb_berepresentativevehicle" runat="server" />
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        製造地區：
                    </td>
                    <td align="left" colspan="3">
                        <asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional">
                            <ContentTemplate>
                                <asp:TextBox ID="tb_producercountry" runat="server" Width="500px"
                                    Height="50px" AutoPostBack="True" 
                                    OnTextChanged="tb_producercountry_TextChanged"></asp:TextBox>&nbsp;
                                <%-- <br />--%>
                                <span style="color: #A9A9A9;">請用<b>、</b>分隔資料 ex: 法國、德國、荷蘭</span>
                            </ContentTemplate>
                        </asp:UpdatePanel>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        基本引擎：
                    </td>
                    <td align="left">
                        <asp:TextBox ID="tb_baseenginename" runat="server" MaxLength="50" Width="250px"></asp:TextBox>
                    </td>
                    <td align="right">
                        複製污染車型：
                    </td>
                    <td align="left">
                        <%-- <asp:DropDownList ID="ddl_airpolcar" runat="server">
                        </asp:DropDownList>--%>
                        <asp:LinkButton ID="btn_airpolcar" runat="server" Text="複製" OnClick="btn_airpolcar_Click"
                            CausesValidation="true" ValidationGroup="airpolcarlist"></asp:LinkButton>&nbsp;
                        <asp:Label ID="lab_airpolcar" runat="server" Text="請先填寫車型名稱再按"></asp:Label>
                    </td>
                </tr>
                <tr runat="server" id="Tr1">
                    <td align="right">
                        複製代表車：
                    </td>
                    <td align="left" colspan="3">
                        <asp:DropDownList ID="ddl_carcompolist" runat="server">
                        </asp:DropDownList>
                        &nbsp;
                        <asp:LinkButton ID="btn_copycarcompomodel_data" runat="server" Text="複製" OnClick="btn_copycarcompomodel_data_Click"
                            CausesValidation="true" ValidationGroup="carcompolist"></asp:LinkButton>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        車型名稱：
                    </td>
                    <td align="left" colspan="3">
                        廠牌：<asp:DropDownList ID="ddl_brand" runat="server">
                        </asp:DropDownList>
                        <br />
                        中文車型名稱：<asp:TextBox ID="tb_carchnmodelname" runat="server" Width="498px" MaxLength="50"></asp:TextBox><br />
                        英文車型名稱：<asp:TextBox ID="tb_carengmodelname" runat="server" Width="498px" MaxLength="80"></asp:TextBox><br />
                        車型代碼：<asp:TextBox ID="tb_carmodelcode" runat="server" Width="524px" MaxLength="30"></asp:TextBox><br />
                        排氣量：<asp:Label ID="lbl_exhaust" runat="server"></asp:Label><br />
                        排檔方式：<asp:Label ID="lbl_transmissiontype" runat="server"></asp:Label><br />
                        排檔數：<asp:DropDownList ID="ddl_transmissionnum" runat="server" AutoPostBack="True"
                            OnSelectedIndexChanged="ddl_transmissionnum_SelectedIndexChanged">
                            <asp:ListItem></asp:ListItem>
                            <asp:ListItem>1</asp:ListItem>
                            <asp:ListItem>2</asp:ListItem>
                            <asp:ListItem>3</asp:ListItem>
                            <asp:ListItem>4</asp:ListItem>
                            <asp:ListItem>5</asp:ListItem>
                            <asp:ListItem>6</asp:ListItem>
                            <asp:ListItem>7</asp:ListItem>
                            <asp:ListItem>8</asp:ListItem>
                            <asp:ListItem>9</asp:ListItem>
                            <asp:ListItem>10</asp:ListItem>
                            <asp:ListItem>11</asp:ListItem>
                            <asp:ListItem>12</asp:ListItem>
                            <asp:ListItem>13</asp:ListItem>
                            <asp:ListItem>14</asp:ListItem>
                            <asp:ListItem>15</asp:ListItem>
                            <asp:ListItem>16</asp:ListItem>
                            <asp:ListItem>17</asp:ListItem>
                            <asp:ListItem>18</asp:ListItem>
                            <asp:ListItem>19</asp:ListItem>
                            <asp:ListItem>20</asp:ListItem>
                        </asp:DropDownList>
                        <br />
                        <div id="Gate_Count" runat="server">
                            <div>
                                門數：<asp:DropDownList ID="ddl_cardoorcount" runat="server">
                                </asp:DropDownList>
                            </div>
                        </div>
                        其他(車型名稱補充)：<asp:TextBox ID="tb_carmodeladd" runat="server" Width="300px" MaxLength="20"></asp:TextBox><br />
                        車種分類：<asp:DropDownList ID="ddl_cartype" runat="server">
                        </asp:DropDownList>
                        <div id="div_D_highlowgear" runat="server">
                            前進檔：<asp:TextBox ID="txt_forwardgearnums" runat="server" Width="50px" MaxLength="2"></asp:TextBox>&nbsp;&nbsp;
                            後退檔：<asp:TextBox ID="txt_backgearnums" runat="server" Width="50px" MaxLength="2"></asp:TextBox>&nbsp;&nbsp;
                            <asp:DropDownList ID="ddl_D_highlowgear" runat="server">
                            </asp:DropDownList>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        車身式樣：
                    </td>
                    <td align="left">
                        <asp:DropDownList ID="ddl_carbodyshape" runat="server">
                        </asp:DropDownList>
                        <asp:TextBox ID="tb_carbodyshapedesc" Style="display: none;" runat="server"></asp:TextBox>
                    </td>
                </tr>
                <tr>
                    <td align="left" colspan="4">
                        <strong>車身尺度與重量</strong>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        全長：
                    </td>
                    <td align="left">
                        <asp:TextBox ID="tb_carbodylenth" runat="server" MaxLength="15" Width="240px"></asp:TextBox>&nbsp;mm
                    </td>
                    <td align="right">
                        全寬：
                    </td>
                    <td align="left">
                        <asp:TextBox ID="tb_carbodywidth" runat="server" MaxLength="15" Width="190px"></asp:TextBox>&nbsp;mm
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        全高：
                    </td>
                    <td align="left">
                        <asp:TextBox ID="tb_carbodyhieght" runat="server" MaxLength="25" Width="240px"></asp:TextBox>&nbsp;mm
                    </td>
                    <td align="right">
                        軸距：
                    </td>
                    <td align="left">
                        <asp:TextBox ID="tb_carwheelbase" runat="server" MaxLength="25" Width="190px"></asp:TextBox>&nbsp;mm
                    </td>
                </tr>
                <tr>
                    <td id="Carwheelbase_f_title" runat="server" align="right">
                        輪距-前：
                    </td>
                    <td id="Carwheelbase_f_content" runat="server" align="left">
                        <asp:TextBox ID="tb_carwheelbase_f" runat="server" MaxLength="25" Width="240px"></asp:TextBox>&nbsp;mm
                    </td>
                    <td id="Carwheelbase_b_title" runat="server" align="right">
                        輪距-後：
                    </td>
                    <td id="Carwheelbase_b_content" runat="server" align="left">
                        <asp:TextBox ID="tb_carwheelbase_b" runat="server" MaxLength="25" Width="190px"></asp:TextBox>&nbsp;mm
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        空重：
                    </td>
                    <td align="left">
                        <asp:TextBox ID="tb_caremptyweight" runat="server" MaxLength="25" Width="240px" Height="22px"></asp:TextBox>&nbsp;kg
                    </td>
                    <td align="right" id="nonD_Cartotal_weight_title" runat="server">
                        總重：
                    </td>
                    <td align="left" id="nonD_Cartotal_weight_content" runat="server">
                        <asp:TextBox ID="tb_cartotalweight" runat="server" MaxLength="25" Width="190px"></asp:TextBox>&nbsp;kg
                    </td>
                </tr>
                <tr>
                    <td align="right" id="nonD_Passengers_title" runat="server">
                        乘坐人數：
                    </td>
                    <td align="left" id="nonD_Passengers_content" runat="server">
                        <asp:TextBox ID="tb_passengers" runat="server" Width="240px" MaxLength="25"></asp:TextBox>&nbsp;人
                    </td>
                    <td id="Load_weight_title" runat="server" align="right">
                        載重：
                    </td>
                    <td id="Load_weight_content" runat="server" align="left">
                        <asp:TextBox ID="Load_weight_TextBox1" runat="server" MaxLength="9" Width="190px"></asp:TextBox>&nbsp;kg
                    </td>
                </tr>
                <tr id="D_CarbodyWeight_title" runat="server">
                    <td align="right">
                        車體總重量：
                    </td>
                    <td runat="server" colspan="3">
                        <asp:TextBox ID="txt_carbodyweight" runat="server" Width="240px" MaxLength="6"></asp:TextBox>&nbsp;kg
                    </td>
                </tr>
                <tr>
                    <td align="left" colspan="4">
                        <strong>基本引擎</strong>
                    </td>
                </tr>
                <tr>
                    <td id="Installation_location_title" runat="server" align="right">
                        安裝位置：
                    </td>
                    <td id="Installation_location_content" runat="server" align="left">
                        <asp:Label ID="lbl_setuppos" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                    </td>
                    <td align="left">
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        總排氣量：
                    </td>
                    <td align="left">
                        <asp:Label ID="lbl_exhaust2" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        <asp:Label ID="Label1_Bore_and_Inner_diameter01" runat="server" Text="Label"></asp:Label>X<asp:Label
                            ID="Stroke_stroke_Label1_title" runat="server"></asp:Label>：
                    </td>
                    <td id="Stroke_stroke_Label1_content" runat="server" align="left">
                        <asp:TextBox ID="tb_cylindermeters" runat="server" Width="79px" MaxLength="9"></asp:TextBox>X<asp:TextBox
                            ID="tb_cylinderstroke" runat="server" Width="84px" MaxLength="9"></asp:TextBox>&nbsp;mm
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        壓縮比：
                    </td>
                    <td align="left">
                        <asp:TextBox ID="tb_compressionratio" runat="server" MaxLength="20" Width="240px"></asp:TextBox>
                    </td>
                    <td align="right">
                        <asp:Label ID="lbl_colheader_cylinder" runat="server" Text="汽缸數："></asp:Label>
                    </td>
                    <td align="left">
                        <asp:Label ID="lbl_cylindernums" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        最大馬力：
                    </td>
                    <td align="left">
                        <asp:TextBox ID="tb_maxhorsepower" runat="server" MaxLength="20" Width="240px"></asp:TextBox>
                    &nbsp;kW
                    <td align="right">
                        最大馬力轉速：
                    </td>
                    <td align="left">
                        <asp:TextBox ID="tb_maxHorsepowerspeed" runat="server" MaxLength="20" Width="200px"></asp:TextBox>&nbsp;rpm
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        最大扭力：
                    </td>
                    <td align="left">
                        <asp:TextBox ID="tb_torque" runat="server" MaxLength="20" Width="240px"></asp:TextBox>&nbsp;kg-m
                    </td>
                    <td align="right">
                        最大扭力轉速：
                    </td>
                    <td align="left">
                        <asp:TextBox ID="tb_torquespeed" runat="server" MaxLength="20" Width="200px"></asp:TextBox>&nbsp;rpm
                    </td>
                </tr>
                <tr id="D_maxhorsepower_Title" runat="server">
                    <td align="right">
                        馬達最大馬力：<br />
                        (僅供HYBID填寫)
                    </td>
                    <td align="left">
                        <asp:TextBox ID="txt_maxhorsepower_hb" runat="server" MaxLength="20" Width="240px"></asp:TextBox>&nbsp;kW
                    </td>
                    <td align="right">
                        馬達最大馬力轉速：<br />
                        (僅供HYBID填寫)
                    </td>
                    <td align="left">
                        <asp:TextBox ID="txt_maxhorsepowerspeed_hb" runat="server" MaxLength="20" Width="200px"></asp:TextBox>&nbsp;rpm
                    </td>
                </tr>
                <tr id="D_torque_Title" runat="server">
                    <td align="right">
                        馬達最大扭力：<br />
                        (僅供HYBID填寫)
                    </td>
                    <td align="left">
                        <asp:TextBox ID="txt_torque_hb" runat="server" MaxLength="20" Width="240px"></asp:TextBox>&nbsp;kg-m
                    </td>
                    <td align="right">
                        馬達最大扭力轉速：<br />
                        (僅供HYBID填寫)
                    </td>
                    <td align="left">
                        <asp:TextBox ID="txt_torquespeed_hb" runat="server" MaxLength="20" Width="200px"></asp:TextBox>&nbsp;rpm
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        <asp:Label ID="lbl_haveturbo" runat="server" Text="增壓器："></asp:Label>
                    </td>
                    <td align="left">
                        <asp:DropDownList ID="ddl_haveturbo" runat="server">
                        </asp:DropDownList>
                        <asp:Label ID="lbl_powerexportway" Style="display: none;" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        冷卻系統：
                    </td>
                    <td align="left">
                        <asp:Label ID="lbl_coolsystem" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        供油方式：
                    </td>
                    <td align="left">
                        <asp:Label ID="lbl_provideoilmethod" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        油箱容量：
                    </td>
                    <td align="left">
                        <asp:TextBox ID="tb_oilcapacity" runat="server" MaxLength="25" Width="200px"></asp:TextBox>&nbsp;公升
                    </td>
                </tr>
                <tr id="use_fuel_01" runat="server">
                    <td align="right">
                        使用燃料：
                    </td>
                    <td align="left">
                        <%--<asp:DropDownList ID="DropDownList1_use_fuel" runat="server">
                        </asp:DropDownList>--%>
                        <%--<asp:Label ID="lbl_use_fuel" Style="display: none;" runat="server"></asp:Label> --%>
                        <asp:Label ID="lbl_use_fuel" runat="server"> </asp:Label>
                    </td>
                    <td align="right">
                        安裝位置及方式：
                    </td>
                    <td align="left">
                        <asp:Label ID="lbl_Installation_location_manner" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right" id="Automotive_fuel_title" runat="server">
                        油料：
                    </td>
                    <td id="Automotive_fuel_title_content" runat="server" align="left">
                        <asp:TextBox ID="tb_929598" runat="server" Width="230px" MaxLength="20"></asp:TextBox>
                        &nbsp;辛烷值
                    </td>
                    <td id="Car_driven_approach_title" runat="server" align="right">
                        驅動方式：
                    </td>
                    <td id="Car_driven_approach_Dr" runat="server" align="left">
                        <asp:RadioButtonList ID="rbl_drivetype" runat="server" RepeatDirection="Horizontal"
                            RepeatLayout="Flow" RepeatColumns="3">
                        </asp:RadioButtonList>
                        <asp:RequiredFieldValidator ID="rfv_drivetype" runat="server" ControlToValidate="rbl_drivetype"
                            ErrorMessage="驅動方式必須挑選"></asp:RequiredFieldValidator>
                    </td>
                </tr>
                <tr id="D_numofaxes_Title" runat="server">
                    <td align="right">
                        前軸軸數：
                    </td>
                    <td align="left">
                        <asp:RadioButtonList ID="rbl_numofaxes_f" runat="server" RepeatLayout="Flow" RepeatDirection="Horizontal">
                        </asp:RadioButtonList>
                    </td>
                    <td align="right">
                        後軸軸數：
                    </td>
                    <td>
                        <asp:RadioButtonList ID="rbl_numofaxes_b" runat="server" RepeatLayout="Flow" RepeatDirection="Horizontal">
                        </asp:RadioButtonList>
                    </td>
                </tr>
                <tr>
                    <td align="right" id="Start_up_mode_title" runat="server">
                        啟動方式：
                    </td>
                    <td id="Start_up_mode_content" runat="server" align="left">
                        <asp:TextBox ID="Start_up_mode_TextBox2" runat="server" MaxLength="20" Width="240px"></asp:TextBox>
                    </td>
                    <td id="The_location_and_direction_of_the_exhaust_port_title" runat="server" align="right">
                        排氣口位置及方向：
                    </td>
                    <td id="The_location_and_direction_of_the_exhaust_port_content" runat="server" align="left">
                        <asp:TextBox ID="The_location_and_direction_of_the_exhaust_port_TextBox1" runat="server"
                            MaxLength="50" Width="240px"></asp:TextBox>
                    </td>
                </tr>
                <tr id="Concentration_of_emissions" runat="server">
                    <td align="right">
                        排放廢氣濃度：
                    </td>
                    <td align="left" colspan="3">
                        <table>
                            <tr>
                                <td>
                                    粒狀汙染物：
                                </td>
                                <td>
                                    <asp:TextBox ID="tb_pm" runat="server" Width="150px" MaxLength="100"></asp:TextBox>
                                </td>
                                <td>
                                    一氧化碳：
                                </td>
                                <td>
                                    <asp:TextBox ID="tb_co" runat="server" Width="150px" MaxLength="100"></asp:TextBox>
                                </td>
                                <td>
                                    碳氫化合物：
                                </td>
                                <td>
                                    <asp:TextBox ID="tb_hc" runat="server" Width="150px" MaxLength="100"></asp:TextBox>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr id="PollutionPreventionControlSystem" runat="server">
                    <td align="left" colspan="4" class="style1">
                        <strong id="System_title" runat="server">防治污染系統</strong>
                    </td>
                </tr>
                <tr>
                    <td id="Automotive_Exhaust_System_title" runat="server" align="right">
                        排氣系統：
                    </td>
                    <td id="Automotive_Exhaust_System_content" runat="server" align="left">
                        <asp:TextBox ID="tb_exhaustsystem" runat="server" MaxLength="100" Width="240px"></asp:TextBox>
                    </td>
                    <td id="PCV_title" runat="server" align="right">
                        PCV：
                    </td>
                    <td id="PCV_content" runat="server" align="left">
                        <asp:TextBox ID="tb_pcv" runat="server" MaxLength="100" Width="240px"></asp:TextBox>
                    </td>
                </tr>
                <tr>
                    <td id="EEC_title" runat="server" align="right">
                        EEC：
                    </td>
                    <td id="EEC_content" runat="server" align="left">
                        <asp:TextBox ID="tb_eec" runat="server" MaxLength="100" Width="240px"></asp:TextBox>
                    </td>
                    <td align="right">
                        &nbsp;
                    </td>
                    <td align="left">
                    </td>
                </tr>
                <tr>
                    <td align="left" colspan="4">
                        <strong>傳動系統</strong>
                    </td>
                </tr>
                <tr id="Reduction_ratio" runat="server">
                    <td align="right">
                        一次減速比：
                    </td>
                    <td align="left">
                        <asp:TextBox ID="Primary_reduction_ratio_TextBox3" runat="server" MaxLength="20"
                            Width="240px"></asp:TextBox>
                    </td>
                    <td align="right">
                        二次減速比：
                    </td>
                    <td align="left">
                        <asp:TextBox ID="Secondary_reduction_ratio_TextBox4" runat="server" MaxLength="20"
                            Width="240px"></asp:TextBox>
                    </td>
                </tr>
                <tr>
                    <td id="Automobile_differential_type_title" runat="server" align="right">
                        差速器-型式：
                    </td>
                    <td id="Automobile_differential_type_content" runat="server" align="left">
                        <asp:TextBox ID="tb_differentialmodel" runat="server" MaxLength="50" Width="240px"></asp:TextBox>
                    </td>
                    <td id="Automobile_differential_gear_ratio_title" runat="server" align="right">
                        差速器-齒比：
                    </td>
                    <td id="Automobile_differential_gear_ratio_content" runat="server" align="left">
                        <asp:TextBox ID="tb_differentialgearratio" runat="server" MaxLength="50" Width="240px"></asp:TextBox>
                    </td>
                </tr>
                <tr id="D_increasepower_title" runat="server">
                    <td align="right">
                        加力箱-型式：
                    </td>
                    <td align="left">
                        <asp:TextBox ID="txt_increasepowerstyle" runat="server" MaxLength="50" Width="240px"></asp:TextBox>
                    </td>
                    <td align="right">
                        加力箱-齒比：
                    </td>
                    <td align="left">
                        <asp:TextBox ID="txt_increasepowerratio" runat="server" MaxLength="20" Width="240px"></asp:TextBox>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        變速系統~型式：
                    </td>
                    <td align="left">
                        <asp:TextBox ID="tb_transmissionmodel" runat="server" MaxLength="50" Width="240px"></asp:TextBox>
                    </td>
                    <td align="right">
                        最高車速：
                    </td>
                    <td align="left">
                        <asp:TextBox ID="tb_highestspeed" runat="server" MaxLength="25" Width="190px"></asp:TextBox>&nbsp;km/hr
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        變速系統~各檔齒比：
                    </td>
                    <td align="left">
                        <div id="Variable_speed_system" runat="server">
                        </div>
                    </td>
                    <td id="Transmission_system_reverse_gear_ratio_title" runat="server" align="right">
                        變速系統~倒檔齒比：
                    </td>
                    <td id="Transmission_system_reverse_gear_ratio_content" runat="server" align="left">
                        <asp:TextBox ID="tb_transmissionbackgearratio" runat="server" MaxLength="20" Width="240px"></asp:TextBox>
                    </td>
                </tr>
                <tr id="D_climbratio_title" runat="server">
                    <td id="Td1" runat="server" align="right">
                        爬坡檔：
                    </td>
                    <td id="Td2" runat="server" align="left" colspan="4">
                        <asp:TextBox ID="txt_climbratio" runat="server" MaxLength="50" Width="240px"></asp:TextBox>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        懸吊系統：
                    </td>
                    <td align="left" colspan="3">
                        前：<asp:TextBox ID="tb_suspensionsystem_f" runat="server" MaxLength="50" Width="240px"></asp:TextBox>
                        後：<asp:TextBox ID="tb_suspensionsystem_b" runat="server" MaxLength="50" Width="240px"></asp:TextBox>
                        <asp:Label ID="lbl_suspensionsystem_sub" runat="server" Text="補："></asp:Label><asp:TextBox
                            ID="txt_suspensionsystem_sub" runat="server" MaxLength="50" Width="200px"></asp:TextBox>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        輪胎規格：
                    </td>
                    <td align="left" colspan="3">
                        標配~前：<asp:TextBox ID="tb_tirespec_std_f" runat="server" MaxLength="20" Width="240px"></asp:TextBox>&nbsp;後：<asp:TextBox
                            ID="tb_tirespec_std_b" runat="server" MaxLength="20" Width="240px"></asp:TextBox><br />
                        選配~前：<asp:TextBox ID="tb_tirespec_cho_f" runat="server" MaxLength="100" Width="240px"></asp:TextBox>&nbsp;後：<asp:TextBox
                            ID="tb_tirespec_cho_b" runat="server" MaxLength="100" Width="240px"></asp:TextBox>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        受驗車重（kg）：
                    </td>
                    <td align="left">
                        <asp:Label ID="lbl_testweight" runat="server"></asp:Label><asp:Label ID="lbl_testweightformula"
                            runat="server" Visible="false"></asp:Label>
                    </td>
                    <td align="right">
                        ★測試檔位總減速比：
                    </td>
                    <td align="left">
                        <asp:TextBox ID="tb_testgearreductionratio" runat="server" MaxLength="20" Width="240px"></asp:TextBox>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        冷卻風扇驅動方式：
                    </td>
                    <td align="left">
                        <asp:TextBox ID="tb_coolingdrivermethod" runat="server" MaxLength="20" Width="240px"></asp:TextBox>
                    </td>
                    <td align="right">
                        輪胎數量（不含備胎）：
                    </td>
                    <td align="left">
                        <asp:TextBox ID="tb_tiresnum" runat="server" MaxLength="20" Width="240px"></asp:TextBox>
                        <asp:RegularExpressionValidator ID="rev_tiresnum" runat="server" ErrorMessage="請輸入整數"
                            ValidationExpression="^[\d]+$" ControlToValidate="tb_tiresnum"></asp:RegularExpressionValidator>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        排氣管開口數量：
                    </td>
                    <td align="left">
                        <asp:TextBox ID="tb_exhaustpipenum" runat="server" Width="240px"></asp:TextBox>
                    </td>
                    <td align="right">
                        輪胎寬度：
                    </td>
                    <td align="left">
                        <asp:TextBox ID="tb_tirewidth" runat="server" MaxLength="20" Width="240px"></asp:TextBox>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        進氣方式：
                    </td>
                    <td align="left">
                        <asp:DropDownList ID="ddl_inletmode" runat="server">
                        </asp:DropDownList>
                    </td>
                    <td align="right">
                    </td>
                    <td align="left">
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        備註：
                    </td>
                    <td align="left" colspan="3">
                        <asp:TextBox ID="txt_remark" runat="server" TextMode="MultiLine" Width="780px" Height="120px"></asp:TextBox>
                    </td>
                </tr>
                <tr>
                    <td align="right" width="150">
                        資料最後修改人：
                    </td>
                    <td align="left">
                        <asp:Label ID="last_modaccount" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        資料最後修改日期：
                    </td>
                    <td align="left" width="150">
                        <asp:Label ID="last_moddate" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="center" colspan="4">
                        <asp:Button ID="Button1" runat="server" Text="存檔" CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'"
                            onmouseout="this.className='btn_mouseout'" OnClick="Button1_Click" />
                    </td>
                </tr>
            </table>
        </div>
    </div>
    <div id="Regist_Select_div">
        <input runat="server" type="hidden" id="Regist_Select" />
    </div>
    <asp:ValidationSummary ID="ValidationSummary1" runat="server" ShowMessageBox="True"
        ShowSummary="False" />
    <asp:HiddenField ID="hidden_flag" runat="server" />
    <script type="text/javascript">
        (function () { Users_Enter_value(); })();

        function return_Server_ID() {

            var Regist_Select_div = document.getElementById('Regist_Select_div');
            var ClientID = Regist_Select_div.getElementsByTagName('input')[0].value;

            return ClientID;
        }

        function Users_Enter_value() {

            var id = return_Server_ID();

            var DoropDown = document.getElementById(id);

            DoropDown.onchange = function () {
                var parent = this.parentNode;
                var input = parent.getElementsByTagName('input')[0];



                var n = this.options.length - 1;
                var currSelectIndex = this.selectedIndex;


                if (n == currSelectIndex) {
                    input.style.display = 'inline';
                } else { input.style.display = 'none'; }
            }
        }
    
    </script>
</asp:Content>
