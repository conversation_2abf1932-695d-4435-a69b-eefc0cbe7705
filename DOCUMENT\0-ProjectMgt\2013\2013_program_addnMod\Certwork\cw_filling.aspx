﻿<%@ Page Title="" Language="C#" MasterPageFile="~/masterpage/MasterPage.master" AutoEventWireup="true"
    CodeFile="cw_filling.aspx.cs" Inherits="Certwork_cw_filling" %>

<%@ Register Src="../usercontrol/GetCalender.ascx" TagName="GetCalender" TagPrefix="uc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="fullheader">
        <table width="100%" cellspacing="0" cellpadding="0" class="fullheaderinfo">
            <tr>
                <td valign="top">
                    <b>
                        <asp:Label ID="lab_filling" runat="server"></asp:Label></b>
                </td>
                <td align="right" valign="top" class="font_loginInfo">
                </td>
            </tr>
        </table>
    </div>
    <!--{* fullheader end *}-->
    <div class="fullcontent">
        <div class="formstyle">
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="font_fullcontent">
                <tr>
                    <td align="right">
                        申請廠商：
                    </td>
                    <td>
                        <asp:Label ID="lbl_CompanyName" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        申請編號：
                    </td>
                    <td>
                        <asp:Label ID="lbl_ApplyNo" runat="server"></asp:Label>
                        <asp:HiddenField ID="hf_YYYY" runat="server" />
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        核發人員：
                    </td>
                    <td>
                        <asp:Label ID="lbl_Account" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        核發時間：
                    </td>
                    <td>
                        <asp:Label ID="lbl_IssueDate" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        收據號碼：
                    </td>
                    <td colspan="3">
                        <asp:TextBox ID="txt_receiptno" runat="server"></asp:TextBox>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        合格證號：
                    </td>
                    <td>
                        <asp:TextBox ID="txt_certdocno" runat="server"></asp:TextBox>
                    </td>
                    <td align="right">
                        合格核發日期：
                    </td>
                    <td>
                  <%--      <asp:Label ID="lbl_certdocissuedate" runat="server"></asp:Label>--%>
                       <uc1:GetCalender ID="cal_certdocissuedate" runat="server" 
                            DateFormat="yyyy/MM/dd" />
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        上傳合格證：
                    </td>
                    <td>
                        <asp:FileUpload ID="fi_Cretificated" runat="server" CssClass="inputType" />
                        <br />
                        <asp:LinkButton ID="lbt_Cretificated" runat="server" OnClick="lbt_Download_Click"></asp:LinkButton>
                    </td>
                    <td align="right">
                        上傳收據：
                    </td>
                    <td>
                        <asp:FileUpload ID="fi_Receipt" runat="server" CssClass="inputType" />
                        <br />
                        <asp:LinkButton ID="lbt_Receipt" runat="server" OnClick="lbt_Download_Click"></asp:LinkButton>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        兩證合一申請方式：
                    </td>
                    <td>
                        <asp:RadioButtonList ID="rdBtnLst_twocerttype" runat="server" RepeatDirection="Horizontal">
                        </asp:RadioButtonList>
                    </td>
                    <td align="right">
                        兩證合一電子檔：
                    </td>
                    <td>
                        <asp:LinkButton ID="lbtn_certificated" runat="server" OnClick="lbtn_certificated_Click">合格證下載</asp:LinkButton>
                    </td>
                </tr>
                <tr>
                    <td colspan="4" align="center">
                        <asp:Button ID="btn_Filling" runat="server" Text="歸檔" OnClick="btn_Filling_Click"
                            CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'" onmouseout="this.className='btn_mouseout'"
                            Visible="False" />
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <asp:Button ID="btn_ReSent" runat="server" Text="重傳" OnClick="btn_btn_ReSent_Click"
                            CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'" onmouseout="this.className='btn_mouseout'" />
                    </td>
                </tr>
            </table>
        </div>
    </div>
    <!--{* fullcontent end *}-->
    <div class="fullfooter">
    </div>
    <!--{* content end *}-->
</asp:Content>
