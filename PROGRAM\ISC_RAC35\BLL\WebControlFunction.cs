﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace ISC_RAC.BLL
{
    /// <summary>
    /// 與網頁元件有關的程式
    /// </summary>
    public class WebControlFunction
    {
        #region 在一個TreeNodeCollection中，尋找具有指定Value的一個TreeNode。
        
        /// <summary>
        /// 在一個TreeNodeCollection中，尋找具有指定Value的一個TreeNode。
        /// </summary>
        /// <param name="AllNodes"></param>
        /// <param name="strValue"></param>
        /// <returns></returns>
        internal static System.Web.UI.WebControls.TreeNode GetTreeNodeByValue(System.Web.UI.WebControls.TreeNodeCollection AllNodes, string strValue)
        {
            System.Web.UI.WebControls.TreeNode GetNode = null;

            for (int intIndex = 0; intIndex < AllNodes.Count; intIndex++)
            {
                if (AllNodes[intIndex].Value == strValue)
                {
                    return AllNodes[intIndex];
                }
                else
                {
                    if (AllNodes[intIndex].ChildNodes.Count > 0)
                    {
                        GetNode = GetTreeNodeByValue(AllNodes[intIndex].ChildNodes, strValue);
                        if (GetNode != null)
                            return GetNode;
                    }
                }
            }

            return GetNode;
        }
        
        #endregion

        #region 將FunctionList裡的所有項目放入到TreeView裡。

        /// <summary>
        /// 將FunctionList裡的所有項目放入到TreeView裡。
        /// </summary>
        /// <param name="trvThis"></param>
        /// <param name="liFunctionList"></param>
        /// <param name="bIncludeNavigation"></param>
        internal static void PutFunctionListIntoTreeView(System.Web.UI.WebControls.TreeView trvThis, List<ISC_RAC.Model.RowItem_FunctionList> liFunctionList, bool bIncludeNavigation, bool bShowUID)
        {
            trvThis.Nodes.Clear();

            List<ISC_RAC.Model.RowItem_FunctionList> liRootFunctionList = (
                from SearchItem in liFunctionList
                where string.IsNullOrEmpty(SearchItem.ParentUID) == true
                select SearchItem).ToList<ISC_RAC.Model.RowItem_FunctionList>();

            for (int intIndex = 0; intIndex < liRootFunctionList.Count; intIndex++)
            {
                ISC_RAC.Model.RowItem_FunctionList thisFunctionItem = liRootFunctionList[intIndex];
                PutThisFunctionItemIntoTreeView(trvThis.Nodes, liFunctionList, thisFunctionItem, bIncludeNavigation, bShowUID);
            }
        }

        protected static void PutThisFunctionItemIntoTreeView(System.Web.UI.WebControls.TreeNodeCollection NodesPool, List<ISC_RAC.Model.RowItem_FunctionList> liAllFunctionList, ISC_RAC.Model.RowItem_FunctionList thisFunctionItem, bool bIncludeNavigation, bool bShowUID)
        {
            //產生Node
            System.Web.UI.WebControls.TreeNode thisNode = new System.Web.UI.WebControls.TreeNode();
            thisNode.Text = ISC_RAC.BLL.DatabaseFunction.GetFunctionShowName(thisFunctionItem, false, bShowUID);
            thisNode.Value = thisFunctionItem.UID;
            thisNode.Expanded = true;
            if (bIncludeNavigation == true)
            {
                if (string.IsNullOrEmpty(thisFunctionItem.NavigateURL) == false)
                {
                    thisNode.NavigateUrl = thisFunctionItem.NavigateURL;
                    if (ISC_RAC.BLL.SystemFunction.IsThisTypeInNavigateTargetTypeList(thisFunctionItem.NavigateTarget) == false)
                        thisFunctionItem.NavigateTarget = ISC_RAC.Model.NavigateTargetType.Self;
                    thisNode.Target = thisFunctionItem.NavigateTarget;
                }
            }

            //加到Tree裡
            NodesPool.Add(thisNode);

            //加入子節點
            List<ISC_RAC.Model.RowItem_FunctionList> liChildFunctionList = (
               from SearchItem in liAllFunctionList
               where SearchItem.ParentUID == thisFunctionItem.UID
               select SearchItem).ToList<ISC_RAC.Model.RowItem_FunctionList>();

            for (int intIndex = 0; intIndex < liChildFunctionList.Count; intIndex++)
            {
                ISC_RAC.Model.RowItem_FunctionList ChildFunctionItem = liChildFunctionList[intIndex];
                PutThisFunctionItemIntoTreeView(thisNode.ChildNodes, liAllFunctionList, ChildFunctionItem, bIncludeNavigation, bShowUID);
            }
        }

        #endregion

        #region 在網頁上顯示alert訊息

        /*
        /// <summary>
        /// 在網頁上顯示alert訊息
        /// </summary>
        /// <param name="thisPage"></param>
        /// <param name="strMessage"></param>
        /// <param name="strScriptKey"></param>
        public static void RegisterAlertScriptInPage(System.Web.UI.Page thisPage, string strMessage, string strScriptKey)
        {
            strMessage.Replace("\r", "\\r").Replace("\n", "\\n");
            string strScript = "alert('" + strMessage + "');";

            thisPage.ClientScript.RegisterClientScriptBlock(thisPage.GetType(), strScriptKey, strScript, true);
        }//*/

        public static void RegisterAjaxAlertScriptInPage(System.Web.UI.Control thisControl, string strMessage, string strScriptKey)
        {
            strMessage.Replace("\r", "\\r").Replace("\n", "\\n");
            string strScript = "alert('" + strMessage + "');";

            System.Web.UI.ScriptManager.RegisterStartupScript(thisControl, thisControl.GetType(), strScriptKey, strScript, true);
        }

        #endregion

        #region 將一個ControlCollection裡的所有Button，包含所有內部皆層，設定其Visible=False。

        /// <summary>
        /// 將一個ControlCollection裡的所有Button，包含所有內部皆層，設定其Visible=False。
        /// </summary>
        /// <param name="ControlList"></param>
        internal static void SetAllButtonInvisible(System.Web.UI.ControlCollection ControlList)
        {
            foreach (System.Web.UI.Control thisControl in ControlList)
            {
                if (thisControl.GetType() == typeof(System.Web.UI.WebControls.Button))
                    thisControl.Visible = false;
                SetAllButtonInvisible(thisControl.Controls);
            }
        }

        #endregion

        #region 將FunctionList裡的所有項目放入到Menu裡。

        internal static void PutFunctionListIntoMenu(System.Web.UI.WebControls.Menu MenuPool, List<ISC_RAC.Model.RowItem_FunctionList> liAllFunctionList, List<ISC_RAC.Model.RowItem_RolePermissionOfFunction> liAccessRightList, List<string> liRoleUIDListOfUser)
        {
            MenuPool.Items.Clear();

            List<ISC_RAC.Model.RowItem_FunctionList> liRootFunctionList = (
                from SearchItem in liAllFunctionList
                where string.IsNullOrEmpty(SearchItem.ParentUID) == true
                select SearchItem).ToList<ISC_RAC.Model.RowItem_FunctionList>();

            for (int intIndex = 0; intIndex < liRootFunctionList.Count; intIndex++)
            {
                ISC_RAC.Model.RowItem_FunctionList thisFunctionItem = liRootFunctionList[intIndex];
                PutThisFunctionItemIntoMenu(MenuPool.Items, liAllFunctionList, thisFunctionItem, liAccessRightList, liRoleUIDListOfUser);
            }
        }

        internal static void PutThisFunctionItemIntoMenu(System.Web.UI.WebControls.MenuItemCollection MenuPool, List<ISC_RAC.Model.RowItem_FunctionList> liAllFunctionList, ISC_RAC.Model.RowItem_FunctionList thisFunctionItem, List<ISC_RAC.Model.RowItem_RolePermissionOfFunction> liAccessRightList, List<string> liRoleUIDListOfUser)
        {   
            //要顯示才加入
            if (thisFunctionItem.IsShowOnMenu == true && thisFunctionItem.Active=="Y")
            {
                //有權權限，才加入
                List<string> liAccessRight = (
                    from AccessRightItem in liAccessRightList
                    join UserRoleItem in liRoleUIDListOfUser
                    on AccessRightItem.RoleUID equals UserRoleItem
                    where AccessRightItem.FunctionUID == thisFunctionItem.UID
                    select AccessRightItem.FunctionUID
                    ).ToList<string>();
                if (liAccessRight.Count > 0)
                {
                    //產生Node
                    System.Web.UI.WebControls.MenuItem thisMenu = new System.Web.UI.WebControls.MenuItem();
                    thisMenu.Text = ISC_RAC.BLL.DatabaseFunction.GetFunctionShowName(thisFunctionItem, true, false);
                    thisMenu.Value = thisFunctionItem.UID;
                    thisMenu.NavigateUrl = thisFunctionItem.NavigateURL;
                    if (ISC_RAC.BLL.SystemFunction.IsThisTypeInNavigateTargetTypeList(thisFunctionItem.NavigateTarget) == false)
                        thisFunctionItem.NavigateTarget = ISC_RAC.Model.NavigateTargetType.Self;
                    thisMenu.Target = thisFunctionItem.NavigateTarget;

                    //加到Menu裡
                    MenuPool.Add(thisMenu);

                    //加入子選單
                    List<ISC_RAC.Model.RowItem_FunctionList> liChildFunctionList = (
                        from SearchItem in liAllFunctionList
                        where SearchItem.ParentUID == thisFunctionItem.UID
                        select SearchItem).ToList<ISC_RAC.Model.RowItem_FunctionList>();

                    for (int intIndex = 0; intIndex < liChildFunctionList.Count; intIndex++)
                    {
                        ISC_RAC.Model.RowItem_FunctionList ChildFunctionItem = liChildFunctionList[intIndex];
                        PutThisFunctionItemIntoMenu(thisMenu.ChildItems, liAllFunctionList, ChildFunctionItem, liAccessRightList, liRoleUIDListOfUser);
                    }
                }
            }
        }        

        #endregion

        #region 將一個ControlCollection裡的所有Input元件，包含所有內部皆層，鎖定不能編輯。

        /// <summary>
        /// 將一個ControlCollection裡的所有Input元件，包含所有內部皆層，鎖定不能編輯。
        /// </summary>
        /// <param name="ControlList"></param>
        internal static void LockAllInputControls(System.Web.UI.ControlCollection ControlList)
        {
            foreach (System.Web.UI.Control thisControl in ControlList)
            {
                if (thisControl.GetType() == typeof(System.Web.UI.WebControls.Button))
                    thisControl.Visible = false;

                if (thisControl.GetType() == typeof(System.Web.UI.WebControls.TextBox))
                    ((System.Web.UI.WebControls.TextBox)thisControl).Enabled = false;

                if (thisControl.GetType() == typeof(System.Web.UI.WebControls.RadioButtonList))
                    ((System.Web.UI.WebControls.RadioButtonList)thisControl).Enabled = false;

                if (thisControl.GetType() == typeof(System.Web.UI.WebControls.DropDownList))
                    ((System.Web.UI.WebControls.DropDownList)thisControl).Enabled = false;

                if (thisControl.GetType() == typeof(System.Web.UI.WebControls.CheckBoxList))
                    ((System.Web.UI.WebControls.CheckBoxList)thisControl).Enabled = false;

                LockAllInputControls(thisControl.Controls);
            }
        }

        #endregion

        #region 根據目前執行序的語系，置換UI的顯示文字。

        /// <summary>
        /// 根據指定的語系，置換UI的顯示文字。
        /// </summary>
        /// <param name="ControlList"></param>
        internal static void ReplaceResourceString(System.Web.UI.ControlCollection ControlList, string strCultureUID)
        {
            foreach (System.Web.UI.Control thisControl in ControlList)
            {
                if (thisControl.GetType() == typeof(System.Web.UI.WebControls.Button))
                {
                    System.Web.UI.WebControls.Button btnControl = (System.Web.UI.WebControls.Button)thisControl;
                    btnControl.Text = GetReplacedResourceString(strCultureUID, btnControl.Text);
                }

                if (thisControl.GetType() == typeof(System.Web.UI.WebControls.Label))
                {
                    System.Web.UI.WebControls.Label lblControl = (System.Web.UI.WebControls.Label)thisControl;
                    lblControl.Text = GetReplacedResourceString(strCultureUID, lblControl.Text);
                }

                if (thisControl.GetType() == typeof(System.Web.UI.WebControls.GridView))
                {
                    System.Web.UI.WebControls.GridView grvControl = (System.Web.UI.WebControls.GridView)thisControl;
                    grvControl.EmptyDataText = GetReplacedResourceString(strCultureUID, grvControl.EmptyDataText);
                }

                if (thisControl.GetType() == typeof(System.Web.UI.WebControls.RadioButtonList))
                {
                    System.Web.UI.WebControls.RadioButtonList rblControl = (System.Web.UI.WebControls.RadioButtonList)thisControl;
                    for (int intIndex = 0; intIndex < rblControl.Items.Count; intIndex++)
                    {
                        rblControl.Items[intIndex].Text = GetReplacedResourceString(strCultureUID, rblControl.Items[intIndex].Text);
                    }
                }

                ReplaceResourceString(thisControl.Controls, strCultureUID);
            }
        }

        /// <summary>
        /// 分析並置換成顯示文字
        /// </summary>
        /// <param name="strSource"></param>
        /// <param name="strCultureUID"></param>
        /// <returns></returns>
        internal static string GetReplacedResourceString(string strCultureUID, string strSource)
        {
            string strResult = strSource;

            string[] strPart = strSource.Split(':');
            if (strPart.Length > 1)
            {
                if (strPart[0] == "CultureResourceString")
                {
                    System.Text.StringBuilder sbResourceKey = new StringBuilder();
                    for (int intIndex = 1; intIndex < strPart.Length; intIndex++)
                    {
                        if (sbResourceKey.Length > 0)
                            sbResourceKey.Append(":");
                        sbResourceKey.Append(strPart[intIndex]);
                    }
                    strResult = ISC_RAC.BLL.CultureResourceString.Instance.GetResourceString(sbResourceKey.ToString());
                }
            }

            return strResult;
        }

        #endregion

        #region 檢查一個ControlCollection裡的所有元件，包含所有內部皆層，是否含有危險字元。

        /// <summary>
        /// 檢查一個ControlCollection裡的所有元件，包含所有內部皆層，是否含有危險字元。
        /// </summary>
        /// <param name="ControlList"></param>
        /// <param name="bRaiseError">true:檢查到有危險字元時，會擲出例外。false:根據InsecureCharacter資料表的設定，置換字元。</param>
        public static void CheckInsecureCharacterInAllControls(System.Web.UI.ControlCollection ControlList, bool bRaiseError)
        {
            foreach (System.Web.UI.Control thisControl in ControlList)
            {
                //TextBox
                if (thisControl.GetType() == typeof(System.Web.UI.WebControls.TextBox))
                {
                    System.Web.UI.WebControls.TextBox txtControl = (System.Web.UI.WebControls.TextBox)thisControl;
                    txtControl.Text = ReplaceInsecureCharacterInString(txtControl.Text, bRaiseError);
                }
                //Button
                else if (thisControl.GetType() == typeof(System.Web.UI.WebControls.Button))
                {
                    System.Web.UI.WebControls.Button btnControl = (System.Web.UI.WebControls.Button)thisControl;
                    btnControl.Text = ReplaceInsecureCharacterInString(btnControl.Text, bRaiseError);
                }
                //DropDownList
                else if (thisControl.GetType() == typeof(System.Web.UI.WebControls.DropDownList))
                {
                    System.Web.UI.WebControls.DropDownList ddlControl = (System.Web.UI.WebControls.DropDownList)thisControl;
                    for (int iCount = 0; iCount < ddlControl.Items.Count; iCount++)
                    {
                        System.Web.UI.WebControls.ListItem liThis = ddlControl.Items[iCount];
                        liThis.Value = ReplaceInsecureCharacterInString(liThis.Value, bRaiseError);
                    }
                }
                //CheckBox
                else if (thisControl.GetType() == typeof(System.Web.UI.WebControls.CheckBox))
                {
                    System.Web.UI.WebControls.CheckBox chkControl = (System.Web.UI.WebControls.CheckBox)thisControl;
                    chkControl.Text = ReplaceInsecureCharacterInString(chkControl.Text, bRaiseError);
                }
                //CheckBoxList
                else if (thisControl.GetType() == typeof(System.Web.UI.WebControls.CheckBoxList))
                {
                    System.Web.UI.WebControls.CheckBoxList cblControl = (System.Web.UI.WebControls.CheckBoxList)thisControl;
                    for (int iCount = 0; iCount < cblControl.Items.Count; iCount++)
                    {
                        System.Web.UI.WebControls.ListItem liThis = cblControl.Items[iCount];
                        liThis.Value = ReplaceInsecureCharacterInString(liThis.Value, bRaiseError);
                    }
                }
                //RadioButtonList
                else if (thisControl.GetType() == typeof(System.Web.UI.WebControls.RadioButtonList))
                {
                    System.Web.UI.WebControls.RadioButtonList rblControl = (System.Web.UI.WebControls.RadioButtonList)thisControl;
                    for (int iCount = 0; iCount < rblControl.Items.Count; iCount++)
                    {
                        System.Web.UI.WebControls.ListItem liThis = rblControl.Items[iCount];
                        liThis.Value = ReplaceInsecureCharacterInString(liThis.Value, bRaiseError);
                    }
                }

                //Children Controls
                if (thisControl.Controls.Count > 0)
                {
                    CheckInsecureCharacterInAllControls(thisControl.Controls, bRaiseError);
                }
            }
        }

        /// <summary>
        /// 檢查某字串，是否有危險字元。
        /// </summary>
        /// <param name="strSource"></param>
        /// <param name="bRaiseError"></param>
        /// <returns></returns>
        protected static string ReplaceInsecureCharacterInString(string strSource, bool bRaiseError)
        {
            List<ISC_RAC.Model.RowItem_InsecureCharacter> liInsecureCharacterList=ISC_RAC.Common.SystemParameters.Instance.InsecureCharacterList;
            for (int intIndex = 0; intIndex < liInsecureCharacterList.Count; intIndex++)
            {
                if (bRaiseError == true)
                {
                    if (strSource.Contains(liInsecureCharacterList[intIndex].Original) == true)
                        throw new System.Exception(ISC_RAC.BLL.CultureResourceString.Instance.GetResourceString("InsecureCharacterIsDetected"));
                }
                else
                {
                    strSource = strSource.Replace(liInsecureCharacterList[intIndex].Original, liInsecureCharacterList[intIndex].Substitute);
                }
            }
            return strSource;
        }

        #endregion

        #region 檢查網頁參數裡，是否有危險字元。

        /// <summary>
        /// 檢查網頁參數裡，是否有危險字元。
        /// </summary>
        /// <param name="thisPage"></param>
        public static void CheckInsecureCharacterInQueryStringOfPage(System.Web.UI.Page thisPage)
        {
            //網頁參數是唯讀，所以只能Raise Error
            for (int intIndex=0;intIndex<thisPage.Request.QueryString.Count;intIndex++)
            {
                string strCheck=thisPage.Request.QueryString[intIndex].ToString();
                ReplaceInsecureCharacterInString(strCheck,true);
            }
        }

        #endregion
    }
}
