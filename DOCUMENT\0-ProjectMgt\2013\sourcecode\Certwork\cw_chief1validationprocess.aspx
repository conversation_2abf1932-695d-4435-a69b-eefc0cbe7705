﻿<%@ Page Title="" Language="C#" MasterPageFile="~/masterpage/MasterPage.master" AutoEventWireup="true"
    CodeFile="cw_chief1validationprocess.aspx.cs" Inherits="Certwork_cw_chief1validationprocess" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="fullheader">
        <table width="100%" cellspacing="0" cellpadding="0" class="fullheaderinfo">
            <tr>
                <td valign="top">
                    <b>核決作業</b>
                </td>
                <td align="right" valign="top" class="font_loginInfo">
                </td>
            </tr>
        </table>
    </div>
    <!--{* fullheader end *}-->
    <div class="fullcontent">
        <div id="div_maintain" runat="server" class="formstyle">
            <asp:Literal ID="ltr_HeaderData" runat="server"></asp:Literal>
            <table border='0' cellspacing='0' cellpadding='0' width='100%' class='font_fullcontent'>
                <tr>
                    <td align="right">
                        <asp:HyperLink ID="hl_ValidationProcess" runat="server" Target="_blank">審驗歷程</asp:HyperLink>
                        &nbsp;
                        <asp:HyperLink ID="hl_RePairProcess" runat="server" Target="_blank">補件通知歷程</asp:HyperLink>
                    </td>
                </tr>
                <tr>
                    <td>
                        車型組：<asp:DropDownList ID="ddl_CarCompoModel" runat="server">
                        </asp:DropDownList>
                        <asp:Button ID="btn_GetCarCompoModelInfo" runat="server" Text="取得車型組資料" CssClass="btn_mouseout"
                            OnClick="btn_GetCarCompoModelInfo_Click" OnClientClick="return confirm(&quot;確定要取得此車型組資料嗎？&quot;)" />
                        &nbsp;&nbsp;
                        <asp:Button ID="btn_ChiefComplete" runat="server" Text="完成核決" CssClass="btn_mouseout"
                            OnClick="btn_DoubleComplete_Click" Visible="False" />
                        <asp:Button ID="btn_BackToFirst" runat="server" Text="退回初審" CssClass="btn_mouseout"
                            OnClick="btn_BackToFirst_Click" Visible="False" />
                    </td>
                </tr>
                <tr>
                    <td>
                        <div class="tableoutcome">
                            <asp:GridView ID="gv_data" runat="server" AutoGenerateColumns="False" Width="100%"
                                CellPadding="0" BorderWidth="0px" GridLines="None" CssClass="font_fullcontent"
                                EnableModelValidation="True" DataKeyNames="vcn_validationitemno" OnDataBound="gv_data_DataBound"
                                OnLoad="gv_data_Load" OnRowDataBound="gv_data_RowDataBound">
                                <Columns>
                                    <asp:TemplateField HeaderText="項次">
                                        <ItemTemplate>
                                            <asp:Label ID="lbl_no" runat="server" Text='<%# Bind("orderid") %>'></asp:Label>
                                        </ItemTemplate>
                                        <HeaderStyle CssClass="header" HorizontalAlign="Center" />
                                        <ItemStyle CssClass="lineleft" HorizontalAlign="Center" />
                                    </asp:TemplateField>
                                    <asp:TemplateField HeaderText="審查項目">
                                        <ItemTemplate>
                                            <asp:HyperLink ID="hl_item" runat="server" Target="_blank" NavigateUrl='<%# Eval("vilup_desc").ToString().Replace("applyno=","applyno=" + Server.UrlEncode(Request["p"])).Replace("carcompomodelno=", "carcompomodelno="+Server.UrlEncode(EnCode(Eval("vcn_carcompomodelno").ToString()))).Replace("carmodelno=", "carmodelno="+Server.UrlEncode(EnCode(Eval("cmdg_carmodelno").ToString()))) %>'><%# Eval("vil_desc") %></asp:HyperLink>
                                        </ItemTemplate>
                                        <HeaderStyle CssClass="header" />
                                        <ItemStyle CssClass="lineleft" />
                                    </asp:TemplateField>
                                    <asp:TemplateField HeaderText="不需&lt;br/&gt;檢附">
                                        <ItemTemplate>
                                            <asp:CheckBox ID="cbx_NotNeeded" runat="server" Checked='<%# Eval("vcn_notneeded").ToString().Equals("Y") %>'
                                                onclick="CheckNotNeeded(this);" Enabled="False" />
                                        </ItemTemplate>
                                        <HeaderStyle CssClass="header" HorizontalAlign="Center" />
                                        <ItemStyle CssClass="lineleft" HorizontalAlign="Center" />
                                    </asp:TemplateField>
                                    <asp:TemplateField HeaderText="審查意見">
                                        <ItemTemplate>
                                            <asp:Label ID="lab_validationitemstatus" runat="server" Text='<%# Eval("vcn_validationitemstatus")%>'></asp:Label>
                                        </ItemTemplate>
                                        <HeaderStyle CssClass="lineright" />
                                        <ItemStyle CssClass="lineright" />
                                    </asp:TemplateField>
                                </Columns>
                                <PagerStyle HorizontalAlign="Center" Font-Bold="true" CssClass="S_PageCss" />
                            </asp:GridView>
                        </div>
                    </td>
                </tr>
            </table>
        </div>
        <asp:HiddenField ID="hf_CarCompoModelNo" runat="server" />
    </div>
    <!--{* fullcontent end *}-->
    <div class="fullfooter">
    </div>
    <!--{* content end *}-->
</asp:Content>
