﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Configuration;
using System.Text;
using System.Data;

public partial class Certwork_cw_issuedoc : BaseAdminPage
{
    protected void Page_Load(object sender, EventArgs e)
    {
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = '../accountmt/index.aspx';</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" && (GetAgentAccountGrpList().Contains("epa_adm") || GetAgentAccountGrpList().Contains("ac_adm") || GetAgentAccountGrpList().Contains("tc_adm") || GetAgentAccountGrpList().Contains("rv_adm") || GetAgentAccountGrpList().Contains("rs_adm") || GetAgentAccountGrpList().Contains("fis_adm") || GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("pg_adm")))
            {
                //if (!IsPostBack)
                //{
                    InitData();
                //}
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }
    }

    private string ApplyNo
    {
        get
        {
            return Request["p"] == null ? string.Empty : DeCode(Request["p"].ToString());
        }
    }

    private void InitData()
    {
        lbl_CompanyName.Text = new RejectControl().GetCompanyName(ApplyNo);
        lbl_ApplyNo.Text = ApplyNo;
        lbl_Account.Text = GetAgentAccount();
        lbl_IssueDate.Text = DateTime.Now.ToString("yyyy/MM/dd");        
        //核閱完成
        if (new FirstProcessControl().IsValidationStatus(ApplyNo, "35"))
        {
            btn_Issue.Visible = true;

        }
        DataView dv = new MailParaControl().GetApplyBaseData(ApplyNo);
        string EngineFamily = dv[0]["EngineFamily"].ToString();
        string BelongCompidno = new RejectControl().GetCompanyNo(ApplyNo); 
        lab_exEngineNo.Text = new RejectControl().getExEnginesn(BelongCompidno, EngineFamily);
    }

    private bool chkForm()
    {
        StringBuilder sb = new StringBuilder();
        if (string.IsNullOrEmpty(txt_EngineNo.Text.ToUpper())) { sb.Append("引擎流水編號未填寫！\n"); }
        else if (new IssueDocControl().ExistsEngineNo(txt_EngineNo.Text.ToUpper()))
        {
            sb.Append("引擎流水編號已存在！\n");
        }
        #region 特殊字元判斷-有MasterPage
        ContentPlaceHolder mpContentPlaceHolder;
        mpContentPlaceHolder = (ContentPlaceHolder)Master.FindControl("ContentPlaceHolder1");
        foreach (object ctrl in mpContentPlaceHolder.Controls)
        {
            if (ctrl is TextBox)
            {
                TextBox objTextBox = default(TextBox);
                objTextBox = (TextBox)ctrl;
                if ((tbWord(objTextBox.Text)))
                {
                    sb.Append(objTextBox.Text + "含有系統不允許的特殊字元！\n");
                }
            }
        }
        #endregion
        if (sb.Length.Equals(0))
        {
            return true;
        }
        else
        {
            MessageBox.Show(sb.ToString());
            return false;
        }
    }

    //核發
    protected void btn_Issue_Click(object sender, EventArgs e)
    {

        if (chkForm())
        {
            new IssueDocControl().UpdateIssue(ApplyNo, txt_EngineNo.Text);//更新核發日
            new FirstProcessControl().ChangeApplyStatus(ApplyNo, "50");//已完成審查
            new FirstProcessControl().LogValidationProcess(ApplyNo, DBNull.Value, "36");//Log
            RegisterStartupScript("script", @"
<script>alert('案件已核發！'); window.location='cw_firstvalidationqry.aspx';</script>
");
        }
    }  

    protected void lb_Certificated_Click(object sender, EventArgs e)
    {       
        string scriptString = "";
        scriptString = string.Format(@"window.open('cw_certificated.aspx?p={0}&EngineNo={1}',10,'scrollbars=yes'); ", Server.UrlEncode(EnCode(ApplyNo)),  Server.UrlEncode(EnCode(txt_EngineNo.Text)));
        this.ClientScript.RegisterStartupScript(this.GetType(), "", scriptString, true);        
    }
}