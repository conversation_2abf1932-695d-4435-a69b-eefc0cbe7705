﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Configuration;
using System.Data;

public partial class Certwork_cw_firstvalidationprocess : BaseAdminPage
{
    protected void Page_Load(object sender, EventArgs e)
    {
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = '../accountmt/index.aspx';</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" && (GetAgentAccountGrpList().Contains("fis_adm") || GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("pg_adm")))
            {
                if (!IsPostBack)
                {
                    InitData();
                }
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }
    }

    private string ApplyNo
    {
        get
        {
            return Request["p"] == null ? string.Empty : DeCode(Request["p"].ToString());
        }
    }

    private void InitData()
    {
        ltr_HeaderData.Text = new FirstProcessControl().TagFirstProcess(ApplyNo);
        DataUtils.BindList(ddl_CarCompoModel, new FirstProcessControl().LisCarCompoModel(ApplyNo));
        btn_Issue.Visible = new FirstProcessControl().IsValidationStatus(ApplyNo, "35");//核閱完成
        btn_Filling.Visible = new FirstProcessControl().IsValidationStatus(ApplyNo, "50");//已上傳平台
        hl_ValidationProcess.NavigateUrl = "cw_validationprocesslog.aspx?p=" + Server.UrlEncode(Request["p"]);
        hl_RePairProcess.NavigateUrl = "cw_resupplyprocesslog.aspx?p=" + Server.UrlEncode(Request["p"]);
    }

    //取得車型組資料
    protected void btn_GetCarCompoModelInfo_Click(object sender, EventArgs e)
    {

        hf_CarCompoModelNo.Value = ddl_CarCompoModel.SelectedValue;
        gv_data.DataSource = new FirstProcessControl().ListCarCompoModelInfo(ApplyNo, hf_CarCompoModelNo.Value);
        gv_data.DataBind();
        set_Button_be_show();

    }

    //RowDataBound
    protected void gv_data_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        List<ListItem> lis = new FirstProcessControl().LisMostusedOpinion();
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            DropDownList ddl = (DropDownList)e.Row.FindControl("ddl_CommonOpinion");
            DataUtils.BindList(ddl, lis, "請選擇");

            string sitemno = DataBinder.Eval(e.Row.DataItem, "vcn_validationitemno").ToString().Trim();
            string orderid = DataBinder.Eval(e.Row.DataItem, "orderid").ToString().Trim();
            TextBox txt_Opinion = (TextBox)e.Row.FindControl("txt_Opinion");
            CheckBox cbx_Result = (CheckBox)e.Row.FindControl("cbx_Result");//審查結果
            DropDownList ddl_CommonOpinion = (DropDownList)e.Row.FindControl("ddl_CommonOpinion");
            Button btn_SetOpinion = (Button)e.Row.FindControl("btn_SetOpinion");
            CheckBox cbx_NotNeeded = (CheckBox)e.Row.FindControl("cbx_NotNeeded");//不需檢附
            if (sitemno.Equals("19") || sitemno.Equals("20"))
            {
                txt_Opinion.ReadOnly = true;

                txt_Opinion.BackColor = System.Drawing.ColorTranslator.FromHtml("#D3D3D3");
                cbx_Result.Visible = false;//審查結果
                ddl_CommonOpinion.Visible = false;
                btn_SetOpinion.Visible = false;
                cbx_NotNeeded.Visible = false;
            }
        }
    }
    private bool CheckResult()
    {
        bool sCheck = true;
        for (int i = 0; i < gv_data.Rows.Count; i++)
        {
            string ValidationItemStatus = ((CheckBox)gv_data.Rows[i].FindControl("cbx_Result")).Checked ? "OK" : "NG";
            if (ValidationItemStatus == "NG")
            {
                sCheck = false;
                break;
            }
        }
        return sCheck;
    }

    private void SaveItem()
    {
        string CarCompoModelNo = hf_CarCompoModelNo.Value;

        #region 特殊字元判斷
        foreach (GridViewRow grv in gv_data.Rows)
        {
            if (grv.RowType == DataControlRowType.DataRow)
            {
                TextBox cbl_select = (TextBox)grv.FindControl("txt_Opinion");
                if ((tbWord(cbl_select.Text)))
                {
                    MessageBox.Show(cbl_select.Text + ":含有系統不允許的特殊字元");
                    return;
                }
            }
        }
        #endregion

        string NotNeeded = "";
        string ValidationItemStatus = "";
        for (int i = 0; i < gv_data.Rows.Count; i++)
        {
            string ValidationItemNo = gv_data.DataKeys[i].Values["vcn_validationitemno"].ToString();

            string Opinion = ((TextBox)gv_data.Rows[i].FindControl("txt_Opinion")).Text;
            //跳過複審和二級主管意見的審核
            if (ValidationItemNo.Equals("19") || ValidationItemNo.Equals("20"))
            {
                NotNeeded = "N";
                ValidationItemStatus = "OK";
            }
            else
            {
                NotNeeded = ((CheckBox)gv_data.Rows[i].FindControl("cbx_NotNeeded")).Checked ? "Y" : "N";
                ValidationItemStatus = ((CheckBox)gv_data.Rows[i].FindControl("cbx_Result")).Checked ? "OK" : "NG";
            }

            new FirstProcessControl().UpdateValidationCheckNote(ApplyNo, CarCompoModelNo, ValidationItemNo, NotNeeded, Opinion, ValidationItemStatus);
        }
    }

    //暫存
    protected void btn_Save_Click(object sender, EventArgs e)
    {
        SaveItem();
        MessageBox.Show("車型組已暫存！");
    }

    //完成初審
    protected void btn_FirstComplete_Click(object sender, EventArgs e)
    {
        SaveItem();
        string s_CarCertResul = "";
        wsApplybasedataDetail applydatadetail = new wsApplybasedataDetail();
        //判斷與污染車欄位資料是否相同
        s_CarCertResul = CheckCarCertApplySubmit();

        if (s_CarCertResul.Length > 0)
        {
            MessageBox.Show(applydatadetail.compareEnStrtoCn(s_CarCertResul) + "\n欄位與污染車有所不一致!!!");
            return;
        }
        if (new FirstProcessControl().ChackCarCompoModelIsOK(ApplyNo) && s_CarCertResul.Length == 0)
        {
            new FirstProcessControl().ChangeApplyStatus(ApplyNo, "32");//複審中
            new FirstProcessControl().LogValidationProcess(ApplyNo, DBNull.Value, "31");//Log
            MailDoubleNotify();//SendMail
            RegisterStartupScript("script", @"
<script>alert('已完成初審！'); window.location='cw_firstvalidationqry.aspx';</script>
");
        }
        else
        {
            MessageBox.Show("尚有車型組未檢視或項目不OK，無法完成審查！");
        }
    }

    private void MailDoubleNotify()
    {
        DataView dv = new MailParaControl().GetApplyBaseData(ApplyNo);
        string CompName = dv[0]["CompName"].ToString();
        string EngineFamily = dv[0]["EngineFamily"].ToString();
        DataView GropuAccount = new MailParaControl().GetGroupAccount("03");
        for (int i = 0; i < GropuAccount.Count; i++)
        {
            string AccountName = GropuAccount[i]["AccountName"].ToString();
            string AccountMail = GropuAccount[i]["AccountMail"].ToString();
            string[] mailTo = new string[] { AccountMail };
            string[] mailCc = new string[1];//副本
            mailCc[0] = "";
            string[] mailBcc = new string[1];//密件
            mailBcc[0] = "";
            string Float = "複審";
            new SendMail().Mail_DoubleNotify(mailTo, mailCc, mailBcc, AccountName, ApplyNo, CompName, EngineFamily, Float);
        }
    }

    //補件
    protected void btn_Repair_Click(object sender, EventArgs e)
    {
        SaveItem();
        if (new FirstProcessControl().ChackCarCompoModelIsOK(ApplyNo))
        {
            MessageBox.Show("所有車型組項目皆OK，無法進入補件！");
        }
        else
        {
            string msg = new FirstProcessControl().SetValidationCheckNote(ApplyNo);//寫入補件資料，更新狀態
            if (!msg.Length.Equals(0)) { MessageBox.Show(msg); return; }
            new FirstProcessControl().LogValidationProcess(ApplyNo, DBNull.Value, "40");//Log
            MailRepairNotify();//SendMail
            RegisterStartupScript("script", @"
                <script>alert('已通知補件！'); window.location='cw_firstvalidationqry.aspx';</script>
                ");
        }
    }

    private void MailRepairNotify()
    {
        DataView dv = new MailParaControl().GetApplyBaseData(ApplyNo);
        string[] mailTo = new string[] { dv[0]["ContactMail"].ToString() };
        string[] mailCc = new string[1];//副本
        mailCc[0] = "";
        string[] mailBcc = new string[1];//密件
        mailBcc[0] = "";
        string ContactName = dv[0]["ContactName"].ToString();
        string CompName = dv[0]["CompName"].ToString();
        string EngineFamily = dv[0]["EngineFamily"].ToString();
        string StrCarCompoNo = new FirstProcessControl().StrCarCompoNoNG(ApplyNo);
        string ValidationMail = dv[0]["AccontMail"].ToString();
        new SendMail().Mail_RepairNotify(mailTo, mailCc, mailBcc, ContactName, ApplyNo, CompName, EngineFamily, StrCarCompoNo, ValidationMail);
    }

    //退件
    protected void btn_Return_Click(object sender, EventArgs e)
    {
        Response.Redirect("cw_rejectapplyprocess.aspx?p=" + Server.UrlEncode(EnCode(ApplyNo)));
    }

    //核發
    protected void btn_Issue_Click(object sender, EventArgs e)
    {
        Response.Redirect("cw_issuedoc.aspx?p=" + Server.UrlEncode(EnCode(ApplyNo)));
    }

    //歸檔
    protected void btn_Filling_Click(object sender, EventArgs e)
    {
        Response.Redirect("cw_filling.aspx?p=" + Server.UrlEncode(EnCode(ApplyNo)));
    }
    #region 判斷是否與污染車欄位相同
    private string CheckCarCertApplySubmit()
    {
        string strApplyno = DeCode(Request["p"].ToString());
        DataView dv = new MailParaControl().GetApplyBaseData(strApplyno);

        Cryptography crypy = new Cryptography();
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        string[] radom_ad = crypy.wsApplybasedataDetailValid(crypy.getRadNum());

        string sUnSame = "";
        string smode = "2";
        string CarstyleYear = dv[0]["CarstyleYear"].ToString();
        string EngineFamily = dv[0]["EngineFamily"].ToString();
        string compidnoE = dv[0]["CompNo"].ToString();
        string IsImport = dv[0]["IsImport"].ToString();
        string CarType = dv[0]["CarType"].ToString();

        if (CarType.ToUpper().Equals("G"))
        {
            smode = "1";
        }

        connectCarCert.CarCert CarCert = new connectCarCert.CarCert();
        //connectARTC_D.ARTC ARTC_D = new connectARTC_D.ARTC();
        string[] radom_CarCert = wsCarCertValid(Convert.ToInt16(DateTime.Now.DayOfWeek));

        DataSet ds_CarCert_data = new DataSet();
        if (!(CarType.ToUpper().Equals("D")))
        {
            #region 取得污染與噪音的車型資料
            switch (CarType.ToUpper())
            {
                case "G":
                case "M":
                    ds_CarCert_data = CarCert.CheckApplyBasicData(EnCode(smode), compidnoE, EnCode(CarstyleYear), EnCode(EngineFamily), radom_CarCert[0].ToString(), radom_CarCert[1].ToString());//汙染車資料
                    // ds_CarCert_data = wsApplyData.CommonDataForPopulation_G("6B3DU+Zqs/QCcC0yRN4Heg==", "G", "2", "2013", "U1234567_o", radom_ad[0].ToString(), radom_ad[1].ToString());                          
                    break;
                //case "D":
                //    ds_CarCert_data = ARTC_D.CheckconsReceiveCaseInfo(compidnoE, EnCode(CarstyleYear), EnCode(EngineFamily), radom_CarCert[0].ToString(), radom_CarCert[1].ToString());//汙染車資料
                //    break;
            }

            DataSet ds_Carcompare_data = new DataSet(); //噪音車資料             
            if (CarType.ToUpper().Equals("G"))
            {
                for (int i = 0; i < ds_CarCert_data.Tables[0].Rows.Count; i++)
                {
                    //存入nv_pollutioncomparedapplydata
                    wsApplyData.Insert_tmp("[Manufacturer：" + ds_CarCert_data.Tables[0].Rows[i]["Manufacturer"].ToString() + "]" + "[MailAddress：" + ds_CarCert_data.Tables[0].Rows[i]["MailAddress"].ToString() + "]" + "[EngineKw：" + ds_CarCert_data.Tables[0].Rows[i]["EngineKw"].ToString() + "]" + "[EngineRpm：" + ds_CarCert_data.Tables[0].Rows[i]["EngineRpm"].ToString() + "]" + "[BasicEngine：" + ds_CarCert_data.Tables[0].Rows[i]["BasicEngine"].ToString() + "]" + "[Transmission：" + ds_CarCert_data.Tables[0].Rows[i]["Transmission"].ToString() + "]" + "[Brand：" + ds_CarCert_data.Tables[0].Rows[i]["Brand"].ToString() + "]" + "[CCarModel：" + ds_CarCert_data.Tables[0].Rows[i]["CCarModel"].ToString() + "]" + "[ECarModel：" + ds_CarCert_data.Tables[0].Rows[i]["ECarModel"].ToString() + "]" + "[CarModelType：" + ds_CarCert_data.Tables[0].Rows[i]["CarModelType"].ToString() + "]" + "[Exhaust：" + ds_CarCert_data.Tables[0].Rows[i]["Exhaust"].ToString() + "]" + "[TransmissionType：" + ds_CarCert_data.Tables[0].Rows[i]["TransmissionType"].ToString() + "]" + "[TransmissionNum：" + ds_CarCert_data.Tables[0].Rows[i]["TransmissionNum"].ToString() + "]" + "[CarDoorCount：" + ds_CarCert_data.Tables[0].Rows[i]["CarDoorCount"].ToString() + "]" + "[Others：" + ds_CarCert_data.Tables[0].Rows[i]["Others"].ToString() + "]", strApplyno);
                }
                ds_Carcompare_data = wsApplyData.SelfDataForPopulation_G(strApplyno, radom_ad[0].ToString(), radom_ad[1].ToString());
            }
            else if (CarType.ToUpper().Equals("M"))
            {
                for (int i = 0; i < ds_CarCert_data.Tables[0].Rows.Count; i++)
                {
                    //存入nv_pollutioncomparedapplydata
                    wsApplyData.Insert_tmp("[Manufacturer：" + ds_CarCert_data.Tables[0].Rows[i]["Manufacturer"].ToString() + "]" + "[MailAddress：" + ds_CarCert_data.Tables[0].Rows[i]["MailAddress"].ToString() + "]" + "[EngineKw：" + ds_CarCert_data.Tables[0].Rows[i]["EngineKw"].ToString() + "]" + "[EngineRpm：" + ds_CarCert_data.Tables[0].Rows[i]["EngineRpm"].ToString() + "]" + "[BasicEngine：" + ds_CarCert_data.Tables[0].Rows[i]["BasicEngine"].ToString() + "]" + "[Transmission：" + ds_CarCert_data.Tables[0].Rows[i]["Transmission"].ToString() + "]" + "[Brand：" + ds_CarCert_data.Tables[0].Rows[i]["Brand"].ToString() + "]" + "[CCarModel：" + ds_CarCert_data.Tables[0].Rows[i]["CCarModel"].ToString() + "]" + "[ECarModel：" + ds_CarCert_data.Tables[0].Rows[i]["ECarModel"].ToString() + "]" + "[CarModelType：" + ds_CarCert_data.Tables[0].Rows[i]["CarModelType"].ToString() + "]" + "[Exhaust：" + ds_CarCert_data.Tables[0].Rows[i]["Exhaust"].ToString() + "]" + "[TransmissionType：" + ds_CarCert_data.Tables[0].Rows[i]["TransmissionType"].ToString() + "]" + "[TransmissionNum：" + ds_CarCert_data.Tables[0].Rows[i]["TransmissionNum"].ToString() + "]" + "[Others：" + ds_CarCert_data.Tables[0].Rows[i]["Others"].ToString() + "]", strApplyno);
                }
                ds_Carcompare_data = wsApplyData.SelfDataForPopulation_M(strApplyno, radom_ad[0].ToString(), radom_ad[1].ToString());
            }
            else //D
            {
                for (int i = 0; i < ds_CarCert_data.Tables[0].Rows.Count; i++)
                {
                    //存入nv_pollutioncomparedapplydata
                    wsApplyData.Insert_tmp("[Manufacturer：" + ds_CarCert_data.Tables[0].Rows[i]["Manufacturer"].ToString() + "]" + "[MailAddress：" + ds_CarCert_data.Tables[0].Rows[i]["SplyComAddr"].ToString() + "]" + "[EngineKw：" + ds_CarCert_data.Tables[0].Rows[i]["MaxKW"].ToString() + "]" + "[EngineRpm：" + ds_CarCert_data.Tables[0].Rows[i]["Maxrpm"].ToString() + "]" + "[BasicEngine：" + ds_CarCert_data.Tables[0].Rows[i]["BasicEngineNo"].ToString() + "]" + "[Transmission：" + ds_CarCert_data.Tables[0].Rows[i]["TSD"].ToString() + "]" + "[Brand：" + ds_CarCert_data.Tables[0].Rows[i]["Brand"].ToString() + "]" + "[CCarModel：" + ds_CarCert_data.Tables[0].Rows[i]["CarStyleName_CName"].ToString() + "]" + "[ECarModel：" + ds_CarCert_data.Tables[0].Rows[i]["CarStyleName_E"].ToString() + "]" + "[CarModelType：" + ds_CarCert_data.Tables[0].Rows[i]["CarStyleID"].ToString() + "]" + "[Exhaust：" + ds_CarCert_data.Tables[0].Rows[i]["CC"].ToString() + "]" + "[TransmissionType：" + ds_CarCert_data.Tables[0].Rows[i]["Gear"].ToString() + "]" + "[TransmissionNum：" + ds_CarCert_data.Tables[0].Rows[i]["GearCount"].ToString() + "]" + "[Others：" + ds_CarCert_data.Tables[0].Rows[i]["Others"].ToString() + "]", strApplyno);
                }
                ds_Carcompare_data = wsApplyData.SelfDataForPopulation_D(strApplyno, radom_ad[0].ToString(), radom_ad[1].ToString());

            }
            #endregion
            DataRow[] dt2_compare_carstylename = new DataRow[0];
            DataTable dt_IsExistCarcompare_data = new DataTable();
            #region 當污染和噪音都有資料時才比對資料是否相同
            if (ds_CarCert_data.Tables[0].Rows.Count > 0 && ds_Carcompare_data.Tables[0].Rows.Count > 0)
            {
                for (int j = 0; j < ds_Carcompare_data.Tables[0].Rows.Count; j++)
                {
                    if (CarType.ToUpper().Equals("G"))
                    {
                        //20130708 增加 先比對車型是否存在 ，再決定是否往下比
                        dt2_compare_carstylename = ds_CarCert_data.Tables[0].Select("Brand='" + ds_Carcompare_data.Tables[0].Rows[j]["ct_bd_desc"].ToString() + "' and CCarModel='" + ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carchnmodelname"].ToString() + "' and ECarModel= '" + ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carengmodelname"].ToString() + "' and CarModelType= '" + ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carmodelcode"].ToString() + "' and Exhaust= '" + ds_Carcompare_data.Tables[0].Rows[j]["cpm_exhuast"].ToString().Replace(".00", "") + "' and TransmissionType= '" + ds_Carcompare_data.Tables[0].Rows[j]["ct_gm_desc"].ToString() + "' and TransmissionNum= '" + ds_Carcompare_data.Tables[0].Rows[j]["cmdg_transmissionnum"].ToString() + "' and CarDoorCount='" + ds_Carcompare_data.Tables[0].Rows[j]["ct_vr_desc"].ToString() + "' and Others='" + ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carmodeladd"].ToString() + "'");


                        if (dt2_compare_carstylename.Length > 0)
                        {
                            dt_IsExistCarcompare_data = dt2_compare_carstylename.CopyToDataTable();
                            //資料比對cmdg_transmissionmodel-->Transmission(變速系統);ct_gm_desc-->TransmissionType(排檔方式)                           
                            string[] aryCarNoise_data = new string[] { ds_Carcompare_data.Tables[0].Rows[j]["abd_factoryname"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["abd_mailcertidocaddr"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_maxhorsepower"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_maxhorsepowerspeed"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_baseenginename"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_transmissionmodel"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["ct_bd_desc"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carchnmodelname"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carengmodelname"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carmodelcode"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cpm_exhuast"].ToString().Replace(".00", ""), ds_Carcompare_data.Tables[0].Rows[j]["ct_gm_desc"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_transmissionnum"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["ct_vr_desc"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carmodeladd"].ToString() };
                            //噪音與污染車資料比對及紀錄不一致欄位
                            sUnSame = CheckAndRecord(dt_IsExistCarcompare_data, aryCarNoise_data, CarType.ToUpper());
                            //紀錄比對不到的車型名稱Log
                            if (sUnSame.Length > 0)
                            {
                                string exErr = "[Fullcarstylename：" + ds_Carcompare_data.Tables[0].Rows[j]["fullcarstylename"].ToString() + "]";
                                wsApplyData.Insert_tmp(exErr, strApplyno);                               
                            }
                        }
                    }
                    else if (CarType.ToUpper().Equals("M"))
                    {
                        dt2_compare_carstylename = ds_CarCert_data.Tables[0].Select("Brand='" + ds_Carcompare_data.Tables[0].Rows[j]["ct_bd_desc"].ToString() + "' and CCarModel='" + ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carchnmodelname"].ToString() + "' and ECarModel= '" + ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carengmodelname"].ToString() + "' and CarModelType= '" + ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carmodelcode"].ToString() + "' and Exhaust= '" + ds_Carcompare_data.Tables[0].Rows[j]["cpm_exhuast"].ToString().Replace(".00", "") + "' and TransmissionType= '" + ds_Carcompare_data.Tables[0].Rows[j]["ct_gm_desc"].ToString() + "' and TransmissionNum= '" + ds_Carcompare_data.Tables[0].Rows[j]["cmdg_transmissionnum"].ToString() + "' and Others='" + ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carmodeladd"].ToString() + "'");
                        if (dt2_compare_carstylename.Length > 0)
                        {
                            dt_IsExistCarcompare_data = dt2_compare_carstylename.CopyToDataTable();
                            //資料比對cmdg_transmissionmodel-->Transmission(變速系統);ct_gm_desc-->TransmissionType(排檔方式)                         
                            string[] aryCarNoise_data = new string[] { ds_Carcompare_data.Tables[0].Rows[j]["abd_factoryname"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["abd_mailcertidocaddr"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_maxhorsepower"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_maxhorsepowerspeed"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_baseenginename"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_transmissionmodel"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["ct_bd_desc"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carchnmodelname"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carengmodelname"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carmodelcode"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cpm_exhuast"].ToString().Replace(".00", ""), ds_Carcompare_data.Tables[0].Rows[j]["ct_gm_desc"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_transmissionnum"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carmodeladd"].ToString() };
                            //噪音與污染車資料比對及紀錄不一致欄位
                            sUnSame = CheckAndRecord(dt_IsExistCarcompare_data, aryCarNoise_data, CarType.ToUpper());
                            //紀錄比對不到的車型名稱Log
                            if (sUnSame.Length > 0)
                            {
                                string exErr = "[Fullcarstylename：" + ds_Carcompare_data.Tables[0].Rows[j]["fullcarstylename"].ToString() + "]";
                                wsApplyData.Insert_tmp(exErr, strApplyno);
                            }
                        }
                    }
                    else //D
                    {
                        //20130708 增加 先比對車型是否存在 ，再決定是否往下比
                        dt2_compare_carstylename = ds_CarCert_data.Tables[0].Select("Brand='" + ds_Carcompare_data.Tables[0].Rows[j]["ct_bd_desc"].ToString() + "' and CarStyleName_CName='" + ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carchnmodelname"].ToString() + "' and CarStyleName_E= '" + ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carengmodelname"].ToString() + "' and CarStyleID= '" + ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carmodelcode"].ToString() + "' and CC= '" + ds_Carcompare_data.Tables[0].Rows[j]["cpm_exhuast"].ToString().Replace(".00", "") + "' and Gear= '" + ds_Carcompare_data.Tables[0].Rows[j]["ct_gm_desc"].ToString() + "' and GearCount= '" + ds_Carcompare_data.Tables[0].Rows[j]["cmdg_transmissionnum"].ToString() + "' and DoorCount='" + ds_Carcompare_data.Tables[0].Rows[j]["ct_vr_desc"].ToString() + "' and Others='" + ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carmodeladd"].ToString() + "'");
                        if (dt2_compare_carstylename.Length > 0)
                        {
                            dt_IsExistCarcompare_data = dt2_compare_carstylename.CopyToDataTable();
                            //資料比對cmdg_transmissionmodel-->Transmission(變速系統);ct_gm_desc-->TransmissionType(排檔方式)                                     
                            string[] aryCarNoise_data = new string[] { ds_Carcompare_data.Tables[0].Rows[j]["abd_factoryname"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["abd_mailcertidocaddr"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_maxhorsepower_hb"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_maxhorsepowerspeed_hb"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_baseenginename"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_transmissionmodel"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["ct_bd_desc"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carchnmodelname"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carengmodelname"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carmodelcode"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cpm_exhuast"].ToString().Replace(".00", ""), ds_Carcompare_data.Tables[0].Rows[j]["ct_gm_desc"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_transmissionnum"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["ct_vr_desc"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carmodeladd"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["carbodyshapedesc"].ToString() };
                            //噪音與污染車資料比對及紀錄不一致欄位
                            sUnSame = CheckAndRecord(dt_IsExistCarcompare_data, aryCarNoise_data, CarType.ToUpper());
                            //紀錄比對不到的車型名稱Log
                            if (sUnSame.Length > 0)
                            {
                                string exErr = "[Fullcarstylename：" + ds_Carcompare_data.Tables[0].Rows[j]["fullcarstylename"].ToString() + "]";
                                wsApplyData.Insert_tmp(exErr, strApplyno);
                            }
                        }
                    }

                }

            }
            else
            {
                return ""; //沒有污染車資料
            }
            #endregion
            #region 回傳不一致欄位
            if (sUnSame.Length < 0)
            {
                return ""; //沒有不一致
            }
            else
            {
                return sUnSame.TrimEnd(',');
            }
            #endregion
        }
        else
        {
            return "";
        }
    }

    #endregion
    //按鈕顯示
    private void set_Button_be_show()
    {
        if (new FirstProcessControl().IsValidationStatus(ApplyNo, "35"))  //核閱完成
        {
            btn_Save.Visible = false;          //暫存
            btn_FirstComplete.Visible = false; //完成初審            
            btn_Repair.Visible = false;        //補發
            btn_Issue.Visible = true;         //核發
            btn_Filling.Visible = false;       //歸檔
            btn_Return.Visible = false;        //退件
        }
        else if (new FirstProcessControl().IsValidationStatus(ApplyNo, "50")) //已上傳平台
        {
            btn_Save.Visible = false;          //暫存
            btn_FirstComplete.Visible = false; //完成初審            
            btn_Repair.Visible = false;        //補發
            btn_Issue.Visible = false;          //核發
            btn_Filling.Visible = true;        //歸檔
            btn_Return.Visible = false;         //退件
        }
        else if (new FirstProcessControl().IsValidationStatus(ApplyNo, "40")) //補件中
        {
            btn_Save.Visible = false;          //暫存
            btn_FirstComplete.Visible = false; //完成初審            
            btn_Repair.Visible = false;        //補發
            btn_Issue.Visible = false;          //核發
            btn_Filling.Visible = false;        //歸檔
            btn_Return.Visible = false;         //退件
        }
        else
        {
            btn_Save.Visible = true;          //暫存
            btn_FirstComplete.Visible = true; //完成初審            
            btn_Repair.Visible = true;        //補發
            btn_Return.Visible = true;        //退件
        }

    }

    #region 噪音與污染車資料比對及紀錄不一致欄位
    private string CheckAndRecord(DataTable dt_CarCert_data, string[] aryCarNoise_data, string CarType)
    {
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        DataTable dtCarCert = dt_CarCert_data;

        string strUnSame = "";
        string[] aryCarCert_data = new string[aryCarNoise_data.Length];
        //比對欄位
        switch (CarType)
        {
            case "G":
                aryCarCert_data = new string[] { "Manufacturer", "MailAddress", "EngineKw", "EngineRpm", "BasicEngine", "Transmission", "Brand", "CCarModel", "ECarModel", "CarModelType", "Exhaust", "TransmissionType", "TransmissionNum", "CarDoorCount", "Others" };
                break;
            case "M":
                aryCarCert_data = new string[] { "Manufacturer", "MailAddress", "EngineKw", "EngineRpm", "BasicEngine", "Transmission", "Brand", "CCarModel", "ECarModel", "CarModelType", "Exhaust", "TransmissionType", "TransmissionNum", "Others" };
                break;
            case "D":
                aryCarCert_data = new string[] { "Manufacturer", "SplyComAddr", "MaxKW", "Maxrpm", "BasicEngineNo", "TSD", "Brand", "CarStyleName_CName", "CarStyleName_E", "CarStyleID", "CC", "Gear", "GearCount", "DoorCount", "Others", "CarStyleName_C" };
                break;
        }
        for (int i = 0; i < aryCarCert_data.Length; i++)
        {
            //紀錄不一致欄位
            if (wsApplyData.compareLinqToDS(dtCarCert, aryCarCert_data[i], aryCarNoise_data[i]) != "1")
            {
                strUnSame += wsApplyData.compareLinqToDS(dtCarCert, aryCarCert_data[i], aryCarNoise_data[i]) + ",";
            }
        }
        return strUnSame;
    }
    #endregion
}