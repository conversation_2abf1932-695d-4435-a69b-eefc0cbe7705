﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data;
using System.Data.SqlClient;

/// <summary>
/// Summary description for ResupplyProcess
/// </summary>
public class ResupplyControl
{
	public ResupplyControl()
	{
	}

	public DataView ListResupplyProcess(string ApplyNo)
	{
		SqlCommand sqlCmd = new SqlCommand(@"
--declare @ApplyNo varchar(9) set @ApplyNo='*********'
--;
with C0 as(
	select distinct
	vcnnp_id RepairNo
    ,vcnnp_applyno ApplyNo
	,cast(vcnnp_noteapplycompdate as nvarchar)+'/'+vcnnp_noteapplycompaccount RepairDate
	,vcnnpd_carcompomodelno CarCompNo
	from dbo.nv_validationchecknotefornotpassdetail d
	inner join dbo.nv_validationchecknotefornotpass m on m.vcnnp_id=d.vcnnpd_vcnnpid and m.vcnnp_applyno=d.vcnnpd_applyno
	where vcnnpd_applyno=@ApplyNo and vcnnpd_carcompomodelno in (select DISTINCT vcnnpd_carcompomodelno from nv_validationchecknotefornotpassdetail
    where vcnnpd_applyno=@ApplyNo )
)
select RepairNo, RepairDate, CarCompNo, ApplyNo from C0 order by RepairNo
");
		sqlCmd.Parameters.AddWithValue("@ApplyNo",ApplyNo);
		DataView dv = Common.Data_noisevalidation.runParaCmd(sqlCmd);
		return dv;
	}

	public DataView ListResupplyDetail(string RepairNo, string CarCompoNo)
	{
		SqlCommand sqlCmd = new SqlCommand(@"
select 
dbo.fn_CastValidationItemCn(vcnnpd_validationno) ValidationItemCn
,isnull(vcnnpd_thistimeopinion,'') Opinion,vcn_validationitemstatus
from dbo.nv_validationchecknotefornotpassdetail
join nv_validationchecknote on vcn_validationitemno=vcnnpd_validationno and vcn_applyno=vcnnpd_applyno and vcn_carcompomodelno=vcnnpd_carcompomodelno
where vcnnpd_vcnnpid=@RepairNo
and vcnnpd_carcompomodelno=@CarCompoNo
");
		sqlCmd.Parameters.AddWithValue("@RepairNo", RepairNo);
		sqlCmd.Parameters.AddWithValue("@CarCompoNo", CarCompoNo);
		DataView dv = Common.Data_noisevalidation.runParaCmd(sqlCmd);
		return dv;
	}
}