﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data.SqlClient;
using System.Data;
using Microsoft.Reporting.WebForms;

using System.Configuration;
using System.Xml;
using Common;
using System.IO;
using System.Drawing;
using System.Drawing.Imaging;
public partial class applydatamt_noisedefenseplan_rpt : BaseAdminPage
{
    protected void Page_Load(object sender, EventArgs e)
    {
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = '../accountmt/index.aspx';</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" &&
                (
                (
                GetAgentAccountGrpList().Contains("epa_adm") || GetAgentAccountGrpList().Contains("ac_adm") || GetAgentAccountGrpList().Contains("tc_adm") || GetAgentAccountGrpList().Contains("rv_adm") || GetAgentAccountGrpList().Contains("rs_adm") || GetAgentAccountGrpList().Contains("fis_adm") || GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("pg_adm")
                )
                ||
                (
                  (GetAgentAccountGrpList().Contains("cga_adm") || GetAgentAccountGrpList().Contains("cam_adm")) && CheckSingleApplyDataAccessRight(DeCode(Request["applyno"].ToString()))
                )
                )
                )
            {
                if (!IsPostBack)
                {
                    if (Request["applyno"].ToString() != "")
                    {
                        Cryptography crypy = new Cryptography();
                        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
                        int keynum = crypy.getRadNum();
                        string[] arr = crypy.wsApplybasedataDetailValid(keynum);
                        DataTable dt_applydatadetail = wsApplyData.get_applybasedate_detail(crypy.DeCode(Request["applyno"].ToString()), arr[0].ToString(), arr[1].ToString());
                        if (dt_applydatadetail.Rows.Count > 0)
                        {
                            //if (dt_applydatadetail.Rows[0]["abd_databelongcompidno"].ToString() == Session["compidno"].ToString())
                            //{
                                BindData();

                                dt_applydatadetail.Dispose();
                            //}
                            //else
                            //{
                            //    dt_applydatadetail.Dispose();
                            //    Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                            //    Response.End();
                            //}
                        }
                        else
                        {
                            dt_applydatadetail.Dispose();
                            Response.Write("發生取不到申請編號錯誤，系統將導回首頁！！");
                            Response.Redirect("../index.aspx");
                        }
                    }
                    else
                    {

                        Response.Write("發生取不到申請編號錯誤，系統將導回首頁！！");
                        Response.Redirect("../index.aspx");
                    }
                }
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }
    }

    #region 更改的麵包屑傳入的參數
    protected void Page_LoadComplete(object sender, EventArgs e)
    {
        ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text = ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text.Replace("applydatamt/applybasedata_detail.aspx", "applydatamt/applybasedata_detail.aspx?applyno=" + Server.UrlEncode(Request["applyno"].ToString()));
    }
    #endregion

    public void BindData()
    {
        Cryptography crypy = new Cryptography();
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        int keynum = crypy.getRadNum();
        string[] arr = crypy.wsApplybasedataDetailValid(keynum);
        string applyno = DeCode(Request["applyno"].ToString());
        string carcompomodelno = DeCode(Request["carcompomodelno"].ToString());       
        string s_title = "車型組( " + carcompomodelno + " ) - 噪音防制對策";
        //申請狀態資料
        DataTable dt_applystatus = wsApplyData.get_applystatus(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());            
        //車型名稱
        DataTable dt_carmodelnames = wsApplyData.get_Nv_carmodelnames(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());

        if (dt_carmodelnames.Rows.Count > 0)
        {
            //判斷是否為代表車

            //代表車           
            foreach (DataRow dr in dt_carmodelnames.Rows)
            {
                if (dr["cmdg_berepresentativevehicle"].ToString().Trim().Equals("Y", StringComparison.CurrentCultureIgnoreCase))
                {
                    dt_carmodelnames.Rows[0]["fullcarstylename"] = dt_carmodelnames.Rows[0]["fullcarstylename"].ToString().Trim() + "(代表車)";
                }
            }
        }

        //噪音防制對策
        DataTable dt_noisedefenseplan = wsApplyData.get_Nv_noisedefenseplan_lists(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
       /*取得流水號，產生Barcode
        string barcodeno = wsApplyData.getNewBarCodeNo(arr[0].ToString(), arr[1].ToString());
        byte[] barcodeimg = getBarCode.BitmapDataFromBitmap(getBarCode.GetCode39(barcodeno), ImageFormat.Jpeg);
        wsApplyData.Update_Nv_applybasedataBarcode(applyno, barcodeno, barcodeimg, arr[0].ToString(), arr[1].ToString());
        */
        //Barcode
        DataTable dt_applybasebarcode = wsApplyData.get_Nv_Applybasebarcode(applyno, arr[0].ToString(), arr[1].ToString());

        //開啟Viewer顯示   
        rpv_MyData.Visible = true;        
        //指定Viewer的rdlc檔   
        rpv_MyData.LocalReport.ReportPath = Server.MapPath("rpt_noisedefenseplans.rdlc");
        //清掉之前的狀態，重新把資料丟進去，並做Viewer的Refresh   
        rpv_MyData.LocalReport.DataSources.Clear();
        rpv_MyData.LocalReport.EnableExternalImages = true; //圖檔
        rpv_MyData.LocalReport.DataSources.Add(new ReportDataSource("DataSet_get_applybasebarcode", dt_applybasebarcode));
        rpv_MyData.LocalReport.DataSources.Add(new ReportDataSource("DataSet_applystatus", dt_applystatus));
        rpv_MyData.LocalReport.DataSources.Add(new ReportDataSource("DataSet_noisedefenseplan", dt_noisedefenseplan));
        rpv_MyData.LocalReport.DataSources.Add(new ReportDataSource("DataSet_carmodelnames", dt_carmodelnames));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_title", s_title));
        rpv_MyData.LocalReport.Refresh();
        
    }
}