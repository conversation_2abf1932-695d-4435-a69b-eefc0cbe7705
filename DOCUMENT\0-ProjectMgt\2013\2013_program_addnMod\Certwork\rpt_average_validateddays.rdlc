<?xml version="1.0" encoding="utf-8"?>
<Report xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner" xmlns="http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition">
  <DataSources>
    <DataSource Name="certificatedDataSet">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>79c58acb-e992-46f3-b739-e000e8c5d41d</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="DT_average_validateddays">
      <Fields>
        <Field Name="FristValidDays">
          <DataField>FristValidDays</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="vp_applyno">
          <DataField>vp_applyno</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="abd_vechiletype">
          <DataField>abd_vechiletype</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="cpm_applytype">
          <DataField>cpm_applytype</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="vp_execdate">
          <DataField>vp_execdate</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="cpm_carcompomodelno">
          <DataField>cpm_carcompomodelno</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ApplyStatusCn">
          <DataField>ApplyStatusCn</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="abd_validatechargeaccount">
          <DataField>abd_validatechargeaccount</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="vp_execaccount">
          <DataField>vp_execaccount</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="workDay">
          <DataField>workDay</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="tmp_vechile">
          <DataField>tmp_vechile</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="at_desc">
          <DataField>at_desc</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="AccountCn">
          <DataField>AccountCn</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="wd_tpye">
          <DataField>wd_tpye</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ai_username">
          <DataField>ai_username</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>certificatedDataSet</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <rd:DataSetInfo>
        <rd:DataSetName>certificatedDataSet</rd:DataSetName>
        <rd:SchemaPath>D:\Olivia\noisevalidation\PROGRAM\webRoot\App_Code\DataSet\certificatedDataSet.xsd</rd:SchemaPath>
        <rd:TableName>average_validateddays_DataTable</rd:TableName>
        <rd:TableAdapterFillMethod />
        <rd:TableAdapterGetDataMethod />
        <rd:TableAdapterName />
      </rd:DataSetInfo>
    </DataSet>
  </DataSets>
  <Body>
    <ReportItems>
      <Tablix Name="Tablix2">
        <TablixCorner>
          <TablixCornerRows>
            <TablixCornerRow>
              <TablixCornerCell>
                <CellContents>
                  <Textbox Name="Textbox61">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>初審人員</Value>
                            <Style />
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox61</rd:DefaultName>
                    <Style>
                      <Border>
                        <Color>LightGrey</Color>
                        <Style>Solid</Style>
                      </Border>
                      <BackgroundColor>#e0eabf</BackgroundColor>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </CellContents>
              </TablixCornerCell>
              <TablixCornerCell>
                <CellContents>
                  <Textbox Name="Textbox10">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>車輛種類</Value>
                            <Style />
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox10</rd:DefaultName>
                    <Style>
                      <Border>
                        <Color>LightGrey</Color>
                        <Style>Solid</Style>
                      </Border>
                      <BackgroundColor>#e0eabf</BackgroundColor>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </CellContents>
              </TablixCornerCell>
              <TablixCornerCell>
                <CellContents>
                  <Textbox Name="Textbox11">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>申請型式</Value>
                            <Style />
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox11</rd:DefaultName>
                    <Style>
                      <Border>
                        <Color>LightGrey</Color>
                        <Style>Solid</Style>
                      </Border>
                      <BackgroundColor>#e0eabf</BackgroundColor>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </CellContents>
              </TablixCornerCell>
            </TablixCornerRow>
          </TablixCornerRows>
        </TablixCorner>
        <TablixBody>
          <TablixColumns>
            <TablixColumn>
              <Width>0.59055in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.59055in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.59055in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.59055in</Width>
            </TablixColumn>
          </TablixColumns>
          <TablixRows>
            <TablixRow>
              <Height>0.23622in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox9">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=CountDistinct(Fields!cpm_carcompomodelno.Value)</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox9</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox12">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=CountDistinct(Fields!cpm_carcompomodelno.Value)</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox9</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox47">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=IIf(CountDistinct(Fields!cpm_carcompomodelno.Value)=0,"0",Max(Fields!FristValidDays.Value))</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox42</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox48">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=IIf(CountDistinct(Fields!cpm_carcompomodelno.Value)=0,"0",Min(Fields!FristValidDays.Value))</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox44</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
          </TablixRows>
        </TablixBody>
        <TablixColumnHierarchy>
          <TablixMembers>
            <TablixMember>
              <TablixHeader>
                <Size>0.23622in</Size>
                <CellContents>
                  <Textbox Name="Textbox15">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>車型數</Value>
                            <Style />
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox15</rd:DefaultName>
                    <Style>
                      <Border>
                        <Color>LightGrey</Color>
                        <Style>Solid</Style>
                      </Border>
                      <BackgroundColor>#e0eabf</BackgroundColor>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </CellContents>
              </TablixHeader>
            </TablixMember>
            <TablixMember>
              <Group Name="Group_workDay">
                <GroupExpressions>
                  <GroupExpression>=Fields!wd_tpye.Value</GroupExpression>
                </GroupExpressions>
              </Group>
              <SortExpressions>
                <SortExpression>
                  <Value>=Fields!wd_tpye.Value</Value>
                </SortExpression>
              </SortExpressions>
              <TablixMembers>
                <TablixMember>
                  <TablixHeader>
                    <Size>0.23622in</Size>
                    <CellContents>
                      <Textbox Name="workDay">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>=Switch(
Fields!wd_tpye.Value = "1","0~5",
Fields!wd_tpye.Value = "2","5~10",
Fields!wd_tpye.Value = "3","10~20",
Fields!wd_tpye.Value = "4","20~"
)</Value>
                                <Style />
                              </TextRun>
                            </TextRuns>
                            <Style>
                              <TextAlign>Center</TextAlign>
                            </Style>
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>workDay</rd:DefaultName>
                        <Style>
                          <Border>
                            <Color>LightGrey</Color>
                            <Style>Solid</Style>
                          </Border>
                          <BackgroundColor>#e0eabf</BackgroundColor>
                          <VerticalAlign>Middle</VerticalAlign>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                </TablixMember>
              </TablixMembers>
            </TablixMember>
            <TablixMember>
              <TablixHeader>
                <Size>0.23622in</Size>
                <CellContents>
                  <Textbox Name="Textbox45">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>最久</Value>
                            <Style />
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox41</rd:DefaultName>
                    <Style>
                      <Border>
                        <Color>LightGrey</Color>
                        <Style>Solid</Style>
                      </Border>
                      <BackgroundColor>#e0eabf</BackgroundColor>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </CellContents>
              </TablixHeader>
            </TablixMember>
            <TablixMember>
              <TablixHeader>
                <Size>0.23622in</Size>
                <CellContents>
                  <Textbox Name="Textbox46">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>最快</Value>
                            <Style />
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox43</rd:DefaultName>
                    <Style>
                      <Border>
                        <Color>LightGrey</Color>
                        <Style>Solid</Style>
                      </Border>
                      <BackgroundColor>#e0eabf</BackgroundColor>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </CellContents>
              </TablixHeader>
            </TablixMember>
          </TablixMembers>
        </TablixColumnHierarchy>
        <TablixRowHierarchy>
          <TablixMembers>
            <TablixMember>
              <Group Name="Group_account">
                <GroupExpressions>
                  <GroupExpression>=Fields!AccountCn.Value</GroupExpression>
                </GroupExpressions>
                <PageBreak>
                  <BreakLocation>Between</BreakLocation>
                </PageBreak>
              </Group>
              <SortExpressions>
                <SortExpression>
                  <Value>=Fields!AccountCn.Value</Value>
                </SortExpression>
              </SortExpressions>
              <TablixHeader>
                <Size>0.98425in</Size>
                <CellContents>
                  <Textbox Name="AccountCn">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>=Fields!AccountCn.Value</Value>
                            <Style />
                          </TextRun>
                        </TextRuns>
                        <Style />
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>AccountCn</rd:DefaultName>
                    <Style>
                      <Border>
                        <Color>LightGrey</Color>
                        <Style>Solid</Style>
                      </Border>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </CellContents>
              </TablixHeader>
              <TablixMembers>
                <TablixMember>
                  <Group Name="VechileType">
                    <GroupExpressions>
                      <GroupExpression>=Fields!tmp_vechile.Value</GroupExpression>
                    </GroupExpressions>
                  </Group>
                  <SortExpressions>
                    <SortExpression>
                      <Value>=Switch(
Fields!tmp_vechile.Value = "G","A",
Fields!tmp_vechile.Value = "M","B",
Fields!tmp_vechile.Value = "D","C",
Fields!tmp_vechile.Value = "E","D"
)</Value>
                    </SortExpression>
                  </SortExpressions>
                  <TablixHeader>
                    <Size>0.98425in</Size>
                    <CellContents>
                      <Textbox Name="abd_vechiletype">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>=Switch(
Fields!tmp_vechile.Value = "G","汽車",
Fields!tmp_vechile.Value = "M","機車",
Fields!tmp_vechile.Value = "D","柴油車",
Fields!tmp_vechile.Value = "E","電動車"
)</Value>
                                <Style />
                              </TextRun>
                            </TextRuns>
                            <Style />
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>abd_vechiletype</rd:DefaultName>
                        <Style>
                          <Border>
                            <Color>LightGrey</Color>
                            <Style>Solid</Style>
                          </Border>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                  <TablixMembers>
                    <TablixMember>
                      <Group Name="ApplyStatusDesc">
                        <GroupExpressions>
                          <GroupExpression>=Fields!at_desc.Value</GroupExpression>
                        </GroupExpressions>
                      </Group>
                      <SortExpressions>
                        <SortExpression>
                          <Value>=Switch(
Fields!at_desc.Value = "新車型","A",
Fields!at_desc.Value = "沿用","B",
Fields!at_desc.Value = "延伸","C",
Fields!at_desc.Value = "修改","D"
)</Value>
                        </SortExpression>
                      </SortExpressions>
                      <TablixHeader>
                        <Size>0.98425in</Size>
                        <CellContents>
                          <Textbox Name="at_desc">
                            <CanGrow>true</CanGrow>
                            <KeepTogether>true</KeepTogether>
                            <Paragraphs>
                              <Paragraph>
                                <TextRuns>
                                  <TextRun>
                                    <Value>=Fields!at_desc.Value</Value>
                                    <Style />
                                  </TextRun>
                                </TextRuns>
                                <Style />
                              </Paragraph>
                            </Paragraphs>
                            <rd:DefaultName>at_desc</rd:DefaultName>
                            <Style>
                              <Border>
                                <Color>LightGrey</Color>
                                <Style>Solid</Style>
                              </Border>
                              <PaddingLeft>2pt</PaddingLeft>
                              <PaddingRight>2pt</PaddingRight>
                              <PaddingTop>2pt</PaddingTop>
                              <PaddingBottom>2pt</PaddingBottom>
                            </Style>
                          </Textbox>
                        </CellContents>
                      </TablixHeader>
                    </TablixMember>
                  </TablixMembers>
                </TablixMember>
              </TablixMembers>
            </TablixMember>
          </TablixMembers>
        </TablixRowHierarchy>
        <RepeatColumnHeaders>true</RepeatColumnHeaders>
        <FixedColumnHeaders>true</FixedColumnHeaders>
        <DataSetName>DT_average_validateddays</DataSetName>
        <Top>0.62653cm</Top>
        <Left>0.44133cm</Left>
        <Height>1.2cm</Height>
        <Width>13.49997cm</Width>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
        </Style>
      </Tablix>
    </ReportItems>
    <Height>0.92708in</Height>
    <Style />
  </Body>
  <Width>6.10494in</Width>
  <Page>
    <PageHeight>29.7cm</PageHeight>
    <PageWidth>21cm</PageWidth>
    <LeftMargin>2cm</LeftMargin>
    <RightMargin>2cm</RightMargin>
    <TopMargin>2cm</TopMargin>
    <BottomMargin>2cm</BottomMargin>
    <ColumnSpacing>0.13cm</ColumnSpacing>
    <Style />
  </Page>
  <rd:ReportID>81a12f11-e14c-468d-864e-635e6941c540</rd:ReportID>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
</Report>