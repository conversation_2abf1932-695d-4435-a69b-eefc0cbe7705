﻿<%@ Page Title="" Language="C#" MasterPageFile="~/masterpage/MasterPage.master" AutoEventWireup="true"
    CodeFile="accountmt.aspx.cs" Inherits="accountmt_accountmt" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script language="javascript" type="text/javascript">
        function OpenPostNo() {
            window.open('http://www.post.gov.tw/post/internet/f_searchzone/index.jsp?ID=190102', '', 'dialogWidth:400px;dialogHeight:400px;');
            return false;
        }
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="fullheader">
        <table width="100%" cellspacing="0" cellpadding="0" class="fullheaderinfo">
            <tr>
                <td valign="top">
                    <b>使用者資料變更</b>
                </td>
                <td align="right" valign="top" class="font_loginInfo">
                </td>
            </tr>
        </table>
    </div>
    <!--{* fullheader end *}-->
    <div class="fullcontent">
        <div class="formstyle">
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="font_fullcontent">
                <tr>
                    <td colspan="4">
                        <span class="font_fulltitle"><b>帳號申請人資料</b></span>
                    </td>
                </tr>
                <tr>
                    <td align="right" class="style1">
                        申請人姓名：
                    </td>
                    <td>
                        <asp:Label ID="lbl_username" runat="server"></asp:Label>
                    </td>
                    <td colspan="2">
                        <asp:Button ID="btn_Sync" runat="server" Text="申請同步" OnClick="btn_Sync_Click"
                             onmouseout="this.className='btn_mouseout'" Enabled="False"/>
                    </td>
                </tr>
                <tr>
                    <td align="right" class="style1">
                        <span style="color: Red;">*</span>申請人電話：
                    </td>
                    <td>
                        <asp:TextBox ID="txt_usertel" runat="server" MaxLength="50" CssClass="inputTypeS"></asp:TextBox>
                        <asp:RequiredFieldValidator ID="rfusertel" runat="server" ErrorMessage="申請人電話不能空白"
                            ControlToValidate="txt_usertel" Display="None"></asp:RequiredFieldValidator>
                    </td>
                    <td align="right">
                        申請人傳真：
                    </td>
                    <td>
                        <asp:TextBox ID="txt_userfax" runat="server" MaxLength="50" CssClass="inputTypeS"></asp:TextBox>
                    </td>
                </tr>
                <tr>
                    <td align="right" class="style1">
                        申請人行動電話：
                    </td>
                    <td>
                        <asp:TextBox ID="txt_cellphone" runat="server" MaxLength="50" CssClass="inputTypeS"></asp:TextBox>
                    </td>
                    <td align="right">
                        <span style="color: Red;">*</span>申請人電子郵件地址：
                    </td>
                    <td>
                        <asp:TextBox ID="txt_useremail" runat="server" Width="217px" MaxLength="80" CssClass="inputTypeM"></asp:TextBox>
                        <asp:RequiredFieldValidator ID="rfuseremail" runat="server" ErrorMessage="申請人電子郵件地址不能空白"
                            Display="None" ControlToValidate="txt_useremail"></asp:RequiredFieldValidator>
                        <asp:RegularExpressionValidator ID="revEmail" runat="server" ControlToValidate="txt_useremail"
                            ValidationExpression="\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*" ErrorMessage="您輸入的 Mail 格式有誤"></asp:RegularExpressionValidator>
                    </td>
                </tr>
                <tr>
                    <td align="right" class="style1">
                        <span style="color: Red;">*</span>申請人郵寄地址：
                    </td>
                    <td colspan="3">
                        縣市別：<asp:DropDownList ID="ddl_useraddrcity" runat="server" AutoPostBack="True" OnSelectedIndexChanged="ddl_useraddrcity_SelectedIndexChanged"
                            CssClass="inputType">
                        </asp:DropDownList>
                        鄉鎮市區別：<asp:DropDownList ID="ddl_useraddrlocalarea" runat="server" AutoPostBack="True"
                            OnSelectedIndexChanged="ddl_useraddrlocalarea_SelectedIndexChanged" CssClass="inputType">
                        </asp:DropDownList>
                        郵遞區號：<asp:Label ID="lbl_useraddrpostno" runat="server" Width="55px"></asp:Label>
                        <asp:LinkButton ID="lbtn_userpostno" runat="server" Text="查詢" OnClientClick="OpenPostNo();"
                            CausesValidation="False"></asp:LinkButton><br />
                        <asp:TextBox ID="txt_useraddr" runat="server" Width="449px" MaxLength="120" CssClass="inputTypeM"></asp:TextBox>(請輸入路巷弄號)
                        <asp:RequiredFieldValidator ID="rfuseraddr" runat="server" ErrorMessage="申請人郵寄地址不能空白"
                            Display="None" ControlToValidate="txt_useraddr"></asp:RequiredFieldValidator>
                    </td>
                </tr>
                <tr>
                    <td align="right" class="style1">
                        最後異動時間：
                    </td>
                    <td colspan="3">
                        <asp:Label ID="lbl_lastchangedate" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td colspan="4" align="center">
                        <asp:Button ID="btn_Save" runat="server" Text="存檔" OnClick="btn_Save_Click" CssClass="btn_mouseout"
                            onmouseover="this.className='btn_mouseover'" onmouseout="this.className='btn_mouseout'" />
                    </td>
                </tr>
            </table>
        </div>
    </div>
    <!--{* fullcontent end *}-->
    <div class="fullfooter">
    </div>
    <!--{* content end *}-->
    <asp:ValidationSummary ID="ValidationSummary1" runat="server" ShowMessageBox="True"
        ShowSummary="False" />
</asp:Content>
