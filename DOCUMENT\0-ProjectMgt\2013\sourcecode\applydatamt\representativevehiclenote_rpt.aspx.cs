﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data.SqlClient;
using System.Data;
using Microsoft.Reporting.WebForms;

using System.Configuration;
using System.Xml;
using Common;
using System.IO;

using System.Drawing;
using System.Drawing.Imaging;

public partial class applydatamt_representativevehiclenote_rpt : BaseAdminPage
{
    protected void Page_Load(object sender, EventArgs e)
    {
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = '../accountmt/index.aspx';</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" &&
                (
                (
                GetAgentAccountGrpList().Contains("epa_adm") || GetAgentAccountGrpList().Contains("ac_adm") || GetAgentAccountGrpList().Contains("tc_adm") || GetAgentAccountGrpList().Contains("rv_adm") || GetAgentAccountGrpList().Contains("rs_adm") || GetAgentAccountGrpList().Contains("fis_adm") || GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("pg_adm")
                )
                ||
                (
                  (GetAgentAccountGrpList().Contains("cga_adm") || GetAgentAccountGrpList().Contains("cam_adm")) && CheckSingleApplyDataAccessRight(DeCode(Request["applyno"].ToString()))
                )
                )
                )
            {
                if (!IsPostBack)
                {
                    if (Request["applyno"].ToString() != "")
                    {
                        Cryptography crypy = new Cryptography();
                        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
                        int keynum = crypy.getRadNum();
                        string[] arr = crypy.wsApplybasedataDetailValid(keynum);
                        DataTable dt_applydatadetail = wsApplyData.get_applybasedate_detail(crypy.DeCode(Request["applyno"].ToString()), arr[0].ToString(), arr[1].ToString());
                        if (dt_applydatadetail.Rows.Count > 0)
                        {
                            //if (dt_applydatadetail.Rows[0]["abd_databelongcompidno"].ToString() == Session["compidno"].ToString())
                            //{
                            BindData();

                            dt_applydatadetail.Dispose();
                            //}
                            //else
                            //{
                            //    dt_applydatadetail.Dispose();
                            //    Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                            //    Response.End();
                            //}
                        }
                        else
                        {
                            dt_applydatadetail.Dispose();
                            Response.Write("發生取不到申請編號錯誤，系統將導回首頁！！");
                            Response.Redirect("../index.aspx");
                        }
                    }
                    else
                    {

                        Response.Write("發生取不到申請編號錯誤，系統將導回首頁！！");
                        Response.Redirect("../index.aspx");
                    }
                }
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }
    }
    #region 更改的麵包屑傳入的參數
    protected void Page_LoadComplete(object sender, EventArgs e)
    {
        ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text = ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text.Replace("applydatamt/applybasedata_detail.aspx", "applydatamt/applybasedata_detail.aspx?applyno=" + Server.UrlEncode(Request["applyno"].ToString()));
    }
    #endregion

    public void BindData()
    {
        Cryptography crypy = new Cryptography();
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        int keynum = crypy.getRadNum();
        string[] arr = crypy.wsApplybasedataDetailValid(keynum);
        string applyno = DeCode(Request["applyno"].ToString());
        string carcompomodelno = DeCode(Request["carcompomodelno"].ToString());
        string s_carengmodelname = "-";
        string s_carbodyno = "-";
        string s_engineno = "-";
        string s_speedupgear = " ";
        string s_speed = " ";
        string s_isimport = " ";
        string s_stayrpmparam = "-";
        string s_stayrpmcondi = "-";
        string s_speedupcondi = "-";
        string s_title = "車型組( " + carcompomodelno + " ) - 代表車選定審查表";
        /*取得流水號，產生Barcode
        string barcodeno = wsApplyData.getNewBarCodeNo(arr[0].ToString(), arr[1].ToString());
        byte[] barcodeimg = getBarCode.BitmapDataFromBitmap(getBarCode.GetCode39(barcodeno), ImageFormat.Jpeg);
        wsApplyData.Update_Nv_applybasedataBarcode(applyno, barcodeno, barcodeimg, arr[0].ToString(), arr[1].ToString());
         */
        /*檔案
        imgPIC.ImageUrl = "../accountmt/ImageHandler.ashx?File=" + Server.UrlEncode(EnCode(applyno));
        picSet pic = new picSet();
        bool result = pic.IsImageOverSet(imgPIC.ImageUrl, applyno);

        if (result)
        {
            imgPIC.Attributes.Add("Width", pic.picWidth);
            imgPIC.Attributes.Add("Height", pic.picHeight);
        }
        */

        //Barcode
        DataTable dt_applybasebarcode = wsApplyData.get_Nv_Applybasebarcode(applyno, arr[0].ToString(), arr[1].ToString());
        //車型組車型資料
        DataTable dt_carmodeldatarpt = wsApplyData.get_Nv_carmodeldatarpt(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
        if (dt_carmodeldatarpt.Rows.Count > 0)
        {
            //代表車           
            foreach (DataRow dr in dt_carmodeldatarpt.Rows)
            {
                if (dr["cmdg_berepresentativevehicle"].ToString().Trim().Equals("Y", StringComparison.CurrentCultureIgnoreCase))
                {
                    s_carengmodelname = dr["fullcarstylename"].ToString();                   
                }
            }
        }
        //車身編號/引擎號碼
        DataTable dt_latestcarbodyno = wsApplyData.get_Nv_latestcarbodyno(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
        if (dt_latestcarbodyno.Rows.Count > 0)
        {
            s_carbodyno = dt_latestcarbodyno.Rows[0]["carbodyno"].ToString().Trim() == "" ? "-" : dt_latestcarbodyno.Rows[0]["carbodyno"].ToString().Trim();
            s_engineno = dt_latestcarbodyno.Rows[0]["engineno"].ToString().Trim() == "" ? "-" : dt_latestcarbodyno.Rows[0]["engineno"].ToString().Trim(); 
        }

        DataTable dt_combinecarlist = wsApplyData.get_Nv_combinecarlist(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());

        if (dt_combinecarlist.Rows[0]["abd_isimport"].Equals("1"))
        {
            s_isimport = "製造地區";
        }
        else
        {
            s_isimport = "進口地區";
        }

        #region 資料狀態-當申請型式 不為新申請 且有 50,55資料 取得上次申請代表車資料 否 擇讀取自己本身填寫資料
        DataTable dt_applystatus = wsApplyData.get_applystatus(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
        bool b_IsApplied = wsApplyData.IsApplied(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
        if (dt_applystatus.Rows.Count > 0 && !(dt_applystatus.Rows[0]["ct_aps_desc"].Equals("新申請")) && b_IsApplied)
        {

            DataTable dt_unNewApplyData = wsApplyData.get_unNewApplyData(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
            if (dt_unNewApplyData.Rows.Count > 0)
            {
                applyno = dt_unNewApplyData.Rows[0]["cmdg_applyno"].ToString();
                carcompomodelno = dt_unNewApplyData.Rows[0]["cmdg_carcompomodelno"].ToString();
            }
        }
        #endregion
        //噪音測定報告
        DataTable dt_representativevehicle = wsApplyData.get_Nv_representativevehiclerpt(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
        if (dt_representativevehicle.Rows.Count > 0)
        {
            //測定報告

            s_stayrpmparam = dt_representativevehicle.Rows[0]["ntr_stayrpmparam"].ToString().Trim() == "" ? " " : dt_representativevehicle.Rows[0]["ntr_stayrpmparam"].ToString().Trim();
            s_stayrpmcondi = dt_representativevehicle.Rows[0]["ntr_stayrpmcondi"].ToString().Trim() == "" ? " " : dt_representativevehicle.Rows[0]["ntr_stayrpmcondi"].ToString().Trim();
            s_speedupcondi = dt_representativevehicle.Rows[0]["ntr_speedupcondi"].ToString().Trim() == "" ? " " : dt_representativevehicle.Rows[0]["ntr_speedupcondi"].ToString().Trim();
            DataTable dt_vehiclespeedrpt = new DataTable();
            if (dt_representativevehicle.Rows.Count > 0)
            {
                //加速測定數值
                dt_vehiclespeedrpt = wsApplyData.get_Nv_vehiclespeedrpt(dt_representativevehicle.Rows[0]["ntr_testrptno"].ToString(), dt_representativevehicle.Rows[0]["ntr_carmodelno"].ToString(), arr[0].ToString(), arr[1].ToString());
                foreach (DataRow dr in dt_vehiclespeedrpt.Rows)
                {
                    s_speedupgear += dr["trsui_speedupgear"].ToString().Trim() == "" ? " " : dr["trsui_speedupgear"].ToString().Trim() + ",";
                    s_speed += dr["trsui_speed"].ToString().ToString().Trim() == "" ? " " : dr["trsui_speed"].ToString().ToString().Trim() + ",";
                }

            }
        }
        #region 去逗號
        s_speedupgear = s_speedupgear.TrimEnd(',');        
        s_speed = s_speed.TrimEnd(',');        
        #endregion

        //開啟Viewer顯示   
        rpv_MyData.Visible = true;
        //指定Viewer的rdlc檔   
        rpv_MyData.LocalReport.ReportPath = Server.MapPath("rpt_representati_vevehicle_note.rdlc");
        //清掉之前的狀態，重新把資料丟進去，並做Viewer的Refresh   
        rpv_MyData.LocalReport.DataSources.Clear();
        rpv_MyData.LocalReport.DataSources.Add(new ReportDataSource("DataSet_get_carmodeldatarpt", dt_carmodeldatarpt));
        rpv_MyData.LocalReport.DataSources.Add(new ReportDataSource("DataSet_get_representativevehiclerpt", dt_representativevehicle));
        rpv_MyData.LocalReport.DataSources.Add(new ReportDataSource("DataSet_get_combinecarlist", dt_combinecarlist));
        rpv_MyData.LocalReport.DataSources.Add(new ReportDataSource("DataSet_get_applybasebarcode", dt_applybasebarcode));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_speedupgear", s_speedupgear));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_speed", s_speed));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_rcarengmodelname", s_carengmodelname));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_rcarbodyno", s_carbodyno));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_rengineno", s_engineno));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_stayrpmparam", s_stayrpmparam));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_stayrpmcondi", s_stayrpmcondi));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_speedupcondi", s_speedupcondi));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_isimport", s_isimport));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_title", s_title));
        rpv_MyData.LocalReport.Refresh();






    }

}