﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.IO;
using System.Text;

using System.Xml;
using Common;

public partial class applydatamt_fixcatalog_editor : BaseAdminPage
{
    string command = string.Empty;
    string fixid = string.Empty;
    protected void Page_Load(object sender, EventArgs e)
    {
        #region TextBox在Textmode=MultiLine狀態下MaxLength的字數限制無效
        txt_fixitem.Attributes.Add("maxlength", "100");
        txt_fixitem.Attributes.Add("onkeyup", "return ismaxlength(this)");

        txt_beforefix.Attributes.Add("maxlength", "100");
        txt_beforefix.Attributes.Add("onkeyup", "return ismaxlength(this)");

        txt_afterfix.Attributes.Add("maxlength", "100");
        txt_afterfix.Attributes.Add("onkeyup", "return ismaxlength(this)");
        #endregion

        command = Request.QueryString["command"];
        fixid = Request.QueryString["fixid"];

        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = '../accountmt/index.aspx';</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" && (GetAgentAccountGrpList().Contains("cga_adm") || GetAgentAccountGrpList().Contains("cam_adm") || GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("pg_adm")))
            {
                if (!IsPostBack)
                {
                    if (Request["applyno"].ToString() != "")
                    {
                        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
                        Cryptography crypy = new Cryptography();
                        int keynum = crypy.getRadNum();
                        string[] arr = crypy.wsApplybasedataDetailValid(keynum);
                        DataTable dt_applydatadetail = wsApplyData.get_applybasedate_detail(crypy.DeCode(Request["applyno"].ToString()), arr[0].ToString(), arr[1].ToString());
                        if (dt_applydatadetail.Rows.Count > 0)
                        {
                            if (dt_applydatadetail.Rows[0]["abd_databelongcompidno"].ToString() == Session["compidno"].ToString())
                            {
                                BindData();
                                dt_applydatadetail.Dispose();
                            }
                            else
                            {
                                dt_applydatadetail.Dispose();
                                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                                Response.End();
                            }
                        }
                        else
                        {
                            dt_applydatadetail.Dispose();
                            Response.Write("<script languge='javascript'>alert('發生取不到申請編號錯誤，系統將導回首頁！！');window.location.href='../index.aspx'</script>");
                            Response.End();
                        }
                    }
                    else
                    {
                        Response.Write("<script languge='javascript'>alert('發生取不到申請編號錯誤，系統將導回首頁！！');window.location.href='../index.aspx'</script>");
                        Response.End();
                    }
                }
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }

        }
    }

    #region 申請型式為沿用、不異動、撤件，則不可填寫並導回查詢頁面
    public void CheckApplyType(string sApplyType)
    {
        string compidnoE = Session["compidno"].ToString(); //公司統編
        DataView dv = new MailParaControl().GetApplyBaseData(lbl_applyno.Text);
        string applyno = DeCode(Request["applyno"].ToString());
        string carcompomodelno = DeCode(Request["carcompomodelno"].ToString());//車型組編號
        string EngineFamily = dv[0]["EngineFamily"].ToString(); //引擎族
        Cryptography crypy = new Cryptography();
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        int keynum = getRadNum();
        string[] arr = wsApplybasedataDetailValid(keynum);
        //延伸、修改 沿用 且非系統第一筆不可編輯;沿用(非第一筆)、不異動、撤銷不可編輯         
        if (new FirstProcessControl().IsValidationApplyType(applyno, carcompomodelno, "6") || new FirstProcessControl().IsValidationApplyType(applyno, carcompomodelno, "9"))
        {
            Response.Write("<script languge='javascript'>alert('申請型式為「" + sApplyType + "」，不可編輯！！');window.location.href='carcompomodelList.aspx?applyno=" + Server.UrlEncode(Request["applyno"].ToString()) + "'</script>");
            Response.End();
        }
    }
    #endregion

    #region 判斷資料狀態不為填寫中、補件、拒件，則不可填寫並導回查詢頁面
    public void CheckApplyStatus(string sStatusCode, string sStatusDesc)
    {
        string compidnoE = Session["compidno"].ToString(); //公司統編
        DataView dv = new MailParaControl().GetApplyBaseData(lbl_applyno.Text);
        string carcompomodelno = DeCode(Request["carcompomodelno"].ToString());//車型組編號
        string EngineFamily = dv[0]["EngineFamily"].ToString(); //引擎族
        Cryptography crypy = new Cryptography();
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        int keynum = getRadNum();
        string[] arr = wsApplybasedataDetailValid(keynum);

        if (!(sStatusCode.Equals("10") || sStatusCode.Equals("40") || sStatusCode.Equals("90")))
        {
            Response.Write("<script languge='javascript'>alert('資料狀態為「" + sStatusDesc + "」，不可編輯！！');window.location.href='carcompomodelList.aspx?applyno=" + Server.UrlEncode(Request["applyno"].ToString()) + "'</script>");
            Response.End();
        }

    }
    #endregion

    public void BindData()
    {
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        Cryptography crypy = new Cryptography();
        string applyno = DeCode(Request["applyno"].ToString());
        string carcompomodelno = DeCode(Request["carcompomodelno"].ToString());
        string strStatusCode = "";
        string strStatusDesc = "";
       
        lbl_applyno.Text = applyno;
        lbl_carcompomodelno.Text = carcompomodelno;
        int keynum = crypy.getRadNum();
        string[] arr = crypy.wsApplybasedataDetailValid(keynum);
        //申請型式
        DataTable dt_applyno = wsApplyData.get_applyno(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
        if (dt_applyno.Rows.Count > 0)
        {
            lbl_applytype.Text = dt_applyno.Rows[0]["ct_at_desc"].ToString();
            CheckApplyType(dt_applyno.Rows[0]["ct_at_desc"].ToString());
        }
        //資料狀態
        DataTable dt_applystatus = wsApplyData.get_applystatus(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
        if (dt_applystatus.Rows.Count > 0)
        {
            strStatusDesc = dt_applystatus.Rows[0]["ct_aps_desc"].ToString();
            strStatusCode = dt_applystatus.Rows[0]["abd_applystatus"].ToString();
            CheckApplyStatus(strStatusCode, strStatusDesc);

        }

        if (command == "edit")
        {
            DataTable dt_fixcataloglist = wsApplyData.get_Nv_fixcataloglist(DeCode(fixid), arr[0].ToString(), arr[1].ToString());
            txt_fixitem.Text = dt_fixcataloglist.Rows[0]["fc_fixitem"].ToString();
            txt_beforefix.Text = dt_fixcataloglist.Rows[0]["fc_beforefix"].ToString();
            txt_afterfix.Text = dt_fixcataloglist.Rows[0]["fc_afterfix"].ToString();
            btn_Delet.Visible = true;
        }
    }
    protected void btn_Save_Click(object sender, EventArgs e)
    {
        #region 特殊字元判斷-沒有MasterPage
        foreach (Control ctl in Page.Form.Controls)
        {
            Control content = (Control)ctl;
            foreach (Control c in content.Controls)
            {
                if (c is TextBox)
                {
                    TextBox objTextBox = default(TextBox);
                    objTextBox = (TextBox)c;
                    if ((tbWord(objTextBox.Text)))
                    {
                        MessageBox.Show(objTextBox.Text + ":含有系統不允許的特殊字元");
                        return;
                    }
                }
            }
        }
        #endregion

        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        Cryptography crypy = new Cryptography();
        int keynum = crypy.getRadNum();
        string[] arr = crypy.wsApplybasedataDetailValid(keynum);
        string applyno = DeCode(Request["applyno"].ToString());
        string carcompomodelno = DeCode(Request["carcompomodelno"].ToString());

        try
        {

            if (command == "add")
            {

                if (txt_fixitem.Text.ToString().Trim() == "" && txt_beforefix.Text.ToString().Trim() == "" && txt_afterfix.Text.ToString().Trim() == "")
                {
                    MessageBox.Show("修正項目、修正前、修正後，不可都空白，請至少填寫一個欄位。\n");
                    return;
                }
                else
                {
                    wsApplyData.Insert_Nv_fixcatalog(applyno, carcompomodelno, txt_fixitem.Text.Trim(), txt_beforefix.Text.Trim(), txt_afterfix.Text.Trim(), GetAgentAccount(), arr[0].ToString(), arr[1].ToString());
                    BindData();
                }
            }
            else if (command == "edit")
            {
                if (txt_fixitem.Text.ToString().Trim() == "" && txt_beforefix.Text.ToString().Trim() == "" && txt_afterfix.Text.ToString().Trim() == "")
                {
                    MessageBox.Show("修正項目、修正前、修正後，不可都空白，請至少填寫一個欄位。\n");
                    return;
                }
                else
                {
                    wsApplyData.Update_Nv_fixcatalog(DeCode(fixid), txt_fixitem.Text.Trim(), txt_beforefix.Text.Trim(), txt_afterfix.Text.Trim(), GetAgentAccount(), arr[0].ToString(), arr[1].ToString());
                    BindData();

                }
            }
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", "<script>alert('修改成功!');location.href='fixcatalog.aspx?applyno=" + Server.UrlEncode(Request["applyno"].ToString()) + "&carcompomodelno=" + Server.UrlEncode(Request["carcompomodelno"].ToString()) + "'</script>");
        }
        catch (Exception ex)
        {

            ex.Message.ToString();
            MessageBox.Show("存檔失敗");
        }
    }

    protected void btn_Delet_Click(object sender, EventArgs e)
    {
        try
        {
            wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
            Cryptography crypy = new Cryptography();
            int keynum = crypy.getRadNum();
            string[] arr = crypy.wsApplybasedataDetailValid(keynum);
            wsApplyData.Del_Nv_fixcatalog(DeCode(fixid), arr[0].ToString(), arr[1].ToString());
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", "<script>alert('刪除成功!');location.href='fixcatalog.aspx?applyno=" + Server.UrlEncode(Request["applyno"].ToString()) + "&carcompomodelno=" + Server.UrlEncode(Request["carcompomodelno"].ToString()) + "'</script>");
        }
        catch (Exception ex)
        {
            ex.Message.ToString();
            MessageBox.Show("刪除失敗");
        }
    }
    protected void btn_Canl_Click(object sender, EventArgs e)
    {
        //Page.ClientScript.RegisterStartupScript(this.GetType(), "", "<script>location.href='fixcatalog.aspx?applyno=" + Server.UrlEncode(Request["applyno"].ToString()) + "&carcompomodelno=" + Server.UrlEncode(Request["carcompomodelno"].ToString()) + "'</script>");
        Response.Redirect(string.Format("fixcatalog.aspx?applyno={0}&carcompomodelno={1}&fixid={2}", Server.UrlEncode(Request["applyno"].ToString()), Server.UrlEncode(Request["carcompomodelno"].ToString()), false));
    }

}