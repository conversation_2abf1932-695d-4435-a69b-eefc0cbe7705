﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Xml;
using Common;
using System.IO;

public partial class applydatamt_representativevehiclenote_readnoly : BaseAdminPage
{
    public DataTable dt_list
    {
        get { return (DataTable)ViewState["_dt_list"]; }
        set { ViewState["_dt_list"] = value; }
    }
    protected void Page_Load(object sender, EventArgs e)
    {
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = '../accountmt/index.aspx';</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" &&
                (
                (
                GetAgentAccountGrpList().Contains("epa_adm") || GetAgentAccountGrpList().Contains("ac_adm") || GetAgentAccountGrpList().Contains("tc_adm") || GetAgentAccountGrpList().Contains("rv_adm") || GetAgentAccountGrpList().Contains("rs_adm") || GetAgentAccountGrpList().Contains("fis_adm") || GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("pg_adm")
                )
                ||
                (
                  (GetAgentAccountGrpList().Contains("cga_adm") || GetAgentAccountGrpList().Contains("cam_adm")) && CheckSingleApplyDataAccessRight(DeCode(Request["applyno"].ToString()))
                )
                )
                )
            {
                if (!IsPostBack)
                {
                    if (Request["applyno"].ToString() != "")
                    {
                        Cryptography crypy = new Cryptography();
                        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
                        int keynum = crypy.getRadNum();
                        string[] arr = crypy.wsApplybasedataDetailValid(keynum);
                        DataTable dt_applydatadetail = wsApplyData.get_applybasedate_detail(crypy.DeCode(Request["applyno"].ToString()), arr[0].ToString(), arr[1].ToString());
                        if (dt_applydatadetail.Rows.Count > 0)
                        {
                            //if (dt_applydatadetail.Rows[0]["abd_databelongcompidno"].ToString() == Session["compidno"].ToString())
                            //{
                            BindData();

                            dt_applydatadetail.Dispose();
                            //}
                            //else
                            //{
                            //    dt_applydatadetail.Dispose();
                            //    Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                            //    Response.End();
                            //}
                        }
                        else
                        {
                            dt_applydatadetail.Dispose();
                            Response.Write("發生取不到申請編號錯誤，系統將導回首頁！！");
                            Response.Redirect("../index.aspx");
                        }
                    }
                    else
                    {

                        Response.Write("發生取不到申請編號錯誤，系統將導回首頁！！");
                        Response.Redirect("../index.aspx");
                    }
                }
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }
    }

    #region 更改的麵包屑傳入的參數
    protected void Page_LoadComplete(object sender, EventArgs e)
    {
        ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text = ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text.Replace("applydatamt/applybasedata_detail.aspx", "applydatamt/applybasedata_detail.aspx?applyno=" + Server.UrlEncode(Request["applyno"].ToString()));
    }
    #endregion

    public void BindData()
    {
        Cryptography crypy = new Cryptography();
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        int keynum = crypy.getRadNum();
        string[] arr = crypy.wsApplybasedataDetailValid(keynum);
        string applyno = DeCode(Request["applyno"].ToString());
        string carcompomodelno = DeCode(Request["carcompomodelno"].ToString());
        lbl_applyno.Text = applyno;
        lbl_carcompomodelno.Text = carcompomodelno;

        #region 申請資料
        dt_list = wsApplyData.get_Nv_combinecarlist(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
        if (dt_list.Rows.Count > 0)
        {
            lbl_applystatus.Text = dt_list.Rows[0]["ct_aps_desc"].ToString();
            lbl_carstyleyear.Text = dt_list.Rows[0]["abd_carstyleyear"].ToString();
            lbl_databelongcompname.Text = dt_list.Rows[0]["abd_databelongcompname"].ToString();
            lbl_enginefamily.Text = dt_list.Rows[0]["abd_enginefamily"].ToString();

            lbl_carbrand.Text = dt_list.Rows[0]["cmdg_carbrand"].ToString();
            lbl_factoryname.Text = dt_list.Rows[0]["abd_factoryname"].ToString();
            //如果abd_isimport為1(國產)，cpm_importarea顯示在製造地區；若為2(進口)則cpm_importarea顯示在進口地區
            if (dt_list.Rows[0]["abd_isimport"].ToString() == "1")
            {
                //lbl_contacttel.Text = dt_list.Rows[0]["ct_cy_desc"].ToString();
                lbl_contacttel.Text = "中華民國";
            }
            else if (dt_list.Rows[0]["abd_isimport"].ToString() == "2")
            {
                lbl_importarea.Text = dt_list.Rows[0]["ct_cy_desc"].ToString();
            }
            else
            {
                lbl_contacttel.Text = "-";
                lbl_importarea.Text = "-";
            }


        }
        else
        {
            lbl_applystatus.Text = "-";
            lbl_carstyleyear.Text = "-";
            lbl_databelongcompname.Text = "-";
            lbl_enginefamily.Text = "-";
            lbl_carbrand.Text = "-";
            lbl_factoryname.Text = "-";
            lbl_contacttel.Text = "-";
        }
        #endregion

        #region 車型組車型資料
        DataTable dt_carmodeldatarpt = wsApplyData.get_Nv_carmodeldatarpt(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
        if (dt_carmodeldatarpt.Rows.Count > 0)
        {
            //代表車
            foreach (DataRow dr in dt_carmodeldatarpt.Rows)
            {
                if (dr["cmdg_berepresentativevehicle"].ToString().Trim().Equals("Y", StringComparison.CurrentCultureIgnoreCase))
                {
                    lbl_carengmodelname.Text = dr["fullcarstylename"].ToString();
                }
            }
        }
        else
        {
            lbl_carengmodelname.Text = "-";
        }
        gv_carmodelrpt.DataSource = dt_carmodeldatarpt;
        gv_carmodelrpt.DataBind();

        //車身編號/引擎號碼
        DataTable dt_latestcarbodyno = wsApplyData.get_Nv_latestcarbodyno(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
        if (dt_latestcarbodyno.Rows.Count > 0)
        {           
            lbl_carbodyno.Text = dt_latestcarbodyno.Rows[0]["carbodyno"].ToString().Trim() == "" ? "-" : dt_latestcarbodyno.Rows[0]["carbodyno"].ToString().Trim();
            lbl_engineno.Text = dt_latestcarbodyno.Rows[0]["engineno"].ToString().Trim() == "" ? "-" : dt_latestcarbodyno.Rows[0]["engineno"].ToString().Trim();
        }


        #endregion

        #region 資料狀態-當申請型式 不為新申請 且有 50,55資料 取得上次申請代表車資料 否 擇讀取自己本身填寫資料
        DataTable dt_applystatus = wsApplyData.get_applystatus(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
        bool b_IsApplied = wsApplyData.IsApplied(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
        if (dt_applystatus.Rows.Count > 0 && !(dt_applystatus.Rows[0]["ct_aps_desc"].Equals("新申請")) && b_IsApplied)
        {

            DataTable dt_unNewApplyData = wsApplyData.get_unNewApplyData(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
            if (dt_unNewApplyData.Rows.Count > 0)
            {
                applyno = dt_unNewApplyData.Rows[0]["cmdg_applyno"].ToString();
                carcompomodelno = dt_unNewApplyData.Rows[0]["cmdg_carcompomodelno"].ToString();
            }
        }
        #endregion
        //噪音測定報告
        DataTable dt_representativevehicle = wsApplyData.get_Nv_representativevehiclerpt(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
        if (dt_representativevehicle.Rows.Count > 0)
        {

            //沿用不帶"-"表示
            lbl_stayrpmparam.Text = dt_representativevehicle.Rows[0]["ntr_stayrpmparam"].ToString();
            lbl_stayrpmcondi.Text = dt_representativevehicle.Rows[0]["ntr_stayrpmcondi"].ToString();
            lbl_speedupcondi.Text = dt_representativevehicle.Rows[0]["ntr_speedupcondi"].ToString();

            //加速測定數值
            DataTable dt_vehiclespeedrpt = wsApplyData.get_Nv_vehiclespeedrpt(dt_representativevehicle.Rows[0]["ntr_testrptno"].ToString(), dt_representativevehicle.Rows[0]["ntr_carmodelno"].ToString(), arr[0].ToString(), arr[1].ToString());

            if (dt_vehiclespeedrpt.Rows.Count > 0)
            {
                rpt_speedupgear.DataSource = dt_vehiclespeedrpt;
                rpt_speedupgear.DataBind();
                rpt_speed.DataSource = dt_vehiclespeedrpt;
                rpt_speed.DataBind();

            }
        }
    }

    protected void gv_carmodelrpt_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        gv_carmodelrpt.PageIndex = e.NewPageIndex;
        BindData();
    }
    protected void btn_Print_Click(object sender, EventArgs e)
    {

        //到列印畫面
        string scriptString = "";
        scriptString = string.Format(@"var newwin=window.open('representativevehiclenote_rpt.aspx?applyno={0}&carcompomodelno={1}','winRV','scrollbars=yes');newwin.resizeTo(screen.width-20,screen.height-20) ;newwin.moveTo(0,0); ", Server.UrlEncode(Request["applyno"].ToString()), Server.UrlEncode(Request["carcompomodelno"].ToString()));
        this.ClientScript.RegisterStartupScript(this.GetType(), "", scriptString, true);

    }

    protected void rpt_speedupgear_ItemDataBound(object sender, RepeaterItemEventArgs e)
    {
        Cryptography crypy = new Cryptography();
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        int keynum = crypy.getRadNum();
        string[] arr = crypy.wsApplybasedataDetailValid(keynum);

        Label lbl_speedupgear = (Label)e.Item.FindControl("lbl_speedupgear");

        if (e.Item.ItemType == ListItemType.Item || e.Item.ItemType == ListItemType.AlternatingItem)
        {
            lbl_speedupgear.Text = DataBinder.Eval(e.Item.DataItem, "trsui_speedupgear").ToString().Trim();
        }
    }
    protected void rpt_speed_ItemDataBound(object sender, RepeaterItemEventArgs e)
    {
        Cryptography crypy = new Cryptography();
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        int keynum = crypy.getRadNum();
        string[] arr = crypy.wsApplybasedataDetailValid(keynum);

        Label lbl_speed = (Label)e.Item.FindControl("lbl_speed");

        if (e.Item.ItemType == ListItemType.Item || e.Item.ItemType == ListItemType.AlternatingItem)
        {
            lbl_speed.Text = DataBinder.Eval(e.Item.DataItem, "trsui_speed").ToString().Trim();
        }
    }
    protected void gv_carmodelrpt_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            Label lbl_maxhorsepower = (Label)e.Row.FindControl("lbl_maxhorsepower");
            string maxhorsepower = DataBinder.Eval(e.Row.DataItem, "cmdg_maxhorsepower").ToString();// +"kw / " + DataBinder.Eval(e.Row.DataItem, "cmdg_maxhorsepowerspeed").ToString() + "rpm"; //引擎最大馬力+ 轉速

            lbl_maxhorsepower.Text = maxhorsepower;
        }
    }
}