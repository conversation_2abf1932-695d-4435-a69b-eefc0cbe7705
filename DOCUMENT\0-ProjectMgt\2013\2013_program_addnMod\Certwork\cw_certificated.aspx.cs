﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Configuration;
using System.Data;
using Microsoft.Reporting.WebForms;

public partial class Certwork_cw_certificated : BaseAdminPage
{
    protected void Page_Load(object sender, EventArgs e)
    {
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = '../accountmt/index.aspx';</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" && (GetAgentAccountGrpList().Contains("epa_adm") || GetAgentAccountGrpList().Contains("ac_adm") || GetAgentAccountGrpList().Contains("tc_adm") || GetAgentAccountGrpList().Contains("rv_adm") || GetAgentAccountGrpList().Contains("rs_adm") || GetAgentAccountGrpList().Contains("fis_adm") || GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("pg_adm")))
            {
                if (!IsPostBack)
                {
                    InitData();
                }
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }
    }
    #region 更改的麵包屑傳入的參數
    protected void Page_LoadComplete(object sender, EventArgs e)
    {
        ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text = ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text.Replace("certwork/cw_issuedoc.aspx", "certwork/cw_issuedoc.aspx?p=" + Server.UrlEncode(EnCode(ApplyNo)));
    }
    #endregion
    private string ApplyNo
    {
        get
        {
            return Request["p"] == null ? string.Empty : DeCode(Request["p"].ToString());
        }
    }
    private string s_EngineNo
    {
        get
        {
            return Request["EngineNo"] == null ? string.Empty : DeCode(Request["EngineNo"].ToString());
        }
    }

    private void InitData()
    {
        
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        DataView dvRemark = new DataView();
        string[] arr_apd = wsApplybasedataDetailValid(getRadNum());
        string s_Remark = "";
       
        DataView dv = new CertificatedControl().ListCertificatedInfo(ApplyNo);
        //取得車型組清單
        DataTable dt_carmodeldata = wsApplyData.get_carmodeldata(ApplyNo, arr_apd[0].ToString(), arr_apd[1].ToString());

        for (int z = 0; z < dt_carmodeldata.Rows.Count; z++)
        {
            //取得測定報告備註資料

            #region 資料狀態-當申請型式 不為新申請 且有 50,55資料 取得上次申請代表車資料 否 擇讀取自己本身填寫資料

            DataTable dt_applystatus = wsApplyData.get_applystatus(ApplyNo, dt_carmodeldata.Rows[z]["cpm_carcompomodelno"].ToString(), arr_apd[0].ToString(), arr_apd[1].ToString());
            bool b_IsApplied = wsApplyData.IsApplied(ApplyNo, dt_carmodeldata.Rows[z]["cpm_carcompomodelno"].ToString(), arr_apd[0].ToString(), arr_apd[1].ToString());
            if (dt_applystatus.Rows.Count > 0 && !(dt_applystatus.Rows[0]["ct_aps_desc"].Equals("新申請")) && b_IsApplied)
            {

                DataTable dt_unNewApplyData = wsApplyData.get_unNewApplyData(ApplyNo, dt_carmodeldata.Rows[z]["cpm_carcompomodelno"].ToString(), arr_apd[0].ToString(), arr_apd[1].ToString());
                if (dt_unNewApplyData.Rows.Count > 0)
                {

                    dvRemark = new CertificatedControl().ListRemarkForRptInfo(dt_unNewApplyData.Rows[0]["cmdg_carcompomodelno"].ToString(), dt_unNewApplyData.Rows[0]["cmdg_carmodelno"].ToString());
                    DataTable dt_noisestandarddata = wsApplyData.get_Nv_noisetestrpt(dt_unNewApplyData.Rows[0]["cmdg_carcompomodelno"].ToString(), dt_unNewApplyData.Rows[0]["cmdg_carmodelno"].ToString(), arr_apd[0].ToString(), arr_apd[1].ToString());
                    if (dt_noisestandarddata.Rows.Count > 0)
                    {                       
                            dv[z]["ntr_speedupnoise"] = dt_noisestandarddata.Rows[0]["ntr_speedupnoise"].ToString();
                            dv[z]["ntr_staynoise"] = dt_noisestandarddata.Rows[0]["ntr_staynoise"].ToString();
                    }                                  
                }
            }
            else
            {
                dvRemark = new CertificatedControl().ListRemarkForRptInfo(dt_carmodeldata.Rows[z]["cpm_carcompomodelno"].ToString(), dt_carmodeldata.Rows[z]["carmodelno"].ToString());
            }
            #endregion


            for (int r = 0; r < dvRemark.Count; r++)
            {
                //當是第一筆資料的時候才顯示車型組代號
                s_Remark += r == 0 ? dt_carmodeldata.Rows[z]["cpm_carcompomodelno"].ToString() + "\n" + dvRemark[r]["Remark"].ToString() + "\n" : "" + dvRemark[r]["Remark"].ToString() + "\n";


            }

        }
        for (int i = 0; i < dv.Count; i++)
        {
            dv[i]["compidno"] = DeCode(dv[i]["compidno"].ToString());
            dv[i]["remark"] = s_Remark;
            dv[i]["abd_enginesn"] = dv[i]["abd_enginesn"].ToString().Trim().Equals("") ? s_EngineNo : dv[i]["abd_enginesn"];
        }

        //開啟Viewer顯示   
        rpv_MyData.Visible = true;
        //指定Viewer的rdlc檔   
        rpv_MyData.LocalReport.ReportPath = Server.MapPath("Sheet1.rdlc");
        //rpv_MyData.LocalReport. = "111";
        //檔案命名
        rpv_MyData.LocalReport.DisplayName = "NC" + dv[0]["abd_vechiletype"].ToString() + "2_" + DateTime.Now.Date.ToString("yyyyMMdd") + "_" + DateTime.Now.ToString("HHmmss");
        //清掉之前的狀態，重新把資料丟進去，並做Viewer的Refresh   
        rpv_MyData.LocalReport.DataSources.Clear();

        rpv_MyData.LocalReport.DataSources.Add(new ReportDataSource("DataSet1", dv));

        //rpv_MyData.LocalReport.DataSources.Add(new ReportDataSource("DataSet2", dvRemark));
        new ReportDataSource("DataSet1", dv);
        // rpv_MyData.LocalReport.OriginalParametersToDrillthrough;
        rpv_MyData.LocalReport.Refresh();
    }
}