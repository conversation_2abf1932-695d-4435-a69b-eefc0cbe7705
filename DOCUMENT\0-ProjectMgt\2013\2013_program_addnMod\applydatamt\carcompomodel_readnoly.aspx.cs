﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Xml;
using Common;
using System.IO;

public partial class applydatamt_carcompomodel_readnoly : BaseAdminPage
{
    public DataTable dt_list
    {
        get { return (DataTable)ViewState["_dt_list"]; }
        set { ViewState["_dt_list"] = value; }
    }
    protected void Page_Load(object sender, EventArgs e)
    {
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = '../accountmt/index.aspx';</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" &&
                (
                (
                GetAgentAccountGrpList().Contains("epa_adm") || GetAgentAccountGrpList().Contains("ac_adm") || GetAgentAccountGrpList().Contains("tc_adm") || GetAgentAccountGrpList().Contains("rv_adm") || GetAgentAccountGrpList().Contains("rs_adm") || GetAgentAccountGrpList().Contains("fis_adm") || GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("pg_adm")
                )
                ||
                (
                  (GetAgentAccountGrpList().Contains("cga_adm") || GetAgentAccountGrpList().Contains("cam_adm")) && CheckSingleApplyDataAccessRight(DeCode(Request["applyno"].ToString()))
                )
                )
                )
            {
                if (!IsPostBack)
                {
                    if (Request["applyno"].ToString() != "")
                    {
                        Cryptography crypy = new Cryptography();
                        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
                        int keynum = crypy.getRadNum();
                        string[] arr = crypy.wsApplybasedataDetailValid(keynum);
                        DataTable dt_applydatadetail = wsApplyData.get_applybasedate_detail(crypy.DeCode(Request["applyno"].ToString()), arr[0].ToString(), arr[1].ToString());
                        if (dt_applydatadetail.Rows.Count > 0)
                        {
                            //if (dt_applydatadetail.Rows[0]["abd_databelongcompidno"].ToString() == Session["compidno"].ToString())
                            //{
                                BindData();
                                dt_applydatadetail.Dispose();
                            //}
                            //else
                            //{
                            //    dt_applydatadetail.Dispose();
                            //    Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                            //    Response.End();
                            //}
                        }
                        else
                        {
                            dt_applydatadetail.Dispose();                         
                            Response.Write("<script languge='javascript'>alert('查無資料，系統將導回首頁！');window.location.href='../index.aspx'</script>");
                            Response.End();
                        }
                    }
                    else
                    {

                        Response.Write("<script languge='javascript'>alert('發生取不到申請編號錯誤，系統將導回首頁！');window.location.href='../index.aspx'</script>");
                        Response.End();
                    }
                }
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }
    }

    #region 更改的麵包屑傳入的參數
    protected void Page_LoadComplete(object sender, EventArgs e)
    {
        ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text = ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text.Replace("applydatamt/applybasedata_detail.aspx", "applydatamt/applybasedata_detail.aspx?applyno=" + Server.UrlEncode(Request["applyno"].ToString()));
    }
    #endregion

    public void BindData()
    {
        Cryptography crypy = new Cryptography();
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        wsCarCompoModel wsCarCmpModel = new wsCarCompoModel();
        int keynum = crypy.getRadNum();
        string[] Warr = crypy.wsApplybasedataDetailValid(keynum);
        string[] CMarr = crypy.wsCarCompoModelValidate(crypy.getRadNum());
        string applyno = DeCode(Request["applyno"].ToString());
        string carcompomodelno = DeCode(Request["carcompomodelno"].ToString());
       
        string sVechiletype = "";
        lbl_applyno.Text = applyno;
        lbl_carcompomodelno.Text = carcompomodelno;

        DataSet dsCarCmpModel = wsCarCmpModel.get_CarcompomodelBasicData(applyno, carcompomodelno,CMarr[0].ToString(), CMarr[1].ToString());
        if (dsCarCmpModel.Tables[0].Rows.Count > 0)
        {
            sVechiletype = wsCarCmpModel.Get_Vechiletype(applyno, CMarr[0].ToString(), CMarr[1].ToString());
            
            lbl_applystatus.Text = dsCarCmpModel.Tables[0].Rows[0]["ct_aps_desc"].ToString(); //申請狀態
            lbl_enginefamily.Text = dsCarCmpModel.Tables[0].Rows[0]["abd_enginefamily"].ToString();
            lbl_carstyleyear.Text = dsCarCmpModel.Tables[0].Rows[0]["abd_carstyleyear"].ToString();
            lbl_vechiletype.Text = sVechiletype;
            switch (sVechiletype.Trim())
            {
                default:
                    div_M.Style.Add("display", "none");
                    div_G.Style.Add("display", "none");
                    break;

                case "G": //汽車   
                case "D": //柴油車   
                    div_G.Style.Add("display", "inline");
                    lbl_ct_at_desc_G.Text = dsCarCmpModel.Tables[0].Rows[0]["ct_at_desc"].ToString(); //申請型式
                    lbl_importarea_G.Text = dsCarCmpModel.Tables[0].Rows[0]["ct_cy_desc"].ToString();//進口國
                    lbl_useeurocert_G.Text = dsCarCmpModel.Tables[0].Rows[0]["ct_eu_desc"].ToString();//以歐盟合格申請
                    lbl_gasolinetype_G.Text = dsCarCmpModel.Tables[0].Rows[0]["ct_ft_desc"].ToString();    //使用燃料
                    lbl_transmissiontypetype_G.Text = dsCarCmpModel.Tables[0].Rows[0]["ct_gm_desc"].ToString();//排檔型式
                    lbl_provideoilmethod_G.Text = dsCarCmpModel.Tables[0].Rows[0]["ct_le_desc"].ToString();//供油方式   
                    //lbl_drivetype_G.Text = dsCarCmpModel.Tables[0].Rows[0]["ct_dt_desc"].ToString();//驅動方式
                    lbl_cylindernums_G.Text = dsCarCmpModel.Tables[0].Rows[0]["cpm_cylindernums"].ToString();//汽缸數
                    lbl_fituptype_G.Text = dsCarCmpModel.Tables[0].Rows[0]["ct_ep_desc_g"].ToString();//引擎安裝位置
                    lbl_coolsystem_G.Text = dsCarCmpModel.Tables[0].Rows[0]["ct_cs_desc"].ToString();//冷卻系統
                    break;

                case "M": //機車     
                    div_M.Style.Add("display", "inline");
                    lbl_ct_at_desc_M.Text = dsCarCmpModel.Tables[0].Rows[0]["ct_at_desc"].ToString(); //申請型式            
                    lbl_transmissiontypetype_M.Text = dsCarCmpModel.Tables[0].Rows[0]["ct_gm_desc"].ToString();//排檔型式
                    lbl_useeurocert_M.Text = dsCarCmpModel.Tables[0].Rows[0]["ct_eu_desc"].ToString();//以歐盟合格申請
                    lbl_gasolinetype_M.Text = dsCarCmpModel.Tables[0].Rows[0]["ct_ft_desc"].ToString();    //使用燃料        
                    lbl_powerexportway_M.Text = dsCarCmpModel.Tables[0].Rows[0]["ct_ebm_desc"].ToString();//燃燒循環
                    lbl_provideoilmethod_M.Text = dsCarCmpModel.Tables[0].Rows[0]["ct_le_desc"].ToString();//供油方式
                    lbl_exhuast_M.Text = dsCarCmpModel.Tables[0].Rows[0]["cpm_exhuast"].ToString().Replace(".00", "");//總排氣量
                    lbl_cylindernums_M.Text = dsCarCmpModel.Tables[0].Rows[0]["cpm_cylindernums"].ToString();//缸數
                    lbl_cylindertype_M.Text = dsCarCmpModel.Tables[0].Rows[0]["ct_cp_desc_m"].ToString();//汽缸排列                    
                    lbl_enginesetupposnmethod_M.Text = dsCarCmpModel.Tables[0].Rows[0]["cpm_enginesetupposnmethod"].ToString();//引擎安裝位置及方式
                    lbl_coolsystem_M.Text = dsCarCmpModel.Tables[0].Rows[0]["ct_cs_desc"].ToString();//冷卻系統
                    lbl_importarea_M.Text = dsCarCmpModel.Tables[0].Rows[0]["ct_cy_desc"].ToString();    //進口國     
                    break;
            }

            // lbl_isimport.Text = dsCarCmpModel.Tables[0].Rows[0]["abd_isimport"].ToString() == "1" ? "國產" : "進口";

        }

        DataTable dt_carmodeldata = wsApplyData.get_carmodeldataForReadnoly(applyno, carcompomodelno, Warr[0].ToString(), Warr[1].ToString());
        gv_data.DataSource = dt_carmodeldata;
        gv_data.DataBind();

    }

    protected void gv_data_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            Cryptography crypy = new Cryptography();
            Label lbl_berepresentativevehicle = (Label)e.Row.FindControl("lbl_berepresentativevehicle");
            Label lbl_noise = (Label)e.Row.FindControl("lbl_noise");
            HyperLink hlk_testrptno = (HyperLink)e.Row.FindControl("hlk_testrptno");
            hlk_testrptno.NavigateUrl = string.Format("noisetestrpt_readnoly.aspx?applyno={0}&carcompomodelno={1}&carmodelno={2}", Server.UrlEncode(crypy.EnCode(lbl_applyno.Text)), Server.UrlEncode(crypy.EnCode(lbl_carcompomodelno.Text)), Server.UrlEncode(EnCode(DataBinder.Eval(e.Row.DataItem, "cmdg_carmodelno").ToString().Trim())));
            HyperLink hlk_fullcarstylename = (HyperLink)e.Row.FindControl("hlk_fullcarstylename");
            hlk_fullcarstylename.NavigateUrl = "carmodeldata_readonly.aspx?applyno=" + Server.UrlEncode(crypy.EnCode(lbl_applyno.Text)) + "&carcompomodelno=" + Server.UrlEncode(crypy.EnCode(lbl_carcompomodelno.Text)) + "&carmodelno=" + Server.UrlEncode(EnCode(DataBinder.Eval(e.Row.DataItem, "cmdg_carmodelno").ToString().Trim())) + "&mode=" + Server.UrlEncode(EnCode(lbl_vechiletype.Text).ToString().Trim());
            hlk_fullcarstylename.Text = DataBinder.Eval(e.Row.DataItem, "fullcarstylename").ToString();
            string s_berepresentativevehicle = DataBinder.Eval(e.Row.DataItem, "cmdg_berepresentativevehicle").ToString().ToLower() == "y" ? "是" : "否";
            string s_ntr_testrptno = DataBinder.Eval(e.Row.DataItem, "ntr_testrptno").ToString()+"<br>" ;
            string s_noise = "加速噪音值:" + DataBinder.Eval(e.Row.DataItem, "ntr_speedupnoise").ToString() + "<br>原地噪音值:" + DataBinder.Eval(e.Row.DataItem, "ntr_staynoise").ToString();
            lbl_berepresentativevehicle.Text = s_berepresentativevehicle;
            hlk_testrptno.Text = s_ntr_testrptno ;
            lbl_noise.Text = s_noise;
        }
    }

}