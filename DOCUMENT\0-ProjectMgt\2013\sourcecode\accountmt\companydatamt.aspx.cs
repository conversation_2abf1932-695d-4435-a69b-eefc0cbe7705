﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.IO;

public partial class accountmt_companydatamt : BaseAdminPage
{
    wsCompany wscompany = new wsCompany();
    wsAccountInfo wsaccount = new wsAccountInfo();
    wsFileInfo wsfile = new wsFileInfo();
    wsCodetbl wscodetbl = new wsCodetbl();
    protected void Page_Load(object sender, EventArgs e)
    {
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = 'index.aspx';</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2"
                       && (GetAgentAccountGrpList().Contains("cam_adm") || GetAgentAccountGrpList().Contains("pg_adm") || GetAgentAccountGrpList().Contains("sys_adm")))
            {
                if (!IsPostBack)
                {
                    BindCity();
                    BindCertCity();
                    BindCompBase();
                }
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }
    }
    private void BindCompBase()
    {
        string[] radom_C = wsCompanyValidate(getRadNum());
        string[] radom_A = wsAccountValidate(getRadNum());
        string[] radom_F = wsFileValidate(getRadNum());
        string[] radom_CB = wsCodetblValidate(getRadNum());
        DataSet ds_company = wscompany.getCompanyBase(Session["compidno"].ToString(), radom_C[0].ToString(), radom_C[1].ToString());
        lbl_compidno.Text = DeCode(Session["compidno"].ToString());
        lbl_applycount.Text = Convert.ToString(wsaccount.hIssuedAccountbyComp(Session["compidno"].ToString(), radom_A[0].ToString(), radom_A[1].ToString()));
        txt_compname.Text = ds_company.Tables[0].Rows[0]["ci_compname"].ToString();
        txt_compename.Text = ds_company.Tables[0].Rows[0]["ci_compename"].ToString();
        txt_chargeman.Text = ds_company.Tables[0].Rows[0]["ci_chargeman"].ToString();
        txt_comptel.Text = ds_company.Tables[0].Rows[0]["ci_comptel"].ToString();
        txt_compfax.Text = ds_company.Tables[0].Rows[0]["ci_compfax"].ToString();
        //公司地址
        ddl_compaddrcity.SelectedValue = ds_company.Tables[0].Rows[0]["ci_compaddrcity"].ToString();
        DataSet ds_zip_company = wscodetbl.getZipCode(ddl_compaddrcity.SelectedValue, radom_CB[0].ToString(), radom_CB[1].ToString());
        ddl_compaddrlocalarea.DataSource = ds_zip_company;
        ddl_compaddrlocalarea.DataTextField = "ct_zipcode_desc";
        ddl_compaddrlocalarea.DataValueField = "ct_zipcode_id";
        ddl_compaddrlocalarea.DataBind();
        ddl_compaddrlocalarea.SelectedValue = ds_company.Tables[0].Rows[0]["ci_compaddrlocalarea"].ToString();
        lbl_compaddrpostno.Text = ds_company.Tables[0].Rows[0]["ci_compaddrpostno"].ToString();
        txt_compaddr.Text = ds_company.Tables[0].Rows[0]["ci_compaddr"].ToString();
        //合格證地址
        ddl_certaddrCity.SelectedValue = ds_company.Tables[0].Rows[0]["ci_certaddrcity"].ToString();
        DataSet ds_zip_cert = wscodetbl.getZipCode(ddl_certaddrCity.SelectedValue, radom_CB[0].ToString(), radom_CB[1].ToString());
        ddl_certaddrlocalarea.DataSource = ds_zip_cert;
        ddl_certaddrlocalarea.DataTextField = "ct_zipcode_desc";
        ddl_certaddrlocalarea.DataValueField = "ct_zipcode_id";
        ddl_certaddrlocalarea.DataBind();
        ddl_certaddrlocalarea.SelectedValue = ds_company.Tables[0].Rows[0]["ci_certaddrlocalarea"].ToString();
        lbl_certaddrpostno.Text = ds_company.Tables[0].Rows[0]["ci_certaddrpostno"].ToString();
        txt_certaddr.Text = ds_company.Tables[0].Rows[0]["ci_certaddr"].ToString();

        //檔案
        DataTable dtFile = wsfile.getCompanyFileList(radom_F[0].ToString(), radom_F[1].ToString(), Session["compidno"].ToString());
        foreach (DataRow dr in dtFile.Rows)
        {
            string userfilename = dr["afi_userfilename"].ToString();
            string fileID = dr["afi_fileid"].ToString();
            if (dr["afi_filetype"].ToString() == "1")
            {
                hlk_comDoc.NavigateUrl = "../FileDownloadSeal.aspx?File=" + Server.UrlEncode(EnCode(fileID));
                hlk_comDoc.Text = userfilename;
            }
            else if (dr["afi_filetype"].ToString() == "2")
            {
                hlk_facCert.NavigateUrl = "../FileDownloadSeal.aspx?File=" + Server.UrlEncode(EnCode(fileID));
                hlk_facCert.Text = userfilename;
            }
            else if (dr["afi_filetype"].ToString() == "3")
            {
                hlk_authLetter.NavigateUrl = "../FileDownloadSeal.aspx?File=" + Server.UrlEncode(EnCode(fileID));
                hlk_authLetter.Text = userfilename;
            }
            else if (dr["afi_filetype"].ToString() == "4")
            {
                hlk_importCert.NavigateUrl = "../FileDownloadSeal.aspx?File=" + Server.UrlEncode(EnCode(fileID));
                hlk_importCert.Text = userfilename;
            }
        }

        lbl_loginname.Text = ds_company.Tables[0].Rows[0]["ci_lastchangename"].ToString();
        lbl_logindate.Text = ds_company.Tables[0].Rows[0]["ci_lastchangedate"].ToString();

        //記錄目前資料
        ViewState["compname"] = txt_compname.Text.Trim();
        ViewState["compename"] = txt_compename.Text.Trim();
        ViewState["chargeman"] = txt_chargeman.Text.Trim();
        ViewState["compaddrpostno"] = lbl_compaddrpostno.Text.Trim();
        ViewState["compaddr"] = txt_compaddr.Text.Trim();
        ViewState["compfax"] = txt_compfax.Text.Trim();
        ViewState["compshortname"] = ds_company.Tables[0].Rows[0]["ci_compshortname"].ToString();
        ViewState["stampcompno_g"] = ds_company.Tables[0].Rows[0]["ci_stampcompno_g"].ToString();
        ViewState["stampcompno_m"] = ds_company.Tables[0].Rows[0]["ci_stampcompno_m"].ToString();
        //顯示同步按鈕
        if (doCarCertCompanyCheck()) //回傳 true存在;不能新增公司
        {
            btn_Sync.Enabled = false;
        }
        else
        {
            btn_Sync.Enabled = true;
        }
    }
    private void BindCity()
    {
        string[] arr = wsCodetblValidate(getRadNum());
        DataSet ds_city = wscodetbl.getCityCode(arr[0].ToString(), arr[1].ToString());
        //廠商端
        ddl_compaddrcity.DataSource = ds_city;
        ddl_compaddrcity.DataTextField = "ct_city_desc";
        ddl_compaddrcity.DataValueField = "ct_city_id";
        ddl_compaddrcity.DataBind();
        ddl_compaddrcity.Items.Insert(0, new ListItem("請選擇", "000"));
    }
    private void BindCertCity()
    {
        string[] arr = wsCodetblValidate(getRadNum());
        DataSet ds_city = wscodetbl.getCityCode(arr[0].ToString(), arr[1].ToString());
        //合格證
        ddl_certaddrCity.DataSource = ds_city;
        ddl_certaddrCity.DataTextField = "ct_city_desc";
        ddl_certaddrCity.DataValueField = "ct_city_id";
        ddl_certaddrCity.DataBind();
        ddl_certaddrCity.Items.Insert(0, new ListItem("請選擇", "000"));
    }
    private void BindZip()
    {
        wsCodetbl wscodetbl = new wsCodetbl();
        string[] arr = wsCodetblValidate(getRadNum());
        if (ddl_compaddrcity.SelectedValue != "000")
        {
            DataSet ds_zip_company = wscodetbl.getZipCode(ddl_compaddrcity.SelectedValue, arr[0].ToString(), arr[1].ToString());
            ddl_compaddrlocalarea.DataSource = ds_zip_company;
            ddl_compaddrlocalarea.DataTextField = "ct_zipcode_desc";
            ddl_compaddrlocalarea.DataValueField = "ct_zipcode_id";
            ddl_compaddrlocalarea.DataBind();
        }
        else
        {
            ddl_compaddrlocalarea.Items.Clear();
            lbl_compaddrpostno.Text = "";
        }
    }
    private void BindCertZip()
    {
        string[] arr = wsCodetblValidate(getRadNum());
        if (ddl_certaddrCity.SelectedValue != "000")
        {
            DataSet ds_zip_company = wscodetbl.getZipCode(ddl_certaddrCity.SelectedValue, arr[0].ToString(), arr[1].ToString());

            ddl_certaddrlocalarea.DataSource = ds_zip_company;
            ddl_certaddrlocalarea.DataTextField = "ct_zipcode_desc";
            ddl_certaddrlocalarea.DataValueField = "ct_zipcode_id";
            ddl_certaddrlocalarea.DataBind();
        }
        else
        {
            ddl_certaddrlocalarea.Items.Clear();
            lbl_certaddrpostno.Text = "";
        }
    }
    protected void btn_Save_Click(object sender, EventArgs e)
    {
        #region Accountmt特殊字元判斷-有MasterPage
        ContentPlaceHolder mpContentPlaceHolder;
        mpContentPlaceHolder = (ContentPlaceHolder)Master.FindControl("ContentPlaceHolder1");
        foreach (object ctrl in mpContentPlaceHolder.Controls)
        {
            if (ctrl is TextBox)
            {
                TextBox objTextBox = default(TextBox);
                objTextBox = (TextBox)ctrl;
                if ((tbWord(objTextBox.Text)))
                {
                    MessageBox.Show(objTextBox.Text + ":含有系統不允許的特殊字元");
                    return;
                }
            }
        }
        #endregion

        #region 辨識文件類型
        if (fu_compDoc.FileName.Length > 0) //公司證明文件
        {
            string subFileName = System.IO.Path.GetExtension(fu_compDoc.PostedFile.FileName).Substring(1);
            if (!subFileName.Equals("pdf", StringComparison.CurrentCultureIgnoreCase))
            {
                MessageBox.Show("您上傳的檔案為" + subFileName + "，請改上傳PDF檔\n");
                return;
            }

        }
        if (fu_facCert.FileName.Length > 0) //工廠登記證
        {
            string subFileName = System.IO.Path.GetExtension(fu_facCert.PostedFile.FileName).Substring(1);
            if (!subFileName.Equals("pdf", StringComparison.CurrentCultureIgnoreCase))
            {
                MessageBox.Show("您上傳的檔案為" + subFileName + "，請改上傳PDF檔\n");
                return;
            }
        }
        if (fu_authLetter.FileName.Length > 0) //代理授權證明函
        {
            string subFileName = System.IO.Path.GetExtension(fu_authLetter.PostedFile.FileName).Substring(1);
            if (!subFileName.Equals("pdf", StringComparison.CurrentCultureIgnoreCase))
            {
                MessageBox.Show("您上傳的檔案為" + subFileName + "，請改上傳PDF檔\n");
                return;
            }
        }
        if (fu_importCert.FileName.Length > 0) //進口商證明
        {
            string subFileName = System.IO.Path.GetExtension(fu_importCert.PostedFile.FileName).Substring(1);
            if (!subFileName.Equals("pdf", StringComparison.CurrentCultureIgnoreCase))
            {
                MessageBox.Show("您上傳的檔案為" + subFileName + "，請改上傳PDF檔\n");
                return;
            }
        }
        #endregion

        string compidnoE = Session["compidno"].ToString();
        string compidno = DeCode(compidnoE);
        string[] radom_C = wsCompanyValidate(getRadNum());
        string submit_resultUpdateCompany = "";
        connectCarCert.CarCert CarCert = new connectCarCert.CarCert();
        string[] radom_CarCert = wsCarCertValid(Convert.ToInt16(DateTime.Now.DayOfWeek));

        string[] radom_A = wsAccountValidate(getRadNum());
        DataSet ds_company = wscompany.getCompanyBase(Session["compidno"].ToString(), radom_C[0].ToString(), radom_C[1].ToString());
        DataSet ds_Account = wsaccount.getAccountBase(DeCode(Session["account"].ToString()), radom_A[0].ToString(), radom_A[1].ToString());
        string Stampcompno_G = ds_company.Tables[0].Rows[0]["ci_stampcompno_g"].ToString().Length <= 3 ? ds_company.Tables[0].Rows[0]["ci_stampcompno_g"].ToString() : ds_company.Tables[0].Rows[0]["ci_stampcompno_g"].ToString().Substring(1, ds_company.Tables[0].Rows[0]["ci_stampcompno_g"].ToString().Length - 1);
        string Stampcompno_M = ds_company.Tables[0].Rows[0]["ci_stampcompno_m"].ToString().Length <= 3 ? ds_company.Tables[0].Rows[0]["ci_stampcompno_m"].ToString() : ds_company.Tables[0].Rows[0]["ci_stampcompno_m"].ToString().Substring(1, ds_company.Tables[0].Rows[0]["ci_stampcompno_m"].ToString().Length - 1);

        //基本資料修改
        wscompany.updateCompBaseData(radom_C[0].ToString(), radom_C[1].ToString(), compidnoE
            , txt_comptel.Text.Trim(), txt_compfax.Text.Trim(), ddl_certaddrCity.SelectedValue, ddl_certaddrlocalarea.SelectedValue, lbl_certaddrpostno.Text.Trim(), txt_certaddr.Text.Trim(), GetAgentName());

        #region Check CarCert驗證 UpdateCustomer
        //判斷是否有異動到需審核的資料
        bool isCarCertChangeData = false;
        if (ViewState["compname"].ToString() != txt_compname.Text.Trim() || ViewState["compename"].ToString() != txt_compename.Text.Trim()
            || ViewState["chargeman"].ToString() != txt_chargeman.Text.Trim() || ViewState["compaddrpostno"].ToString() != lbl_compaddrpostno.Text.Trim()
            || ViewState["compaddr"].ToString() != txt_compaddr.Text.Trim()
            || ViewState["compfax"].ToString() != txt_compfax.Text.Trim())
        {
            //判斷此公司是否存在於CarCert；若存在 則更新資料
            if (isCarCertChangeData)
            {
                if (doCarCertCompanyCheck() && ds_company.Tables[0].Rows.Count > 0 && ds_Account.Tables[0].Rows.Count > 0)
                {
                    submit_resultUpdateCompany = CarCert.UpdateCustomer(compidnoE, EnCode(txt_compname.Text.Trim()), EnCode(txt_compename.Text.Trim()), EnCode(txt_chargeman.Text.Trim()),
                           EnCode(ds_company.Tables[0].Rows[0]["ci_compshortname"].ToString()), EnCode(DateTime.Now.Date.ToString("yyyyMMdd")), EnCode(txt_comptel.Text.Trim()), EnCode(txt_compfax.Text.Trim()), EnCode(ddl_compaddrcity.SelectedValue), EnCode(ddl_compaddrlocalarea.SelectedValue),
                          EnCode(lbl_compaddrpostno.Text.Trim()), EnCode(txt_compaddr.Text.Trim()), EnCode(ds_Account.Tables[0].Rows[0]["ai_username"].ToString()), EnCode(ds_Account.Tables[0].Rows[0]["ai_email"].ToString()), EnCode(ds_Account.Tables[0].Rows[0]["ai_usertel"].ToString()), EnCode(ds_Account.Tables[0].Rows[0]["ai_usercellphone"].ToString()),
                          EnCode(ds_Account.Tables[0].Rows[0]["ai_userfax"].ToString()), EnCode(Stampcompno_G), EnCode(Stampcompno_M), EnCode("1"), radom_CarCert[0].ToString(), radom_CarCert[1].ToString());

                    if (submit_resultUpdateCompany.Equals("修改公司資料成功"))
                        submit_resultUpdateCompany = " ;同步修改公司資料成功!";
                    else
                        submit_resultUpdateCompany = " ;同步修改公司資料失敗!";
                }
            }
        }
        #endregion

        //判斷是否有異動到需審核的資料
        bool isChangeByApproveData = false;
        if (ViewState["compname"].ToString() != txt_compname.Text.Trim() || ViewState["compename"].ToString() != txt_compename.Text.Trim()
            || ViewState["chargeman"].ToString() != txt_chargeman.Text.Trim() || ViewState["compaddrpostno"].ToString() != lbl_compaddrpostno.Text.Trim()
            || ViewState["compaddr"].ToString() != txt_compaddr.Text.Trim()
            || fu_compDoc.FileName != "" || fu_facCert.FileName != "" || fu_authLetter.FileName != "" || fu_importCert.FileName != "")
            isChangeByApproveData = true;

        if (isChangeByApproveData)
        {
            //判斷是否已有重要資料在審核中，TRUE：有
            if (wscompany.isExistsApplyingCompChange(radom_C[0].ToString(), radom_C[1].ToString(), compidnoE))
            {
                MessageBox.Show("尚有需審核的資料待審驗人員確認中，不可再異動，只成功異動未需審核的資料！");
            }
            else
            {
                wsGroupInfo wsgroup = new wsGroupInfo();
                string[] radom_gp = wsGroupValidate(getRadNum());
                DataTable dtFirstAdmList = wsgroup.getFirstAdmList(radom_gp[0].ToString(), radom_gp[1].ToString());
                if (dtFirstAdmList.Rows.Count > 0)
                {
                    string certpersonlist = string.Empty;
                    foreach (DataRow dr in dtFirstAdmList.Rows)
                    {
                        certpersonlist += dr["ai_email"].ToString() + ",";
                    }
                    certpersonlist = certpersonlist.Substring(0, certpersonlist.Length - 1);

                    //email
                    string[] _mailTo = new string[1];
                    _mailTo[0] = certpersonlist;//收件人                
                    string[] _mailCc = new string[1];//副本
                    _mailCc[0] = "";
                    string[] _mailBcc = new string[1];//密件
                    _mailBcc[0] = "";
                    if (Convert.ToBoolean(ConfigurationManager.AppSettings["DEBUGMode"].ToString()))
                    {
                        //測試
                        _mailTo[0] = ConfigurationManager.AppSettings["MailTestTo"].ToString();
                    }

                    SendMail mail = new SendMail();
                    if (mail.Mail_CompImportChg(_mailTo, _mailCc, _mailBcc, txt_compname.Text.Trim()))
                    {
                        wscompany.insertCompChange(radom_C[0].ToString(), radom_C[1].ToString(), compidnoE
                            , txt_compname.Text.Trim(), txt_compename.Text.Trim(), txt_chargeman.Text.Trim()
                            , txt_comptel.Text.Trim(), txt_compfax.Text.Trim()
                            , ddl_compaddrcity.SelectedValue, ddl_compaddrlocalarea.SelectedValue, lbl_compaddrpostno.Text, txt_compaddr.Text.Trim()
                            , ddl_certaddrCity.SelectedValue, ddl_certaddrlocalarea.SelectedValue, lbl_certaddrpostno.Text, txt_certaddr.Text.Trim()
                            , GetAgentName());

                        string filepath = ConfigurationManager.AppSettings["FilePath"].ToString();
                        string strUpLoadPath = filepath + "Company\\" + compidno + "\\";
                        //如果上傳路徑中沒有該目錄，則自動新增
                        if (!Directory.Exists(strUpLoadPath.Substring(0, strUpLoadPath.LastIndexOf("\\"))))
                        {
                            Directory.CreateDirectory(strUpLoadPath.Substring(0, strUpLoadPath.LastIndexOf("\\")));
                        }
                        string[] strFileNm;
                        string filename1 = "";
                        string filename2 = "";
                        string newfile = "";
                        string[] radom_F = wsFileValidate(getRadNum());
                        //公司證明文件
                        if (fu_compDoc.FileName.Length > 0)
                        {
                            strFileNm = fu_compDoc.PostedFile.FileName.Split('\\');
                            filename1 = strFileNm[strFileNm.GetUpperBound(0)];
                            filename2 = compidno + "_1" + "_" + DateTime.Now.ToString("yyyyMMddHHmmss") + "_" + filename1;
                            wsfile.InsertCompanyFile("1", wsfile.getFileSNByFileType(radom_F[0].ToString(), radom_F[1].ToString(), "1"), filename1, filename2, strUpLoadPath, "公司證明文件", GetAgentAccount()
                                        , compidnoE, radom_F[0].ToString(), radom_F[1].ToString(), "C");
                            newfile = strUpLoadPath + filename2;
                            fu_compDoc.PostedFile.SaveAs(newfile);
                        }
                        //工廠登記證
                        if (fu_facCert.FileName.Length > 0)
                        {
                            strFileNm = fu_facCert.PostedFile.FileName.Split('\\');
                            filename1 = strFileNm[strFileNm.GetUpperBound(0)];
                            filename2 = compidno + "_2" + "_" + DateTime.Now.ToString("yyyyMMddHHmmss") + "_" + filename1;
                            wsfile.InsertCompanyFile("2", wsfile.getFileSNByFileType(radom_F[0].ToString(), radom_F[1].ToString(), "2"), filename1, filename2, strUpLoadPath, "工廠登記證", GetAgentAccount()
                                        , compidnoE, radom_F[0].ToString(), radom_F[1].ToString(), "C");
                            newfile = strUpLoadPath + filename2;
                            fu_facCert.PostedFile.SaveAs(newfile);
                        }
                        //代理授權證明函
                        if (fu_authLetter.FileName.Length > 0)
                        {
                            strFileNm = fu_authLetter.PostedFile.FileName.Split('\\');
                            filename1 = strFileNm[strFileNm.GetUpperBound(0)];
                            filename2 = compidno + "_3" + "_" + DateTime.Now.ToString("yyyyMMddHHmmss") + "_" + filename1;
                            wsfile.InsertCompanyFile("3", wsfile.getFileSNByFileType(radom_F[0].ToString(), radom_F[1].ToString(), "3"), filename1, filename2, strUpLoadPath, "代理授權證明函", GetAgentAccount()
                                        , compidnoE, radom_F[0].ToString(), radom_F[1].ToString(), "C");
                            newfile = strUpLoadPath + filename2;
                            fu_authLetter.PostedFile.SaveAs(newfile);
                        }
                        //進口商證明文件
                        if (fu_importCert.FileName.Length > 0)
                        {
                            strFileNm = fu_importCert.PostedFile.FileName.Split('\\');
                            filename1 = strFileNm[strFileNm.GetUpperBound(0)];
                            filename2 = compidno + "_4" + "_" + DateTime.Now.ToString("yyyyMMddHHmmss") + "_" + filename1;
                            wsfile.InsertCompanyFile("4", wsfile.getFileSNByFileType(radom_F[0].ToString(), radom_F[1].ToString(), "4"), filename1, filename2, strUpLoadPath, "進口商證明文件", GetAgentAccount()
                                        , compidnoE, radom_F[0].ToString(), radom_F[1].ToString(), "C");
                            newfile = strUpLoadPath + filename2;
                            fu_importCert.PostedFile.SaveAs(newfile);
                        }

                        MessageBox.Show("您已經異動到需審核的資料，您的異動將待審驗人員確認後才可生效！");
                    }
                    else
                    {
                        MessageBox.Show("寄件失敗");
                    }
                }
                else
                {
                    MessageBox.Show("已異動到需審核的資料，但尚未有審驗人員資料，請通知系統管理人員");
                }
            }
        }
        else
        {
            MessageBox.Show("公司資料異動成功! " + submit_resultUpdateCompany);
        }

        BindCompBase();

    }
    protected void ddl_compaddrcity_SelectedIndexChanged(object sender, EventArgs e)
    {
        BindZip();
        lbl_compaddrpostno.Text = ddl_compaddrlocalarea.SelectedValue;
    }
    protected void ddl_compaddrlocalarea_SelectedIndexChanged(object sender, EventArgs e)
    {
        lbl_compaddrpostno.Text = ddl_compaddrlocalarea.SelectedValue;
    }
    protected void ddl_certaddrCity_SelectedIndexChanged(object sender, EventArgs e)
    {
        BindCertZip();
        lbl_certaddrpostno.Text = ddl_certaddrlocalarea.SelectedValue;
    }
    protected void ddl_certaddrlocalarea_SelectedIndexChanged(object sender, EventArgs e)
    {
        lbl_certaddrpostno.Text = ddl_certaddrlocalarea.SelectedValue;
    }

    #region CarCert同步按鈕
    protected void btn_Sync_Click(object sender, EventArgs e)
    {
        try
        {
            string compidnoE = Session["compidno"].ToString();
            string compidno = DeCode(compidnoE);
            string submit_resultCompany = "";
            string submit_resultAccount = "";
            connectCarCert.CarCert CarCert = new connectCarCert.CarCert();
            string[] radom_CarCert = wsCarCertValid(Convert.ToInt16(DateTime.Now.DayOfWeek));
            string[] radom_C = wsCompanyValidate(getRadNum());
            string[] radom_A = wsAccountValidate(getRadNum());
            DataSet ds_company = wscompany.getCompanyBase(Session["compidno"].ToString(), radom_C[0].ToString(), radom_C[1].ToString());
            DataSet ds_Account = wsaccount.getAccountBase(DeCode(Session["account"].ToString()), radom_A[0].ToString(), radom_A[1].ToString());
            string Stampcompno_G = ds_company.Tables[0].Rows[0]["ci_stampcompno_g"].ToString().Length <= 3 ? ds_company.Tables[0].Rows[0]["ci_stampcompno_g"].ToString() : ds_company.Tables[0].Rows[0]["ci_stampcompno_g"].ToString().Substring(1, ds_company.Tables[0].Rows[0]["ci_stampcompno_g"].ToString().Length - 1);
            string Stampcompno_M = ds_company.Tables[0].Rows[0]["ci_stampcompno_m"].ToString().Length <= 3 ? ds_company.Tables[0].Rows[0]["ci_stampcompno_m"].ToString() : ds_company.Tables[0].Rows[0]["ci_stampcompno_m"].ToString().Substring(1, ds_company.Tables[0].Rows[0]["ci_stampcompno_m"].ToString().Length - 1);
            //同步-新增CarCert公司資料
            if (!doCarCertCompanyCheck())
            {
                submit_resultCompany = CarCert.InsertCustomer(compidnoE, EnCode(txt_compname.Text.Trim()), EnCode(txt_compename.Text.Trim()), EnCode(txt_chargeman.Text.Trim()),
                     EnCode(ds_company.Tables[0].Rows[0]["ci_compshortname"].ToString()), EnCode(DateTime.Now.Date.ToString("yyyyMMdd")), EnCode(txt_comptel.Text.Trim()), EnCode(ds_company.Tables[0].Rows[0]["ci_compfax"].ToString()), EnCode(ddl_compaddrcity.SelectedValue), EnCode(ddl_compaddrlocalarea.SelectedValue),
                    EnCode(lbl_compaddrpostno.Text.Trim()), EnCode(txt_compaddr.Text.Trim()), EnCode(ds_Account.Tables[0].Rows[0]["ai_username"].ToString()), EnCode(ds_Account.Tables[0].Rows[0]["ai_email"].ToString()), EnCode(ds_Account.Tables[0].Rows[0]["ai_usertel"].ToString()), EnCode(ds_Account.Tables[0].Rows[0]["ai_usercellphone"].ToString()),
                    EnCode(ds_Account.Tables[0].Rows[0]["ai_userfax"].ToString()), EnCode(Stampcompno_G), EnCode(Stampcompno_M), radom_CarCert[0].ToString(), radom_CarCert[1].ToString());
            }
            if (submit_resultCompany.Equals("新增公司資料成功"))
            {
                #region 信件通知CarCert

                string mailCC = ConfigurationManager.AppSettings["MailCC"].ToString();

                string[] _mailTo = new string[1];
                _mailTo[0] = ConfigurationManager.AppSettings["population_contactmail"].ToString();//ds_Account.Tables[0].Rows[0]["ai_email"].ToString();//收件人                
                string[] _mailCc = new string[1];//副本
                _mailCc[0] = "";
                string[] _mailBcc = new string[1];//密件
                _mailBcc[0] = "";
                if (Convert.ToBoolean(ConfigurationManager.AppSettings["DEBUGMode"].ToString()))
                {
                    //測試
                    _mailTo[0] = ConfigurationManager.AppSettings["MailTestTo"].ToString();
                }
                //信件通知
                SendMail sendmail = new SendMail();

                string CompName = ds_company.Tables[0].Rows[0]["ci_compname"].ToString();
                string Account = DeCode(Session["account"].ToString());
                sendmail.Mail_ApplyCarCertCompany(_mailTo, _mailCc, _mailBcc, CompName, Account, ds_Account.Tables[0].Rows[0]["ai_username"].ToString());
                #endregion
            }
            //同步-新增CarCert帳號資料;條件ai_accountstatus 已核發已啟用、 ai_use_gm_system 噪音汙染公用
            DataTable dt_accountbase = wsaccount.getSameComAccountList(compidnoE, radom_A[0].ToString(), radom_A[1].ToString());
            if (dt_accountbase.Rows.Count > 0)
            {
                for (int i = 0; i < dt_accountbase.Rows.Count; i++)
                {
                    //Check 帳號是否存在於CarCert 如存在，更新資料；不存在則新增
                    if (CarCert.CheckUserID(EnCode(dt_accountbase.Rows[i]["ai_account"].ToString()), radom_CarCert[0].ToString(), radom_CarCert[1].ToString()))
                        submit_resultAccount = CarCert.UpdateAccount(EnCode(dt_accountbase.Rows[i]["ai_account"].ToString()), EnCode(dt_accountbase.Rows[i]["ai_username"].ToString()), EnCode(dt_accountbase.Rows[i]["ai_usercellphone"].ToString()), EnCode(dt_accountbase.Rows[i]["ai_email"].ToString()), radom_CarCert[0].ToString(), radom_CarCert[1].ToString());
                    else
                        submit_resultAccount = CarCert.InsertAccount(EnCode(dt_accountbase.Rows[i]["ai_account"].ToString()), EnCode(dt_accountbase.Rows[i]["ai_username"].ToString()), compidnoE, EnCode(dt_accountbase.Rows[i]["ai_usercellphone"].ToString()), EnCode(dt_accountbase.Rows[i]["ai_email"].ToString()), dt_accountbase.Rows[i]["ai_password"].ToString(), radom_CarCert[0].ToString(), radom_CarCert[1].ToString());
                }
            }

            MessageBox.Show(submit_resultCompany + ";" + submit_resultAccount);

            BindCompBase();
        }
        catch (Exception ex)
        {

            ex.Message.ToString();
            MessageBox.Show("存檔失敗");
            return;
        }
    }
    #endregion

    #region Check CarCert 公司驗證
    private bool doCarCertCompanyCheck()
    {

        connectCarCert.CarCert CarCert = new connectCarCert.CarCert();
        string[] radom_CarCert = wsCarCertValid(Convert.ToInt16(DateTime.Now.DayOfWeek));
        //公司是否存在 CarCert
        bool isCompanyCarCert = CarCert.CheckCustomer(EnCode(GetAgentIDNo()), radom_CarCert[0].ToString(), radom_CarCert[1].ToString());


        if (isCompanyCarCert)
            return true;
        else
            return false;
    }
    #endregion

    #region Check CarCert 帳號驗證
    private bool doCarCertReaccountCheck()
    {
        connectCarCert.CarCert CarCert = new connectCarCert.CarCert();
        string[] radom_CarCert = wsCarCertValid(Convert.ToInt16(DateTime.Now.DayOfWeek));
        //帳號是否存在 CarCert
        bool isReaccountCarCert = CarCert.CheckUserID(EnCode(GetAgentAccount()), radom_CarCert[0].ToString(), radom_CarCert[1].ToString());


        if (isReaccountCarCert)
            return true;
        else
            return false;
    }
    #endregion
}