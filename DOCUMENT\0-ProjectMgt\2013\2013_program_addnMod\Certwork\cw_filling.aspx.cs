﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Configuration;
using System.Text;
using System.Data;
using System.IO;


using System.Data.SqlClient;

using System.Globalization;
using System.Xml;
using Common;


public partial class Certwork_cw_filling : BaseAdminPage
{
    protected void Page_Load(object sender, EventArgs e)
    {
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = '../accountmt/index.aspx';</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" && (GetAgentAccountGrpList().Contains("epa_adm") || GetAgentAccountGrpList().Contains("ac_adm") || GetAgentAccountGrpList().Contains("tc_adm") || GetAgentAccountGrpList().Contains("rv_adm") || GetAgentAccountGrpList().Contains("rs_adm") || GetAgentAccountGrpList().Contains("fis_adm") || GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("pg_adm")))
            {
                if (!IsPostBack)
                {
                    InitData();
                }
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }
    }

    private string ApplyNo
    {
        get
        {
            return Request["p"] == null ? string.Empty : DeCode(Request["p"].ToString());
        }
    }

    private void InitData()
    {
        DataView dv = new FillingControl().GetIssueInfo(ApplyNo);
        Cryptography crypy = new Cryptography();
        wsCodetbl wsCodeTbl = new wsCodetbl();
        DataView dvBaseData = new MailParaControl().GetApplyBaseData(ApplyNo);
        string cdocDate = dvBaseData[0]["CertdocDate"].ToString();


        string[] expectedFormats = { "yyyy/MM/dd tt hh:mm:ss", "yyyy/M/dd tt hh:mm:ss", "yyyy/MM/d tt hh:mm:ss", "yyyy/M/d tt hh:mm:ss" };
        if (cdocDate.Length > 5)
        {
            cdocDate = DateTime.ParseExact(cdocDate, expectedFormats, CultureInfo.CurrentCulture, AllowWhiteSpaces).ToString("yyyy/MM/dd");
        }
        lbl_CompanyName.Text = new RejectControl().GetCompanyName(ApplyNo); //申請廠商
        lbl_ApplyNo.Text = ApplyNo;                                         //申請編號
        //lbl_certdocissuedate.Text = DateTime.Now.ToString("yyyy/MM/dd");    //合格核發日期
        txt_receiptno.Text = dvBaseData[0]["ReceiptNo"].ToString().Equals("") ? "" : dvBaseData[0]["ReceiptNo"].ToString(); //可能是重傳或是第一次填寫
        txt_certdocno.Text = dvBaseData[0]["CertdocNo"].ToString().Equals("") ? "" : dvBaseData[0]["CertdocNo"].ToString(); //可能是重傳或是第一次填寫
        cal_certdocissuedate.GetDate = dvBaseData[0]["CertdocDate"].ToString().Equals("NULL") ? "" : cdocDate;

        if (new FirstProcessControl().IsValidationStatus(ApplyNo, "55"))  //歸檔後的狀態
        {
            btn_Filling.Visible = false;
        }
        if (new FirstProcessControl().IsValidationStatus(ApplyNo, "50"))  //可以歸檔的狀態
        {
            lab_filling.Text = "歸檔";
            btn_Filling.Visible = true;
        }
        else
        {
            lab_filling.Text = "重傳";
        }

        if (dv.Count.Equals(1))
        {
            lbl_Account.Text = dv[0]["Account"].ToString();     //核發人員
            lbl_IssueDate.Text = dv[0]["IssueDate"].ToString(); //核發時間
            hf_YYYY.Value = dv[0]["YYYY"].ToString();
            reLoadFile();
        }
        // 
        if (new RejectControl().GetTwocertType(ApplyNo).Length > 0)
        {
            rdBtnLst_twocerttype.SelectedValue = new RejectControl().GetTwocertType(ApplyNo);
        }

        #region 兩證合一申請方式
        string[] arr_twct = crypy.wsCodetblValidate(crypy.getRadNum());
        DataSet dsTwoCertType = wsCodeTbl.getTwoCertType(arr_twct[0].ToString(), arr_twct[1].ToString());
        rdBtnLst_twocerttype.DataSource = dsTwoCertType;
        rdBtnLst_twocerttype.DataTextField = "ct_twct_desc";
        rdBtnLst_twocerttype.DataValueField = "ct_twct_id";
        rdBtnLst_twocerttype.DataBind();
        #endregion

    }

    private void reLoadFile()
    {
        DataView dv = new FillingControl().GetFileName(ApplyNo);
        lbt_Cretificated.Text = dv[0]["CertdocFileName"].ToString();
        lbt_Cretificated.CommandArgument = dv[0]["YYYY"].ToString();
        lbt_Receipt.Text = dv[0]["ReceiptFileName"].ToString();
        lbt_Receipt.CommandArgument = dv[0]["YYYY"].ToString();
        lbt_Cretificated.CommandName = dv[0]["CertdocFileSysName"].ToString();
        lbt_Receipt.CommandName = dv[0]["ReceiptFileSysName"].ToString();

    }

    private bool chkForm()
    {
        StringBuilder sb = new StringBuilder();

        if (fi_Cretificated.FileName.Length.Equals(0) && lbt_Cretificated.Text.Length.Equals(0))
        {
            sb.Append("合格證未選擇！\n");
        }
        if (fi_Receipt.FileName.Length.Equals(0) && lbt_Receipt.Text.Length.Equals(0))
        {
            sb.Append("收據未選擇！\n");
        }
        if (fi_Cretificated.FileName.Length > 0)
        {
            IOFiles io = new IOFiles();
            string extension = io.GetExtension(fi_Cretificated.PostedFile);
            if (extension != ".pdf") { sb.Append("合格證副檔名需為.pdf！\n"); }
        }
        if (fi_Receipt.FileName.Length > 0)
        {
            IOFiles io = new IOFiles();
            string extension = io.GetExtension(fi_Receipt.PostedFile);
            if (extension != ".pdf") { sb.Append("收據副檔名需為.pdf！\n"); }
        }

        #region 特殊字元判斷-有MasterPage
        ContentPlaceHolder mpContentPlaceHolder;
        mpContentPlaceHolder = (ContentPlaceHolder)Master.FindControl("ContentPlaceHolder1");
        foreach (object ctrl in mpContentPlaceHolder.Controls)
        {
            if (ctrl is TextBox)
            {
                TextBox objTextBox = default(TextBox);
                objTextBox = (TextBox)ctrl;
                if ((tbWord(objTextBox.Text)))
                {
                    sb.Append("含有系統不允許的特殊字元！\n");
                }
            }
        }
        #endregion
        if (sb.Length.Equals(0))
        {
            return true;
        }
        else
        {
            MessageBox.Show(sb.ToString());
            return false;
        }
    }

    //歸檔
    protected void btn_Filling_Click(object sender, EventArgs e)
    {
        string sMessage = string.Empty;
        if (chkForm())
        {
            if (fi_Cretificated.FileName.Length > 0)
            {
                sMessage = FillingOperate(fi_Cretificated.PostedFile, "20");
            }
            if (fi_Receipt.FileName.Length > 0)
            {
                sMessage += FillingOperate(fi_Receipt.PostedFile, "22");
            }
            if (int.Parse(rdBtnLst_twocerttype.SelectedValue) > 0)
            {
                new FillingControl().UpdateTwocertType(ApplyNo, rdBtnLst_twocerttype.SelectedValue.ToString());
            }

            if (sMessage.Length > 0)
            {
                MessageBox.Show("檔案名稱重複，上傳失敗！");
                return;
            }
            else
            {
                new FirstProcessControl().ChangeApplyStatus(ApplyNo, "55");//歸檔
                new FirstProcessControl().LogValidationProcess(ApplyNo, DBNull.Value, "55");//Log      

                if (cal_certdocissuedate.GetDate.ToString().Length > 0)
                {
                    new FirstProcessControl().UpdateCertReceipt(ApplyNo, txt_certdocno.Text.Trim(), DateTime.Parse(cal_certdocissuedate.GetDate.ToString()).ToString("yyyy-MM-dd hh:mm:ss.mmm"), txt_receiptno.Text.Trim());//合格證編號、核發日期、收據
                }
                else
                {
                    new FirstProcessControl().UpdateCertReceipt(ApplyNo, txt_certdocno.Text.Trim(), cal_certdocissuedate.GetDate.ToString(), txt_receiptno.Text.Trim());//合格證編號、核發日期、收據
                }

                RegisterStartupScript("script", @"
            <script>alert('案件已歸檔！'); window.location='cw_firstvalidationqry.aspx';</script>
            ");
            }
        }
    }

    //上傳檔案、更新資料庫、刪除舊檔案
    private string FillingOperate(HttpPostedFile hpf, string filetype)
    {
        string Userfilename = Path.GetFileName(hpf.FileName);
        string Sysfilename = DateTime.Now.ToString("yyyyMMddhhmmss") + "_" + Userfilename;
        string reStrLoad = string.Empty;
        string Filepath = ConfigurationManager.AppSettings["FilePath"].ToString() + DateTime.Now.ToString("yyyy") + "\\" + ApplyNo + "\\";

        IOFiles io = new IOFiles();
        io.ParentFolder = DateTime.Now.ToString("yyyy") + "\\" + ApplyNo + "\\";

        if (io.UploadFile(hpf, false))
        {
            string deletefile = new FillingControl().UpdateAttachfileinfo(ApplyNo, filetype, Userfilename, Sysfilename, Filepath, rdBtnLst_twocerttype.SelectedValue.ToString());
            if (!deletefile.Length.Equals(0))
            {
                io.FileName = deletefile;
                io.DeleteFile();
            }
            reLoadFile();
            //實體檔案上傳
            string newfile = Filepath + Sysfilename;
            if (filetype.Equals("20")) //合格證
            {
                fi_Cretificated.PostedFile.SaveAs(newfile);
            }
            else
            {
                fi_Receipt.PostedFile.SaveAs(newfile);
            }
            return reStrLoad;
        }
        else
        {
            return reStrLoad = "檔案：" + Path.GetFileName(hpf.FileName) + " 已存在，上傳失敗！";
        }



    }

    //下載檔案
    protected void lbt_Download_Click(object sender, EventArgs e)
    {
        LinkButton lbt = (LinkButton)sender;
        string filename = lbt.CommandName;
        string foldername = DateTime.Now.ToString("yyyy") + "\\" + ApplyNo + "\\";
        IOFiles io = new IOFiles(filename, foldername);
        io.DownLoadFile();
    }
    //重傳
    protected void btn_btn_ReSent_Click(object sender, EventArgs e)
    {
        // DateTime.Parse(cal_certdocissuedate.GetDate.ToString()).ToString("yyyy-MM-dd hh:mm:ss.mmm")

        if (cal_certdocissuedate.GetDate.ToString().Length > 0)
        {
            new FirstProcessControl().UpdateCertReceipt(ApplyNo, txt_certdocno.Text.Trim(), DateTime.Parse(cal_certdocissuedate.GetDate.ToString()).ToString("yyyy-MM-dd hh:mm:ss.mmm"), txt_receiptno.Text.Trim());//合格證編號、核發日期、收據
        }
        else
        {
            new FirstProcessControl().UpdateCertReceipt(ApplyNo, txt_certdocno.Text.Trim(), cal_certdocissuedate.GetDate.ToString(), txt_receiptno.Text.Trim());//合格證編號、核發日期、收據
        }
        //上傳合格證
        string sMessage = string.Empty;
        if (fi_Cretificated.FileName.Length > 0)
        {
            IOFiles ioCretificated = new IOFiles();
            string sCretificated = ioCretificated.GetExtension(fi_Cretificated.PostedFile);
            if (sCretificated != ".pdf")
            {
                MessageBox.Show("副檔名需為.pdf！");
                return;
            }
            sMessage = FillingOperate(fi_Cretificated.PostedFile, "20");
        }
        //上傳收據
        if (fi_Receipt.FileName.Length > 0)
        {
            IOFiles ioReceipt = new IOFiles();
            string sReceipt = ioReceipt.GetExtension(fi_Receipt.PostedFile);
            if (sReceipt != ".pdf")
            {
                MessageBox.Show("副檔名需為.pdf！");
                return;
            }
            sMessage += FillingOperate(fi_Receipt.PostedFile, "22");
        }
        if (int.Parse(rdBtnLst_twocerttype.SelectedValue) > 0)
        {
            new FillingControl().UpdateTwocertType(ApplyNo, rdBtnLst_twocerttype.SelectedValue.ToString());
        }
        if (sMessage.Length > 0)
        {
            MessageBox.Show("檔案名稱重複，上傳失敗！");
            return;
        }
        else
        {
            RegisterStartupScript("script", @"
            <script>alert('案件資料更新！'); window.location='cw_applylistqry.aspx';</script>
            ");
        }


    }
    protected void lbtn_certificated_Click(object sender, EventArgs e)
    {
        //按下合格證下載更新資料
        new FillingControl().UpdateTwocertType(ApplyNo, rdBtnLst_twocerttype.SelectedValue.ToString());
        LinkButton lbtn_certificated = (LinkButton)sender;
        string scriptString = "";
        DataView dvBaseData = new MailParaControl().GetApplyBaseData(ApplyNo);
        string EngineFamily = dvBaseData[0]["EngineFamily"].ToString();
        //歸檔、已上傳平台
        if (new FirstProcessControl().IsValidationStatus(ApplyNo, "55") || new FirstProcessControl().IsValidationStatus(ApplyNo, "50"))
        {
            scriptString = string.Format(@"window.open('cw_certificated.aspx?p={0}&EngineNo={1}',10,'scrollbars=yes'); ", Server.UrlEncode(EnCode(ApplyNo)), Server.UrlEncode(EnCode(EngineFamily)));
            this.ClientScript.RegisterStartupScript(this.GetType(), "", scriptString, true);
        }
    }

    public DateTimeStyles AllowWhiteSpaces { get; set; }
}