﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using System.Configuration;

public partial class accountmt_forgetpwd : System.Web.UI.Page
{
    protected void Page_Load(object sender, EventArgs e)
    {
        //EGPWebPageEx.ValidateCode
    }
    protected void btn_Send_Click(object sender, EventArgs e)
    {
        if (Check())
        {
            wsAccountInfo wsaccount = new wsAccountInfo();
            Cryptography crypt = new Cryptography();
            string[] radom_A = crypt.wsAccountValidate(crypt.getRadNum());

            //帳號為已核發者，才可重新申請密碼(1,2)
            if (wsaccount.IsExistsAccount(txt_account.Text.Trim(), radom_A[0].ToString(), radom_A[1].ToString()))
            {
                DataSet ds_account = wsaccount.getAccountBase(txt_account.Text.Trim(), radom_A[0].ToString(), radom_A[1].ToString());
                string password = crypt.GetRandomPassword(6);

                string[] _mailTo = new string[1];
                _mailTo[0] = ds_account.Tables[0].Rows[0]["ai_email"].ToString();
                string[] _mailCc = new string[1];//副本
                _mailCc[0] = "";
                string[] _mailBcc = new string[1];//密件
                _mailBcc[0] = "";

                if (Convert.ToBoolean(ConfigurationManager.AppSettings["DEBUGMode"].ToString()))
                {
                    //測試
                    _mailTo[0] = ConfigurationManager.AppSettings["MailTestTo"].ToString();
                }

                SendMail sendmail = new SendMail();
                bool result = sendmail.Mail_Forgetpwd(_mailTo, _mailCc, _mailBcc, txt_account.Text, password);
                if (result)
                {
                    //"新密碼已經寄發至使用者電子郵件信箱，請登入後再次修改密碼方能使用系統！’
                    wsaccount.UpdataPWD(txt_account.Text.Trim(), crypt.GetPAWDHash(password), radom_A[0].ToString(), radom_A[1].ToString());
                    ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('新密碼已經寄發至使用者電子郵件信箱，請登入後再次修改密碼方能使用系統！');
                                                    window.close();</script>");
                }
                else
                {
                    MessageBox.Show("請再試一次");
                }
            }
            else
            {
                //取得帳號目前狀態
                //getAccountBase
                DataSet ds_account = wsaccount.getAccountBase(txt_account.Text.Trim(), radom_A[0].ToString(), radom_A[1].ToString());
                if (ds_account.Tables[0].Rows.Count > 0)
                {
                    string accountstatus = ds_account.Tables[0].Rows[0]["ai_accountstatus"].ToString();
                    if (accountstatus == "0" || accountstatus == "4")
                        MessageBox.Show("使用者帳號尚未核發，不可申請密碼資訊");
                    else if (accountstatus == "3")
                        MessageBox.Show("使用者帳號已被停用，不可申請密碼資訊");
                }
                else
                {
                    MessageBox.Show("使用者帳號不存在，請重新輸入");
                }

                txt_account.Text = "";
                txt_ValidateCode.Text = "";
            }
        }
    }

    private bool Check()
    {
        StringBuilder sb = new StringBuilder();

        #region Accountmt特殊字元判斷-沒有MasterPage
        BaseAdminPage egpadmin = new BaseAdminPage();
        foreach (Control ctl in Page.Form.Controls)
        {
            if (ctl is TextBox)
            {
                TextBox objTextBox = default(TextBox);
                objTextBox = (TextBox)ctl;
                if ((egpadmin.tbWord(objTextBox.Text)))
                {
                    sb.Append(objTextBox.Text + ":含有系統不允許的特殊字元");                    
                }
            }
        }
        #endregion

        if (txt_ValidateCode.Text.Trim() != Session["validatecode"].ToString())
        {
            sb.Append("驗證碼不正確");
            txt_ValidateCode.Text = "";
        }

        if (sb.Length > 0)
        {
            MessageBox.Show(sb.ToString());
            return false;
        }
        else
        {
            return true;
        }
    }
}