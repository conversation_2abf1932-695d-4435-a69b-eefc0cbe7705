﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.IO;
using System.Drawing;
using System.Drawing.Imaging;


public partial class applydatamt_carcompomodelList : BaseAdminPage
{
    wsCodetbl wscode = new wsCodetbl();
    wsCompany wscompany = new wsCompany();
    //Cryptography crypy = new Cryptography();
    wsApplylistQry oALQ = new wsApplylistQry();

    protected void Page_Load(object sender, EventArgs e)
    {
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = '../accountmt/index.aspx';</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" &&
                (
                (
                 GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("pg_adm") || GetAgentAccountGrpList().Contains("rs_adm") || GetAgentAccountGrpList().Contains("fis_adm") || GetAgentAccountGrpList().Contains("rv_adm")
                )
                ||
                (
                  (GetAgentAccountGrpList().Contains("cga_adm") || GetAgentAccountGrpList().Contains("cam_adm")) && CheckSingleApplyDataAccessRight(DeCode(Request["applyno"].ToString()))
                )
                )
                )
            {
                string applyno = DeCode(Request["applyno"].ToString());
                lbl_applyno.Text = applyno;
                if (!IsPostBack)
                {
                    BindData(applyno);
                    string[] arr = wsApplybasedataDetailValid(getRadNum());
                    wsApplybasedataDetail applydatadetail = new wsApplybasedataDetail();
                    string s_CarCertResul = "";
                    string datacheck_result = applydatadetail.submit_datacheck(lbl_applyno.Text.Trim(), arr[0].ToString(), arr[1].ToString());
                    if (datacheck_result == "")
                    {
                        //與污染比對
                        s_CarCertResul = CheckCarCertApplySubmit();
                        if (s_CarCertResul.Length > 0)
                        {
                            btn_applysubmit.Attributes["onclick"] = string.Format(@"if (!confirm('{0} \n欄位與污染車有所不一致!!!'))return false;", applydatadetail.compareEnStrtoCn(s_CarCertResul));
                        }
                    }
                }
                applybase.NavigateUrl = "applybasedata.aspx?applyno=" + Server.UrlEncode(Request["applyno"]);
                carcompomodel.NavigateUrl = "carcompomodelList.aspx?applyno=" + Server.UrlEncode(Request["applyno"]);
                docreview.NavigateUrl = "";
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }

    }

    #region 更改的麵包屑傳入的參數
    protected void Page_LoadComplete(object sender, EventArgs e)
    {
        ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text = ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text.Replace("applydatamt/carcompomodelList.aspx", "applydatamt/carcompomodelList.aspx?applyno=" + Server.UrlEncode(Request["applyno"].ToString()));
    }
    #endregion

    protected void addnew_carcompomodel_Click(object sender, EventArgs e)
    {
        switch (lbl_vechiletype.Text)
        {
            case "G":
            case "D":
                Response.Redirect("carcompomodel_g.aspx?applyno=" + Server.UrlEncode(EnCode(lbl_applyno.Text)) + "&carcompomodelno=");
                break;
            case "M":
                Response.Redirect("carcompomodel_m.aspx?applyno=" + Server.UrlEncode(EnCode(lbl_applyno.Text)) + "&carcompomodelno=");
                break;
            default:
                break;
        }

    }

    protected void gv_carcompomodel_RowDataBound(object sender, GridViewRowEventArgs e)
    {

        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            string carcompomodelno = DataBinder.Eval(e.Row.DataItem, "cpm_carcompomodelno").ToString();
            Button btn_delete = (Button)e.Row.FindControl("btn_delete");

            //lb_delete.Attributes["OnClick"] = "btn_delete_Click(" + carcompomodelno + ")";
            btn_delete.Attributes["carcompomodelno"] = carcompomodelno;
            LinkButton lbtn_examineform = (LinkButton)e.Row.FindControl("lbtn_examineform");
            lbtn_examineform.Attributes["carcompomodelno"] = carcompomodelno;
            lbtn_examineform.Attributes["carmodelno"] = DataBinder.Eval(e.Row.DataItem, "carmodelno").ToString().Trim();
            LinkButton lbtn_testreport = (LinkButton)e.Row.FindControl("lbtn_testreport");
            lbtn_testreport.Attributes["carcompomodelno"] = carcompomodelno;
            lbtn_testreport.Attributes["ct_at_desc"] = DataBinder.Eval(e.Row.DataItem, "ct_at_desc").ToString().Trim();
            lbtn_testreport.Attributes["carmodelno"] = DataBinder.Eval(e.Row.DataItem, "carmodelno").ToString().Trim();
            LinkButton lbtn_carspectbl = (LinkButton)e.Row.FindControl("lbtn_carspectbl");
            lbtn_carspectbl.Attributes["carcompomodelno"] = carcompomodelno;
            lbtn_carspectbl.Attributes["carmodelno"] = DataBinder.Eval(e.Row.DataItem, "carmodelno").ToString().Trim();
            lbtn_carspectbl.Attributes["ct_at_desc"] = DataBinder.Eval(e.Row.DataItem, "ct_at_desc").ToString().Trim();
            if (DataBinder.Eval(e.Row.DataItem, "carmodelno").ToString().Trim() == "")
            {
                lbtn_testreport.Enabled = false;
                lbtn_examineform.Enabled = false;
                lbtn_carspectbl.Enabled = false;
            }
            HyperLink hlk_carcompomodelno = (HyperLink)e.Row.FindControl("hlk_carcompomodelno");
            hlk_carcompomodelno.Text = carcompomodelno;
            Label carmodelnums = (Label)e.Row.FindControl("lbl_carmodelnums");
            HyperLink hlk_edit_carmodellist = (HyperLink)e.Row.FindControl("hlk_edit_carmodellist");
            hlk_edit_carmodellist.ImageUrl = "../images/show.GIF";

            hlk_edit_carmodellist.NavigateUrl = "carcompomodel_carmodellist.aspx?applyno=" + Server.UrlEncode(EnCode(lbl_applyno.Text)) + "&carcompomodelno=" + Server.UrlEncode(EnCode(carcompomodelno));

            carmodelnums.Text = DataBinder.Eval(e.Row.DataItem, "carmodelnums").ToString();
            Label fullstylename = (Label)e.Row.FindControl("lbl_fullstylename");
            fullstylename.Text = DataBinder.Eval(e.Row.DataItem, "fullcarstylename").ToString();
            switch (lbl_vechiletype.Text)
            {
                case "G":
                case "D":
                    hlk_carcompomodelno.NavigateUrl = "carcompomodel_g.aspx?applyno=" + Server.UrlEncode(EnCode(lbl_applyno.Text)) + "&carcompomodelno=" + Server.UrlEncode(EnCode(carcompomodelno));
                    break;
                case "M":
                    hlk_carcompomodelno.NavigateUrl = "carcompomodel_m.aspx?applyno=" + Server.UrlEncode(EnCode(lbl_applyno.Text)) + "&carcompomodelno=" + Server.UrlEncode(EnCode(carcompomodelno));
                    break;
                default:
                    hlk_carcompomodelno.NavigateUrl = "";
                    break;
            }

            HyperLink hlk_noisedefenseplan = (HyperLink)e.Row.FindControl("hlk_noisedefenseplan");
            hlk_noisedefenseplan.ImageUrl = "../images/show.GIF";
            hlk_noisedefenseplan.NavigateUrl = "noisedefenseplan.aspx?applyno=" + Server.UrlEncode(EnCode(lbl_applyno.Text)) + "&carcompomodelno=" + Server.UrlEncode(EnCode(carcompomodelno));
            HyperLink hlk_fixcatalog = (HyperLink)e.Row.FindControl("hlk_fixcatalog");
            hlk_fixcatalog.ImageUrl = "../images/show.GIF";
            hlk_fixcatalog.NavigateUrl = "fixcatalog.aspx?applyno=" + Server.UrlEncode(EnCode(lbl_applyno.Text)) + "&carcompomodelno=" + Server.UrlEncode(EnCode(carcompomodelno));
            HyperLink hlk_uploadfileinfo = (HyperLink)e.Row.FindControl("hlk_uploadfileinfo");
            hlk_uploadfileinfo.ImageUrl = "../images/show.GIF";
            hlk_uploadfileinfo.NavigateUrl = "carmodellist_otherfileupload.aspx?applyno=" + Server.UrlEncode(EnCode(lbl_applyno.Text)) + "&carcompomodelno=" + Server.UrlEncode(EnCode(carcompomodelno));
            //if ( DataBinder.Eval(e.Row.DataItem, "ct_at_desc").ToString().Trim() == "撤銷" || lbl_applystatus.Text == "填單中") // || lbl_applystatus.Text == "補件中" || lbl_applystatus.Text == "拒件")
            //{
            btn_delete.Visible = true;
            //}
            //else
            //{
            //    btn_delete.Visible = false;
            //}
        }
    }

    private void BindData(string applyno)
    {
        //gv_carcompomodel.DataSource = "";

        int keynum = getRadNum();
        string[] arr = wsApplylistQryValid(keynum);

        string[] arr_applyqrylist = wsApplylistQryValid(keynum);
        DataTable dtList2 = oALQ.get_applylistqryno_list("", applyno, "", "", "", "", "", "", "", "", "", "", "", arr_applyqrylist[0].ToString(), arr_applyqrylist[1].ToString());
        if (dtList2.Rows.Count > 0)
        {
            lbl_applystatus.Text = dtList2.Rows[0]["ct_aps_desc"].ToString();
            lbl_vechiletype.Text = dtList2.Rows[0]["abd_vechiletype"].ToString();
            //填單中、補件中、拒件  
            if (dtList2.Rows[0]["abd_applystatus"].ToString() == "10" || dtList2.Rows[0]["abd_applystatus"].ToString() == "40" || dtList2.Rows[0]["abd_applystatus"].ToString() == "90")
            {
                addnew_carcompomodel.Visible = true;
                btn_applysubmit.Visible = true;
            }
            else
            {
                addnew_carcompomodel.Visible = false;
                btn_applysubmit.Visible = false;
            }
        }

        DataTable dtList = oALQ.get_carcompomodel_qry(applyno, arr[0].ToString(), arr[1].ToString());
        gv_carcompomodel.DataSource = dtList;
        gv_carcompomodel.DataBind();
        dtList.Dispose();
        dtList2.Dispose();
    }

    protected void btn_delete_Click(object sender, EventArgs e)
    {
        //lb_delete.Attributes["carcompomodelno"] 
        int keynum = getRadNum();
        string[] arr = wsCarCompoModelValidate(keynum);
        wsCarCompoModel wsCarCmpModel = new wsCarCompoModel();
        Button btn_delete = (Button)sender;
        int delete_result = wsCarCmpModel.Delete_single_carcompomodel_whole_data(lbl_applyno.Text.Trim(), btn_delete.Attributes["carcompomodelno"].ToString(), arr[0].ToString(), arr[1].ToString());
        switch (delete_result)
        {
            case -1: MessageBox.Show("刪除失敗！"); break;
            case 1: MessageBox.Show("刪除失敗！"); break;
            default: MessageBox.Show("刪除完成！"); BindData(lbl_applyno.Text.Trim()); break;
        }
        wsCarCmpModel.Dispose();
    }
    protected void lbtn_examineform_Click(object sender, EventArgs e)
    {
        //審查表
        LinkButton lbtn_examineform = (LinkButton)sender;

        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        int keynum = getRadNum();
        string[] arr = wsApplybasedataDetailValid(keynum);
        string applyno = Request["applyno"].ToString();
        string carcompomodelno = lbtn_examineform.Attributes["carcompomodelno"].ToString();

        string scriptString = "";
        scriptString = string.Format(@"window.open('representativevehiclenote.aspx?applyno={0}&carcompomodelno={1}',10,'scrollbars=yes');window.location.href='carcompomodelList.aspx?applyno={0}';", Server.UrlEncode(applyno), Server.UrlEncode(EnCode(carcompomodelno)));
        this.ClientScript.RegisterStartupScript(this.GetType(), "", scriptString, true);
    }
    protected void lbtn_testreport_Click(object sender, EventArgs e)
    {
        //測定報告

        LinkButton lbtn_testreport = (LinkButton)sender;
        string scriptString = "";

        DataView dv = new MailParaControl().GetApplyBaseData(lbl_applyno.Text);
        string compidnoE = dv[0]["CompNo"].ToString(); //公司統編
        string EngineFamily = dv[0]["EngineFamily"].ToString(); //引擎族
        Cryptography crypy = new Cryptography();
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        int keynum = getRadNum();
        string[] arr = wsApplybasedataDetailValid(keynum);

        string applyno = Request["applyno"].ToString();
        string carcompomodelno = lbtn_testreport.Attributes["carcompomodelno"].ToString();
        string carmodelno = lbtn_testreport.Attributes["carmodelno"].ToString();

        #region 沿用 如果為系統第一筆需調整為可編輯
        if (wsApplyData.IsDateoneExistForTRP(compidnoE, carcompomodelno, EngineFamily, arr[0].ToString(), arr[1].ToString()))
        {
            scriptString = string.Format(@"window.open('noisetestrpt_readnoly.aspx?applyno={0}&carcompomodelno={1}&carmodelno={2}',10,'scrollbars=yes'); window.location.href='carcompomodelList.aspx?applyno={0}';", Server.UrlEncode(applyno), Server.UrlEncode(EnCode(carcompomodelno)), Server.UrlEncode(EnCode(carmodelno)));
            this.ClientScript.RegisterStartupScript(this.GetType(), "", scriptString, true);
        }
        else
        {
            //第一筆 判斷是否有測定報告如果有就到編輯畫面，沒有則到新增
            if (wsApplyData.IsNoisetestrptExist(carcompomodelno, carmodelno, arr[0].ToString(), arr[1].ToString()))
            {
                scriptString = string.Format(@"var Mleft = (screen.width - 800) / 2;var Mtop = (screen.height - 650) / 2;window.open('noisetestrpt_edit.aspx?applyno={0}&carcompomodelno={1}&carmodelno={2}','TR','scrollbars=yes,height=600,width=890,top='+Mtop+',left='+Mleft+''); window.location.href='carcompomodelList.aspx?applyno={0}';", Server.UrlEncode(applyno), Server.UrlEncode(EnCode(carcompomodelno)), Server.UrlEncode(EnCode(carmodelno)));
                this.ClientScript.RegisterStartupScript(this.GetType(), "", scriptString, true);
            }
            else
            {
                scriptString = string.Format(@"var Mleft = (screen.width - 800) / 2;var Mtop = (screen.height - 650) / 2;window.open('noisetestrpt.aspx?applyno={0}&carcompomodelno={1}&carmodelno={2}','TRE','scrollbars=yes,height=600,width=890,top='+Mtop+',left='+Mleft+''); window.location.href='carcompomodelList.aspx?applyno={0}';", Server.UrlEncode(applyno), Server.UrlEncode(EnCode(carcompomodelno)), Server.UrlEncode(EnCode(carmodelno)));
                this.ClientScript.RegisterStartupScript(this.GetType(), "", scriptString, true);
            }
        }

        #endregion
    }

    protected void lbtn_carspectbl_Click(object sender, EventArgs e)
    {
        //規格表
        LinkButton lbtn_carspectbl = (LinkButton)sender;
        string scriptString = "";

        DataView dv = new MailParaControl().GetApplyBaseData(lbl_applyno.Text);
        string compidnoE = dv[0]["CompNo"].ToString(); //公司統編
        string EngineFamily = dv[0]["EngineFamily"].ToString(); //引擎族
        Cryptography crypy = new Cryptography();
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        int keynum = getRadNum();
        string[] arr = wsApplybasedataDetailValid(keynum);

        string applyno = Request["applyno"].ToString();
        string carcompomodelno = lbtn_carspectbl.Attributes["carcompomodelno"].ToString();
        string carmodelno = lbtn_carspectbl.Attributes["carmodelno"].ToString();

        #region 沿用 如果為系統第一筆需調整為可編輯否則進唯獨其餘的都可以編輯
        if (wsApplyData.IsDateoneExist(compidnoE, carcompomodelno, EngineFamily, arr[0].ToString(), arr[1].ToString()))
        {
            scriptString = string.Format(@"window.open('carmodeldata_readonly.aspx?applyno={0}&carcompomodelno={1}&carmodelno={2}&mode={3}',10,'scrollbars=yes'); window.location.href='carcompomodelList.aspx?applyno={0}';", Server.UrlEncode(applyno), Server.UrlEncode(EnCode(carcompomodelno)), Server.UrlEncode(EnCode(carmodelno)), Server.UrlEncode(EnCode(lbl_vechiletype.Text.Trim())));
            this.ClientScript.RegisterStartupScript(this.GetType(), "", scriptString, true);
        }
        else
        {
            scriptString = string.Format(@"window.open('carmodeldatamt_g.aspx?applyno={0}&carcompomodelno={1}&carmodelno={2}&mode={3}',10,'scrollbars=yes'); window.location.href='carcompomodelList.aspx?applyno={0}';", Server.UrlEncode(applyno), Server.UrlEncode(EnCode(carcompomodelno)), Server.UrlEncode(EnCode(carmodelno)), Server.UrlEncode(EnCode(lbl_vechiletype.Text.Trim())));
            this.ClientScript.RegisterStartupScript(this.GetType(), "", scriptString, true);
        }
        #endregion
    }

    protected void btn_applysubmit_Click(object sender, EventArgs e)
    {
        //pr_check_submit_applydata       
        string[] arr = wsApplybasedataDetailValid(getRadNum());
        wsApplybasedataDetail applydatadetail = new wsApplybasedataDetail();
        string datacheck_result = applydatadetail.submit_datacheck(lbl_applyno.Text.Trim(), arr[0].ToString(), arr[1].ToString());
        //string s_CarCertResul = "";
        //在 if (!IsPostBack) 做污染比對
        ////與污染比對
        //s_CarCertResul = CheckCarCertApplySubmit();
        //if (s_CarCertResul.Length > 0)
        //{
        //    btn_applysubmit.Attributes["onclick"] = string.Format(@"if (!confirm('{0} \n欄位與污染車有所不一致!!!'))return false;", applydatadetail.compareEnStrtoCn(s_CarCertResul));
        //}
        if (datacheck_result == "")
        {
            //pr_applysubmit_datahandle 送件
            string submit_result = applydatadetail.submit_applydata(lbl_applyno.Text.Trim(), DeCode(Session["account"].ToString()), arr[0].ToString(), arr[1].ToString());

            if (submit_result == "1")
            {
                //取得流水號，產生Barcode

                string barcodeno = applydatadetail.getNewBarCodeNo(arr[0].ToString(), arr[1].ToString());
                byte[] barcodeimg = getBarCode.BitmapDataFromBitmap(getBarCode.GetCode39(barcodeno), ImageFormat.Jpeg);

                applydatadetail.Update_Nv_applybasedataBarcode(lbl_applyno.Text.Trim(), barcodeno, barcodeimg, arr[0].ToString(), arr[1].ToString());
                //MAIL
                MailReceiverNotify();

                MessageBox.Show("已完成送件");
                Response.Redirect("../applydatamt/nv_applylistqry.aspx");
            }
            else
            {
                MessageBox.Show("送件失敗：" + submit_result);
            }

        }
        else
        {
            MessageBox.Show("送件失敗：" + datacheck_result);
        }
    }
    //收件mail通知
    private void MailReceiverNotify()
    {
        DataView dv = new MailParaControl().GetApplyBaseData(lbl_applyno.Text.Trim());
        string CompName = dv[0]["CompName"].ToString();
        string EngineFamily = dv[0]["EngineFamily"].ToString();
        string[] arr_GroupAccount = { "02" };//審驗人員02,03,04,05
        //通知全部審驗人員02,03,04,05
        for (int j = 0; j < arr_GroupAccount.Length; j++)
        {
            DataView GropuAccount = new MailParaControl().GetGroupAccount(arr_GroupAccount[j]);

            for (int i = 0; i < GropuAccount.Count; i++)
            {
                string AccountName = GropuAccount[i]["AccountName"].ToString();
                string AccountMail = GropuAccount[i]["AccountMail"].ToString();
                string[] mailTo = new string[] { AccountMail };
                string[] _mailCc = new string[1];//副本
                _mailCc[0] = "";
                string[] _mailBcc = new string[1];//密件
                _mailBcc[0] = "";

                new SendMail().Mail_ReceiverNotify(mailTo, _mailCc, _mailBcc, AccountName, lbl_applyno.Text.Trim(), CompName);

            }
        }
    }
    protected void gv_carcompomodel_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        gv_carcompomodel.PageIndex = e.NewPageIndex;
        BindData(lbl_applyno.Text);
    }

    private string CheckCarCertApplySubmit()
    {
        string strApplyno = DeCode(Request["applyno"].ToString());
        DataView dv = new MailParaControl().GetApplyBaseData(strApplyno);

        Cryptography crypy = new Cryptography();
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        string[] radom_ad = crypy.wsApplybasedataDetailValid(crypy.getRadNum());
        string sUnSame = "";
        string smode = "2";
        string CarstyleYear = dv[0]["CarstyleYear"].ToString();
        string EngineFamily = dv[0]["EngineFamily"].ToString();
        string compidnoE = dv[0]["CompNo"].ToString();
        string IsImport = dv[0]["IsImport"].ToString();
        //string s_ = "";        
        switch (lbl_vechiletype.Text.ToUpper())
        {
            case "G":
                smode = "1"; //汽車
                break;
            default:
                smode = "2"; //機車
                break;
        }


        //connectARTC_D.ARTC ARTC_D = new connectARTC_D.ARTC();
        connectCarCert.CarCert CarCert = new connectCarCert.CarCert();
        string[] radom_CarCert = wsCarCertValid(Convert.ToInt16(DateTime.Now.DayOfWeek));
        DataSet ds_CarCert_data = new DataSet();
        if (!(lbl_vechiletype.Text.ToUpper().Equals("D")))
        {
            #region 取得污染與噪音的車型資料
            switch (lbl_vechiletype.Text.ToUpper())
            {
                case "G":
                case "M":
                    ds_CarCert_data = CarCert.CheckApplyBasicData(EnCode(smode), compidnoE, EnCode(CarstyleYear), EnCode(EngineFamily), radom_CarCert[0].ToString(), radom_CarCert[1].ToString());//汙染車資料                             
                    break;
                //case "D":
                //    ds_CarCert_data = ARTC_D.CheckconsReceiveCaseInfo(compidnoE, EnCode(CarstyleYear), EnCode(EngineFamily), radom_CarCert[0].ToString(), radom_CarCert[1].ToString());//汙染車資料               
                //    break;
            }
            DataSet ds_Carcompare_data = new DataSet(); //噪音車資料
            string sGearRatios = "";
            if (lbl_vechiletype.Text.ToUpper().Equals("G"))
            {
                ds_Carcompare_data = wsApplyData.SelfDataForPopulation_G(strApplyno, radom_ad[0].ToString(), radom_ad[1].ToString());
                //ds_Carcompare_data = wsApplyData.CommonDataForPopulation_G("6B3DU+Zqs/QCcC0yRN4Heg==", "G", "2", "2013", "U1234567_1222", radom_ad[0].ToString(), radom_ad[1].ToString());
            }
            else if (lbl_vechiletype.Text.ToUpper().Equals("M"))
            {
                ds_Carcompare_data = wsApplyData.SelfDataForPopulation_M(strApplyno, radom_ad[0].ToString(), radom_ad[1].ToString());
            }
            else //D
            {
                ds_Carcompare_data = wsApplyData.SelfDataForPopulation_D(strApplyno, radom_ad[0].ToString(), radom_ad[1].ToString());
            }
            #endregion

            DataRow[] dt2_compare_carstylename = new DataRow[0];
            DataTable dt_IsExistCarcompare_data = new DataTable();

            #region 當污染和噪音都有資料時才比對資料是否相同
            if (ds_CarCert_data.Tables[0].Rows.Count > 0 && ds_Carcompare_data.Tables[0].Rows.Count > 0)
            {
                switch (lbl_vechiletype.Text.ToUpper())
                {
                    case "G":
                    case "M":
                        for (int a = 1; a < 9; a++)
                        {
                            //合併齒比
                            sGearRatios += ds_CarCert_data.Tables[0].Rows[0]["GearRatios" + a].ToString().Trim() == "" ? "" : ds_CarCert_data.Tables[0].Rows[0]["GearRatios" + a].ToString().Trim() + ",";
                        }

                        sGearRatios = sGearRatios.TrimEnd(',');//sGearRatios.Length > 0 ? sGearRatios.Substring(0, sGearRatios.Length - 1) : sGearRatios;

                        for (int j = 0; j < ds_Carcompare_data.Tables[0].Rows.Count; j++)
                        {
                            if (lbl_vechiletype.Text.ToUpper().Equals("G"))
                            {
                                //重設污染車齒比
                                ds_CarCert_data.Tables[0].Rows[0]["GearRatios1"] = sGearRatios;
                                //資料比對cmdg_transmissionmodel-->Transmission(變速系統);ct_gm_desc-->TransmissionType(排檔方式)
                                //20130708 增加 先比對車型是否存在 ，再決定是否往下比
                                dt2_compare_carstylename = ds_CarCert_data.Tables[0].Select("Brand='" + ds_Carcompare_data.Tables[0].Rows[j]["ct_bd_desc"].ToString() + "' and CCarModel='" + ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carchnmodelname"].ToString() + "' and ECarModel= '" + ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carengmodelname"].ToString() + "' and CarModelType= '" + ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carmodelcode"].ToString() + "' and Exhaust= '" + ds_Carcompare_data.Tables[0].Rows[j]["cpm_exhuast"].ToString().Replace(".00", "") + "' and TransmissionType= '" + ds_Carcompare_data.Tables[0].Rows[j]["ct_gm_desc"].ToString() + "' and TransmissionNum= '" + ds_Carcompare_data.Tables[0].Rows[j]["cmdg_transmissionnum"].ToString() + "' and CarDoorCount='" + ds_Carcompare_data.Tables[0].Rows[j]["ct_vr_desc"].ToString() + "' and Others='" + ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carmodeladd"].ToString() + "'");
                                if (dt2_compare_carstylename.Length > 0)
                                {
                                    dt_IsExistCarcompare_data = dt2_compare_carstylename.CopyToDataTable();
                                    string[] aryCarNoise_data = new string[] { ds_Carcompare_data.Tables[0].Rows[j]["abd_factoryname"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["abd_mailcertidocaddr"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_maxhorsepower"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_maxhorsepowerspeed"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_baseenginename"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_transmissionmodel"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["ct_cs_desc"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cpm_cylindernums"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_cylindermeters"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_cylinderstroke"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_compressionratio"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["inletmode"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_drivetype"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_differentialgearratio"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["ct_bd_desc"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carchnmodelname"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carengmodelname"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carmodelcode"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cpm_exhuast"].ToString().Replace(".00", ""), ds_Carcompare_data.Tables[0].Rows[j]["ct_gm_desc"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_transmissionnum"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["ct_vr_desc"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carmodeladd"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["tmp_gearratio"].ToString() };
                                    //噪音與污染車資料比對及紀錄不一致欄位
                                    sUnSame = CheckAndRecord(dt_IsExistCarcompare_data, aryCarNoise_data);
                                }
                            }
                            if (lbl_vechiletype.Text.ToUpper().Equals("M"))
                            {
                                //重設污染車齒比
                                ds_CarCert_data.Tables[0].Rows[0]["GearRatios1"] = sGearRatios;
                                //資料比對cmdg_transmissionmodel-->Transmission(變速系統);ct_gm_desc-->TransmissionType(排檔方式)
                                //20130708 增加 先比對車型是否存在 ，再決定是否往下比
                                dt2_compare_carstylename = ds_CarCert_data.Tables[0].Select("Brand='" + ds_Carcompare_data.Tables[0].Rows[j]["ct_bd_desc"].ToString() + "' and CCarModel='" + ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carchnmodelname"].ToString() + "' and ECarModel= '" + ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carengmodelname"].ToString() + "' and CarModelType= '" + ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carmodelcode"].ToString() + "' and Exhaust= '" + ds_Carcompare_data.Tables[0].Rows[j]["cpm_exhuast"].ToString().Replace(".00", "") + "' and TransmissionType= '" + ds_Carcompare_data.Tables[0].Rows[j]["ct_gm_desc"].ToString() + "' and TransmissionNum= '" + ds_Carcompare_data.Tables[0].Rows[j]["cmdg_transmissionnum"].ToString() + "' and Others='" + ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carmodeladd"].ToString() + "'");
                                if (dt2_compare_carstylename.Length > 0)
                                {
                                    dt_IsExistCarcompare_data = dt2_compare_carstylename.CopyToDataTable();
                                    string[] aryCarNoise_data = new string[] { ds_Carcompare_data.Tables[0].Rows[j]["abd_factoryname"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["abd_mailcertidocaddr"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_maxhorsepower"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_maxhorsepowerspeed"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_baseenginename"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_transmissionmodel"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["ct_cs_desc"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cpm_cylindernums"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_cylindermeters"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_cylinderstroke"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_compressionratio"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["ct_bd_desc"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carchnmodelname"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carengmodelname"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carmodelcode"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cpm_exhuast"].ToString().Replace(".00", ""), ds_Carcompare_data.Tables[0].Rows[j]["ct_gm_desc"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_transmissionnum"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carmodeladd"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["tmp_gearratio"].ToString() };
                                    //噪音與污染車資料比對及紀錄不一致欄位
                                    sUnSame = CheckAndRecord(dt_IsExistCarcompare_data, aryCarNoise_data);
                                }
                            }

                        }
                        break;
                    case "D":
                        for (int j = 0; j < ds_Carcompare_data.Tables[0].Rows.Count; j++)
                        {
                            //資料比對cmdg_transmissionmodel-->Transmission(變速系統);ct_gm_desc-->TransmissionType(排檔方式)
                            //20130708 增加 先比對車型是否存在 ，再決定是否往下比
                            dt2_compare_carstylename = ds_CarCert_data.Tables[0].Select("Brand='" + ds_Carcompare_data.Tables[0].Rows[j]["ct_bd_desc"].ToString() + "' and CarStyleName_CName='" + ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carchnmodelname"].ToString() + "' and CarStyleName_E= '" + ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carengmodelname"].ToString() + "' and CarStyleID= '" + ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carmodelcode"].ToString() + "' and CC= '" + ds_Carcompare_data.Tables[0].Rows[j]["cpm_exhuast"].ToString().Replace(".00", "") + "' and Gear= '" + ds_Carcompare_data.Tables[0].Rows[j]["ct_gm_desc"].ToString() + "' and GearCount= '" + ds_Carcompare_data.Tables[0].Rows[j]["cmdg_transmissionnum"].ToString() + "' and DoorCount='" + ds_Carcompare_data.Tables[0].Rows[j]["ct_vr_desc"].ToString() + "' and Others='" + ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carmodeladd"].ToString() + "' and CarStyleName_C='" + ds_Carcompare_data.Tables[0].Rows[j]["carbodyshapedesc"].ToString() + "'");
                            if (dt2_compare_carstylename.Length > 0)
                            {
                                dt_IsExistCarcompare_data = dt2_compare_carstylename.CopyToDataTable();
                                string[] aryCarNoise_data = new string[] { ds_Carcompare_data.Tables[0].Rows[j]["abd_factoryname"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["abd_mailcertidocaddr"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_maxhorsepower_hb"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_maxhorsepowerspeed_hb"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_baseenginename"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_transmissionmodel"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["ct_cs_desc"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cpm_cylindernums"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_cylindermeters"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_cylinderstroke"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_compressionratio"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["inletmode"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_drivetype"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_differentialgearratio"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["ct_bd_desc"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carchnmodelname"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carengmodelname"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carmodelcode"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cpm_exhuast"].ToString().Replace(".00", ""), ds_Carcompare_data.Tables[0].Rows[j]["ct_gm_desc"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_transmissionnum"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["ct_vr_desc"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["cmdg_carmodeladd"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["carbodyshapedesc"].ToString(), ds_Carcompare_data.Tables[0].Rows[j]["tmp_gearratio"].ToString() };
                                //噪音與污染車資料比對及紀錄不一致欄位
                                sUnSame = CheckAndRecord(dt_IsExistCarcompare_data, aryCarNoise_data);
                            }
                        }
                        break;
                }
            }
            else
            {
                return ""; //沒有污染車資料
            }
            #endregion

            #region 回傳不一致欄位
            if (sUnSame.Length < 0)
            {
                return ""; //沒有不一致
            }
            else
            {
                return sUnSame.TrimEnd(',');
            }
            #endregion
        }
        else
        {
            return "";
        }
    }


    #region 噪音與污染車資料比對及紀錄不一致欄位
    private string CheckAndRecord(DataTable dt_CarCert_data, string[] aryCarNoise_data)
    {
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        DataTable dtCarCert = dt_CarCert_data;

        string strUnSame = "";
        string[] aryCarCert_data = new string[aryCarNoise_data.Length];
        //比對欄位
        switch (lbl_vechiletype.Text.ToUpper())
        {
            case "G":
                aryCarCert_data = new string[] { "Manufacturer", "MailAddress", "EngineKw", "EngineRpm", "BasicEngine", "Transmission", "Coolin", "CylinderNum", "Bore", "Stroke", "Compression", "AirAspiration", "DrivenWheels", "FinalDriveRatio", "Brand", "CCarModel", "ECarModel", "CarModelType", "Exhaust", "TransmissionType", "TransmissionNum", "CarDoorCount", "Others", "GearRatios1" };
                break;
            case "M":
                aryCarCert_data = new string[] { "Manufacturer", "MailAddress", "EngineKw", "EngineRpm", "BasicEngine", "Transmission", "Coolin", "CylinderNum", "Bore", "Stroke", "Compression", "Brand", "CCarModel", "ECarModel", "CarModelType", "Exhaust", "TransmissionType", "TransmissionNum", "Others", "GearRatios1" };
                break;
            case "D":
                aryCarCert_data = new string[] { "Manufacturer", "SplyComAddr", "MaxKW", "Maxrpm", "BasicEngineNo", "TSD", "TypeCoolingSystem", "NumberCylinders", "Bore", "Stroke", "CompressionRatio_Nominal", "MethodAirAspiration", "DW", "FDR", "Brand", "CarStyleName_CName", "CarStyleName_E", "CarStyleID", "CC", "Gear", "GearCount", "DoorCount", "Others", "CarStyleName_C", "GN" };
                break;
        }
        for (int i = 0; i < aryCarCert_data.Length; i++)
        {
            //紀錄不一致欄位
            if (wsApplyData.compareLinqToDS(dtCarCert, aryCarCert_data[i], aryCarNoise_data[i]) != "1")
            {
                strUnSame += wsApplyData.compareLinqToDS(dtCarCert, aryCarCert_data[i], aryCarNoise_data[i]) + ",";
            }
        }
        return strUnSame;
    }
    #endregion
}