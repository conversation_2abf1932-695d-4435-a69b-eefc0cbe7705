﻿<%@ Page Title="" Language="C#" MasterPageFile="~/masterpage/MasterOutset.master" AutoEventWireup="true" CodeFile="enabledpollutionlogin.aspx.cs" Inherits="accountmt_enabledpollutionlogin" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" Runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
 <div class="fullheader">
        <table width="100%" cellspacing="0" cellpadding="0" class="fullheaderinfo">
            <tr>
                <td valign="top">
                    <b>啟用由污染新增之公司資料_登入</b>
                </td>
                <td align="right" valign="top" class="font_loginInfo">
                </td>
            </tr>
        </table>
    </div>
    <!--{* fullheader end *}-->
<div class="fullcontent">
        <div class="formstyle">
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="font_fullcontent">
                <tr>
                    <td align="right">
                        統一編號：
                    </td>
                    <td>
                        <asp:TextBox ID="txt_compidno" runat="server" CssClass="inputTypeS" 
                            MaxLength="20"></asp:TextBox>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        污染系統帳號：
                    </td>
                    <td>
                        <asp:TextBox ID="txt_account" runat="server" CssClass="inputTypeS" 
                            MaxLength="20"></asp:TextBox>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        污染系統密碼：
                    </td>
                    <td>
                        <asp:TextBox ID="txt_password" runat="server" CssClass="inputTypeS" 
                            TextMode="Password" 
                            onkeypress="return clickButton(event,'ctl00_ContentPlaceHolder1_btn_Login')" 
                            MaxLength="20"></asp:TextBox>
                    </td>
                </tr>
                <tr>
                    <td align="right" colspan="2">
                        <asp:Button ID="btn_Login" runat="server" Text="登入" CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'"
                            onmouseout="this.className='btn_mouseout'" onclick="btn_Login_Click" />
                    </td>
                </tr>
            </table>
        </div>
    </div>
    <!--{* fullcontent end *}-->
    <div class="fullfooter">
    </div>
</asp:Content>

