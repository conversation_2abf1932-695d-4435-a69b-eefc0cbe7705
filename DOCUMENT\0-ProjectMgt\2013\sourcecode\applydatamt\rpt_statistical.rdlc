<?xml version="1.0" encoding="utf-8"?>
<Report xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner" xmlns="http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition">
  <DataSources>
    <DataSource Name="certificatedDataSet">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>13479388-b7e7-4d9c-9eef-274d09442bfa</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="DT_statistica">
      <Fields>
        <Field Name="abd_applyno">
          <DataField>abd_applyno</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="cardesc">
          <DataField>cardesc</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="vechile">
          <DataField>vechile</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="powerfrom">
          <DataField>powerfrom</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="months">
          <DataField>months</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="import">
          <DataField>import</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="cmdg_cartype">
          <DataField>cmdg_cartype</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="abd_vechiletype">
          <DataField>abd_vechiletype</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="abd_powerfrom">
          <DataField>abd_powerfrom</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="abd_validateddate">
          <DataField>abd_validateddate</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="abd_isimport">
          <DataField>abd_isimport</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="cmdg_carcompomodelno">
          <DataField>cmdg_carcompomodelno</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>certificatedDataSet</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <rd:DataSetInfo>
        <rd:DataSetName>certificatedDataSet</rd:DataSetName>
        <rd:SchemaPath>D:\Olivia\noisevalidation\PROGRAM\webRoot\App_Code\DataSet\certificatedDataSet.xsd</rd:SchemaPath>
        <rd:TableName>statistica_DataTable</rd:TableName>
        <rd:TableAdapterFillMethod />
        <rd:TableAdapterGetDataMethod />
        <rd:TableAdapterName />
      </rd:DataSetInfo>
    </DataSet>
  </DataSets>
  <Body>
    <ReportItems>
      <Tablix Name="Tablix7">
        <TablixCorner>
          <TablixCornerRows>
            <TablixCornerRow>
              <TablixCornerCell>
                <CellContents>
                  <Textbox Name="Textbox221">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value />
                            <Style />
                          </TextRun>
                        </TextRuns>
                        <Style />
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox160</rd:DefaultName>
                    <Style>
                      <Border>
                        <Style>Solid</Style>
                      </Border>
                      <BackgroundColor>#e0eabf</BackgroundColor>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                  <ColSpan>3</ColSpan>
                  <RowSpan>2</RowSpan>
                </CellContents>
              </TablixCornerCell>
              <TablixCornerCell />
              <TablixCornerCell />
            </TablixCornerRow>
            <TablixCornerRow>
              <TablixCornerCell />
              <TablixCornerCell />
              <TablixCornerCell />
            </TablixCornerRow>
          </TablixCornerRows>
        </TablixCorner>
        <TablixBody>
          <TablixColumns>
            <TablixColumn>
              <Width>0.98425in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.98425in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.98425in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.98425in</Width>
            </TablixColumn>
          </TablixColumns>
          <TablixRows>
            <TablixRow>
              <Height>0.23622in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox76">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=CountDistinct(Fields!abd_applyno.Value)</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox46</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Bottom</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox162">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Count(Fields!cmdg_carcompomodelno.Value)</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox148</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <RightBorder>
                          <Width>=IIF(Fields!months.Value = 12,"2pt","1pt")</Width>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox299">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=CountDistinct(Fields!abd_applyno.Value)</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox293</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Bottom</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox300">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Count(Fields!cmdg_carcompomodelno.Value)</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox297</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.23622in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox307">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=CountDistinct(Fields!abd_applyno.Value)</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox302</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <BottomBorder>
                          <Width>2pt</Width>
                        </BottomBorder>
                        <VerticalAlign>Bottom</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox308">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Count(Fields!cmdg_carcompomodelno.Value)</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox303</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <BottomBorder>
                          <Width>2pt</Width>
                        </BottomBorder>
                        <RightBorder>
                          <Width>=IIF(Fields!months.Value = 12,"2pt","1pt")</Width>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox309">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=CountDistinct(Fields!abd_applyno.Value)</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox304</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <BottomBorder>
                          <Width>2pt</Width>
                        </BottomBorder>
                        <VerticalAlign>Bottom</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox310">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Count(Fields!cmdg_carcompomodelno.Value)</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox305</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <BottomBorder>
                          <Width>2pt</Width>
                        </BottomBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.23622in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox311">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=CountDistinct(Fields!abd_applyno.Value)</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox302</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Width>=IIF(Fields!import.Value = 1,"2pt","1pt")</Width>
                        </TopBorder>
                        <VerticalAlign>Bottom</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox312">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Count(Fields!cmdg_carcompomodelno.Value)</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox303</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Width>=IIF(Fields!import.Value = 1,"2pt","1pt")</Width>
                        </TopBorder>
                        <RightBorder>
                          <Width>=IIF(Fields!months.Value = 12,"2pt","1pt")</Width>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox313">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=CountDistinct(Fields!abd_applyno.Value)</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox304</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Width>=IIF(Fields!import.Value = 1,"2pt","1pt")</Width>
                        </TopBorder>
                        <VerticalAlign>Bottom</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox466">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Count(Fields!cmdg_carcompomodelno.Value)</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox464</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Width>=IIF(Fields!import.Value = 1,"2pt","1pt")</Width>
                        </TopBorder>
                        <RightBorder>
                          <Width>=IIF(Fields!months.Value = 12,"2pt","1pt")</Width>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.23622in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox421">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=CountDistinct(Fields!abd_applyno.Value)</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox421</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Bottom</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox422">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Count(Fields!cmdg_carcompomodelno.Value)</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox422</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <RightBorder>
                          <Width>=IIF(Fields!months.Value = 12,"2pt","1pt")</Width>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox423">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=CountDistinct(Fields!abd_applyno.Value)</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox423</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Bottom</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox314">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Count(Fields!cmdg_carcompomodelno.Value)</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox305</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
          </TablixRows>
        </TablixBody>
        <TablixColumnHierarchy>
          <TablixMembers>
            <TablixMember>
              <Group Name="Group_months4">
                <GroupExpressions>
                  <GroupExpression>=Fields!months.Value</GroupExpression>
                </GroupExpressions>
              </Group>
              <SortExpressions>
                <SortExpression>
                  <Value>=Fields!months.Value</Value>
                </SortExpression>
              </SortExpressions>
              <TablixHeader>
                <Size>0.23622in</Size>
                <CellContents>
                  <Textbox Name="Textbox42">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>=Fields!months.Value &amp; " 月"</Value>
                            <Style />
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox39</rd:DefaultName>
                    <Style>
                      <Border>
                        <Style>Solid</Style>
                      </Border>
                      <RightBorder>
                        <Width>=IIF(Fields!months.Value = 12,"2pt","1pt")</Width>
                      </RightBorder>
                      <BackgroundColor>#e0eabf</BackgroundColor>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </CellContents>
              </TablixHeader>
              <TablixMembers>
                <TablixMember>
                  <TablixHeader>
                    <Size>0.23622in</Size>
                    <CellContents>
                      <Textbox Name="Textbox200">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>合格證</Value>
                                <Style />
                              </TextRun>
                            </TextRuns>
                            <Style>
                              <TextAlign>Center</TextAlign>
                            </Style>
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox184</rd:DefaultName>
                        <Style>
                          <Border>
                            <Style>Solid</Style>
                          </Border>
                          <BackgroundColor>#e0eabf</BackgroundColor>
                          <VerticalAlign>Middle</VerticalAlign>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                </TablixMember>
                <TablixMember>
                  <TablixHeader>
                    <Size>0.23622in</Size>
                    <CellContents>
                      <Textbox Name="Textbox201">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>車型組</Value>
                                <Style />
                              </TextRun>
                            </TextRuns>
                            <Style>
                              <TextAlign>Center</TextAlign>
                            </Style>
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox186</rd:DefaultName>
                        <Style>
                          <Border>
                            <Style>Solid</Style>
                          </Border>
                          <RightBorder>
                            <Width>=IIF(Fields!months.Value = 12,"2pt","1pt")</Width>
                          </RightBorder>
                          <BackgroundColor>#e0eabf</BackgroundColor>
                          <VerticalAlign>Middle</VerticalAlign>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                </TablixMember>
              </TablixMembers>
            </TablixMember>
            <TablixMember>
              <TablixHeader>
                <Size>0.23622in</Size>
                <CellContents>
                  <Textbox Name="Textbox294">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>小計</Value>
                            <Style />
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox291</rd:DefaultName>
                    <Style>
                      <Border>
                        <Style>Solid</Style>
                      </Border>
                      <BackgroundColor>#e0eabf</BackgroundColor>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </CellContents>
              </TablixHeader>
              <TablixMembers>
                <TablixMember>
                  <TablixHeader>
                    <Size>0.23622in</Size>
                    <CellContents>
                      <Textbox Name="Textbox295">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>合格證</Value>
                                <Style />
                              </TextRun>
                            </TextRuns>
                            <Style>
                              <TextAlign>Center</TextAlign>
                            </Style>
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox292</rd:DefaultName>
                        <Style>
                          <Border>
                            <Style>Solid</Style>
                          </Border>
                          <BackgroundColor>#e0eabf</BackgroundColor>
                          <VerticalAlign>Middle</VerticalAlign>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                </TablixMember>
                <TablixMember>
                  <TablixHeader>
                    <Size>0.23622in</Size>
                    <CellContents>
                      <Textbox Name="Textbox298">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>車型組</Value>
                                <Style />
                              </TextRun>
                            </TextRuns>
                            <Style>
                              <TextAlign>Center</TextAlign>
                            </Style>
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox296</rd:DefaultName>
                        <Style>
                          <Border>
                            <Style>Solid</Style>
                          </Border>
                          <BackgroundColor>#e0eabf</BackgroundColor>
                          <VerticalAlign>Middle</VerticalAlign>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                </TablixMember>
              </TablixMembers>
            </TablixMember>
          </TablixMembers>
        </TablixColumnHierarchy>
        <TablixRowHierarchy>
          <TablixMembers>
            <TablixMember>
              <Group Name="Group_vechile6">
                <GroupExpressions>
                  <GroupExpression>=Fields!vechile.Value</GroupExpression>
                </GroupExpressions>
              </Group>
              <SortExpressions>
                <SortExpression>
                  <Value>=Fields!import.Value</Value>
                </SortExpression>
              </SortExpressions>
              <TablixHeader>
                <Size>0.98425in</Size>
                <CellContents>
                  <Textbox Name="Group14">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>=Switch(
Fields!vechile.Value = "G","汽油(G)",
Fields!vechile.Value = "D","柴油(D)",
Fields!vechile.Value = "M","機車(M)",
Fields!vechile.Value = "E","電動車(E)"
)</Value>
                            <Style />
                          </TextRun>
                        </TextRuns>
                        <Style />
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Group3</rd:DefaultName>
                    <Style>
                      <Border>
                        <Style>Solid</Style>
                      </Border>
                      <BottomBorder>
                        <Width>2pt</Width>
                      </BottomBorder>
                      <BackgroundColor>White</BackgroundColor>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </CellContents>
              </TablixHeader>
              <TablixMembers>
                <TablixMember>
                  <Group Name="Group_import4">
                    <GroupExpressions>
                      <GroupExpression>=Fields!import.Value</GroupExpression>
                    </GroupExpressions>
                  </Group>
                  <SortExpressions>
                    <SortExpression>
                      <Value>=Fields!cardesc.Value</Value>
                    </SortExpression>
                  </SortExpressions>
                  <TablixHeader>
                    <Size>0.98425in</Size>
                    <CellContents>
                      <Textbox Name="Group5">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>=Switch(Fields!import.Value = "1","國產",Fields!import.Value = "2","進口")</Value>
                                <Style />
                              </TextRun>
                            </TextRuns>
                            <Style />
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Group1</rd:DefaultName>
                        <Style>
                          <Border>
                            <Style>Solid</Style>
                          </Border>
                          <TopBorder>
                            <Width>=IIF(Fields!import.Value=2,"2pt","1pt")</Width>
                          </TopBorder>
                          <BottomBorder>
                            <Width>=IIF(Fields!import.Value=2,"2pt","1pt")</Width>
                          </BottomBorder>
                          <BackgroundColor>White</BackgroundColor>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                  <TablixMembers>
                    <TablixMember>
                      <Group Name="Group_cardesc4">
                        <GroupExpressions>
                          <GroupExpression>=Fields!cardesc.Value</GroupExpression>
                        </GroupExpressions>
                      </Group>
                      <SortExpressions>
                        <SortExpression>
                          <Value>=Fields!cardesc.Value</Value>
                        </SortExpression>
                      </SortExpressions>
                      <TablixHeader>
                        <Size>1.34884in</Size>
                        <CellContents>
                          <Textbox Name="Group15">
                            <CanGrow>true</CanGrow>
                            <KeepTogether>true</KeepTogether>
                            <Paragraphs>
                              <Paragraph>
                                <TextRuns>
                                  <TextRun>
                                    <Value>=Switch(
Fields!cardesc.Value = "1","轎車、旅行車(A)",
Fields!cardesc.Value = "2","小客、貨車(B)",
Fields!cardesc.Value = "3","大客車(C)",
Fields!cardesc.Value = "4","大貨車(D)",
Fields!cardesc.Value = "5","50cc以下(E)",
Fields!cardesc.Value = "6","50~100cc(F)",
Fields!cardesc.Value = "7","101cc以上(G)"
)</Value>
                                    <Style />
                                  </TextRun>
                                </TextRuns>
                                <Style />
                              </Paragraph>
                            </Paragraphs>
                            <rd:DefaultName>Group5</rd:DefaultName>
                            <Style>
                              <Border>
                                <Style>Solid</Style>
                              </Border>
                              <BackgroundColor>White</BackgroundColor>
                              <PaddingLeft>2pt</PaddingLeft>
                              <PaddingRight>2pt</PaddingRight>
                              <PaddingTop>2pt</PaddingTop>
                              <PaddingBottom>2pt</PaddingBottom>
                            </Style>
                          </Textbox>
                        </CellContents>
                      </TablixHeader>
                      <TablixMembers>
                        <TablixMember>
                          <TablixMembers>
                            <TablixMember />
                          </TablixMembers>
                        </TablixMember>
                      </TablixMembers>
                    </TablixMember>
                    <TablixMember>
                      <TablixHeader>
                        <Size>1.34884in</Size>
                        <CellContents>
                          <Textbox Name="Textbox306">
                            <CanGrow>true</CanGrow>
                            <KeepTogether>true</KeepTogether>
                            <Paragraphs>
                              <Paragraph>
                                <TextRuns>
                                  <TextRun>
                                    <Value>小計</Value>
                                    <Style />
                                  </TextRun>
                                </TextRuns>
                                <Style />
                              </Paragraph>
                            </Paragraphs>
                            <rd:DefaultName>Textbox301</rd:DefaultName>
                            <Style>
                              <Border>
                                <Style>Solid</Style>
                              </Border>
                              <BottomBorder>
                                <Width>2pt</Width>
                              </BottomBorder>
                              <BackgroundColor>White</BackgroundColor>
                              <PaddingLeft>2pt</PaddingLeft>
                              <PaddingRight>2pt</PaddingRight>
                              <PaddingTop>2pt</PaddingTop>
                              <PaddingBottom>2pt</PaddingBottom>
                            </Style>
                          </Textbox>
                        </CellContents>
                      </TablixHeader>
                      <KeepWithGroup>Before</KeepWithGroup>
                    </TablixMember>
                  </TablixMembers>
                </TablixMember>
              </TablixMembers>
            </TablixMember>
            <TablixMember>
              <TablixHeader>
                <Size>0.98425in</Size>
                <CellContents>
                  <Textbox Name="Textbox346">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>小計</Value>
                            <Style />
                          </TextRun>
                        </TextRuns>
                        <Style />
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox346</rd:DefaultName>
                    <Style>
                      <Border>
                        <Style>Solid</Style>
                      </Border>
                      <TopBorder>
                        <Width>=IIF(Fields!import.Value = 1,"2pt","1pt")</Width>
                      </TopBorder>
                      <BackgroundColor>White</BackgroundColor>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </CellContents>
              </TablixHeader>
              <TablixMembers>
                <TablixMember>
                  <Group Name="Group1">
                    <GroupExpressions>
                      <GroupExpression>=Fields!import.Value</GroupExpression>
                    </GroupExpressions>
                  </Group>
                  <SortExpressions>
                    <SortExpression>
                      <Value>=Fields!import.Value</Value>
                    </SortExpression>
                  </SortExpressions>
                  <TablixHeader>
                    <Size>2.33309in</Size>
                    <CellContents>
                      <Textbox Name="Group1">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>=Switch(Fields!import.Value = "1","國產",Fields!import.Value = "2","進口")</Value>
                                <Style />
                              </TextRun>
                            </TextRuns>
                            <Style />
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Group1</rd:DefaultName>
                        <Style>
                          <Border>
                            <Style>Solid</Style>
                          </Border>
                          <TopBorder>
                            <Width>=IIF(Fields!import.Value = 1,"2pt","1pt")</Width>
                          </TopBorder>
                          <BackgroundColor>White</BackgroundColor>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                </TablixMember>
                <TablixMember>
                  <TablixHeader>
                    <Size>2.33309in</Size>
                    <CellContents>
                      <Textbox Name="Textbox419">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>小計</Value>
                                <Style />
                              </TextRun>
                            </TextRuns>
                            <Style />
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox419</rd:DefaultName>
                        <Style>
                          <Border>
                            <Style>Solid</Style>
                          </Border>
                          <BackgroundColor>White</BackgroundColor>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                  <KeepWithGroup>Before</KeepWithGroup>
                </TablixMember>
              </TablixMembers>
            </TablixMember>
          </TablixMembers>
        </TablixRowHierarchy>
        <RepeatColumnHeaders>true</RepeatColumnHeaders>
        <RepeatRowHeaders>true</RepeatRowHeaders>
        <FixedColumnHeaders>true</FixedColumnHeaders>
        <FixedRowHeaders>true</FixedRowHeaders>
        <DataSetName>DT_statistica</DataSetName>
        <Top>0.77639cm</Top>
        <Left>1.02058cm</Left>
        <Height>3.59999cm</Height>
        <Width>18.42602cm</Width>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
        </Style>
      </Tablix>
    </ReportItems>
    <Height>1.82869in</Height>
    <Style>
      <Border>
        <Color>LightGrey</Color>
        <Style>None</Style>
      </Border>
    </Style>
  </Body>
  <Width>7.86423in</Width>
  <Page>
    <PageHeight>29.7cm</PageHeight>
    <PageWidth>21cm</PageWidth>
    <LeftMargin>2cm</LeftMargin>
    <RightMargin>2cm</RightMargin>
    <TopMargin>2cm</TopMargin>
    <BottomMargin>2cm</BottomMargin>
    <ColumnSpacing>0.13cm</ColumnSpacing>
    <Style />
  </Page>
  <rd:ReportID>79facbe0-6856-40a9-89e3-507e1edb8c55</rd:ReportID>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
</Report>