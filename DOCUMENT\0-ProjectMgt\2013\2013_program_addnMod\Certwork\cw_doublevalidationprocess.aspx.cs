﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Configuration;
using System.Data;
using System.Text;

public partial class Certwork_cw_doublevalidationprocess : BaseAdminPage
{
    protected void Page_Load(object sender, EventArgs e)
    {
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = '../accountmt/index.aspx';</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" && (GetAgentAccountGrpList().Contains("rv_adm") || GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("pg_adm")))
            {
                if (!IsPostBack)
                {
                    InitData();
                }
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }
    }

    private string ApplyNo
    {
        get
        {
            return Request["p"] == null ? string.Empty : DeCode(Request["p"].ToString());
        }
    }

    private void InitData()
    {
        ltr_HeaderData.Text = new FirstProcessControl().TagFirstProcess(ApplyNo);
        DataUtils.BindList(ddl_CarCompoModel, new FirstProcessControl().LisCarCompoModel(ApplyNo));
        hl_ValidationProcess.NavigateUrl = "cw_validationprocesslog.aspx?p=" + Server.UrlEncode(Request["p"]);
        hl_RePairProcess.NavigateUrl = "cw_resupplyprocesslog.aspx?p=" + Server.UrlEncode(Request["p"]);
    }

    //取得車型組資料
    protected void btn_GetCarCompoModelInfo_Click(object sender, EventArgs e)
    {

        hf_CarCompoModelNo.Value = ddl_CarCompoModel.SelectedValue;
        gv_data.DataSource = new FirstProcessControl().ListCarCompoModelInfo(ApplyNo, hf_CarCompoModelNo.Value);
        gv_data.DataBind();
        set_Button_be_show(true);
    }

    private TableCell GetCell(string txt, HorizontalAlign HAlign)
    {
        TableCell tc = new TableCell();
        tc.Text = txt;
        tc.CssClass = "lineleft";
        tc.HorizontalAlign = HAlign;
        return tc;
    }

    private TableCell GetCell()
    {
        TableCell tc = new TableCell();
        tc.CssClass = "lineright";
        TextBox txt = new TextBox();
        txt.ID = "txt_Opinion";
        txt.TextMode = TextBoxMode.MultiLine;
        txt.Rows = 4;
        txt.Width = Unit.Pixel(450);
        tc.Controls.Add(txt);
        return tc;
    }

    private bool chkOpinion(string Opinion)
    {
        StringBuilder sb = new StringBuilder();
        if (string.IsNullOrEmpty(Opinion)) { sb.Append("複審意見未填寫！"); }
        #region 特殊字元判斷
        foreach (GridViewRow grv in gv_data.Rows)
        {
            if (grv.RowType == DataControlRowType.DataRow)
            {
                TextBox cbl_select = (TextBox)grv.FindControl("txt_Opinion");
                if ((tbWord(cbl_select.Text)))
                {
                    sb.Append("含有系統不允許的特殊字元！");
                }
            }
        }
        #endregion
        if (sb.Length.Equals(0))
        {
            return true;
        }
        else
        {
            MessageBox.Show(sb.ToString());
            return false;
        }
    }

    //完成複審
    protected void btn_DoubleComplete_Click(object sender, EventArgs e)
    {
        int ino = 0;
        string ValidationItemNo = "";
        for (int i = 0; i < gv_data.Rows.Count; i++)
        {
            ValidationItemNo = gv_data.DataKeys[i].Values["vcn_validationitemno"].ToString();
            if (ValidationItemNo.Equals("19"))
            {
                ino += i;
                break;
            }
        }
        //記錄複審意見
        string Opinion = ((TextBox)gv_data.Controls[0].Controls[ino + 1].FindControl("txt_Opinion")).Text;
        if (chkOpinion(Opinion))
        {
            UpdateOpinion(Opinion, ValidationItemNo);
            if (new FirstProcessControl().ChackCarCompoModelIsOpinion(ApplyNo, "19"))
            {
                new FirstProcessControl().ChangeApplyStatus(ApplyNo, "33");//二級主管核閱
                new FirstProcessControl().LogValidationProcess(ApplyNo, Opinion, "32");//Log
                MailChiefNotify();//SendMail
                RegisterStartupScript("script", @"
<script>alert('已完成複審！'); window.location='cw_doublevalidationqry.aspx';</script>
");
            }
        }
        else
        {
            MessageBox.Show("尚有車型組未檢視，無法完成審查！");
        }
    }

    private void MailChiefNotify()
    {
        DataView dv = new MailParaControl().GetApplyBaseData(ApplyNo);
        string CompName = dv[0]["CompName"].ToString();
        string EngineFamily = dv[0]["EngineFamily"].ToString();
        DataView GropuAccount = new MailParaControl().GetGroupAccount("04");
        for (int i = 0; i < GropuAccount.Count; i++)
        {
            string AccountName = GropuAccount[i]["AccountName"].ToString();
            string AccountMail = GropuAccount[i]["AccountMail"].ToString();
            string[] mailTo = new string[] { AccountMail };
            string[] mailCc = new string[1];//副本
            mailCc[0] = "";
            string[] mailBcc = new string[1];//密件
            mailBcc[0] = "";
            string Float = "二級主管";
            new SendMail().Mail_ChiefNotify(mailTo, mailCc, mailBcc, AccountName, ApplyNo, CompName, EngineFamily, Float);
        }
    }

    //退回初審
    protected void btn_BackToFirst_Click(object sender, EventArgs e)
    {
        int ino = 0;
        string ValidationItemNo = "";

        for (int i = 0; i < gv_data.Rows.Count; i++)
        {
            ValidationItemNo = gv_data.DataKeys[i].Values["vcn_validationitemno"].ToString();
            if (ValidationItemNo.Equals("19"))
            {
                ino += i;
                break;
            }
        }
        //記錄複審意見
        string Opinion = ((TextBox)gv_data.Controls[0].Controls[ino + 1].FindControl("txt_Opinion")).Text;
        if (chkOpinion(Opinion))
        {
            UpdateOpinion(Opinion, ValidationItemNo);
            new FirstProcessControl().ChangeApplyStatus(ApplyNo, "91");//複審核閱退回
            new FirstProcessControl().LogValidationProcess(ApplyNo, Opinion, "91");//Log			
            MailBackToFirst();//SendMail
            RegisterStartupScript("script", @"
<script>alert('已退回初審！'); window.location='cw_doublevalidationqry.aspx';</script>
");
        }
    }

    private void MailBackToFirst()
    {
        DataView dv = new MailParaControl().GetApplyBaseData(ApplyNo);
        string CompName = dv[0]["CompName"].ToString();
        string EngineFamily = dv[0]["EngineFamily"].ToString();
        DataView GropuAccount = new MailParaControl().GetGroupAccount("02");//初審人員
        for (int i = 0; i < GropuAccount.Count; i++)
        {
            string AccountName = GropuAccount[i]["AccountName"].ToString();
            string AccountMail = GropuAccount[i]["AccountMail"].ToString();
            string[] mailTo = new string[] { AccountMail };
            string[] mailCc = new string[1];//副本
            mailCc[0] = "";
            string[] mailBcc = new string[1];//密件
            mailBcc[0] = "";
          
            new SendMail().Mail_BackToFirst(mailTo, mailCc, mailBcc, AccountName, ApplyNo, CompName, EngineFamily);
        }                
    }

    private void BindOpinionRow()
    {
        if (!gv_data.Rows.Count.Equals(0))
        {
            int i = gv_data.Controls[0].Controls.Count - 1;
            GridViewRow gr = new GridViewRow(i, 0, DataControlRowType.DataRow, DataControlRowState.Normal);
            gr.Controls.Add(GetCell(i.ToString(), HorizontalAlign.Center));
            gr.Controls.Add(GetCell("審核意見", HorizontalAlign.Left));
            gr.Controls.Add(GetCell());
            gv_data.Controls[0].Controls.Add(gr);
        }
    }
    protected void gv_data_Load(object sender, EventArgs e)
    {
        // BindOpinionRow();
    }
    protected void gv_data_DataBound(object sender, EventArgs e)
    {
        // BindOpinionRow();    
    }
    //按鈕顯示
    private void set_Button_be_show(bool be_show)
    {

        if (gv_data.Rows.Count > 0)
        {
            if (be_show)
            {
                btn_DoubleComplete.Visible = true;  //完成複審
                btn_BackToFirst.Visible = true;     //退回初審    
                btn_Save.Visible = true;     //暫存    

            }
            else
            {
                btn_DoubleComplete.Visible = false;  //完成複審
                btn_BackToFirst.Visible = false;     //退回初審  
                btn_Save.Visible = false;     //暫存 

            }
        }
        else
        {
            btn_DoubleComplete.Visible = false;  //完成複審
            btn_BackToFirst.Visible = false;     //退回初審 
            btn_Save.Visible = false;     //暫存 
        }
    }
    //更新意見nv_validationchecknote
    private void UpdateOpinion(string vcnOpinion, string vcnNo)
    {
        Cryptography crypy = new Cryptography();
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        string[] radom_A = crypy.wsApplybasedataDetailValid(crypy.getRadNum());
        wsApplyData.UpdateValidationChecknote(ApplyNo, ddl_CarCompoModel.SelectedValue, vcnOpinion, vcnNo, radom_A[0].ToString(), radom_A[1].ToString());
    }
    protected void gv_data_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            Cryptography crypy = new Cryptography();
            wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
            string[] Warr = crypy.wsApplybasedataDetailValid(crypy.getRadNum());

            Label lab_validationitemstatus = (Label)e.Row.FindControl("lab_validationitemstatus");
            TextBox txt_Opinion = (TextBox)e.Row.FindControl("txt_Opinion");

            string sitemno = DataBinder.Eval(e.Row.DataItem, "vcn_validationitemno").ToString().Trim();
            string orderid = DataBinder.Eval(e.Row.DataItem, "orderid").ToString().Trim();

            if (sitemno.Equals("19"))
            {
                txt_Opinion.Visible = true;//審查意見
                lab_validationitemstatus.Visible = false;
                txt_Opinion.Rows = 4;
                txt_Opinion.Width = Unit.Pixel(450);

                txt_Opinion.Text = DataBinder.Eval(e.Row.DataItem, "vcn_opinion").ToString().Trim() == "" ? "" : DataBinder.Eval(e.Row.DataItem, "vcn_opinion").ToString().Trim();

            }
            if (sitemno.Equals("20"))
            {
                lab_validationitemstatus.Text = DataBinder.Eval(e.Row.DataItem, "vcn_opinion").ToString().Trim();
            }

        }
    }
    private void SaveItem()
    {
        string CarCompoModelNo = hf_CarCompoModelNo.Value;

        #region 特殊字元判斷
        foreach (GridViewRow grv in gv_data.Rows)
        {
            if (grv.RowType == DataControlRowType.DataRow)
            {
                TextBox cbl_select = (TextBox)grv.FindControl("txt_Opinion");
                if ((tbWord(cbl_select.Text)))
                {
                    MessageBox.Show(cbl_select.Text + ":含有系統不允許的特殊字元");
                    return;
                }
            }
        }
        #endregion

        int ino = 0;
        string ValidationItemNo = "";
        //記錄複審意見
        for (int i = 0; i < gv_data.Rows.Count; i++)
        {
            ValidationItemNo = gv_data.DataKeys[i].Values["vcn_validationitemno"].ToString();
            if (ValidationItemNo.Equals("19"))
            {
                ino += i;
                break;
            }
        }
        string Opinion = ((TextBox)gv_data.Controls[0].Controls[ino + 1].FindControl("txt_Opinion")).Text;
        if (chkOpinion(Opinion))
        {
            UpdateOpinion(Opinion, ValidationItemNo);
        }

    }
    protected void btn_Save_Click(object sender, EventArgs e)
    {
        SaveItem();
        MessageBox.Show("車型組已暫存！");
    }
}