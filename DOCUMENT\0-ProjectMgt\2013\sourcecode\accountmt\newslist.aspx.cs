﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.IO;
using System.Configuration;
using System.Data;

public partial class accountmt_newslist : Cryptography
{
    wsNewsInfo wsnews = new wsNewsInfo();
    public DataTable dt_NewsList
    {
        get { return (DataTable)ViewState["_dt_NewsList"]; }
        set { ViewState["_dt_NewsList"] = value; }
    }
    public DataTable dt_BreifList
    {
        get { return (DataTable)ViewState["_dt_BreifList"]; }
        set { ViewState["_dt_BreifList"] = value; }
    }
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            if (Request["datatype"] == null)
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.參數正確性"].ToString());
                Response.End();
                return;
            }

            string datatype = DeCode(Request["datatype"].ToString());
            if (datatype == "01")
            {
                BindNews();
                gv_news.Visible = true;
                gv_Brief.Visible = false;
                lbl_Title.Text = "最新消息";
            }
            else if (datatype == "03")
            {
                BindBrief();
                gv_news.Visible = false;
                gv_Brief.Visible = true;
                lbl_Title.Text = "參考資料";
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.參數正確性"].ToString());
                Response.End();
                return;
            }
        }
    }

    private void BindNews()
    {
        //取得News
        string[] radom_N = wsNewsValidate(getRadNum());
        dt_NewsList = wsnews.getNewsList(false, false, false, "", "", radom_N[0].ToString(), radom_N[1].ToString()).Tables[0];
        gv_news.DataSource = dt_NewsList;
        gv_news.DataBind();
        gv_news.PageIndex = 0;
    }

    private void BindBrief()
    {
        //取得參考資料
        string[] radom_N = wsNewsValidate(getRadNum());
        dt_BreifList = wsnews.getBrief(false, false, false, "", "", radom_N[0].ToString(), radom_N[1].ToString()).Tables[0];
        gv_Brief.DataSource = dt_BreifList;
        gv_Brief.DataBind();
        gv_Brief.PageIndex = 0;
    }

    protected void gv_news_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        //最新消息
        if (e.Row.RowIndex < 0)
        {
            return;
        }

        Label lbl_title = (Label)e.Row.FindControl("lbl_title");
        string ED_TITLE = DataBinder.Eval(e.Row.DataItem, "ed_title").ToString();
        string ED_ID = DataBinder.Eval(e.Row.DataItem, "ed_id").ToString();
        lbl_title.Text = ED_TITLE;

        Label lbl_newsdate = (Label)e.Row.FindControl("lbl_newsdate");
        string newsdate = Convert.ToDateTime(DataBinder.Eval(e.Row.DataItem, "ed_newsdate").ToString()).ToString("yyyy-MM-dd");
        lbl_newsdate.Text = newsdate;

        Label lbl_content = (Label)e.Row.FindControl("lbl_content");
        string content = DataBinder.Eval(e.Row.DataItem, "ed_content").ToString();
        lbl_content.Text = content;
    }
    protected void gv_Brief_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        //取得參考資料
        Cryptography crypt = new Cryptography();
        if (e.Row.RowIndex < 0)
        {
            return;
        }
        HyperLink hlk_title = (HyperLink)e.Row.FindControl("hlk_title");
        string ED_TITLE = DataBinder.Eval(e.Row.DataItem, "afi_userfilename").ToString();
        string FileID = DataBinder.Eval(e.Row.DataItem, "ed_afi_fileid").ToString();
        string FilePath = DataBinder.Eval(e.Row.DataItem, "afi_filepath").ToString();
        string FileName = DataBinder.Eval(e.Row.DataItem, "afi_sysfilename").ToString();
        hlk_title.Text = ED_TITLE;
        if (File.Exists(FilePath + FileName))
        {
            hlk_title.NavigateUrl = "../FileDownloadSeal.aspx?File=" + Server.UrlEncode(crypt.EnCode(FileID));
        }

        Label lbl_newsdate = (Label)e.Row.FindControl("lbl_newsdate");
        string newsdate = Convert.ToDateTime(DataBinder.Eval(e.Row.DataItem, "ed_newsdate").ToString()).ToString("yyyy-MM-dd");
        lbl_newsdate.Text = newsdate;
    }
    protected void lbtn_TOP_Click(object sender, EventArgs e)
    {
        Response.Redirect("index.aspx", false);
    }


    protected void gv_news_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        this.gv_news.PageIndex = e.NewPageIndex;
        gv_news.DataSource = dt_NewsList;
        gv_news.DataBind();
    }
    protected void gv_Brief_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        this.gv_Brief.PageIndex = e.NewPageIndex;
        gv_Brief.DataSource = dt_BreifList;
        gv_Brief.DataBind();
    }
}