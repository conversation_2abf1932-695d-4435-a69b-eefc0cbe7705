﻿<%@ Page Title="" Language="C#" MasterPageFile="~/masterpage/MasterPage.master" AutoEventWireup="true"
    CodeFile="noisedefenseplan_readnoly.aspx.cs" Inherits="applydatamt_noisedefenseplan_readnoly" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="fullheader">
        <table width="100%" cellspacing="0" cellpadding="0" class="fullheaderinfo">
            <tr>
                <td valign="top">
                    <b>車型組(<asp:Label ID="lbl_carcompomodelno" runat="server"></asp:Label>)-噪音防制對策</b>
                </td>
                <td align="right" valign="top" class="font_loginInfo">
                </td>
            </tr>
        </table>
    </div>
    <div class="fullcontent">
        <div id="div_maintain" runat="server" class="formstyle">
            <table border="0" cellspacing="0" cellpadding="0" width="100%" class="font_fullcontent">
                <tr>
                    <td align="right">
                        申請編號：
                    </td>
                    <td colspan="2">
                        <asp:Label ID="lbl_applyno" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        資料狀態：
                    </td>
                    <td>
                        <asp:Label ID="lbl_applystatus" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        車型名稱：
                    </td>
                    <td colspan="4">
                        <div class="formstyle">
                            <asp:Repeater ID="rpt_representvehicle" runat="server" OnItemDataBound="rpt_representvehicle_ItemDataBound">
                                <ItemTemplate>
                                    <asp:Label runat="server" ID="lbl_carengmodelname"></asp:Label>
                                </ItemTemplate>
                                <SeparatorTemplate>
                                    <asp:Label ID="lbl_dot" runat="server" Text=";"></asp:Label><br>
                                </SeparatorTemplate>
                            </asp:Repeater>
                        </div>
                    </td>
                </tr>
            </table>
        </div>
        <div class="tableoutcome">
            <asp:GridView ID="gv_data" runat="server" AutoGenerateColumns="false" Width="100%"
                CellSpacing="0" CellPadding="0" BorderWidth="0" GridLines="None" CssClass="font_fullcontent"
                EmptyDataText="查無資料" OnRowDataBound="gv_data_RowDataBound">
                <Columns>
                <asp:BoundField HeaderText="位置" DataField="ndp_position" ItemStyle-CssClass="lineleft"
                        HeaderStyle-CssClass="header" ItemStyle-HorizontalAlign="Center" HeaderStyle-HorizontalAlign="Center">
                        <HeaderStyle CssClass="header"></HeaderStyle>
                        <ItemStyle CssClass="lineleft"></ItemStyle>
                    </asp:BoundField>
                     <asp:BoundField HeaderText="設施(品名)" DataField="ndp_defenseeqpname" ItemStyle-CssClass="lineleft"
                        HeaderStyle-CssClass="header" ItemStyle-HorizontalAlign="Center" HeaderStyle-HorizontalAlign="Center">
                        <HeaderStyle CssClass="header"></HeaderStyle>
                        <ItemStyle CssClass="lineleft"></ItemStyle>
                    </asp:BoundField>
                    <asp:BoundField HeaderText="材質" DataField="ndp_eqpmaterial" ItemStyle-CssClass="lineleft"
                        HeaderStyle-CssClass="header" ItemStyle-HorizontalAlign="Center" HeaderStyle-HorizontalAlign="Center">
                        <HeaderStyle CssClass="header"></HeaderStyle>
                        <ItemStyle CssClass="lineleft"></ItemStyle>
                    </asp:BoundField>
                    <asp:BoundField HeaderText="厚度" DataField="ndp_eqpheight" ItemStyle-CssClass="lineleft"
                        HeaderStyle-CssClass="header" ItemStyle-HorizontalAlign="Center" HeaderStyle-HorizontalAlign="Center">
                        <HeaderStyle CssClass="header"></HeaderStyle>
                        <ItemStyle CssClass="lineleft"></ItemStyle>
                    </asp:BoundField>
                     <asp:BoundField HeaderText="備註" DataField="ndp_equremark" ItemStyle-CssClass="lineleft"
                        HeaderStyle-CssClass="header" ItemStyle-HorizontalAlign="Center" HeaderStyle-HorizontalAlign="Center">
                        <HeaderStyle CssClass="header"></HeaderStyle>
                        <ItemStyle CssClass="lineleft"></ItemStyle>
                    </asp:BoundField>
                    
                    <asp:TemplateField HeaderText="檔案名稱" ItemStyle-CssClass="lineright" HeaderStyle-CssClass="header"  HeaderStyle-Width="70px">
                          <ItemTemplate>
                            <asp:Repeater ID="rpt_file" runat="server" OnItemDataBound="rpt_file_ItemDataBound">
                                <HeaderTemplate>
                                </HeaderTemplate>
                                <ItemTemplate>
                                 <asp:LinkButton ID="lbl_file" runat="server"></asp:LinkButton>                                  
                                </ItemTemplate>
                                <SeparatorTemplate>
                                    <asp:Label ID="lbl_dot" runat="server" Text=";"></asp:Label>
                                </SeparatorTemplate>
                                <FooterTemplate>
                                </FooterTemplate>
                            </asp:Repeater>
                        </ItemTemplate>
                        <HeaderStyle CssClass="lineright"></HeaderStyle>
                        <ItemStyle CssClass="lineright"></ItemStyle>
                    </asp:TemplateField>
                </Columns>
                <EmptyDataRowStyle ForeColor="Red" />
            </asp:GridView>
        </div>
        <div id="div_subcontent" runat="server" class="formstyle">
            <table border="0" cellspacing="0" cellpadding="0" width="100%" class="font_fullcontent">
                <tr>
                    <td align="center">
                        <asp:Button ID="btn_Print" runat="server" Text="列印" CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'"
                            onmouseout="this.className='btn_mouseout'" CausesValidation="False" OnClick="btn_Print_Click" />
                    </td>
                </tr>
            </table>
        </div>
    </div>
    <!--{* fullcontent end *}-->
    <div class="fullfooter">
    </div>
    <!--{* content end *}-->
</asp:Content>
