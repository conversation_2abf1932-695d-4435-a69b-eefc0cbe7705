﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.IO;

public partial class accountmt_accountvalidate : BaseAdminPage
{
    wsCompany wscompany = new wsCompany();
    wsAccountInfo wsaccount = new wsAccountInfo();
    wsGroupInfo wsgroup = new wsGroupInfo();
    wsCodetbl wscodetbl = new wsCodetbl();
    wsApplyDeny wsapplydeny = new wsApplyDeny();
    wsFileInfo wsfile = new wsFileInfo();
    protected void Page_Load(object sender, EventArgs e)
    {
        #region TextBox在Textmode=MultiLine狀態下MaxLength的字數限制無效
        txt_denybackreason.Attributes.Add("maxlength", "120");
        txt_denybackreason.Attributes.Add("onkeyup", "return ismaxlength(this)");

        #endregion
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = 'index.aspx';</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" && (GetAgentAccountGrpList().Contains("fis_adm") || GetAgentAccountGrpList().Contains("pg_adm") || GetAgentAccountGrpList().Contains("sys_adm")))
            {
                if (!IsPostBack)
                {
                    if (Request["status"] == null || Request["type"] == null || Request["account"] == null || Request["compidno"] == null)
                    {
                        Response.Write(ConfigurationManager.AppSettings["Msg.參數正確性"].ToString());
                        Response.End();
                        return;
                    }
                    else
                    {
                        string type = DeCode(Request["type"].ToString());
                        string status = DeCode(Request["status"].ToString());
                        if (type != "第一次申請")
                        {
                            Response.Write(ConfigurationManager.AppSettings["Msg.無對應資料"].ToString());
                            Response.End();
                            return;
                        }
                        else
                        {
                            if (status == "申請中" || status == "同步中")
                            {
                                txt_denybackreason.Enabled = true;
                                txt_denybackreason.ReadOnly = false;
                                txt_compshortname.Enabled = true;
                                txt_stampcompno_d.Enabled = true;
                                txt_stampcompno_g.Enabled = true;
                                txt_stampcompno_m.Enabled = true;
                                txt_compshortname.ReadOnly = false;
                                txt_stampcompno_d.ReadOnly = false;
                                txt_stampcompno_g.ReadOnly = false;
                                txt_stampcompno_m.ReadOnly = false;
                                btn_OK.Visible = true;
                                btn_NO.Visible = true;

                                lbl_loginname.Text = GetAgentName();
                                lbl_logindate.Text = DateTime.Now.ToString("yyyy/MM/dd");
                            }
                            else
                            {
                                txt_denybackreason.Enabled = false;
                                txt_denybackreason.ReadOnly = true;
                                txt_compshortname.Enabled = false;
                                txt_stampcompno_d.Enabled = false;
                                txt_stampcompno_g.Enabled = false;
                                txt_stampcompno_m.Enabled = false;
                                txt_compshortname.ReadOnly = true;
                                txt_stampcompno_d.ReadOnly = true;
                                txt_stampcompno_g.ReadOnly = true;
                                txt_stampcompno_m.ReadOnly = true;
                                btn_OK.Visible = false;
                                btn_NO.Visible = false;
                            }

                            if (status == "退件")
                            {
                                BindDenyCompany();
                                BindDenyAccount();
                            }
                            else
                            {
                                BindCompany();
                                BindAccount();
                            }

                        }
                    }
                }
            }
            else
            {

                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;

            }
        }
    }
    private void BindCompany()
    {
        string[] radom_C = wsCompanyValidate(getRadNum());
        string[] radom_Code = wsCodetblValidate(getRadNum());

        DataSet ds_company = wscompany.getCompanyBase(Request["compidno"].ToString(), radom_C[0].ToString(), radom_C[1].ToString());
        lbl_compidno.Text = DeCode(Request["compidno"].ToString());
        lbl_compname.Text = ds_company.Tables[0].Rows[0]["ci_compname"].ToString();
        lbl_compename.Text = ds_company.Tables[0].Rows[0]["ci_compename"].ToString();
        txt_compshortname.Text = ds_company.Tables[0].Rows[0]["ci_compshortname"].ToString();
        lbl_chargeman.Text = ds_company.Tables[0].Rows[0]["ci_chargeman"].ToString();
        lbl_comptel.Text = ds_company.Tables[0].Rows[0]["ci_comptel"].ToString();
        lbl_compfax.Text = (ds_company.Tables[0].Rows[0]["ci_compfax"].ToString() == "") ? "&nbsp;" : ds_company.Tables[0].Rows[0]["ci_compfax"].ToString();
        lbl_compaddr.Text = ds_company.Tables[0].Rows[0]["ci_compaddrpostno"].ToString()
            + "  " + wscodetbl.getCityName(ds_company.Tables[0].Rows[0]["ci_compaddrcity"].ToString(), radom_Code[0].ToString(), radom_Code[1].ToString())
            + "  " + wscodetbl.getZipName(ds_company.Tables[0].Rows[0]["ci_compaddrcity"].ToString(), ds_company.Tables[0].Rows[0]["ci_compaddrlocalarea"].ToString(), radom_Code[0].ToString(), radom_Code[1].ToString())
            + "  " + ds_company.Tables[0].Rows[0]["ci_compaddr"].ToString();
        lbl_certaddr.Text = ds_company.Tables[0].Rows[0]["ci_certaddrpostno"].ToString()
            + "  " + wscodetbl.getCityName(ds_company.Tables[0].Rows[0]["ci_certaddrcity"].ToString(), radom_Code[0].ToString(), radom_Code[1].ToString())
            + "  " + wscodetbl.getZipName(ds_company.Tables[0].Rows[0]["ci_certaddrcity"].ToString(), ds_company.Tables[0].Rows[0]["ci_certaddrlocalarea"].ToString(), radom_Code[0].ToString(), radom_Code[1].ToString())
            + "  " + ds_company.Tables[0].Rows[0]["ci_certaddr"].ToString();
        ViewState["applystatus"] = ds_company.Tables[0].Rows[0]["ci_applystatus"].ToString();
        txt_stampcompno_g.Text = ds_company.Tables[0].Rows[0]["ci_stampcompno_g"].ToString();
        txt_stampcompno_m.Text = ds_company.Tables[0].Rows[0]["ci_stampcompno_m"].ToString();
        txt_stampcompno_d.Text = ds_company.Tables[0].Rows[0]["ci_stampcompno_d"].ToString();

        string[] radom_F = wsFileValidate(getRadNum());
        DataTable dtFile = wsfile.getCompanyFileList(radom_F[0].ToString(), radom_F[1].ToString(), Request["compidno"].ToString());
        rpt_fileList.DataSource = dtFile;
        rpt_fileList.DataBind();
    }
    private void BindAccount()
    {
        string[] radom_A = wsAccountValidate(getRadNum());
        string[] radom_Code = wsCodetblValidate(getRadNum());
        int issuedaccountcount = wsaccount.hIssuedAccountbyComp(Request["compidno"].ToString(), radom_A[0].ToString(), radom_A[1].ToString());
        lbl_applycount.Text = issuedaccountcount.ToString();
        DataSet ds_account = wsaccount.getAccountBase(DeCode(Request["account"].ToString()), radom_A[0].ToString(), radom_A[1].ToString());
        lbl_username.Text = ds_account.Tables[0].Rows[0]["ai_username"].ToString();
        lbl_usertel.Text = ds_account.Tables[0].Rows[0]["ai_usertel"].ToString();
        lbl_userfax.Text = (ds_account.Tables[0].Rows[0]["ai_userfax"].ToString() == "") ? "&nbsp;" : ds_account.Tables[0].Rows[0]["ai_userfax"].ToString();
        lbl_usercellphone.Text = (ds_account.Tables[0].Rows[0]["ai_usercellphone"].ToString() == "") ? "&nbsp;" : ds_account.Tables[0].Rows[0]["ai_usercellphone"].ToString();
        lbl_userEMail.Text = ds_account.Tables[0].Rows[0]["ai_email"].ToString();
        lbl_useraddr.Text = ds_account.Tables[0].Rows[0]["ai_useraddrpostno"].ToString()
            + "  " + wscodetbl.getCityName(ds_account.Tables[0].Rows[0]["ai_useraddrcity"].ToString(), radom_Code[0].ToString(), radom_Code[1].ToString())
            + "  " + wscodetbl.getZipName(ds_account.Tables[0].Rows[0]["ai_useraddrcity"].ToString(), ds_account.Tables[0].Rows[0]["ai_useraddrlocalarea"].ToString(), radom_Code[0].ToString(), radom_Code[1].ToString())
            + "  " + ds_account.Tables[0].Rows[0]["ai_useraddr"].ToString();


        lbl_applydate.Text = ds_account.Tables[0].Rows[0]["ai_applydate"].ToString();

        if (DeCode(Request["status"].ToString()) != "申請中")
        {
            lbl_loginname.Text = ds_account.Tables[0].Rows[0]["ai_enabledaccountname"].ToString();
            lbl_logindate.Text = ds_account.Tables[0].Rows[0]["ai_enableddate"].ToString();
        }

        string ai_use_gm_system = ds_account.Tables[0].Rows[0]["ai_use_gm_system"].ToString();
        if (ai_use_gm_system == "1")
        {
            foreach (ListItem item in ckb_usesystem.Items)
            {
                if (item.Value == "1")
                    item.Selected = true;
            }
        }

        ViewState["grpaccount"] = ds_account.Tables[0].Rows[0]["ai_account"].ToString();
        ViewState["accountstatus"] = ds_account.Tables[0].Rows[0]["ai_accountstatus"].ToString();
    }
    private void BindDenyCompany()
    {
        DataTable dtData = new DataTable();
        string[] radom_Deny = wsApplyDenyValidate(getRadNum());
        dtData = wsapplydeny.getApplyDenyCompany(Request["compidno"].ToString(), radom_Deny[0].ToString(), radom_Deny[1].ToString());
        if (dtData.Rows.Count > 0)
        {
            lbl_compidno.Text = DeCode(dtData.Rows[0]["ciad_compidno"].ToString());
            lbl_compname.Text = dtData.Rows[0]["ciad_compname"].ToString();
            txt_compshortname.Text = dtData.Rows[0]["ciad_compshortname"].ToString();
            lbl_chargeman.Text = dtData.Rows[0]["ciad_chargeman"].ToString();
            lbl_comptel.Text = dtData.Rows[0]["ciad_comptel"].ToString();
            lbl_compfax.Text = (dtData.Rows[0]["ciad_compfax"].ToString() == "") ? "&nbsp;" : dtData.Rows[0]["ciad_compfax"].ToString();
            lbl_compaddr.Text = dtData.Rows[0]["ciad_compaddrpostno"].ToString()
                + "  " + wscodetbl.getCityName(dtData.Rows[0]["ciad_compaddrcity"].ToString(), radom_Deny[0].ToString(), radom_Deny[1].ToString())
                + "  " + wscodetbl.getZipName(dtData.Rows[0]["ciad_compaddrcity"].ToString(), dtData.Rows[0]["ciad_compaddrlocalarea"].ToString(), radom_Deny[0].ToString(), radom_Deny[1].ToString())
                + "  " + dtData.Rows[0]["ciad_compaddr"].ToString();
            lbl_certaddr.Text = dtData.Rows[0]["ciad_certaddrpostno"].ToString()
            + "  " + wscodetbl.getCityName(dtData.Rows[0]["ciad_certaddrcity"].ToString(), radom_Deny[0].ToString(), radom_Deny[1].ToString())
            + "  " + wscodetbl.getZipName(dtData.Rows[0]["ciad_certaddrcity"].ToString(), dtData.Rows[0]["ciad_certaddrlocalarea"].ToString(), radom_Deny[0].ToString(), radom_Deny[1].ToString())
            + "  " + dtData.Rows[0]["ciad_certaddr"].ToString();

            string[] radom_F = wsFileValidate(getRadNum());
            DataTable dtFile = wsfile.getCompanyFileList(radom_F[0].ToString(), radom_F[1].ToString(), Request["compidno"].ToString());
            rpt_fileList.DataSource = dtFile;
            rpt_fileList.DataBind();
        }
    }
    private void BindDenyAccount()
    {
        DataTable dtData = new DataTable();
        string[] radom_Deny = wsApplyDenyValidate(getRadNum());
        dtData = wsapplydeny.getApplyDenyAccount(DeCode(Request["account"].ToString()), radom_Deny[0].ToString(), radom_Deny[1].ToString());
        if (dtData.Rows.Count > 0)
        {
            lbl_username.Text = dtData.Rows[0]["aiad_username"].ToString();
            lbl_usertel.Text = dtData.Rows[0]["aiad_usertel"].ToString();
            lbl_userfax.Text = (dtData.Rows[0]["aiad_userfax"].ToString() == "") ? "&nbsp;" : dtData.Rows[0]["aiad_userfax"].ToString();
            lbl_usercellphone.Text = (dtData.Rows[0]["aiad_usercellphone"].ToString() == "") ? "&nbsp;" : dtData.Rows[0]["aiad_usercellphone"].ToString();
            lbl_userEMail.Text = dtData.Rows[0]["aiad_email"].ToString();
            lbl_useraddr.Text = dtData.Rows[0]["aiad_useraddrpostno"].ToString()
                + "  " + wscodetbl.getCityName(dtData.Rows[0]["aiad_useraddrcity"].ToString(), radom_Deny[0].ToString(), radom_Deny[1].ToString())
                + "  " + wscodetbl.getZipName(dtData.Rows[0]["aiad_useraddrcity"].ToString(), dtData.Rows[0]["aiad_useraddrlocalarea"].ToString(), radom_Deny[0].ToString(), radom_Deny[1].ToString())
                + "  " + dtData.Rows[0]["aiad_useraddr"].ToString();

            txt_denybackreason.Text = dtData.Rows[0]["aiad_denyreason"].ToString();

            if (DeCode(Request["status"].ToString()) != "申請中")
            {
                lbl_loginname.Text = dtData.Rows[0]["aiad_enabledaccountname"].ToString(); ;
                lbl_logindate.Text = dtData.Rows[0]["aiad_enableddate"].ToString(); ;
            }

            ViewState["grpaccount"] = dtData.Rows[0]["aiad_account"].ToString();
            lbl_applydate.Text = dtData.Rows[0]["aiad_applydate"].ToString();

            string aiad_use_gm_system = dtData.Rows[0]["aiad_use_gm_system"].ToString();
            if (aiad_use_gm_system == "1")
            {
                foreach (ListItem item in ckb_usesystem.Items)
                {
                    if (item.Value == "1")
                        item.Selected = true;
                }
            }
        }
    }
    protected void btn_OK_Click(object sender, EventArgs e)
    {
        if (txt_compshortname.Text.Trim() == "" || (txt_stampcompno_d.Text.Trim() + txt_stampcompno_g.Text.Trim() + txt_stampcompno_m.Text.Trim()) == "")
        {
            MessageBox.Show("公司簡稱為必填欄位而廠商代碼至少擇一填寫");
            return;
        }
        else
        {
            if (txt_denybackreason.Text.Trim() != "")
            {
                MessageBox.Show("無需填寫退件原因");
                return;
            }
            else
            {
                #region Accountmt特殊字元判斷-有MasterPage
                ContentPlaceHolder mpContentPlaceHolder;
                mpContentPlaceHolder = (ContentPlaceHolder)Master.FindControl("ContentPlaceHolder1");
                foreach (object ctrl in mpContentPlaceHolder.Controls)
                {
                    if (ctrl is TextBox)
                    {
                        TextBox objTextBox = default(TextBox);
                        objTextBox = (TextBox)ctrl;
                        if ((tbWord(objTextBox.Text)))
                        {
                            MessageBox.Show(objTextBox.Text + ":含有系統不允許的特殊字元");
                            return;
                        }
                    }
                }
                #endregion

                string[] radom_C = wsCompanyValidate(getRadNum());
                string[] radom_A = wsAccountValidate(getRadNum());
                string account = DeCode(Request["account"].ToString());
                string accountE = Request["account"].ToString();
                string compidno = DeCode(Request["compidno"].ToString());
                string compidnoE = Request["compidno"].ToString();
                try
                {
                    SendMail mail = new SendMail();
                    string PWD = GetRandomPassword(6);
                    string mailCC = "";
                    string[] _mailTo = new string[1];
                    _mailTo[0] = lbl_userEMail.Text;//收件人                
                    string[] _mailCc = new string[1];//副本
                    _mailCc[0] = mailCC;
                    string[] _mailBcc = new string[1];//密件
                    _mailBcc[0] = "";
                    if (Convert.ToBoolean(ConfigurationManager.AppSettings["DEBUGMode"].ToString()))
                    {
                        //測試
                        _mailTo[0] = ConfigurationManager.AppSettings["MailTestTo"].ToString();
                    }
                    bool result;
                    if (ViewState["accountstatus"].ToString() == "8")
                    {
                        wsaccount.ApproveAccount(account, GetAgentAccount(), DateTime.Now.ToString(), GetIP(), "2", radom_A[0].ToString(), radom_A[1].ToString());
                        result = mail.Mail_ValidatePollution_OK(_mailTo, _mailCc, _mailBcc, lbl_username.Text.Trim());
                    }
                    else
                    {
                        wsaccount.ApproveAccount(account, GetAgentAccount(), DateTime.Now.ToString(), GetIP(), "1", radom_A[0].ToString(), radom_A[1].ToString());
                        result = mail.Mail_ValidateNoise_OK(_mailTo, _mailCc, _mailBcc, lbl_compname.Text.Trim(), lbl_username.Text.Trim(), PWD);
                        wsaccount.UpdataPWD(account, GetPAWDHash(PWD), radom_A[0].ToString(), radom_A[1].ToString());
                    }

                    wscompany.ApproveCompnay(compidnoE, DateTime.Now.ToString(), GetAgentAccount(), GetIP(), "1", radom_C[0].ToString(), radom_C[1].ToString()
                        , txt_compshortname.Text.Trim(), txt_stampcompno_g.Text.Trim(), txt_stampcompno_m.Text.Trim(), txt_stampcompno_d.Text.Trim());


                    //第一組帳號，將設定為公司帳號管理者權限
                    wsaccount.InsertCompManageGrp(account, compidnoE, "50", radom_A[0].ToString(), radom_A[1].ToString());


                    #region 污染車集體設定帳號權限   
                    if (ViewState["accountstatus"].ToString() == "8") //同步中
                    {                      
                        //CarCert帳號資料;條件ai_accountstatus 已核發已啟用、 ai_use_gm_system 噪音汙染公用
                        DataTable dt_accountbase = wsaccount.getSameComAccountList(compidnoE, radom_A[0].ToString(), radom_A[1].ToString());
                        if (dt_accountbase.Rows.Count > 0)
                        {
                            for (int i = 0; i < dt_accountbase.Rows.Count; i++)
                            {
                                //非第一組帳號，將設定為 公司一般帳號權限(51)
                                wsaccount.NonFristInsertCompManageGrp(EnCode(dt_accountbase.Rows[i]["ai_account"].ToString()), compidnoE, "51", radom_A[0].ToString(), radom_A[1].ToString());
                            }
                        }
                    }
                    #endregion

                    if (result)
                        ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('系統已寄發核發通知');
                                                    window.location = 'applyaccountvalidaionlist.aspx';</script>");
                    else
                    {
                        wsaccount.ApproveAccount(account, "", "", "", (ViewState["accountstatus"].ToString() == "8") ? "8" : "0", radom_A[0].ToString(), radom_A[1].ToString());
                        wscompany.ApproveCompnay(compidnoE, "", "", "", (ViewState["applystatus"].ToString() == "8") ? "8" : "0", radom_C[0].ToString(), radom_C[1].ToString(), "", "", "", "");
                        MessageBox.Show("寄件失敗");
                    }
                }
                catch
                {
                    wsaccount.ApproveAccount(account, "", "", "", (ViewState["accountstatus"].ToString() == "8") ? "8" : "0", radom_A[0].ToString(), radom_A[1].ToString());
                    wscompany.ApproveCompnay(compidnoE, "", "", "", (ViewState["applystatus"].ToString() == "8") ? "8" : "0", radom_C[0].ToString(), radom_C[1].ToString(), "", "", "", "");

                    MessageBox.Show("失敗");
                }
            }
        }
    }
    protected void btn_NO_Click(object sender, EventArgs e)
    {
        if (txt_denybackreason.Text.Trim().Length > 200)
        {
            MessageBox.Show("字數超出200，請確認");
            return;
        }
        if (txt_denybackreason.Text.Trim() != "")
        {
            try
            {
                #region Accountmt特殊字元判斷-有MasterPage
                ContentPlaceHolder mpContentPlaceHolder;
                mpContentPlaceHolder = (ContentPlaceHolder)Master.FindControl("ContentPlaceHolder1");
                foreach (object ctrl in mpContentPlaceHolder.Controls)
                {
                    if (ctrl is TextBox)
                    {
                        TextBox objTextBox = default(TextBox);
                        objTextBox = (TextBox)ctrl;
                        if ((tbWord(objTextBox.Text)))
                        {
                            MessageBox.Show(objTextBox.Text + ":含有系統不允許的特殊字元");
                            return;
                        }
                    }
                }
                #endregion
                //mail
                string[] _mailTo = new string[1];
                _mailTo[0] = lbl_userEMail.Text;//收件人                
                string[] _mailCc = new string[1];//副本
                _mailCc[0] = "";
                string[] _mailBcc = new string[1];//密件
                _mailBcc[0] = "";
                if (Convert.ToBoolean(ConfigurationManager.AppSettings["DEBUGMode"].ToString()))
                {
                    //測試
                    _mailTo[0] = ConfigurationManager.AppSettings["MailTestTo"].ToString();
                }

                bool result;
                SendMail mail = new SendMail();
                if (ViewState["accountstatus"].ToString() == "8")
                {
                    result = mail.Mail_ValidatePollution_NG(_mailTo, _mailCc, _mailBcc, lbl_username.Text.Trim(), txt_denybackreason.Text.Trim());
                }
                else
                {
                    result = mail.Mail_ValidateNoise_NG(_mailTo, _mailCc, _mailBcc, lbl_compname.Text.Trim(), lbl_username.Text.Trim(), txt_denybackreason.Text.Trim());
                }
                if (result)
                {
                    string[] radom_D = wsApplyDenyValidate(getRadNum());
                    wsapplydeny.DenyFirst(Request["account"].ToString(), Request["compidno"].ToString(), GetAgentAccount(), GetIP(), txt_denybackreason.Text.Trim(), radom_D[0].ToString(), radom_D[1].ToString());
                    ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('系統已寄發退件通知');
                                                   window.location = 'applyaccountvalidaionlist.aspx';</script>");
                }
                else
                {
                    MessageBox.Show("寄件失敗");
                }
            }
            catch
            {
                MessageBox.Show("失敗");
            }
        }
        else
        {
            MessageBox.Show("請輸入退件原因");
        }
    }
    protected void btn_Cancel_Click(object sender, EventArgs e)
    {
        Response.Redirect("applyaccountvalidaionlist.aspx", false);
    }

    protected void rpt_fileList_ItemDataBound(object sender, RepeaterItemEventArgs e)
    {
        FileInfo file;
        if (e.Item.ItemType == ListItemType.Item || e.Item.ItemType == ListItemType.AlternatingItem)
        {
            string afi_fileid = DataBinder.Eval(e.Item.DataItem, "afi_fileid").ToString();
            string afi_filepath = DataBinder.Eval(e.Item.DataItem, "afi_filepath").ToString();
            string afi_sysfilename = DataBinder.Eval(e.Item.DataItem, "afi_sysfilename").ToString();
            string afi_userfilename = DataBinder.Eval(e.Item.DataItem, "afi_userfilename").ToString();
            file = new FileInfo(afi_filepath + afi_sysfilename);
            HyperLink hlk_file = (HyperLink)e.Item.FindControl("hlk_file");
            hlk_file.Text = afi_userfilename;
            if (file.Exists)
            {
                hlk_file.NavigateUrl = "../FileDownloadSeal.aspx?File=" + Server.UrlEncode(EnCode(afi_fileid));
            }
        }
    }
}