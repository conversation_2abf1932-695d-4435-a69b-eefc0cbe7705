﻿<%@ Page Title="" Language="C#" MasterPageFile="~/masterpage/MasterPage.master" AutoEventWireup="true"
    CodeFile="accountvalidate_notfirstaspx.aspx.cs" Inherits="accountmt_accountvalidate_notfirstaspx" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script type="text/javascript">
        function ismaxlength(obj) {
            var mlength = obj.getAttribute ? parseInt(obj.getAttribute("maxlength")) : "";
            if (obj.getAttribute && obj.value.length > mlength)
                obj.value = obj.value.substring(0, mlength);
        }
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="fullheader">
        <table width="100%" cellspacing="0" cellpadding="0" class="fullheaderinfo">
            <tr>
                <td valign="top">
                    <b>帳號申請審核-非第一次申請</b>
                </td>
                <td align="right" valign="top" class="font_loginInfo">
                </td>
            </tr>
        </table>
    </div>
    <!--{* fullheader end *}-->
    <div class="fullcontent">
        <div class="formstyle">
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="font_fullcontent">
                <tr>
                    <td align="right">
                        申請種類：
                    </td>
                    <td>
                        非第一次申請
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        申請日期：
                    </td>
                    <td>
                        <asp:Label ID="lbl_applydate" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        使用權限：
                    </td>
                    <td>
                        <asp:CheckBoxList ID="ckb_usesystem" runat="server" RepeatLayout="Flow" RepeatDirection="Horizontal"
                            Enabled="false">
                            <asp:ListItem Text="噪音" Value="0" Selected="True" Enabled="false"></asp:ListItem>
                            <asp:ListItem Text="污染" Value="1"></asp:ListItem>
                        </asp:CheckBoxList>
                    </td>
                    <td align="right">
                        已核發帳號數：
                    </td>
                    <td>
                        <asp:Label ID="lbl_appyliedcount" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        帳號管理人確認結果：
                    </td>
                    <td >
                        <asp:Label ID="lbl_accountreuslt" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td colspan="6">
                        <span class="font_fulltitle"><b>公司基本資料</b></span>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        統一編號：
                    </td>
                    <td>
                        <asp:Label ID="lbl_compidno" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        公司名稱：
                    </td>
                    <td>
                        <asp:Label ID="lbl_compname" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        公司英文名稱：
                    </td>
                    <td>
                        <asp:Label ID="lbl_compename" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td colspan="6">
                        <span class="font_fulltitle"><b>帳號申請人資料</b></span>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        申請人姓名：
                    </td>
                    <td>
                        <asp:Label ID="lbl_username" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        申請人電話：
                    </td>
                    <td colspan="3">
                        <asp:Label ID="lbl_usertel" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        申請人傳真：
                    </td>
                    <td>
                        <asp:Label ID="lbl_userfax" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        申請人行動電話：
                    </td>
                    <td colspan="3">
                        <asp:Label ID="lbl_usercellphone" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        申請人電子郵件地址：
                    </td>
                    <td colspan="3">
                        <asp:Label ID="lbl_userEMail" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        申請人郵寄地址：
                    </td>
                    <td colspan="5">
                        <asp:Label ID="lbl_useraddr" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        退件原因：
                    </td>
                    <td colspan="5">
                        <asp:TextBox ID="txt_denybackreason" runat="server" TextMode="MultiLine" Height="50px"
                            Width="570px" ReadOnly="true" Enabled="false" CssClass="inputTypeM" MaxLength="120"></asp:TextBox>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        審核人員：
                    </td>
                    <td>
                        <asp:Label ID="lbl_loginname" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        審核時間：
                    </td>
                    <td colspan="3">
                        <asp:Label ID="lbl_logindate" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td colspan="6" align="center">
                        <asp:Button ID="btn_OK" runat="server" Text="核可" OnClick="btn_OK_Click" Visible="false"
                            CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'" onmouseout="this.className='btn_mouseout'" />&nbsp;&nbsp;<asp:Button
                                ID="btn_NO" runat="server" Text="退件" OnClick="btn_NO_Click" Visible="false" CssClass="btn_mouseout"
                                onmouseover="this.className='btn_mouseover'" onmouseout="this.className='btn_mouseout'" />&nbsp;&nbsp;<asp:Button
                                    ID="btn_Cancel" runat="server" Text="取消" OnClick="btn_Cancel_Click" CssClass="btn_mouseout"
                                    onmouseover="this.className='btn_mouseover'" onmouseout="this.className='btn_mouseout'" />
                    </td>
                </tr>
            </table>
        </div>
    </div>
    <!--{* fullcontent end *}-->
    <div class="fullfooter">
    </div>
    <!--{* content end *}-->
</asp:Content>
