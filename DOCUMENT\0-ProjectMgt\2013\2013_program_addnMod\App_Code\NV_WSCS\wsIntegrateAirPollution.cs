﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Services;
using System.Data.SqlClient;
using System.Data;
using System.IO;


/// <summary>
/// wsIntegrateAirPollution 的摘要描述
/// </summary>
[WebService(Namespace = "http://tempuri.org/")]
[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
// 若要允許使用 ASP.NET AJAX 從指令碼呼叫此 Web 服務，請取消註解下一行。
// [System.Web.Script.Services.ScriptService]
public class wsIntegrateAirPollution : System.Web.Services.WebService
{

    Cryptography crypy = new Cryptography();
    public wsIntegrateAirPollution()
    {

        //如果使用設計的元件，請取消註解下行程式碼 
        //InitializeComponent(); 
    }

    //Account
    /// <summary>
    /// 判斷是否已有重覆帳號
    /// </summary>
    /// <param name="account">帳號</param>
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    /// <returns></returns>
    [WebMethod]
    public bool CheckhReAccount(string account, string ID, string PD)
    {
        if (wsCheckIntegrateAirPollutionValid(ID, PD))
        {
            SqlCommand cmd_check = new SqlCommand(@"select * from nv_accountinfo where ai_account=@account");
            cmd_check.Parameters.AddWithValue("@account", crypy.DeCode(account));
            DataView dv_check = Common.Data_noisevalidation.runParaCmd(cmd_check);
            if (dv_check.Count > 0)
                return true;
            else return false;
        }
        else return false;
    }

    /// <summary>
    /// 新增帳號
    /// </summary>
    /// <param name="account">使用者帳號</param>
    /// <param name="password">密碼</param>
    /// <param name="username">使用者姓名</param>
    /// <param name="usertel">使用者電話</param>
    /// <param name="userfax">使用者傳真</param>
    /// <param name="usercellphone">行動電話</param>
    /// <param name="email">EMAL</param>
    /// <param name="useraddrcity">地址-縣市別</param>
    /// <param name="useraddrlocalarea">地址-鄉鎮市區別</param>
    /// <param name="useraddrpostno">地址-郵遞區號</param>
    /// <param name="useraddr">地址-路巷弄號</param>
    /// <param name="accountbelongcompE">所屬公司統編</param>需加密
    /// <param name="use_gm_system">汽機車汙染</param>污染  1(噪音汙染公用)/0噪音
    /// <param name="isFirst">是否為第一次申請帳號</param> 1是 公司帳號管理者 /0否
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    /// <returns></returns>
    [WebMethod]
    public string InsertAccount(string account, string password, string username, string usertel, string userfax, string usercellphone
        , string email, string useraddrcity, string useraddrlocalarea, string useraddrpostno, string useraddr
        , string accountbelongcompE, string use_gm_system, string isFirst, string ID, string PD)
    {
        if (wsCheckIntegrateAirPollutionValid(ID, PD))
        {
            try
            {
                SqlCommand cmd_insaccount = new SqlCommand(@"

                                declare @IsFirst nvarchar(3)=''
                                declare @Accountstatus nvarchar(3)=''

                                if exists(select * from nv_accountinfo where ai_accountbelongcomp=@accountbelongcomp and ai_accountstatus='2') --表示已申請過
                                begin
		                                set @IsFirst = '0'	-- 非第一次申請
                                        set @Accountstatus = '2'	-- 已核發已啟用	
		                        end
		                        else
                                begin
		                                set @IsFirst = '1'	  -- 第一次申請
                                        set @Accountstatus = '8'	-- 同步中
		                        end

                                SET XACT_ABORT ON
                                begin transaction

                                if(not exists(select * from nv_accountinfo where ai_account=@account)  )
                                begin
                                    INSERT INTO nv_accountinfo
                                    (ai_account,ai_password
                                    ,ai_username,ai_usertel,ai_userfax
                                    ,ai_usercellphone,ai_email
                                    ,ai_useraddrcity,ai_useraddrlocalarea
                                    ,ai_useraddrpostno,ai_useraddr
                                    ,ai_accountstatus,ai_createdbysystem
                                    ,ai_accountbelongcomp,ai_applydate
                                    ,ai_use_gm_system
                                    ,ai_thefirstaccount)
                                    VALUES
                                    (@account,@password
                                    ,@username,@usertel,@userfax
                                    ,@usercellphone,@email
                                    ,@useraddrcity,@useraddrlocalarea
                                    ,@useraddrpostno,@useraddr
                                    ,@Accountstatus,'B'
                                    ,@accountbelongcomp,GETDATE()
                                    ,@use_gm_system
                                    ,@IsFirst)                                     
                                 end

                                    declare @acg_count int
                                    declare @aci_count int

                                    select @acg_count=COUNT(*) from nv_accountbelonggroup
                                    where abg_account=@account --帳號是否存在在nv_accountbelonggroup

                                    select @aci_count=COUNT(*) from nv_accountinfo
                                    where ai_accountbelongcomp=@accountbelongcomp  --公司有幾個人
--INSERT INTO tmp_sqlstr (tmp_sqlstr) VALUES (@aci_count)                                     
                                    if(@acg_count=0) --如果帳號不存在nv_accountbelonggroup
                                    begin
                                         if (@aci_count >1)
                                         begin
                                       	        if(not exists(select * from nv_accountbelonggroup where abg_grpid='51' and abg_account=@account)  )
                                                begin                                                                                              
--INSERT INTO tmp_sqlstr (tmp_sqlstr) VALUES ('51')
                                                    insert into nv_accountbelonggroup
		                                            select @account,'51'  --公司一般帳號
	                                            end		                                                                                  
		                                 end
                                         else
                                         begin
                                                if(not exists(select * from nv_accountbelonggroup where abg_grpid='50' and abg_account=@account) )
                                                begin
--INSERT INTO tmp_sqlstr (tmp_sqlstr) VALUES ('50')
                                                    insert into nv_accountbelonggroup
		                                            select @account,'50'  --公司帳號管理者	                                  
	                                            end
                                         end
                                   end
                                   
                            if (@@ERROR<>0)
                            begin
                                rollback tran;
                                select '新增帳號失敗' 
                            end
                            else
                            begin
                                commit tran;
                                select '新增帳號完成'    
                            end
                              
                            ");

                cmd_insaccount.Parameters.AddWithValue("@account", crypy.DeCode(account));
                cmd_insaccount.Parameters.AddWithValue("@password", password);
                cmd_insaccount.Parameters.AddWithValue("@username", crypy.DeCode(username));
                cmd_insaccount.Parameters.AddWithValue("@usertel", crypy.DeCode(usertel));
                cmd_insaccount.Parameters.AddWithValue("@userfax", crypy.DeCode(userfax));
                cmd_insaccount.Parameters.AddWithValue("@usercellphone", crypy.DeCode(usercellphone));
                cmd_insaccount.Parameters.AddWithValue("@email", crypy.DeCode(email));
                cmd_insaccount.Parameters.AddWithValue("@useraddrcity", crypy.DeCode(useraddrcity));
                cmd_insaccount.Parameters.AddWithValue("@useraddrlocalarea", crypy.DeCode(useraddrlocalarea));
                cmd_insaccount.Parameters.AddWithValue("@useraddrpostno", crypy.DeCode(useraddrpostno));
                cmd_insaccount.Parameters.AddWithValue("@useraddr", crypy.DeCode(useraddr));
                cmd_insaccount.Parameters.AddWithValue("@accountbelongcomp", accountbelongcompE);
                cmd_insaccount.Parameters.AddWithValue("@use_gm_system", crypy.DeCode(use_gm_system));
                //cmd_insaccount.Parameters.AddWithValue("@isFirst", crypy.DeCode(isFirst));

                object obj_return = Common.Data_noisevalidation.runScalar(cmd_insaccount);
                return (string)obj_return;
            }
            catch (Exception ex)
            {
                return ex.ToString().Substring(0, 200);
                //if (ex.ToString().IndexOf("Base") > -1)
                //    return "Base-64字元陣列的無效長度";
                //else
                //    return "新增帳號資料失敗";
            }
        }
        else
        {
            return "驗證失敗";
        }
    }

    /// <summary>
    /// 判斷是否已有公司資料
    /// </summary>
    /// <param name="compidnoE">統一編號</param>ci_compidno需加密crypy.EnCode(string)
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    /// <returns></returns>
    [WebMethod]
    public bool CheckhReCompany(string compidnoE, string ID, string PD)
    {
        if (wsCheckIntegrateAirPollutionValid(ID, PD))
        {
            SqlCommand cmd_check = new SqlCommand(@"select * from nv_companyinfo where ci_compidno=@compidno");
            cmd_check.Parameters.AddWithValue("@compidno", crypy.EnCode(compidnoE));
            DataView dv_check = Common.Data_noisevalidation.runParaCmd(cmd_check);
            if (dv_check.Count > 0) return true;
            else return false;
        }
        else return false;
    }

    /// <summary>
    /// 新增公司資料
    /// </summary>       
    /// <param name="compidnoE">統一編號/身份證字號</param>ci_compidno需加密crypy.EnCode(string)
    /// <param name="compname">公司名稱</param>
    /// <param name="compename">公司英文名稱</param>
    /// <param name="comptel">公司電話</param>
    /// <param name="chargeman">負責人姓名</param>
    /// <param name="compfax">公司傳真</param>
    /// <param name="compaddrcity">公司地址-縣市別</param>
    /// <param name="compaddrlocalarea">公司地址-鄉鎮市區別</param>
    /// <param name="compaddrpostno">公司地址-郵遞區號</param>
    /// <param name="compaddr">公司地址-路巷弄號</param>
    /// <param name="certaddrcity">合格證地址-縣市別</param>
    /// <param name="certaddrlocalarea">合格證地址-鄉鎮市區別</param>
    /// <param name="certaddrpostno">合格證地址-郵遞區號</param> 
    /// <param name="certaddr">合格證地址-路巷弄號</param> 
    /// <param name="approvedate">啟用時間(核發時間)/退回時間</param>   DateTime.Now.ToString()
    /// <param name="enabledaccount">啟用/核發者/退回者</param> 
    /// <param name="compshortnam">公司簡稱</param> 
    /// <param name="stampcompno_g">廠商代碼-汽</param> 
    /// <param name="stampcompno_m">廠商代碼-機</param>
    /// ci_applystatus
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    /// <returns></returns>
    [WebMethod]
    public string InsertCompany(string compidnoE, string compname, string compename, string comptel, string chargeman, string compfax
        , string compaddrcity, string compaddrlocalarea, string compaddrpostno, string compaddr
        , string certaddrcity, string certaddrlocalarea, string certaddrpostno, string certaddr
        , string approvedate, string enabledaccount, string compshortnam, string stampcompno_g, string stampcompno_m, string ID, string PD)
    {
        if (wsCheckIntegrateAirPollutionValid(ID, PD))
        {
            try
            {
                SqlCommand cmd_inscompany = new SqlCommand(@"

                                    if(not exists(select * from nv_companyinfo where ci_compidno=@compidno)  )
                                    begin
                                    INSERT INTO nv_companyinfo
                                    (ci_compidno,ci_compname,ci_compename
                                    ,ci_chargeman,ci_comptel,ci_compfax
                                    ,ci_compaddrcity,ci_compaddrlocalarea,ci_compaddrpostno,ci_compaddr
                                    ,ci_certaddrcity,ci_certaddrlocalarea,ci_certaddrpostno,ci_certaddr
                                    ,ci_applybysystem,ci_applyname,ci_applydate,ci_lastchangename,ci_lastchangedate
                                    ,ci_compshortname,ci_stampcompno_g,ci_stampcompno_m,ci_applystatus )
                                    VALUES
                                    (@compidno,@compname,@compename
                                    ,@chargeman,@comptel,@compfax
                                    ,@compaddrcity,@compaddrlocalarea,@compaddrpostno,@compaddr
                                    ,@certaddrcity,@certaddrlocalarea,@certaddrpostno,@certaddr
                                    ,'B',@chargeman,getdate(),@chargeman,getdate()                                    
                                    ,@compshortnam,@stampcompno_g,@stampcompno_m,'8') 
                                    end 
                                    select '新增公司資料完成'");

                cmd_inscompany.Parameters.AddWithValue("@compidno", compidnoE);
                cmd_inscompany.Parameters.AddWithValue("@compname", crypy.DeCode(compname));
                cmd_inscompany.Parameters.AddWithValue("@compename", crypy.DeCode(compename));
                cmd_inscompany.Parameters.AddWithValue("@comptel", crypy.DeCode(comptel));
                cmd_inscompany.Parameters.AddWithValue("@chargeman", crypy.DeCode(chargeman));
                cmd_inscompany.Parameters.AddWithValue("@compfax", crypy.DeCode(compfax));
                cmd_inscompany.Parameters.AddWithValue("@compaddrcity", crypy.DeCode(compaddrcity));
                cmd_inscompany.Parameters.AddWithValue("@compaddrlocalarea", crypy.DeCode(compaddrlocalarea));
                cmd_inscompany.Parameters.AddWithValue("@compaddrpostno", crypy.DeCode(compaddrpostno));
                cmd_inscompany.Parameters.AddWithValue("@compaddr", crypy.DeCode(compaddr));
                cmd_inscompany.Parameters.AddWithValue("@certaddrcity", crypy.DeCode(certaddrcity));
                cmd_inscompany.Parameters.AddWithValue("@certaddrlocalarea", crypy.DeCode(certaddrlocalarea));
                cmd_inscompany.Parameters.AddWithValue("@certaddrpostno", crypy.DeCode(certaddrpostno));
                cmd_inscompany.Parameters.AddWithValue("@certaddr", crypy.DeCode(certaddr));
                cmd_inscompany.Parameters.AddWithValue("@approvedate", crypy.DeCode(approvedate));
                cmd_inscompany.Parameters.AddWithValue("@compshortnam", crypy.DeCode(compshortnam));
                cmd_inscompany.Parameters.AddWithValue("@stampcompno_g", crypy.DeCode(stampcompno_g));
                cmd_inscompany.Parameters.AddWithValue("@stampcompno_m", crypy.DeCode(stampcompno_m));

                object obj_return = Common.Data_noisevalidation.runScalar(cmd_inscompany);
                return (string)obj_return;
            }
            catch (Exception ex)
            {
                return ex.ToString().Substring(0, 200);
                //if (ex.ToString().IndexOf("Base") > -1)
                //    return "Base-64字串中的無效字元";
                //else
                //    return "新增公司資料失敗";
            }
        }
        else
        {
            return "驗證失敗";
        }
    }

    /// <summary>
    /// 更改公司資料時需要將資料存到 nv_companychangeinfo(insertCompChange)
    /// 
    /// 公司資料維護作業-基本資料修改
    /// </summary>
    /// <param name="compname">公司名稱</param>
    /// <param name="compename">公司英文名稱</param>
    /// <param name="comptel">公司電話</param>
    /// <param name="compfax">公司傳真</param>    
    /// <param name="compaddrcity">公司地址-縣市別</param>
    /// <param name="compaddrlocalarea">公司地址-鄉鎮市區別</param>
    /// <param name="compaddrpostno">公司地址-郵遞區號</param>
    /// <param name="compaddr">公司地址-路巷弄號</param>
    /// <param name="certaddrcity">合格證地址-縣市別</param>
    /// <param name="certaddrlocalarea">合格證地址-鄉鎮市區別</param>
    /// <param name="certaddrpostno">合格證地址-郵遞區號</param> 
    /// <param name="certaddr">合格證地址-路巷弄號</param> 
    /// <param name="chargeman">負責人姓名</param>
    /// <param name="compshortnam">公司簡稱</param> 
    /// <param name="stampcompno_g">廠商代碼-汽</param> 
    /// <param name="stampcompno_m">廠商代碼-機</param>
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    [WebMethod]
    public string updateCompBaseData(string compidnoE, string compname, string compename, string comptel, string compfax
        , string compaddrcity, string compaddrlocalarea, string compaddrpostno, string compaddr
        , string certaddrcity, string certaddrlocalarea, string certaddrpostno, string certaddr, string changename, string compshortnam, string stampcompno_g, string stampcompno_m, string ID, string PD)
    {
        if (wsCheckIntegrateAirPollutionValid(ID, PD))
        {
            try
            {
                SqlCommand cmd = new SqlCommand(@"update nv_companyinfo
	                                            set ci_comptel=@comptel
                                                    ,ci_compname=@compname
                                                    ,ci_compename=@compename
		                                            ,ci_compfax=@compfax
                                                    ,ci_compaddrcity=@compaddrcity
                                                    ,ci_compaddrlocalarea=@compaddrlocalarea
                                                    ,ci_compaddrpostno=@compaddrpostno
                                                    ,ci_compaddr=@compaddr
		                                            ,ci_certaddrcity=@certaddrcity
		                                            ,ci_certaddrlocalarea=@certaddrlocalarea
		                                            ,ci_certaddr=@certaddr
                                                    ,ci_compshortname=@compshortnam
                                                    ,ci_stampcompno_g=@stampcompno_g
                                                    ,ci_stampcompno_m=@stampcompno_m
		                                            ,ci_chargeman=@changename
		                                            ,ci_lastchangedate=GETDATE()
                                            where ci_compidno=@compidno select '基本資料修改完成'");
                cmd.Parameters.AddWithValue("@compname", crypy.DeCode(compname));
                cmd.Parameters.AddWithValue("@compename", crypy.DeCode(compename));
                cmd.Parameters.AddWithValue("@comptel", crypy.DeCode(comptel));
                cmd.Parameters.AddWithValue("@compfax", crypy.DeCode(compfax));
                cmd.Parameters.AddWithValue("@compaddrcity", crypy.DeCode(compaddrcity));
                cmd.Parameters.AddWithValue("@compaddrlocalarea", crypy.DeCode(compaddrlocalarea));
                cmd.Parameters.AddWithValue("@compaddrpostno", crypy.DeCode(compaddrpostno));
                cmd.Parameters.AddWithValue("@compaddr", crypy.DeCode(compaddr));
                cmd.Parameters.AddWithValue("@certaddrcity", crypy.DeCode(certaddrcity));
                cmd.Parameters.AddWithValue("@certaddrlocalarea", crypy.DeCode(certaddrlocalarea));
                cmd.Parameters.AddWithValue("@certaddr", crypy.DeCode(certaddr));
                cmd.Parameters.AddWithValue("@changename", crypy.DeCode(changename));
                cmd.Parameters.AddWithValue("@compshortnam", crypy.DeCode(compshortnam));
                cmd.Parameters.AddWithValue("@stampcompno_g", crypy.DeCode(stampcompno_g));
                cmd.Parameters.AddWithValue("@stampcompno_m", crypy.DeCode(stampcompno_m));
                cmd.Parameters.AddWithValue("@compidno", compidnoE);

                object obj_return = Common.Data_noisevalidation.runScalar(cmd);
                return (string)obj_return;
            }
            catch (Exception ex)
            {
                return ex.ToString().Substring(0, 200);
                //if (ex.ToString().IndexOf("Base") > -1)
                //    return "Base-64字串中的無效字元";
                //else
                //    return "基本資料修改失敗";
            }
        }
        else
        {
            return "驗證失敗";
        }
    }

    /// <summary>
    /// 公司資料維護作業-公司資料異動主檔
    /// </summary>
    /// <param name="compidnoE">統一編號/身份證字號</param>ci_compidno需加密crypy.EnCode(string)
    /// <param name="compname">公司名稱</param>
    /// <param name="compename">公司英文名稱</param>
    /// <param name="chargeman">負責人姓名</param>
    /// <param name="comptel">公司電話</param>
    /// <param name="compfax">公司傳真</param>
    /// <param name="compaddrcity">公司地址-縣市別</param>
    /// <param name="compaddrlocalarea">公司地址-鄉鎮市區別</param>
    /// <param name="compaddrpostno">公司地址-郵遞區號</param>
    /// <param name="compaddr">公司地址-路巷弄號</param>
    /// <param name="certaddrcity">合格證地址-縣市別</param>
    /// <param name="certaddrlocalarea">合格證地址-鄉鎮市區別</param>
    /// <param name="certaddrpostno">合格證地址-郵遞區號</param> 
    /// <param name="certaddr">合格證地址-路巷弄號</param> 
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    [WebMethod]
    public string insertCompChange(string compidnoE, string compname, string compename, string chargeman, string comptel
    , string compfax, string compaddrcity, string compaddrlocalarea, string compaddrpostno, string compaddr
    , string certaddrcity, string certaddrlocalarea, string certaddrpostno, string certaddr
    , string lastchangename, string ID, string PD)
    {
        if (wsCheckIntegrateAirPollutionValid(ID, PD))
        {
            try
            {
                SqlCommand cmd = new SqlCommand(@"INSERT INTO nv_companychangeinfo
                                        (cci_compidno,cci_compname,cci_compename
                                        ,cci_chargeman,cci_comptel,cci_compfax
                                        ,cci_compaddrcity,cci_compaddrlocalarea
                                        ,cci_compaddrpostno,cci_compaddr
                                        ,cci_certaddrcity,cci_certaddrlocalarea
                                        ,cci_certaddrpostno,cci_certaddr
                                        ,cci_lastchangename,cci_lastchangedate)
                                        VALUES
                                        (@compidno,@compname,@compename
                                        ,@chargeman,@comptel,@compfax
                                        ,@compaddrcity,@compaddrlocalarea
                                        ,@compaddrpostno,@compaddr
                                        ,@certaddrcity,@certaddrlocalarea
                                        ,@certaddrpostno,@certaddr
                                        ,@lastchangename,getdate())  select '資料異動完成' ");
                cmd.Parameters.AddWithValue("@compidno", crypy.DeCode(compidnoE));
                cmd.Parameters.AddWithValue("@compname", crypy.DeCode(compname));
                cmd.Parameters.AddWithValue("@compename", crypy.DeCode(compename));
                cmd.Parameters.AddWithValue("@chargeman", crypy.DeCode(chargeman));
                cmd.Parameters.AddWithValue("@comptel", crypy.DeCode(comptel));
                cmd.Parameters.AddWithValue("@compfax", crypy.DeCode(compfax));
                cmd.Parameters.AddWithValue("@compaddrcity", crypy.DeCode(compaddrcity));
                cmd.Parameters.AddWithValue("@compaddrlocalarea", crypy.DeCode(compaddrlocalarea));
                cmd.Parameters.AddWithValue("@compaddrpostno", crypy.DeCode(compaddrpostno));
                cmd.Parameters.AddWithValue("@compaddr", crypy.DeCode(compaddr));
                cmd.Parameters.AddWithValue("@certaddrcity", crypy.DeCode(certaddrcity));
                cmd.Parameters.AddWithValue("@certaddrlocalarea", crypy.DeCode(certaddrlocalarea));
                cmd.Parameters.AddWithValue("@certaddrpostno", crypy.DeCode(certaddrpostno));
                cmd.Parameters.AddWithValue("@certaddr", crypy.DeCode(certaddr));
                cmd.Parameters.AddWithValue("@lastchangename", crypy.DeCode(lastchangename));

                object obj_return = Common.Data_noisevalidation.runScalar(cmd);
                return (string)obj_return;
            }
            catch (Exception ex)
            {
                return ex.ToString().Substring(0, 200);
                //if (ex.ToString().IndexOf("Base") > -1)
                //    return "Base-64字串中的無效字元";
                //else
                //    return "資料異動失敗";
            }
        }
        else
        {
            return "驗證失敗";
        }
    }

    /// <summary>
    /// 使用者資料變更
    /// </summary>
    /// <param name="account">帳號</param>DeCode過的帳號
    /// <param name="username">使用者姓名</param>
    /// <param name="usertel">使用者電話</param>
    /// <param name="userfax">使用者傳真</param>
    /// <param name="usercellphone">行動電話</param>
    /// <param name="email">EMAIL</param>
    /// <param name="useraddrcity">地址-縣市別</param>
    /// <param name="useraddrlocalarea">地址-鄉鎮市區別</param>
    /// <param name="useraddrpostno">地址-郵遞區號</param>
    /// <param name="useraddr">地址-路巷弄號</param>
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    [WebMethod]
    public string UpdateAccountBase(string account, string username, string usertel, string userfax, string usercellphone
        , string email, string useraddrcity, string useraddrlocalarea, string useraddrpostno, string useraddr, string ID, string PD)
    {
        if (wsCheckIntegrateAirPollutionValid(ID, PD))
        {
            try
            {
                SqlCommand cmd_upd = new SqlCommand(@"UPDATE nv_accountinfo
                               SET ai_username=@username
                                  ,ai_usertel = @usertel
                                  ,ai_userfax = @userfax
                                  ,ai_usercellphone = @usercellphone
                                  ,ai_email = @email
                                  ,ai_useraddrcity = @useraddrcity
                                  ,ai_useraddrlocalarea = @useraddrlocalarea
                                  ,ai_useraddrpostno = @useraddrpostno
                                  ,ai_useraddr = @useraddr 
                                  ,ai_lastchangedate=getdate()     
                                 WHERE ai_account=@account  select '資料變更完成'");
                cmd_upd.Parameters.AddWithValue("@account", crypy.DeCode(account));
                cmd_upd.Parameters.AddWithValue("@username", crypy.DeCode(username));
                cmd_upd.Parameters.AddWithValue("@usertel", crypy.DeCode(usertel));
                cmd_upd.Parameters.AddWithValue("@userfax", crypy.DeCode(userfax));
                cmd_upd.Parameters.AddWithValue("@usercellphone", crypy.DeCode(usercellphone));
                cmd_upd.Parameters.AddWithValue("@email", crypy.DeCode(email));
                cmd_upd.Parameters.AddWithValue("@useraddrcity", crypy.DeCode(useraddrcity));
                cmd_upd.Parameters.AddWithValue("@useraddrlocalarea", crypy.DeCode(useraddrlocalarea));
                cmd_upd.Parameters.AddWithValue("@useraddrpostno", crypy.DeCode(useraddrpostno));
                cmd_upd.Parameters.AddWithValue("@useraddr", crypy.DeCode(useraddr));

                object obj_return = Common.Data_noisevalidation.runScalar(cmd_upd);
                return (string)obj_return;
            }
            catch (Exception ex)
            {
                return ex.ToString().Substring(0, 200);
                //if (ex.ToString().IndexOf("Base") > -1)
                //    return "Base-64字串中的無效字元";
                //else
                //    return "資料變更失敗";
            }
        }
        else
        {
            return "驗證失敗";
        }
    }

    /// <summary>
    /// 變更密碼
    /// </summary>
    /// <param name="account"></param>帳號
    /// <param name="pwd_H"></param>Hash過的密碼(crypt.GetPAWDHash)
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    [WebMethod]
    public string UpdataPWD(string account, string pwd_H, string ID, string PD)
    {
        if (wsCheckIntegrateAirPollutionValid(ID, PD))
        {
            try
            {
                SqlCommand cmd_upd = new SqlCommand(@"update nv_accountinfo set ai_password=@password where ai_account=@account select '變更密碼完成' ");

                cmd_upd.Parameters.AddWithValue("@password", pwd_H);
                cmd_upd.Parameters.AddWithValue("@account", crypy.DeCode(account));

                object obj_return = Common.Data_noisevalidation.runScalar(cmd_upd);
                return (string)obj_return;
            }
            catch (Exception ex)
            {
                return ex.ToString().Substring(0, 200);
                //if (ex.ToString().IndexOf("Base") > -1)
                //    return "Base-64字串中的無效字元";
                //else
                //    return "變更密碼失敗";
            }
        }
        else
        {
            return "驗證失敗";
        }
    }

    /// <summary>
    /// 修改帳號之帳號狀態
    /// </summary>
    /// <param name="account"></param>需加密
    /// <param name="status"></param>(如果已啟用status傳入2) 0-申請中;1已核發未啟用;2已核發已啟用;3暫停使用;4已確認;7油污染啟用;8同步中;9退回申請
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    [WebMethod]
    public string UpdateStatus(string account, string status, string ID, string PD)
    {
        if (wsCheckIntegrateAirPollutionValid(ID, PD))
        {
            try
            {
                SqlCommand cmd_upd = new SqlCommand(@"update nv_accountinfo set ai_accountstatus=@status where ai_account=@account select '帳號狀態以更改' ");
                cmd_upd.Parameters.AddWithValue("@status", crypy.DeCode(status));
                cmd_upd.Parameters.AddWithValue("@account", crypy.DeCode(account));

                object obj_return = Common.Data_noisevalidation.runScalar(cmd_upd);
                return (string)obj_return;
            }
            catch (Exception ex)
            {
                return ex.ToString().Substring(0, 200);
                //if (ex.ToString().IndexOf("Base") > -1)
                //    return "Base-64字串中的無效字元";
                //else
                //    return "帳號狀態以更改失敗";
            }
        }
        else
        {
            return "驗證失敗";
        }
    }

    /// <summary>
    /// 取得公司檔案清單
    /// </summary>
    /// <param name="compidnoE">公司統一編號</param>
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    /// 
    /// <returns></returns>
    [WebMethod]
    public DataSet getCompanyFileList(string compidnoE, string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsCheckIntegrateAirPollutionValid(ID, PD))
        {
            SqlCommand cmd_file = new SqlCommand(@"select *
                                from nv_companyinfo_uploadfileinfo
                                join nv_attachfileinfo on afi_fileid=ci_ufi_fileid
                                where ci_ufi_filestatus='N'
                                and ci_ufi_compidno=@compidno and afi_filetype in('1','2','3','4') order by afi_filetype");
            cmd_file.Parameters.AddWithValue("@compidno", compidnoE);           
            return Common.Data_noisevalidation.runParaCmdDS(cmd_file);
        }
        return ds;
    }

    /// <summary>
    /// 取得檔案個別資料
    /// </summary>
    /// <param name="compidnoE">公司統一編號</param>
    /// <param name="FileId">公司統一編號</param>
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    /// 
    /// <returns></returns>
    [WebMethod]
    //會回傳nv_attachfileinfo 的 afi_filepath
    public byte[] getFilePath(string compidnoE, string FileId, string ID, string PD)
    {
        Byte[] documentcontents0 = new Byte[1];
        if (wsCheckIntegrateAirPollutionValid(ID, PD))
        {

            SqlCommand cmd_file = new SqlCommand(@"
                                select *
                                from nv_companyinfo_uploadfileinfo
                                join nv_attachfileinfo on afi_fileid=ci_ufi_fileid
                                where ci_ufi_filestatus='N'
                                and ci_ufi_compidno=@compidnoE and ci_ufi_fileid=@FileId");

            cmd_file.Parameters.AddWithValue("@compidnoE", compidnoE);
            cmd_file.Parameters.AddWithValue("@FileId", crypy.DeCode(FileId));


            DataSet ds = Common.Data_noisevalidation.runParaCmdDS(cmd_file);
            if (ds.Tables[0].Rows.Count > 0)
            {//return ds.Tables[0].Rows[0]["afi_filepath"].ToString() + ds.Tables[0].Rows[0]["afi_sysfilename"].ToString(); //傳回 nv_attachfileinfo 的 afi_filepath
                FileStream fs = new FileStream(ds.Tables[0].Rows[0]["afi_filepath"].ToString() + ds.Tables[0].Rows[0]["afi_sysfilename"].ToString(), FileMode.Open);
                int len = (int)fs.Length;
                if (len == 0)
                    len = 1;

                Byte[] documentcontents = new Byte[len];
                fs.Read(documentcontents, 0, len);
                fs.Close();

                return documentcontents;
                //return fs;
            }
            else
            {

                return documentcontents0;
            }
        }
        else
        {

            return documentcontents0;
        }
    }

    /// <summary>
    /// 供汙染複至資料-機踏車
    /// </summary>
    /// <param name="databelongcompidno"></param>公司統編 加密
    /// <param name="vechiletype"></param>char(1) M
    /// <param name="beimport"></param>char(1) 1進口/2國產
    /// <param name="carstyleyear"></param>varchar(4) EX:2012
    /// <param name="engineamily"></param>varchar(30) 引擎族
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    [WebMethod]
    public DataSet CommonDataForPopulation_M(string databelongcompidno, string vechiletype, string beimport, string carstyleyear, string engineamily, string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsCheckIntegrateAirPollutionValid(ID, PD))
        {

            SqlCommand cmd_upd = new SqlCommand(@"exec pr_provide_common_data_for_population_M @databelongcompidno,@vechiletype,@beimport,@carstyleyear,@engineamily ");           
            cmd_upd.Parameters.AddWithValue("@databelongcompidno", databelongcompidno);
            cmd_upd.Parameters.AddWithValue("@vechiletype", crypy.DeCode(vechiletype));
            cmd_upd.Parameters.AddWithValue("@beimport", crypy.DeCode(beimport));
            cmd_upd.Parameters.AddWithValue("@carstyleyear", crypy.DeCode(carstyleyear));
            cmd_upd.Parameters.AddWithValue("@engineamily", crypy.DeCode(engineamily));

            ds = Common.Data_noisevalidation.runParaCmdDS(cmd_upd);

        }
        return ds;
    }

    /// <summary>
    /// 供汙染複至資料-汽車
    /// </summary>
    /// <param name="databelongcompidno"></param>公司統編 加密
    /// <param name="vechiletype"></param>char(1) G
    /// <param name="beimport"></param>char(1) 1進口/2國產
    /// <param name="carstyleyear"></param>varchar(4) EX:2012
    /// <param name="engineamily"></param>varchar(30) 引擎族
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    [WebMethod]
    public DataSet CommonDataForPopulation_G(string databelongcompidno, string vechiletype, string beimport, string carstyleyear, string engineamily, string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsCheckIntegrateAirPollutionValid(ID, PD))
        {

            SqlCommand cmd_upd = new SqlCommand(@"exec pr_provide_common_data_for_population_G @databelongcompidno,@vechiletype,@beimport,@carstyleyear,@engineamily ");
            cmd_upd.Parameters.AddWithValue("@databelongcompidno", databelongcompidno);
            cmd_upd.Parameters.AddWithValue("@vechiletype", crypy.DeCode(vechiletype));
            cmd_upd.Parameters.AddWithValue("@beimport", crypy.DeCode(beimport));
            cmd_upd.Parameters.AddWithValue("@carstyleyear", crypy.DeCode(carstyleyear));
            cmd_upd.Parameters.AddWithValue("@engineamily", crypy.DeCode(engineamily));
           
                ds = Common.Data_noisevalidation.runParaCmdDS(cmd_upd);           
        }
        return ds;
    }

    /// <summary>
    /// 供汙染複至資料-柴油車
    /// </summary>
    /// <param name="databelongcompidno"></param>公司統編 加密
    /// <param name="vechiletype"></param>char(1) D
    /// <param name="beimport"></param>char(1) 1進口/2國產
    /// <param name="carstyleyear"></param>varchar(4) EX:2012
    /// <param name="engineamily"></param>varchar(30) 引擎族
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    [WebMethod]
    public DataSet CommonDataForPopulation_D(string databelongcompidno, string vechiletype, string beimport, string carstyleyear, string engineamily, string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsCheckIntegrateAirPollutionValid(ID, PD))
        {

            SqlCommand cmd_upd = new SqlCommand(@"exec pr_provide_common_data_for_population_D @databelongcompidno,@vechiletype,@beimport,@carstyleyear,@engineamily ");
            cmd_upd.Parameters.AddWithValue("@databelongcompidno", databelongcompidno);
            cmd_upd.Parameters.AddWithValue("@vechiletype", crypy.DeCode(vechiletype));
            cmd_upd.Parameters.AddWithValue("@beimport", crypy.DeCode(beimport));
            cmd_upd.Parameters.AddWithValue("@carstyleyear", crypy.DeCode(carstyleyear));
            cmd_upd.Parameters.AddWithValue("@engineamily", crypy.DeCode(engineamily));

            ds = Common.Data_noisevalidation.runParaCmdDS(cmd_upd);

        }
        return ds;
    }
    /// <summary>
    /// 帳號-個資同意聲明
    /// </summary>
    /// <param name="account">帳號</param>需加密  
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    [WebMethod]
    public string insertPersonalInfo(string account, string ID, string PD)
    {
        if (wsCheckIntegrateAirPollutionValid(ID, PD))
        {
            try
            {                                
                SqlCommand cmd = new SqlCommand(@"
                                if not exists(select pid_account from nv_personalinfodeclare where pid_account=@account)
                                begin
                                        declare @declareinfo varchar(max)                                     
                                        set @declareinfo = '個人資料使用告知事項您好：財團法人車輛研究測試中心（以下簡稱本中心）為遵循個人資料保護法規定及本中心隱私權政策要求，並為保障您的權益，請您務必詳細閱讀本同意書之各項內容，謝謝！1.蒐集目的：■客戶服務（如環保署車輛噪音審驗合格證線上申請作業服務、案件聯繫及管理以及因執行環保署噪音審驗業務上之必要之行為；顧客滿意度問卷調查；客戶管理；不定期發送政府、本中心或產業相關之活動訊息等）；■其他業務必要範圍（如帳務/稅務管理、稽核/審計作業或其他合於營業登記項目或章程所定業務之需要）。2. 您所提供以下的個人資料：姓名、聯絡方式（通訊地址、電話、電子信箱）或其他得以直接或間接識別您個人之資料皆受本中心保全維護，並僅限於上述業務範圍內使用。3. 個人資料利用之期間、地區、對象及方式：(1) 本中心將於蒐集目的之存續期間內合理利用您的個人資料。(2) 除蒐集之目的涉及國際業務或活動外，本中心將僅於中華民國領域內利用您的個人資料。(3) 本中心業務承辦人員於蒐集之目的範圍內，以合理方式利用您的個人資料。(4) 本中心因業務需要而委託外部機關處理您的個人資料時，本中心將善盡監督之責。4. 您可自由選擇是否提供本中心您的個人資料，若您不願提供，本中心將無法為您提供蒐集目的之相關服務。但若您所提供之個人資料不正確，經檢舉、本中心發現或經他人冒用、盜用，有資料不實之情形，本中心有權終止您的權利。'

                                        INSERT INTO nv_personalinfodeclare
                                        (pid_account
                                        ,pid_declareinfo
                                        ,pid_createdate
                                        ,pid_createaccount
                                        ,pid_gemsys)
                                        VALUES
                                        (@account
                                        ,@declareinfo
                                        ,GETDATE()
                                        ,@account
                                        ,'機動車輛排氣暨噪音審驗系統')
                                        end                                        
                                 select '資料新增完成' ");

                cmd.Parameters.AddWithValue("@account", crypy.DeCode(account));               
                object obj_return = Common.Data_noisevalidation.runScalar(cmd);
                return (string)obj_return;
            }
            catch (Exception ex)
            {
                return ex.ToString().Substring(0, 200);
                //if (ex.ToString().IndexOf("Base") > -1)
                //    return "Base-64字串中的無效字元";
                //else
                //    return "資料新增完成";
            }
        }
        else
        {
            return "驗證失敗";
        }
    }
    private bool wsCheckIntegrateAirPollutionValid(string ID, string PD)
    {
        string[][] x = new string[7][];
        x[0] = new string[2] { "CCWS1", "JVqIHd723s" };
        x[1] = new string[2] { "CCWS2", "89HYKYdssd" };
        x[2] = new string[2] { "CCWS3", "mcjsuWEdS" };
        x[3] = new string[2] { "CCWS4", "okDokED" };
        x[4] = new string[2] { "CCWS5", "bhg3dFTox" };
        x[5] = new string[2] { "CCWS6", "32dUH3DS" };
        x[6] = new string[2] { "CCWS7", "oGDjvED1" };

        for (int i = 0; i < 7; i++)
        {
            if (x[i][0].ToString() == ID && x[i][1].ToString() == PD)
            {
                return true;
            }
        }
        return false;
    }
}
