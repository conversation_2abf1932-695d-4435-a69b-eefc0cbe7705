﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace ISC_RAC.Control
{
    [DefaultProperty("MenuID")]
    [ToolboxData("<{0}:HTMLMenu runat=server></{0}:HTMLMenu>")]
    public class HTMLMenu : ISC_RAC.Control.WebControl
    {
        #region Public Property or Method        

        /// <summary>
        /// 設定或取得，要顯示的功能群組。
        /// </summary>
        public int FunctionGroup
        {
            get
            {
                if (this.ViewState["FunctionGroup"] == null)
                    this.ViewState["FunctionGroup"] = 0;
                return (int)this.ViewState["FunctionGroup"];
            }
            set
            {
                this.ViewState["FunctionGroup"] = value;
            }
        }

        /// <summary>
        /// 設定或取得，根ul的HTML ID。
        /// </summary>
        public string MenuID
        {
            get
            {
                if (this.ViewState["MenuID"] == null)
                    this.ViewState["MenuID"] = string.Empty;
                return (string)this.ViewState["MenuID"];
            }
            set
            {
                this.ViewState["MenuID"] = value;
            }
        }

        /// <summary>
        /// 設定或取得，根ul的Css Class。
        /// </summary>
        public string MenuCssClass
        {
            get
            {
                if (this.ViewState["MenuCssClass"] == null)
                    this.ViewState["MenuCssClass"] = string.Empty;
                return (string)this.ViewState["MenuCssClass"];
            }
            set
            {
                this.ViewState["MenuCssClass"] = value;
            }
        }        

        /// <summary>
        /// 使用rac_RoleOfUser資料表所定義的使用者角色，產生功能選單。
        /// </summary>
        /// <param name="strUserUID"></param>
        public void MakeMenu(string strUserUID, List<ISC_RAC.Model.RowItem_NavigateURLParameter> liNavigateURLParameters)
        {
            this.MainMenu(strUserUID, ISC_RAC.BLL.DatabaseFunction.GetAllRoleUIDListOfUser(strUserUID), liNavigateURLParameters);
        }

        /// <summary>
        /// 根據另外定義的使用者角色清單，產生功能選單。
        /// </summary>
        /// <param name="liRoleUIDListOfUser"></param>
        public void MainMenu(string strUserUID, List<string> liRoleUIDListOfUser, List<ISC_RAC.Model.RowItem_NavigateURLParameter> liNavigateURLParameters)
        {
            if (liNavigateURLParameters == null)
                liNavigateURLParameters = new List<Model.RowItem_NavigateURLParameter>();

            System.Web.UI.WebControls.Menu BaseMenu = new System.Web.UI.WebControls.Menu();
            List<ISC_RAC.Model.RowItem_FunctionList> liFunctionList = ISC_RAC.BLL.DatabaseFunction.GetFunctionList_WithGroup(this.FunctionGroup);
            List<ISC_RAC.Model.RowItem_RolePermissionOfFunction> liAccessRightList = ISC_RAC.BLL.DatabaseFunction.GetAllRolePermissionOfFunctionList();
            ISC_RAC.BLL.WebControlFunction.PutFunctionListIntoMenu(BaseMenu, liFunctionList, liAccessRightList, liRoleUIDListOfUser);

            //根據BaseMenu產生
            System.Text.StringBuilder sbMenu = this.MakeMenuHTML(BaseMenu, liNavigateURLParameters);
            this.InnerHTML = sbMenu.ToString();
        }
        
        #endregion

        protected string InnerHTML
        {
            get
            {
                if (this.ViewState["InnerHTML"] == null)
                {
                    System.Text.StringBuilder sbMenu = new System.Text.StringBuilder();
                    sbMenu.Append("<ul>");
                    sbMenu.Append("<li><a href='#'>RAC HTML Menu</a></li>");
                    sbMenu.Append("</ul>");
                    this.ViewState["InnerHTML"] = sbMenu.ToString();
                }
                return (string)this.ViewState["InnerHTML"];
            }
            set
            {
                this.ViewState["InnerHTML"] = value;
            }
        }

        protected override void RenderContents(HtmlTextWriter output)
        {
            output.Write(InnerHTML);
        }

        protected System.Text.StringBuilder MakeMenuHTML(System.Web.UI.WebControls.Menu BaseMenu, List<ISC_RAC.Model.RowItem_NavigateURLParameter> liNavigateURLParameters)
        {
            System.Text.StringBuilder sbMenu = new System.Text.StringBuilder();
            sbMenu.Append("<ul ");
            if (string.IsNullOrEmpty(this.MenuID) == false)
                sbMenu.Append("id='").Append(this.MenuID).Append("' ");
            if (string.IsNullOrEmpty(this.MenuCssClass) == false)
                sbMenu.Append("class='").Append(this.MenuCssClass).Append("' ");
            sbMenu.Append(">");
            if (BaseMenu.Items.Count == 0)
            {
                //完全沒有選單可以顯示時
                //20210628調整首頁登入頁面
                //sbMenu.Append("<li><a href='#'>").Append(ISC_RAC.BLL.CultureResourceString.Instance.GetResourceString("EmptyMenuItem")).Append("</a></li>");
            }
            else
            {
                for (int intIndex = 0; intIndex < BaseMenu.Items.Count; intIndex++)
                {
                    this.AddMenuIntoHTML(sbMenu, BaseMenu.Items[intIndex], liNavigateURLParameters);
                }
            }
            sbMenu.Append("</ul>");

            return sbMenu;
        }

        protected void AddMenuIntoHTML(System.Text.StringBuilder sbMenu, MenuItem thisMenuItem, List<ISC_RAC.Model.RowItem_NavigateURLParameter> liNavigateURLParameters)
        {
            sbMenu.Append("<li>");
            if (string.IsNullOrEmpty(thisMenuItem.NavigateUrl) == true)
            {
                sbMenu.Append("<a href='#'");
            }
            else
            {
                string strNavigateUrl = this.ReplaceNavigateURLParameters(thisMenuItem.NavigateUrl, liNavigateURLParameters);
                sbMenu.Append("<a href='").Append(strNavigateUrl).Append("' ");
                sbMenu.Append("target='").Append(thisMenuItem.Target).Append("'");
            }
            sbMenu.Append(">").Append(thisMenuItem.Text).Append("</a>");

            if (thisMenuItem.ChildItems.Count > 0)
            {
                sbMenu.Append("<ul>");
                for (int intIndex = 0; intIndex < thisMenuItem.ChildItems.Count; intIndex++)
                {
                    this.AddMenuIntoHTML(sbMenu, thisMenuItem.ChildItems[intIndex], liNavigateURLParameters);
                }
                sbMenu.Append("</ul>");
            }

            sbMenu.Append("</li>");
        }

        protected string ReplaceNavigateURLParameters(string strOriginalURL, List<ISC_RAC.Model.RowItem_NavigateURLParameter> liNavigateURLParameters)
        {
            for (int intIndex = 0; intIndex < liNavigateURLParameters.Count; intIndex++)
            {
                ISC_RAC.Model.RowItem_NavigateURLParameter thisParameter = liNavigateURLParameters[intIndex];
                if (string.IsNullOrEmpty(thisParameter.Key) == false)
                    strOriginalURL = strOriginalURL.Replace(thisParameter.Key, thisParameter.Value);
            }
            return strOriginalURL;
        }
    }
}
