﻿<%@ Page Title="" Language="C#" MasterPageFile="~/masterpage/MasterPage.master" AutoEventWireup="true"
    CodeFile="otherfileupload_readnoly.aspx.cs" Inherits="applydatamt_otherfileupload_readnoly" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="fullheader">
        <table cellpadding="0" cellspacing="0" class="fullheaderinfo" width="100%">
            <tr>
                <td valign="top">
                    <b>噪音合格證明申請-其他檔案上傳唯讀</b>
                </td>
                <td align="right" class="font_loginInfo" valign="top">
                </td>
            </tr>
        </table>
    </div>
    <!--{* fullheader end *}-->
    <div class="fullcontent">
        <table border="0" cellspacing="0" cellpadding="0" width="100%" class="formstyle">
            <tr>
                <td align="right">
                    申請編號：
                </td>
                <td>
                    <asp:Label ID="lab_applyno" runat="server"></asp:Label>
                </td>
                <td align="right">
                    資料狀態：
                </td>
                <td>
                    <asp:Label ID="lab_applystatus" runat="server"></asp:Label>
                </td>
            </tr>
            <tr>
                <td align="right">
                    車型組代號：
                </td>
                <td>
                    <asp:Label ID="lab_carcompomodelno" runat="server"></asp:Label>
                </td>
                <td align="right">
                    代表車型名稱：
                </td>
                <td>
                    <asp:Label ID="lab_carengmodelname" runat="server"></asp:Label>
                </td>
            </tr>
        </table>
        <div class="tableoutcome">
            <asp:GridView ID="gv_data" runat="server" AutoGenerateColumns="false" AllowPaging="True"
                PageSize="20" Width="100%" CellSpacing="0" CellPadding="0" BorderWidth="0" GridLines="None"
                CssClass="font_fullcontent" OnRowDataBound="gv_data_RowDataBound" EmptyDataText="尚無上傳該類型檔案"
                OnPageIndexChanging="gv_data_PageIndexChanging">
                <Columns>
                    <asp:BoundField HeaderText="項次" DataField="vil_no" ItemStyle-CssClass="lineleft"
                        HeaderStyle-CssClass="header" HeaderStyle-Width="10px" ItemStyle-HorizontalAlign="Center"
                        HeaderStyle-HorizontalAlign="Center" />
                    <asp:BoundField HeaderText="項目" DataField="vil_desc" ItemStyle-CssClass="lineleft"
                        HeaderStyle-CssClass="header" HeaderStyle-Width="190px" />
                    <asp:TemplateField HeaderText="上傳檔案" ItemStyle-CssClass="lineleft" HeaderStyle-CssClass="header"
                        ItemStyle-HorizontalAlign="Center" HeaderStyle-HorizontalAlign="Center" HeaderStyle-Width="70px">
                        <ItemTemplate>
                            <asp:Repeater ID="rpt_file" runat="server" OnItemDataBound="rpt_file_ItemDataBound">
                                <HeaderTemplate>
                                    <table id="rTbale">
                                </HeaderTemplate>
                                <ItemTemplate>
                                    <tr>
                                        <td>
                                            <asp:LinkButton ID="lbtn_file" runat="server"></asp:LinkButton>
                                            <asp:Label ID="lbl_fileDate" runat="server"></asp:Label>
                                        </td>
                                    </tr>
                                </ItemTemplate>
                                <FooterTemplate>
                                    </table>
                                </FooterTemplate>
                            </asp:Repeater>
                        </ItemTemplate>
                        <HeaderStyle CssClass="lineright"></HeaderStyle>
                        <ItemStyle CssClass="lineright"></ItemStyle>
                    </asp:TemplateField>
                </Columns>
                <EmptyDataRowStyle ForeColor="Red" />
                <PagerStyle HorizontalAlign="Center" Font-Bold="true" CssClass="S_PageCss" />
            </asp:GridView>
        </div>
    </div>
    <!--{* fullcontent end *}-->
    <div class="fullfooter">
    </div>
    <!--{* content end *}-->
</asp:Content>
