﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data.SqlClient;

/// <summary>
/// Summary description for IssueDocControl
/// </summary>
public class IssueDocControl
{
	public IssueDocControl()
	{
	}

	public bool ExistsEngineNo(string EngineNo)
	{
		SqlCommand sqlCmd = new SqlCommand(@"
if exists (select * from dbo.nv_applybasedata where abd_enginesn=@EngineNo)
begin
	select 1
	return
end
select 0
");
		sqlCmd.Parameters.AddWithValue("@EngineNo", EngineNo);
		object obj = Common.Data_noisevalidation.runScalar(sqlCmd);
		return obj.ToString() == "1" ? true : false;
	}

	public void UpdateIssue(string ApplyNo, string enginesn)
	{
		SqlCommand sqlCmd = new SqlCommand(@"
update dbo.nv_applybasedata set
abd_validateddate=getdate(),abd_enginesn=@enginesn
where abd_applyno=@ApplyNo
");
        sqlCmd.Parameters.AddWithValue("@enginesn", enginesn);
		sqlCmd.Parameters.AddWithValue("@ApplyNo", ApplyNo);
		Common.Data_noisevalidation.runParaCmd1(sqlCmd);
	}
}