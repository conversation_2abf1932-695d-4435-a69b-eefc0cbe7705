﻿<%@ Page Title="" Language="C#" MasterPageFile="~/masterpage/MasterOutset.master"
    AutoEventWireup="true" CodeFile="index.aspx.cs" Inherits="index" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script language="javascript" type="text/javascript">
        function OpenForgetpwd() {
            var winLeft = (screen.width - 600) / 2;
            var winTop = (screen.height - 250) / 2;
            if (winTop < 0) winTop = 0;
            if (winLeft < 0) winLeft = 0;
            window.open("forgetpwd.aspx", null, "height=250,width=600,top=" + winTop + ",left=" + winLeft);
            return false;
        }
        function clickButton(e, buttonid) {
            var bt = document.getElementById(buttonid);
            if (typeof bt == 'object') {
                if (navigator.appName.indexOf("Netscape") > (-1)) {
                    if (e.keyCode == 13) {
                        bt.click();
                        return false;
                    }
                }
                if (navigator.appName.indexOf("Microsoft Internet Explorer") > (-1)) {
                    if (event.keyCode == 13) {
                        bt.click();
                        return false;
                    }
                }
            }
        }      
   
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div id="content-wrapper">
        <div id="content-main">
            <!--{* activity start *}-->
            <div class="rightheader">
                <table width="100%" cellspacing="0" cellpadding="0" class="rightheaderinfo">
                    <tr>
                        <td valign="bottom" class="font-white">
                            <img src="../images/icon-title.gif" />&nbsp;<b>最新消息</b>
                        </td>
                        <td align="right">
                            <a href="#" target="_self">
                                <asp:ImageButton ID="imgbtn_newsmore" runat="server" ImageUrl="../images/btn-more.gif"
                                    OnClick="imgbtn_newsmore_Click" />
                            </a>
                        </td>
                    </tr>
                </table>
            </div>
            <div class="rightcontent">
                <!--{* 最新一則公告 start *}-->
                <table width="95%" cellspacing="0" cellpadding="0" class="leftnewslist">
                    <tr>
                        <td>
                            <span class="font_lightdata"><span id="ctl00_ContentPlaceHolder1_rpt_News_ctl00_lbl_newsdate">
                                2013-04-10</span></span><br />
                            <span class="font_news15"><b><a id="ctl00_ContentPlaceHolder1_rpt_News_ctl00_hlk_title"
                                href="http://vnc.epa.gov.tw/FileDownloadSeal.aspx?File=hSWFY0cTCus%3d" target="_self">
                                機動車輛排氣與噪音二證合一執行成效</a></b></span>
                        </td>
                    </tr>
                </table>
                <asp:Repeater ID="rpt_News" runat="server" OnItemDataBound="rpt_News_ItemDataBound">
                    <ItemTemplate>
                        <table width="95%" cellspacing="0" cellpadding="0" class="leftnewslist">
                            <tr>
                                <td>
                                    <span class="font_lightdata">
                                        <asp:Label ID="lbl_newsdate" runat="server"></asp:Label></span><br />
                                    <span class="font_news15"><b>
                                        <asp:HyperLink ID="hlk_title" runat="server" Target="_self"></asp:HyperLink></b></span>
                                </td>
                            </tr>
                        </table>
                    </ItemTemplate>
                </asp:Repeater>
                <!--{* 最新一則公告 end *}-->
            </div>
            <div class="rightfooter">
            </div>
            <!--{* activity end *}-->
            <!--{* downlad start *}-->
            <div class="rightheader">
                <table width="100%" cellspacing="0" cellpadding="0" class="rightheaderinfo">
                    <tr>
                        <td valign="bottom" class="font-white">
                            <b>參考資料下載區</b>
                        </td>
                        <td align="right">
                            <a href="#" target="_self">
                                <asp:ImageButton ID="imgbtn_briefmore" runat="server" ImageUrl="../images/btn-more.gif"
                                    OnClick="imgbtn_briefmore_Click" /></a>
                        </td>
                    </tr>
                </table>
            </div>
            <div class="rightcontent">
                <asp:Repeater ID="rpt_Brief" runat="server" OnItemDataBound="rpt_Brief_ItemDataBound">
                    <ItemTemplate>
                        <table border="0" width="97%" cellspacing="2" cellpadding="2" class="downloadlist">
                            <tr>
                                <td valign="top" class="downtitle">
                                    <img src="../images/icon_download.gif" />
                                    <b>
                                        <asp:HyperLink ID="hlk_title" runat="server" Target="_blank"></asp:HyperLink></b>
                                </td>
                                <td valign="top" align="right">
                                    <asp:Label ID="lbl_newsdate" runat="server"></asp:Label>
                                </td>
                            </tr>
                        </table>
                    </ItemTemplate>
                </asp:Repeater>
            </div>
            <div class="rightfooter">
            </div>
            <!--{* download end *}-->
        </div>
        <!--{* content-main end *}-->
        <div id="content-left">
            <!--{* login start *}-->
            <div class="leftheader">
                <table width="100%" cellspacing="0" cellpadding="0" class="leftheaderinfo">
                    <tr>
                        <td valign="bottom" class="font-white">
                            <img src="../images/icon-title.gif" />&nbsp;<b>登入資訊</b>
                        </td>
                        <td align="right" valign="top">
                            &nbsp;
                        </td>
                    </tr>
                </table>
            </div>
            <div class="leftcontent">
                <table width="100%" cellspacing="2" cellpadding="2" class="leftlogininfo">
                    <tr>
                        <td width="80" align="right">
                            統一編號:
                        </td>
                        <td colspan="2">
                            <asp:TextBox ID="txt_ID" runat="server" Width="120px" CssClass="leftlogininput1"
                                MaxLength="20"></asp:TextBox>
                        </td>
                    </tr>
                    <tr>
                        <td align="right">
                            帳號:
                        </td>
                        <td>
                            <asp:TextBox ID="txt_account" runat="server" Width="120px" CssClass="leftlogininput2"
                                MaxLength="20"></asp:TextBox>
                        </td>
                    </tr>
                    <tr>
                        <td align="right">
                            密碼:
                        </td>
                        <td>
                            <asp:TextBox ID="txt_password" runat="server" TextMode="Password" CssClass="leftlogininput2"
                                Width="120px" onkeypress="return clickButton(event,'ctl00_ContentPlaceHolder1_btn_login')"
                                MaxLength="20"></asp:TextBox>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2" align="right" style="padding-right: 10px;">
                            <asp:LinkButton ID="lbn_forgetpwd" runat="server" Text="忘記密碼" OnClientClick="OpenForgetpwd();"></asp:LinkButton>
                            &nbsp;&nbsp;
                            <asp:Button ID="btn_login" runat="server" Text="登入" OnClick="btn_login_Click" />
                        </td>
                    </tr>
                </table>
            </div>
            <div class="leftfooter">
            </div>
            <!--{* login end *}-->
        </div>
        <!--{* content-left end *}-->
    </div>
    <!--{* content-wrapper end *}-->
</asp:Content>
