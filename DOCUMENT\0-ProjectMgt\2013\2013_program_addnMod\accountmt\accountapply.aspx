﻿<%@ Page Title="" Language="C#" MasterPageFile="~/masterpage/MasterOutset.master"
    AutoEventWireup="true" CodeFile="accountapply.aspx.cs" Inherits="accountmt_accountapply" %>

<%@ Register TagPrefix="uc_pdp" TagName="dtprotection" Src="~/usercontrol/uc_dtprotection.ascx" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script language="javascript" type="text/javascript">
        function OpenPostNo() {
            window.open('http://www.post.gov.tw/post/internet/f_searchzone/index.jsp?ID=190102', '', 'dialogWidth:400px;dialogHeight:400px;');
            return false;
        }
        function KeyPressCheckCap() {
            if (event.keyCode < 65 || event.keyCode > 90) {
                if (event.keyCode >= 97 && event.keyCode <= 122)
                    event.keyCode = event.keyCode - 32;
            }
        }
        function BackIndex() {
            alert('系統已寄發申請確認資料');
            window.location = 'index.aspx';
        }

    </script>
    <style type="text/css">
        .style1
        {
            width: 151px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <asp:ScriptManager ID="ScriptManager1" runat="server">
    </asp:ScriptManager>
    <div class="fullheader">
        <table width="100%" cellspacing="0" cellpadding="0" class="fullheaderinfo">
            <tr>
                <td valign="top">
                    <b>帳號申請</b>
                </td>
                <td align="right" class="font_loginInfo">
                </td>
            </tr>
        </table>
    </div>
    <asp:MultiView ID="mv_apply" runat="server" ActiveViewIndex="0">
        <asp:View ID="View1" runat="server">
            <!--{* fullheader end *}-->
            <div class="fullcontent">
                <div class="formstyle">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0" class="font_fullcontent">
                        <tr>
                            <td colspan="4" align="center">
                                <span class="font_fulltitle" style="font-size: medium"><b>個人資料使用告知事項</b></span>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="4">
                                <uc_pdp:dtprotection ID="uc_PersonalDataProtection" runat="server" />
                            </td>
                        </tr>
                        <tr>
                            <td colspan="4" align="center">
                                <asp:Button ID="ButtonNext" runat="server" Text="我同意" CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'"
                                    onmouseout="this.className='btn_mouseout'" CommandName="NextView" />
                                &nbsp;&nbsp;
                                <asp:Button ID="btn_Disagree" runat="server" Text="我不同意" OnClick="btn_Disagree_Click"
                                    CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'" onmouseout="this.className='btn_mouseout'" />
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            <!--{* fullcontent end *}-->
        </asp:View>
        <asp:View ID="View2" runat="server">
            <!--{* fullheader end *}-->
            <div class="fullcontent">
                <div class="formstyle">
                    <table width="100%" border="0" cellspacing="0" cellpadding="0" class="font_fullcontent">
                        <tr>
                            <td colspan="4">
                                <span class="font_fulltitle"><b>公司基本資料</b></span>
                            </td>
                        </tr>
                        <tr>
                            <td align="right" class="style1">
                                <span style="color: Red;">*</span>統一編號：
                            </td>
                            <td>
                                <asp:TextBox ID="txt_compidno" runat="server" MaxLength="20" AutoPostBack="True"
                                    OnTextChanged="txt_compidno_TextChanged" CssClass="inputTypeS"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfcompidno" runat="server" ControlToValidate="txt_compidno"
                                    Display="None" ErrorMessage="統一編號不能空白"></asp:RequiredFieldValidator>
                            </td>
                            <td align="right">
                                <div id="div_applied1" runat="server" style="display: none;">
                                    已申請帳號數：</div>
                            </td>
                            <td>
                                <div id="div_applied2" runat="server" style="display: none;">
                                    <asp:Label ID="lbl_appliedcount" runat="server"></asp:Label></div>
                            </td>
                        </tr>
                        <tr>
                            <td align="right" class="style1">
                                <span style="color: Red;">*</span>公司名稱：
                            </td>
                            <td colspan="2">
                                <asp:TextBox ID="txt_compname" runat="server" MaxLength="120" Width="310px" CssClass="inputTypeS"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfcompname" runat="server" Display="None" ErrorMessage="公司名稱不能空白"
                                    ControlToValidate="txt_compname"></asp:RequiredFieldValidator>
                            </td>
                        </tr>
                        <tr>
                            <td align="right" class="style1">
                                公司英文名稱 ：
                            </td>
                            <td>
                                <asp:TextBox ID="txt_compename" runat="server" MaxLength="60" Width="237px" CssClass="inputTypeS"></asp:TextBox>
                            </td>
                            <td align="right">
                                <span style="color: Red;">*</span>負責人姓名：
                            </td>
                            <td>
                                <asp:TextBox ID="txt_chargeman" runat="server" Width="152px" MaxLength="30" CssClass="inputTypeS"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfchargeman" runat="server" Display="None" ErrorMessage="負責人姓名不能空白"
                                    ControlToValidate="txt_chargeman"></asp:RequiredFieldValidator>
                            </td>
                        </tr>
                        <tr>
                            <td align="right" class="style1">
                                <span style="color: Red;">*</span>公司電話：
                            </td>
                            <td>
                                <asp:TextBox ID="txt_comptel" runat="server" Width="152px" MaxLength="50" CssClass="inputTypeS"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfcomptel" runat="server" Display="None" ErrorMessage="公司電話不能空白"
                                    ControlToValidate="txt_comptel"></asp:RequiredFieldValidator>
                            </td>
                            <td align="right">
                                公司傳真：
                            </td>
                            <td>
                                <asp:TextBox ID="txt_compfax" runat="server" Width="152px" MaxLength="30" CssClass="inputTypeS"></asp:TextBox>
                            </td>
                        </tr>
                        <tr>
                            <td align="right" class="style1">
                                <span style="color: Red;">*</span>公司地址：
                            </td>
                            <td colspan="3">
                                <asp:UpdatePanel ID="UpdatePanel2" runat="server">
                                    <ContentTemplate>
                                        縣市別：<asp:DropDownList ID="ddl_compaddrcity" runat="server" AutoPostBack="True" OnSelectedIndexChanged="ddl_compaddrcity_SelectedIndexChanged"
                                            class="inputType">
                                        </asp:DropDownList>
                                        鄉鎮市區別：<asp:DropDownList ID="ddl_compaddrlocalarea" runat="server" AutoPostBack="True"
                                            OnSelectedIndexChanged="ddl_compaddrlocalarea_SelectedIndexChanged" CssClass="inputType">
                                        </asp:DropDownList>
                                        郵遞區號：
                                        <asp:Label ID="lbl_compaddrpostno" runat="server"></asp:Label>
                                        <asp:LinkButton ID="lbtn_comppostno" runat="server" Text="查詢" CausesValidation="False"
                                            OnClientClick="OpenPostNo();"></asp:LinkButton><br />
                                        <asp:TextBox ID="txt_compaddr" runat="server" Width="449px" MaxLength="120" CssClass="inputTypeS"></asp:TextBox>(請輸入路巷弄號)
                                        <asp:RequiredFieldValidator ID="rfcompaddr" runat="server" Display="None" ErrorMessage="公司地址不能空白"
                                            ControlToValidate="txt_compaddr"></asp:RequiredFieldValidator></ContentTemplate>
                                </asp:UpdatePanel>
                            </td>
                        </tr>
                        <tr>
                            <td align="right" class="style1">
                                <span style="color: Red;">*合格證寄發地址：</span>
                            </td>
                            <td colspan="3">
                                <asp:UpdatePanel ID="UpdatePanel3" runat="server">
                                    <ContentTemplate>
                                        <asp:CheckBox ID="cb_certaddrthesame" runat="server" Text="同公司地址" AutoPostBack="True"
                                            OnCheckedChanged="cb_certaddrthesame_CheckedChanged" />
                                        縣市別：<asp:DropDownList ID="ddl_certaddrCity" runat="server" AutoPostBack="True" CssClass="inputType"
                                            OnSelectedIndexChanged="ddl_certaddrCity_SelectedIndexChanged">
                                        </asp:DropDownList>
                                        鄉鎮市區別：<asp:DropDownList ID="ddl_certaddrlocalarea" runat="server" AutoPostBack="True"
                                            CssClass="inputType" OnSelectedIndexChanged="ddl_certaddrlocalarea_SelectedIndexChanged">
                                        </asp:DropDownList>
                                        郵遞區號：
                                        <asp:Label ID="lbl_certaddrpostno" runat="server"></asp:Label>
                                        <asp:LinkButton ID="lbtn_certaddrpostno" runat="server" Text="查詢" CausesValidation="False"
                                            OnClientClick="OpenPostNo();"></asp:LinkButton><br />
                                        <asp:TextBox ID="txt_certaddr" runat="server" Width="449px" MaxLength="120" CssClass="inputTypeS"></asp:TextBox>(請輸入路巷弄號)
                                        <asp:RequiredFieldValidator ID="rfcertaddr" runat="server" Display="None" ErrorMessage="合格證寄發地址不能空白"
                                            ControlToValidate="txt_certaddr"></asp:RequiredFieldValidator>
                                    </ContentTemplate>
                                </asp:UpdatePanel>
                            </td>
                        </tr>
                        <tr>
                            <td align="right" class="style1">
                                相關檔案上傳：
                            <td colspan="3">
                                <table>
                                    <tr>
                                        <td>
                                            <asp:Panel ID="p_fileUpload" runat="server">
                                                <span style="color: Red;">*</span>公司證明文件：<asp:FileUpload ID="fu_compDoc" runat="server" /><br />
                                                工廠登記證：<asp:FileUpload ID="fu_compReg" runat="server" /><br />
                                                代理授權證明函：<asp:FileUpload ID="fu_proxyLetter" runat="server" /><br />
                                                進口商證明文件：<asp:FileUpload ID="fu_importersDoc" runat="server" />
                                            </asp:Panel>
                                        </td>
                                        <td>
                                            <asp:Panel ID="p_filedownload" runat="server" Visible="false">
                                                <asp:Repeater ID="rpt_fileList" runat="server" OnItemDataBound="rpt_fileList_ItemDataBound">
                                                    <ItemTemplate>
                                                        <asp:HyperLink ID="hlk_file" runat="server" Target="_blank"></asp:HyperLink>、
                                                    </ItemTemplate>
                                                </asp:Repeater>
                                            </asp:Panel>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                    <table width="100%" border="0" cellspacing="0" cellpadding="0" class="font_fullcontent">
                        <tr>
                            <td colspan="4">
                                <span class="font_fulltitle"><b>帳號申請人資料</b></span>
                            </td>
                        </tr>
                        <tr>
                            <td align="right">
                                <span style="color: Red;">*</span>設定帳號：
                            </td>
                            <td>
                                <asp:TextBox ID="txt_account" runat="server" MaxLength="20" CssClass="inputTypeS"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfaccount" runat="server" ErrorMessage="帳號不能空白" Display="None"
                                    ControlToValidate="txt_account"></asp:RequiredFieldValidator>
                            </td>
                            <td align="right">
                                <span style="color: Red;">*</span>使用權限：
                            </td>
                            <td>
                                <asp:CheckBoxList ID="ckb_usesystem" runat="server" RepeatLayout="Flow" RepeatDirection="Horizontal">
                                    <asp:ListItem Text="噪音" Value="0" Selected="True" Enabled="false"></asp:ListItem>
                                    <asp:ListItem Text="污染" Value="1"></asp:ListItem>
                                </asp:CheckBoxList>
                            </td>
                        </tr>
                        <tr>
                            <td align="right">
                                <span style="color: Red;">*</span>申請人姓名：
                            </td>
                            <td>
                                <asp:TextBox ID="txt_username" runat="server" MaxLength="50" CssClass="inputTypeS"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfusername" runat="server" Display="None" ErrorMessage="申請人姓名不能空白"
                                    ControlToValidate="txt_username"></asp:RequiredFieldValidator>
                            </td>
                            <td align="right">
                                <span style="color: Red;">*</span>申請人電話：
                            </td>
                            <td>
                                <asp:TextBox ID="txt_usertel" runat="server" Width="152px" MaxLength="50" CssClass="inputTypeS"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfusertel" runat="server" Display="None" ErrorMessage="申請人電話不能空白"
                                    ControlToValidate="txt_usertel"></asp:RequiredFieldValidator>
                            </td>
                        </tr>
                        <tr>
                            <td align="right">
                                申請人傳真：
                            </td>
                            <td>
                                <asp:TextBox ID="txt_userfax" runat="server" MaxLength="50" CssClass="inputTypeS"></asp:TextBox>
                            </td>
                            <td align="right">
                                申請人行動電話：
                            </td>
                            <td>
                                <asp:TextBox ID="txt_usercellphone" runat="server" Width="151px" MaxLength="50" CssClass="inputTypeS"></asp:TextBox>
                            </td>
                        </tr>
                        <tr>
                            <td align="right">
                                <span style="color: Red;">*</span>申請人電子郵件地址：
                            </td>
                            <td colspan="3">
                                <asp:TextBox ID="txt_email" runat="server" Width="420px" MaxLength="80" CssClass="inputTypeS"></asp:TextBox>
                                <asp:RegularExpressionValidator ID="revEmail" runat="server" ControlToValidate="txt_email"
                                    ValidationExpression="\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*" ErrorMessage="您輸入的 Mail 格式有誤"></asp:RegularExpressionValidator>
                                <asp:RequiredFieldValidator ID="rfemail" runat="server" Display="None" ErrorMessage="申請人電子郵件地址不能空白"
                                    ControlToValidate="txt_email"></asp:RequiredFieldValidator>
                            </td>
                        </tr>
                        <tr>
                            <td align="right">
                                <span style="color: Red;">*</span>申請人郵寄地址：
                            </td>
                            <td colspan="3">
                                <asp:UpdatePanel ID="UpdatePanel1" runat="server">
                                    <ContentTemplate>
                                        <asp:CheckBox ID="cbx_isthesame" runat="server" Text="同公司地址" AutoPostBack="True"
                                            OnCheckedChanged="cbx_isthesame_CheckedChanged" />
                                        縣市別：<asp:DropDownList ID="ddl_useraddrcity" runat="server" AutoPostBack="True" OnSelectedIndexChanged="ddl_useraddrcity_SelectedIndexChanged"
                                            CssClass="inputType">
                                        </asp:DropDownList>
                                        鄉鎮市區別：<asp:DropDownList ID="ddl_useraddrlocalarea" runat="server" AutoPostBack="True"
                                            OnSelectedIndexChanged="ddl_useraddrlocalarea_SelectedIndexChanged" CssClass="inputType">
                                        </asp:DropDownList>
                                        郵遞區號：
                                        <asp:Label ID="lbl_useraddrpostno" runat="server"></asp:Label>
                                        <asp:LinkButton ID="lbtn_userpostno" runat="server" Text="查詢" CausesValidation="False"
                                            OnClientClick="OpenPostNo();"></asp:LinkButton><br />
                                        <asp:TextBox ID="txt_useraddr" runat="server" Width="449px" MaxLength="120" CssClass="inputTypeS"></asp:TextBox>(請輸入路巷弄號)
                                        <asp:RequiredFieldValidator ID="rfuseraddr" runat="server" Display="None" ErrorMessage="申請人郵寄地址不能空白"
                                            ControlToValidate="txt_useraddr"></asp:RequiredFieldValidator>
                                    </ContentTemplate>
                                </asp:UpdatePanel>
                            </td>
                        </tr>
                        <tr>
                            <td align="right" class="style1">
                                個資聲明：
                            </td>
                            <td colspan="3">
                                <asp:CheckBox ID="cbx_isperson" runat="server" Text="我同意" Checked="True" />
                            </td>
                        </tr>
                        <tr>
                            <td colspan="4" align="center">
                                <asp:Button ID="ButtonPrev" runat="server" Text="回上一頁" CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'"
                                    onmouseout="this.className='btn_mouseout'" CausesValidation="False" CommandName="PrevView" />
                                &nbsp;&nbsp;
                                <asp:Button ID="btn_apply" runat="server" Text="送出申請" OnClick="btn_apply_Click" CssClass="btn_mouseout"
                                    onmouseover="this.className='btn_mouseover'" onmouseout="this.className='btn_mouseout'" />
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            <!--{* fullcontent end *}-->
        </asp:View>
    </asp:MultiView>
    <div class="fullfooter">
    </div>
    <!--{* content end *}-->
    <asp:ValidationSummary ID="ValidationSummary1" runat="server" ShowMessageBox="True"
        ShowSummary="False" />
</asp:Content>
