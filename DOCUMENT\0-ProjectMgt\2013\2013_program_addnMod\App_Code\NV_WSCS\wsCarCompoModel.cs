﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Services;
using System.Data;
using System.Data.SqlClient;

/// <summary>
/// Summary description for wsCarCompoModel
/// </summary>
[WebService(Namespace = "http://tempuri.org/")]
[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
// To allow this Web Service to be called from script, using ASP.NET AJAX, uncomment the following line. 
// [System.Web.Script.Services.ScriptService]
public class wsCarCompoModel : System.Web.Services.WebService {

    public wsCarCompoModel () {

        //Uncomment the following line if using designed components 
        //InitializeComponent(); 
    }

    //車型組func Start
    //取出同一申請下的車型組編號清單
    [WebMethod]
    public DataTable get_carcompomodelno_byapplyno(string applyno, string ID, string PD)
    {
        DataSet ds = new DataSet();
        DataTable dt = new DataTable();
        if (wsCheckCarCompoModelValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select cpm_carcompomodelno from nv_carcompomodel where cpm_applyno=@applyno");
            cmd.Parameters.AddWithValue("@applyno", applyno);
            ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
            dt = ds.Tables[0];
            return dt;
        }
        return dt;
    }

    //取出車型組代號的數目By車型組代號
    [WebMethod]
    public int getCountByCarCompoModelNo(string strCarCompoModelNo, string ID, string PD)
    {
        int cnt = 0;
        DataSet ds = new DataSet();
        if (wsCheckCarCompoModelValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select count(*) from nv_carcompomodel where cpm_carcompomodelno=@carcompomodelno");            
            cmd.Parameters.AddWithValue("@carcompomodelno", strCarCompoModelNo);
            ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
            cnt = Convert.ToInt32(ds.Tables[0].Rows[0][0].ToString());
        }
        return cnt;
    }
    //檢查是否有代表車了
    public bool check_repeat_representativevehicle(string strApplyNo, string strCarCompoModelNo, string ID, string PD)
    {
        if (wsCheckCarCompoModelValid(ID, PD))
        {
            DataSet ds = new DataSet();
            SqlCommand cmd = new SqlCommand(@"select count(*) 'representcar_nums' from nv_carmodeldata where cmdg_carcompomodelno=@carcompomodelno and cmdg_applyno=@applyno and cmdg_berepresentativevehicle='Y'");
            cmd.Parameters.AddWithValue("@carcompomodelno", strCarCompoModelNo);
            cmd.Parameters.AddWithValue("@applyno", strApplyNo);
            ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
            if (Convert.ToInt32(ds.Tables[0].Rows[0][0].ToString()) > 0)
                return false;
            else
                return true;
        }
        else
        {
            return false;
        }
    }
    //取出車型組代號的數目By申請編號+車型組代號
    [WebMethod]
    public int getCountByApplyNo_CarCompoModelNo(string strApplyNo, string strCarCompoModelNo, string ID, string PD)
    {
        int cnt = 0;
        DataSet ds = new DataSet();
        if (wsCheckCarCompoModelValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select count(*) from nv_carcompomodel where cpm_carcompomodelno=@carcompomodelno and cpm_applyno=@applyno");
            cmd.Parameters.AddWithValue("@carcompomodelno", strCarCompoModelNo);
            cmd.Parameters.AddWithValue("@applyno", strApplyNo);
            ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
            cnt = Convert.ToInt32(ds.Tables[0].Rows[0][0].ToString());
        }
        return cnt;
    }

    //取出車型組資料By申請編號+車型組編號
    [WebMethod]
    public DataSet getDataByApplyNo_CarCompoModelNo(string strApplyNo,string strCarCompoModelNo, string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsCheckCarCompoModelValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@" select nv_carcompomodel.*,abd_enginefamily,abd_carstyleyear,abd_databelongcompidno,abd_databelongcompname,abd_powerfrom,abd_vechiletype,abd_isimport,ct_le_desc,ct_cs_desc,ct_ep_desc_g,ct_aps_desc,ct_gm_desc,ct_at_desc,ct_cy_desc,ct_ebm_desc,ct_ft_desc,ct_tf_desc,abd_applystatus,isnull(ct_twf_desc,0) ct_twf_desc
                                    from nv_carcompomodel 
                                    join nv_applybasedata on abd_applyno=cpm_applyno 
                                    left join nv_codetbl_lubricationtype on ct_le_id=cpm_provideoilmethod
                                    left join nv_codetbl_coolsystem on ct_cs_id=cpm_coolsystem
                                    left join nv_codetbl_enginepos on ct_ep_id_g=cpm_fituptype
                                    left join nv_codetbl_applytype on ct_at_id = cpm_applytype
                                    left join nv_codetbl_country on ct_cy_id = cpm_importarea
                                    left join nv_codetbl_applystatus on ct_aps_id = abd_applystatus
                                    left join nv_codetbl_gearmethod on ct_gm_id = cpm_transmissiontypetype
                                    left join nv_codetbl_engineburnmethod on ct_ebm_id = cpm_powerexportway
                                    left join nv_codetbl_fueltype on ct_ft_id = cpm_gasolinetype
                                    left join nv_codetbl_testfactory on ct_tf_id = cpm_testfactory
                                    join nv_codetbl_testweightformula on ct_twf_id=abd_adaptstandarddate
                                    where cpm_applyno=@applyno and cpm_carcompomodelno=@carcompomodelno");
            cmd.Parameters.AddWithValue("@applyno", strApplyNo);
            cmd.Parameters.AddWithValue("@carcompomodelno", strCarCompoModelNo);
            ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
        }
        return ds;
    }

    //取出車型組資料By申請編號+車型組編號
    [WebMethod]
    public DataSet getDataByApplyNo_CarCompoModelNo_m(string strApplyNo, string strCarCompoModelNo, string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsCheckCarCompoModelValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select nv_carcompomodel.*,abd_enginefamily,abd_carstyleyear,abd_databelongcompname,abd_powerfrom,abd_vechiletype,abd_isimport,ct_le_desc,ct_cs_desc,ct_ep_desc_g,ct_aps_desc,ct_at_desc,ct_cy_desc,ct_ebm_desc,ct_ft_desc,ct_tf_desc
                                    from nv_carcompomodel 
                                    left join nv_applybasedata on abd_applyno=cpm_applyno 
                                    left join nv_codetbl_lubricationtype on ct_le_id=cpm_provideoilmethod
                                    left join nv_codetbl_coolsystem on ct_cs_id=cpm_coolsystem
                                    left join nv_codetbl_enginepos on ct_ep_id_g=cpm_fituptype
                                    left join nv_codetbl_applytype on ct_at_id = cpm_applytype
                                    left join nv_codetbl_country on ct_cy_id = cpm_importarea
                                    left join nv_codetbl_applystatus on ct_aps_id = abd_applystatus
                                    left join nv_codetbl_gearmethod on ct_gm_id = cpm_transmissiontypetype
                                    left join nv_codetbl_engineburnmethod on ct_ebm_id = cpm_powerexportway
                                    left join nv_codetbl_fueltype on ct_ft_id = cpm_gasolinetype
                                    left join nv_codetbl_testfactory on ct_tf_id = cpm_testfactory
                                    where cpm_carcompomodelno=@carcompomodelno and cpm_applyno=@applyno");
            cmd.Parameters.AddWithValue("@applyno", strApplyNo);
            cmd.Parameters.AddWithValue("@carcompomodelno", strCarCompoModelNo);
            ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
        }
        return ds;
    }

    //取出車型組資料
    [WebMethod]
    public DataSet get_CarcompomodelBasicData(string strApplyNo, string strCarCompoModelNo, string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsCheckCarCompoModelValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"exec pr_get_carcompomodelBasicData @applyno,@carcompomodelno ");
            cmd.Parameters.AddWithValue("@applyno", strApplyNo);
            cmd.Parameters.AddWithValue("@carcompomodelno", strCarCompoModelNo);         
            ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
        }
        return ds;
    }

    [WebMethod]
    public DataTable get_CarspecTableData(string strApplyNo, string strCarCompoModelNo, string strCarModelNo, string strVechileType, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsCheckCarCompoModelValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"exec pr_get_carspectabledata @applyno,@carcompomodelno,@carmodelno,@vechiletype ");
            cmd.Parameters.AddWithValue("@applyno", strApplyNo);
            cmd.Parameters.AddWithValue("@carcompomodelno", strCarCompoModelNo);
            cmd.Parameters.AddWithValue("@carmodelno", strCarModelNo);
            cmd.Parameters.AddWithValue("@vechiletype", strVechileType);
            dt = Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];           
        }
        return dt;
    }
    [WebMethod]
    public DataTable get_Gearratio_list(string strApplyNo, string strCarCompoModelNo, string strCarModelNo, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsCheckCarCompoModelValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"exec pr_get_gearratio_list @applyno,@carcompomodelno,@carmodelno");
            cmd.Parameters.AddWithValue("@applyno", strApplyNo);
            cmd.Parameters.AddWithValue("@carcompomodelno", strCarCompoModelNo);
            cmd.Parameters.AddWithValue("@carmodelno", strCarModelNo);
            dt = Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;
    }
    [WebMethod]
    public DataTable get_RemarkForRpt(string strApplyNo, string strCarCompoModelNo, string strCarModelNo, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsCheckCarCompoModelValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"exec pr_get_RemarkForRpt @applyno,@carcompomodelno,@carmodelno");
            cmd.Parameters.AddWithValue("@applyno", strApplyNo);
            cmd.Parameters.AddWithValue("@carcompomodelno", strCarCompoModelNo);
            cmd.Parameters.AddWithValue("@carmodelno", strCarModelNo);
            dt = Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;
    }
    //取得Vechiletype
    public string Get_Vechiletype(string applyno, string ID, string PD)
    {      
        if (wsCheckCarCompoModelValid(ID, PD))
        {
            string sql = @"select abd_vechiletype,abd_applyno from nv_applybasedata where abd_applyno = @applyno";

            SqlCommand cmd = new SqlCommand(sql);
            cmd.Parameters.AddWithValue("@applyno", applyno);

            DataTable dt = Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];

            if (dt.Rows.Count > 0)
                return dt.Rows[0]["abd_vechiletype"].ToString();
            else return "-1";
        }
        return "-1";                
    }

    //新增一筆資料(汽油車)
    [WebMethod]    
    public void InsertCarCompoModel_G(string[] arrData, string ID, string PD)
    {
        if (wsCheckCarCompoModelValid(ID, PD))
        {
            string strTable = "nv_carcompomodel";
            string[] arrFields = { "cpm_applyno", "cpm_carcompomodelno", "cpm_applytype",
                                   "cpm_useeurocert", "cpm_transmissiontypetype",
                                   "cpm_gasolinetype", "cpm_provideoilmethod",	"cpm_exhuast",
                                   "cpm_cylindernums", "cpm_fituptype", "cpm_coolsystem",
                                   "cpm_testfactory", "cpm_importarea",	
                                   "cpm_createaccount", "cpm_modaccount","cpm_createdate","cpm_moddate" };
            string[] arrValues = { "@applyno", "@carcompomodelno", "@applytype", 
                                   "@useeurocert", "@transmissiontypetype", 
                                   "@gasolinetype", "@provideoilmethod", "@exhuast", 
                                   "@cylindernums", "@fituptype", "@coolsystem", 
                                   "@testfactory", "@importarea",
                                   "@createaccount", "@modaccount","getdate()","getdate()" };
            string strSql_Fields = "";
            string strSql_Values = "";
            for (int i = 0; i < arrData.Length; i++)
                if (arrData[i] != null)
                {
                    strSql_Fields += arrFields[i] + ",";
                    strSql_Values += arrValues[i] + ",";
                }

            if (strSql_Fields.Length > 0)
                strSql_Fields = strSql_Fields.Substring(0, strSql_Fields.Length - 1);
            
            if (strSql_Values.Length > 0)
                strSql_Values = strSql_Values.Substring(0, strSql_Values.Length - 1);

            if (strSql_Fields.Length > 0)
            {
                SqlCommand cmd_ins = new SqlCommand("Insert into "+ strTable +" (" + strSql_Fields + ") Values (" + strSql_Values + ")");

                for (int i = 0; i < arrData.Length-2; i++)
                    if (arrData[i] != null)
                    {
                        cmd_ins.Parameters.AddWithValue(arrValues[i], arrData[i]);
                    }
                Common.Data_noisevalidation.runParaCmd1(cmd_ins);
            }

        }
    }

    //更新一筆資料(汽油車)
    [WebMethod]
    public void UpdateCarCompoModel_G(string[] arrData, string ID, string PD)
    {
        if (wsCheckCarCompoModelValid(ID, PD))
        {
            string strTable = "nv_carcompomodel";
            string[] arrFields = { "cpm_applyno", "cpm_carcompomodelno", "cpm_applytype",
                                   "cpm_useeurocert", "cpm_transmissiontypetype",
                                   "cpm_gasolinetype", "cpm_provideoilmethod",	"cpm_exhuast",
                                   "cpm_cylindernums", "cpm_fituptype", "cpm_coolsystem",
                                   "cpm_testfactory", "cpm_importarea",	
                                   "cpm_modaccount","cpm_moddate" };
            string[] arrValues = { "@applyno", "@carcompomodelno", "@applytype", 
                                   "@useeurocert", "@transmissiontypetype", 
                                   "@gasolinetype", "@provideoilmethod", "@exhuast", 
                                   "@cylindernums", "@fituptype", "@coolsystem", 
                                   "@testfactory",  "@importarea",
                                   "@modaccount","getdate()" };
            string strSql_Fields = "";
            string strSql_Where = " Where cpm_carcompomodelno=" + arrValues[1] + " and cpm_applyno=" + arrValues[0];

            for (int i = 0; i < arrData.Length; i++)
                if (arrData[i] != null)
                {
                    strSql_Fields += arrFields[i] + "=" + arrValues[i] + ",";
                }

            if (strSql_Fields.Length > 0)
                strSql_Fields = strSql_Fields.Substring(0, strSql_Fields.Length - 1);
            //string update_cmd_str = "update nv_carcompomodel set " +
            //                        "cpm_applytype=@applytype,cpm_useeurocert=@useeurocert, " +
            //                        "cpm_transmissiontypetype=@transmissiontypetype,cpm_gasolinetype=@gasolinetype, " +
            //                        "cpm_provideoilmethod=@provideoilmethod,cpm_exhuast=@exhuast, " +
            //                        "cpm_cylindernums=@cylindernums,cpm_fituptype=@fituptype, cpm_coolsystem=@coolsystem, " +
            //                        "cpm_testfactory=@testfactory, cpm_importarea=@importarea," +
            //                        "cpm_modaccount=@modaccount,  cpm_moddate=getdate() where cpm_carcompomodelno=@carcompomodelno and cpm_applyno=@applyno";

            if (strSql_Fields.Length > 0)
            {
                SqlCommand cmd_upd = new SqlCommand("Update " + strTable + " set " + strSql_Fields + strSql_Where);
                //SqlCommand cmd_upd = new SqlCommand(update_cmd_str);
                //最後一欄使用getdate()，不用加入參數裡面
                for (int i = 0; i < arrData.Length-1; i++)
                    if (arrData[i] != null)
                    {
                        cmd_upd.Parameters.AddWithValue(arrValues[i], arrData[i]);
                    }
                Common.Data_noisevalidation.runParaCmd1(cmd_upd);
            }

        }
    }

    //新增一筆資料(機車)
    [WebMethod]
    public void InsertCarCompoModel_M(string[] arrData, string ID, string PD)
    {
        if (wsCheckCarCompoModelValid(ID, PD))
        {
            string strTable = "nv_carcompomodel";
            string[] arrFields = { "cpm_applyno", "cpm_carcompomodelno", "cpm_applytype",
                                   "cpm_useeurocert",  "cpm_transmissiontypetype",
                                   "cpm_gasolinetype", "cpm_powerexportway","cpm_provideoilmethod",	
                                   "cpm_exhuast", "cpm_cylindernums", "cpm_cylindertype",
                                   "cpm_enginesetupposnmethod", "cpm_coolsystem", "cpm_testfactory", 
                                   "cpm_importarea", "cpm_createaccount", "cpm_modaccount", 
                                   "cpm_createdate", "cpm_moddate" };
            string[] arrValues = { "@applyno", "@carcompomodelno", "@applytype", 
                                   "@useeurocert",  "@transmissiontypetype", 
                                   "@gasolinetype", "@powerexportway", "@provideoilmethod", 
                                   "@exhuast", "@cylindernums", "@cylindertype",
                                   "@enginesetupposnmethod", "@coolsystem", "@testfactory", 
                                   "@importarea", "@createaccount", "@modaccount", 
                                   "getdate()", "getdate()" };
            string strSql_Fields = "";
            string strSql_Values = "";
            for (int i = 0; i < arrData.Length; i++)
                if (arrData[i] != null)
                {
                    strSql_Fields += arrFields[i] + ",";
                    strSql_Values += arrValues[i] + ",";
                }

            if (strSql_Fields.Length > 0)
                strSql_Fields = strSql_Fields.Substring(0, strSql_Fields.Length - 1);

            if (strSql_Values.Length > 0)
                strSql_Values = strSql_Values.Substring(0, strSql_Values.Length - 1);

            if (strSql_Fields.Length > 0)
            {
                SqlCommand cmd_ins = new SqlCommand("Insert into " + strTable + " (" + strSql_Fields + ") Values (" + strSql_Values + ")");

                for (int i = 0; i < arrData.Length - 2; i++)
                    if (arrData[i] != null)
                    {
                        cmd_ins.Parameters.AddWithValue(arrValues[i], arrData[i]);
                    }
                Common.Data_noisevalidation.runParaCmd1(cmd_ins);
            }                       
        }
    }

    //更新一筆資料(機車)
    [WebMethod]
    public void UpdateCarCompoModel_M(string[] arrData, string ID, string PD)
    {
        if (wsCheckCarCompoModelValid(ID, PD))
        {
            string strTable = "nv_carcompomodel";
            string[] arrFields = { "cpm_applyno", "cpm_carcompomodelno", "cpm_applytype",
                                   "cpm_useeurocert",  "cpm_transmissiontypetype",
                                   "cpm_gasolinetype", "cpm_powerexportway","cpm_provideoilmethod",	
                                   "cpm_exhuast", "cpm_cylindernums", "cpm_cylindertype",
                                   "cpm_enginesetupposnmethod", "cpm_coolsystem", "cpm_testfactory", 
                                   "cpm_importarea",  "cpm_modaccount", "cpm_moddate" };
            string[] arrValues = { "@applyno", "@carcompomodelno", "@applytype", 
                                   "@useeurocert", "@transmissiontypetype", 
                                   "@gasolinetype", "@powerexportway", "@provideoilmethod", 
                                   "@exhuast", "@cylindernums", "@cylindertype",
                                   "@enginesetupposnmethod", "@coolsystem", "@testfactory", 
                                   "@importarea", "@modaccount", "getdate()" };
            string strSql_Fields = "";
            string strSql_Where = " Where cpm_carcompomodelno=" + arrValues[1] + " and cpm_applyno=" + arrValues[0];

            for (int i = 0; i < arrFields.Length; i++)
                if (arrFields[i] != null)
                {
                    strSql_Fields += arrFields[i] + "=" + arrValues[i] + ",";
                }

            if (strSql_Fields.Length > 0)
                strSql_Fields = strSql_Fields.Substring(0, strSql_Fields.Length - 1);

            if (strSql_Fields.Length > 0)
            {
                SqlCommand cmd_upd = new SqlCommand("Update " + strTable + " set " + strSql_Fields + strSql_Where);

                for (int i = 0; i < arrData.Length-1; i++)
                    if (arrData[i] != null)
                    {
                        cmd_upd.Parameters.AddWithValue(arrValues[i], arrData[i]);
                    }
                Common.Data_noisevalidation.runParaCmd1(cmd_upd);
            }

        }
    }

    [WebMethod]
    public int Delete_single_carcompomodel_whole_data(string applyno, string carcompomodelno, string ID, string PD)
    {
        if (wsCheckCarCompoModelValid(ID, PD))
        {
            SqlCommand cmd_Delete = new SqlCommand("EXEC pr_delete_carcompomodel_data @applyno,@carcompomodelno");
            cmd_Delete.Parameters.AddWithValue("@applyno", applyno);
            cmd_Delete.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
            int delete_errors = (int)Common.Data_noisevalidation.runScalar(cmd_Delete);
            return delete_errors;
        }
        else
            return -1;
    }

    public DataTable get_VehicleCarInformation(string applyno, string carcompomodelno, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsCheckCarCompoModelValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"exec pr_get_VehicleCarInformation @applyno,@carcompomodelno");

            cmd.Parameters.AddWithValue("@applyno", applyno);
            cmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);

            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;

    }
    //車型組func End
    /*************************************************************************************************************************/
    //車型func Start
    [WebMethod]
    //取得單一車型資料
    public DataTable Get_single_carmodel_data(string applyno, string carcompomodelno, string carmodelno, string ID, string PWD)
    {
        DataSet ds_carmodel_data = new DataSet();
        if (wsCheckCarCompoModelValid(ID, PWD))
        {
            SqlCommand cmd = new SqlCommand(@"EXEC pr_get_carmodeldata_single @applyno,@carcompomodelno,@carmodelno");
            cmd.Parameters.AddWithValue("@applyno", applyno);
            cmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
            cmd.Parameters.AddWithValue("@carmodelno", carmodelno);
            ds_carmodel_data = Common.Data_noisevalidation.runParaCmdDS(cmd);
            return ds_carmodel_data.Tables[0];
        }
        return ds_carmodel_data.Tables[0];
    }

    //取得單一車型規格表資料
    public DataTable Get_single_carspectabledata(string applyno, string carcompomodelno, string carmodelno, string vechiletype, string ID, string PWD)
    {
        DataSet ds_carmodel_data = new DataSet();
        if (wsCheckCarCompoModelValid(ID, PWD))
        {
            SqlCommand cmd = new SqlCommand(@"EXEC pr_get_carspectabledata @applyno,@carcompomodelno,@carmodelno,@vechiletype");
            cmd.Parameters.AddWithValue("@applyno", applyno);
            cmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
            cmd.Parameters.AddWithValue("@carmodelno", carmodelno);
            cmd.Parameters.AddWithValue("@vechiletype", vechiletype);
            ds_carmodel_data = Common.Data_noisevalidation.runParaCmdDS(cmd);
            return ds_carmodel_data.Tables[0];
        }
        return ds_carmodel_data.Tables[0];
    }

    //取得單一車型規格之齒筆資料
    public DataTable Get_single_carmodel_GearRatio(string applyno, string carcompomodelno, string carmodelno, string ID, string PWD)
    {
        DataSet ds_carmodel_data = new DataSet();
        if (wsCheckCarCompoModelValid(ID, PWD))
        {
            SqlCommand cmd = new SqlCommand(@"select * from nv_gearratio where gr_applyno=@applyno and gr_carcompomodelno=@carcompomodelno and gr_carmodelno=@carmodelno order by gr_gearnum");
            cmd.Parameters.AddWithValue("@applyno", applyno);
            cmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
            cmd.Parameters.AddWithValue("@carmodelno", carmodelno);
            ds_carmodel_data = Common.Data_noisevalidation.runParaCmdDS(cmd);
            return ds_carmodel_data.Tables[0];
        }
        return ds_carmodel_data.Tables[0];
    }
    //刪除多餘的齒筆資料  EX：原本挑8黨後來改成5檔  須將6 7 8 檔資料刪除
    public void delete_GearRatio_external(string applyno, string carcompomodelno, string carmodelno,string transmissionnum, string ID, string PWD)
    {
        if (wsCheckCarCompoModelValid(ID, PWD))
        {
            SqlCommand cmd = new SqlCommand(@"delete from nv_gearratio where gr_applyno=@applyno and gr_carcompomodelno=@carcompomodelno and gr_carmodelno=@carmodelno and convert(int,gr_gearnum)>convert(int,@transmissionnum)");
            cmd.Parameters.AddWithValue("@applyno", applyno);
            cmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
            cmd.Parameters.AddWithValue("@carmodelno", carmodelno);
            cmd.Parameters.AddWithValue("@transmissionnum", transmissionnum);
            Common.Data_noisevalidation.runParaCmd1(cmd);
            cmd.Dispose();
        }
    }
    
    //新增車型規格表
    public string Insert_new_carmodeldata(string[] arrData, string ID, string PWD)
    {
        /*
          機車的欄位 載重Cmdg_loadingweight、啟動方式Cmdg_firemethod、排氣口位置及方向Cmdg_strokeposdirection、粒狀汙染物Cmdg_PM、一氧化碳Cmdg_CO、碳氧化合物Cmdg_HC、一次減速比Cmdg_1stReductionratio、二次減速比Cmdg_2ndReductionratio
        */
        if (wsCheckCarCompoModelValid(ID, PWD))
        {
            string strTable = "nv_carmodeldata";
            string[] arrFields = {  "cmdg_applyno","cmdg_carcompomodelno","cmdg_carbodyshape","cmdg_carbodyshapedesc","cmdg_berepresentativevehicle"
                                    ,"cmdg_cartype","cmdg_baseenginename","cmdg_carbrand","cmdg_carchnmodelname","cmdg_carengmodelname"
                                    ,"cmdg_carmodelcode","cmdg_transmissionnum","cmdg_cardoorcount","cmdg_carmodeladd","cmdg_carbodylenth"
                                    ,"cmdg_carbodywidth","cmdg_carbodyhieght","cmdg_carwheelbase","cmdg_carwheelbase_f","cmdg_carwheelbase_b"
                                    ,"cmdg_caremptyweight","cmdg_cartotalweight","cmdg_passengers","cmdg_cylindermeters","cmdg_cylinderstroke"
                                    ,"cmdg_compressionratio","cmdg_maxhorsepower","cmdg_maxhorsepowerspeed","cmdg_torque","cmdg_torquespeed"
                                    ,"cmdg_turborchargers","cmdg_oilcapacity","cmdg_929598","cmdg_exhaustsystem","cmdg_PCV"
                                    ,"cmdg_EEC","cmdg_differentialmodel","cmdg_differentialgearratio","cmdg_transmissionmodel"
                                    ,"cmdg_transmissionbackgearratio","cmdg_highestspeed","cmdg_suspensionsystem_f","cmdg_suspensionsystem_b","cmdg_tirespec_std_f","cmdg_tirespec_std_b","cmdg_tirespec_cho_f","cmdg_tirespec_cho_b"
                                    ,"cmdg_testweight","cmdg_testgearreductionratio","cmdg_coolingdrivermethod","cmdg_tiresnum","cmdg_exhaustpipenum"
                                    ,"cmdg_tirewidth","cmdg_inletmode","cmdg_createaccount","cmdg_modaccount","cmdg_drivetype","cmdg_remark","cmdg_producercountry"
                                    ,"cmdg_loadingweight","cmdg_firemethod","cmdg_strokeposdirection","cmdg_PM","cmdg_CO","cmdg_HC","cmdg_1stReductionratio","cmdg_2ndReductionratio"
                                    ,"cmdg_backgearnums","cmdg_highlowgear","cmdg_carbodyweight","cmdg_maxhorsepower_hb","cmdg_maxhorsepowerspeed_hb","cmdg_torque_hb","cmdg_torquespeed_hb","cmdg_numofaxes_f","cmdg_numofaxes_b","cmdg_increasepowerstyle","cmdg_increasepowerratio","cmdg_climbratio","cmdg_suspensionsystem_supplement","cmdg_forwardgearnums"
                                    ,"cmdg_createdate","cmdg_moddate","cmdg_carmodelno","cmdg_beextendmodel","cmdg_beenmod","cmdg_carusedstatus"

                                    };

            string[] arrValues = {  "@applyno","@carcompomodelno","@carbodyshape","@carbodyshapedesc","@berepresentativevehicle"
                                    ,"@cartype","@baseenginename","@carbrand","@carchnmodelname","@carengmodelname"
                                    ,"@carmodelcode","@transmissionnum","@cardoorcount","@carmodeladd","@carbodylenth"
                                    ,"@carbodywidth","@carbodyhieght","@carwheelbase","@carwheelbase_f","@carwheelbase_b"
                                    ,"@caremptyweight","@cartotalweight","@passengers","@cylindermeters","@cylinderstroke"
                                    ,"@compressionratio","@maxhorsepower","@maxhorsepowerspeed","@torque","@torquespeed"
                                    ,"@turborchargers","@oilcapacity","@929598","@exhaustsystem","@PCV"
                                    ,"@EEC","@differentialmodel" ,"@differentialgearratio","@transmissionmodel"
                                    ,"@transmissionbackgearratio","@highestspeed","@suspensionsystem_f","@suspensionsystem_b","@tirespec_std_f","@tirespec_std_b","@tirespec_cho_f","@tirespec_cho_b"
                                    ,"@testweight","@testgearreductionratio","@coolingdrivermethod","@tiresnum","@exhaustpipenum"
                                    ,"@tirewidth","@inletmode","@createaccount","@modaccount","@cmdg_drivetype","@remark","@producercountry"
                                    ,"@loadingweight","@firemethod","@strokeposdirection","@PM","@CO","@HC","@1stReductionratio","@2ndReductionratio"
                                    ,"@backgearnums","@highlowgear","@carbodyweight","@maxhorsepower_hb","@maxhorsepowerspeed_hb","@torque_hb","@torquespeed_hb","@numofaxes_f","@numofaxes_b","@increasepowerstyle","@increasepowerratio","@climbratio","@suspensionsystem_supplement","@forwardgearnums"
                                    ,"getdate()","getdate()","@CARID","1","0","1"

                                    };
            string strSql_Fields = "";
            string strSql_Values = "";
            for (int i = 0; i < arrFields.Length; i++)
            {
                strSql_Fields += arrFields[i] + ",";
                strSql_Values += arrValues[i] + ",";
            }

            if (strSql_Fields.Length > 0)
                strSql_Fields = strSql_Fields.Substring(0, strSql_Fields.Length - 1);

            if (strSql_Values.Length > 0)
                strSql_Values = strSql_Values.Substring(0, strSql_Values.Length - 1);

            if (strSql_Fields.Length > 0)
            {
                SqlCommand cmd_ins = new SqlCommand("declare @CARID varchar(36) set @CARID=NEWID() Insert into " + strTable + " (" + strSql_Fields + ") Values (" + strSql_Values + ") select @CARID");
                //最後一個為車型組編號，新增為空白無須使用
                for (int i = 0; i < arrData.Length-1; i++)
                    if (arrData[i] != null)
                    {
                        cmd_ins.Parameters.AddWithValue(arrValues[i], arrData[i]);
                    }
                Object obj_ins = Common.Data_noisevalidation.runScalar(cmd_ins);
                return (string)obj_ins;
            }

            //取得車型資料的ID編號
            //select cmdg_carmodelno from nv_carmodeldata where  cmdg_applyno=arrValues[0].ToString() and cmdg_carcompomodelno=arrValues[1].ToString() and 
        }
        return "";
    }

    //更新車型規格表
    public void update_carmodeldata(string[] arrData, string ID, string PWD)
    {
        if (wsCheckCarCompoModelValid(ID, PWD))
        {
            string sql_update_carmodel = "update nv_carmodeldata set ";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_carbodyshape=@carbodyshape,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_carbodyshapedesc=@carbodyshapedesc,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_berepresentativevehicle=@berepresentativevehicle,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_cartype=@cartype,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_baseenginename=@baseenginename,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_carbrand=@carbrand,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_carchnmodelname=@carchnmodelname,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_carengmodelname=@carengmodelname,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_carmodelcode=@carmodelcode,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_transmissionnum=@transmissionnum,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_cardoorcount=@cardoorcount,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_carmodeladd=@carmodeladd,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_carbodylenth=@carbodylenth,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_carbodywidth=@carbodywidth,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_carbodyhieght=@carbodyhieght,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_carwheelbase=@carwheelbase,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_carwheelbase_f=@carwheelbase_f,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_carwheelbase_b=@carwheelbase_b,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_caremptyweight=@caremptyweight,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_cartotalweight=@cartotalweight,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_passengers=@passengers,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_cylindermeters=@cylindermeters,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_cylinderstroke=@cylinderstroke,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_compressionratio=@compressionratio,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_maxhorsepower=@maxhorsepower,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_maxhorsepowerspeed=@maxhorsepowerspeed,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_torque=@torque,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_torquespeed=@torquespeed,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_turborchargers=@turborchargers,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_oilcapacity=@oilcapacity,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_929598=@929598,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_exhaustsystem=@exhaustsystem,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_PCV=@PCV,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_EEC=@EEC,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_differentialmodel=@differentialmodel,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_differentialgearratio=@differentialgearratio,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_transmissionmodel=@transmissionmodel,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_transmissionbackgearratio=@transmissionbackgearratio,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_highestspeed=@highestspeed,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_suspensionsystem_f=@suspensionsystem_f,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_suspensionsystem_b=@suspensionsystem_b,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_tirespec_std_f=@tirespec_std_f,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_tirespec_std_b=@tirespec_std_b,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_tirespec_cho_f=@tirespec_cho_f,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_tirespec_cho_b=@tirespec_cho_b,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_testweight=@testweight,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_testgearreductionratio=@testgearreductionratio,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_coolingdrivermethod=@coolingdrivermethod,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_tiresnum=@tiresnum,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_exhaustpipenum=@exhaustpipenum,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_tirewidth=@tirewidth,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_inletmode=@inletmode,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_modaccount=@modaccount,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_drivetype=@drivetype,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_remark=@remark,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_producercountry=@producercountry,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_loadingweight=@loadingweight,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_firemethod=@firemethod,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_strokeposdirection=@strokeposdirection,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_PM=@PM,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_CO=@CO,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_HC=@HC,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_1stReductionratio=@1stReductionratio,";
            sql_update_carmodel = sql_update_carmodel  + "cmdg_2ndReductionratio=@2ndReductionratio,";
            sql_update_carmodel = sql_update_carmodel + "cmdg_backgearnums=@backgearnums,";
            sql_update_carmodel = sql_update_carmodel + "cmdg_highlowgear=@highlowgear,";
            sql_update_carmodel = sql_update_carmodel + "cmdg_carbodyweight=@carbodyweight,";
            sql_update_carmodel = sql_update_carmodel + "cmdg_maxhorsepower_hb=@maxhorsepower_hb,";
            sql_update_carmodel = sql_update_carmodel + "cmdg_maxhorsepowerspeed_hb=@maxhorsepowerspeed_hb,";
            sql_update_carmodel = sql_update_carmodel + "cmdg_torque_hb=@torque_hb,";
            sql_update_carmodel = sql_update_carmodel + "cmdg_torquespeed_hb=@torquespeed_hb,";
            sql_update_carmodel = sql_update_carmodel + "cmdg_numofaxes_f=@numofaxes_f,";
            sql_update_carmodel = sql_update_carmodel + "cmdg_numofaxes_b=@numofaxes_b,";
            sql_update_carmodel = sql_update_carmodel + "cmdg_increasepowerstyle=@increasepowerstyle,";
            sql_update_carmodel = sql_update_carmodel + "cmdg_increasepowerratio=@increasepowerratio,";
            sql_update_carmodel = sql_update_carmodel + "cmdg_climbratio=@climbratio,";
            sql_update_carmodel = sql_update_carmodel + "cmdg_suspensionsystem_supplement=@suspensionsystem_supplement,";
            sql_update_carmodel = sql_update_carmodel + "cmdg_forwardgearnums=@forwardgearnums,"; 
            sql_update_carmodel = sql_update_carmodel  + "cmdg_moddate=getdate() where cmdg_applyno=@applyno and cmdg_carcompomodelno=@carcompomodelno and cmdg_carmodelno=@carmodelno";
            SqlCommand cmd_ins = new SqlCommand(sql_update_carmodel);
            
            string[] arrValues = {  "@applyno","@carcompomodelno","@carbodyshape","@carbodyshapedesc","@berepresentativevehicle"
                                    ,"@cartype","@baseenginename","@carbrand","@carchnmodelname","@carengmodelname"
                                    ,"@carmodelcode","@transmissionnum","@cardoorcount","@carmodeladd","@carbodylenth"
                                    ,"@carbodywidth","@carbodyhieght","@carwheelbase","@carwheelbase_f","@carwheelbase_b"
                                    ,"@caremptyweight","@cartotalweight","@passengers","@cylindermeters","@cylinderstroke"
                                    ,"@compressionratio","@maxhorsepower","@maxhorsepowerspeed","@torque","@torquespeed"
                                    ,"@turborchargers","@oilcapacity","@929598","@exhaustsystem","@PCV"
                                    ,"@EEC","@differentialmodel" ,"@differentialgearratio","@transmissionmodel"
                                    ,"@transmissionbackgearratio","@highestspeed","@suspensionsystem_f","@suspensionsystem_b","@tirespec_std_f","@tirespec_std_b","@tirespec_cho_f","@tirespec_cho_b"
                                    ,"@testweight","@testgearreductionratio","@coolingdrivermethod","@tiresnum","@exhaustpipenum"
                                    ,"@tirewidth","@inletmode","@createaccount","@modaccount","@drivetype","@remark","producercountry","@loadingweight","@firemethod","@strokeposdirection","@PM","@CO","@HC","@1stReductionratio","@2ndReductionratio"
                                     ,"@backgearnums","@highlowgear","@carbodyweight","@maxhorsepower_hb","@maxhorsepowerspeed_hb","@torque_hb","@torquespeed_hb","@numofaxes_f","@numofaxes_b","@increasepowerstyle","@increasepowerratio","@climbratio","@suspensionsystem_supplement","@forwardgearnums"                                   
                                    ,"@carmodelno"

                                    };

            for (int i = 0; i < arrData.Length; i++)
            {
                if (arrData[i] != null)
                {
                    cmd_ins.Parameters.AddWithValue(arrValues[i], arrData[i]);
                }
            }            
            Common.Data_noisevalidation.runParaCmd1(cmd_ins);
        }        
    }
    //車型func End
    private bool wsCheckCarCompoModelValid(string ID, string PD)
    {
        string[][] x = new string[7][];
        x[0] = new string[2] { "CCMD1", "a1xposz0" };
        x[1] = new string[2] { "CCMD2", "kY8g2wn5" };
        x[2] = new string[2] { "CCMD3", "qsoik83ver" };
        x[3] = new string[2] { "CCMD4", "nhqlLy5xjz" };
        x[4] = new string[2] { "CCMD5", "pledcng2" };
        x[5] = new string[2] { "CCMD6", "Knx23Oig" };
        x[6] = new string[2] { "CCMD7", "mJegS6Xw0u" };

        for (int i = 0; i < 7; i++)
        {
            if (x[i][0].ToString() == ID && x[i][1].ToString() == PD)
            {
                return true;
            }
        }
        return false;
    }

    /// <summary>
    /// 查詢車型資料
    /// </summary>
    /// <returns></returns>
    /// 
    [WebMethod]

    private string Set_Vehicle_Car_sql(string vechiletype)
    {
        string sql="";
        if (vechiletype == "M")
        {
            //機車沒有驅動方式 CC改
            sql = @"select DISTINCT ct_bd_desc,cmdg_carchnmodelname,cmdg_carengmodelname,cmdg_carmodelcode,cpm_exhuast,cpm_transmissiontypetype,cmdg_transmissionnum,cmdg_cardoorcount,cmdg_carmodeladd,cmdg_carbodyshape
                    ,cmdg_maxhorsepower,cmdg_maxhorsepowerspeed,cmdg_berepresentativevehicle
                    ,cmdg_applyno,cmdg_carcompomodelno,cmdg_carmodelno,cpm_applytype,abd_applystatus,ct_gm_desc,ct_cbsm_desc 'ct_cbs_desc',abd_vechiletype
                    ,dbo.fn_FullCarstyleNameStr(@cmdg_applyno,@carcompomodelno,nv_carmodeldata.cmdg_carmodelno) as 'fullcarstylename'
                    from 
                    nv_carmodeldata
                    join nv_carcompomodel on nv_carcompomodel.cpm_applyno =cmdg_applyno and cmdg_carcompomodelno = cpm_carcompomodelno
                    join nv_applybasedata on abd_applyno = cmdg_applyno  
                    left join nv_codetbl_brand on nv_codetbl_brand.ct_bd_id = cmdg_carbrand --廠牌
                    join nv_codetbl_carbodyshape_m on nv_codetbl_carbodyshape_m.ct_cbsm_id = cmdg_carbodyshape
                    left join nv_codetbl_gearmethod on ct_gm_id=cpm_transmissiontypetype                                     
                    where cmdg_applyno = @cmdg_applyno and cpm_carcompomodelno=@carcompomodelno";
        }
        if (vechiletype == "G" || vechiletype == "D")
        {
            sql = @"select DISTINCT ct_bd_desc,cmdg_carchnmodelname,cmdg_carengmodelname,cmdg_carmodelcode,cpm_exhuast,cpm_transmissiontypetype,cmdg_transmissionnum,cmdg_cardoorcount,cmdg_carmodeladd,cmdg_carbodyshape
                    ,cmdg_maxhorsepower,cmdg_maxhorsepowerspeed,cmdg_berepresentativevehicle
                    ,cmdg_applyno,cmdg_carcompomodelno,cmdg_carmodelno,cpm_applytype,abd_applystatus,ct_gm_desc,ct_cbsg_desc 'ct_cbs_desc',abd_vechiletype
                    ,dbo.fn_FullCarstyleNameStr(@cmdg_applyno,@carcompomodelno,nv_carmodeldata.cmdg_carmodelno) as 'fullcarstylename'
                    from 
                    nv_carmodeldata
                    join nv_carcompomodel on nv_carcompomodel.cpm_applyno =cmdg_applyno and cmdg_carcompomodelno = cpm_carcompomodelno
                    join nv_applybasedata on abd_applyno = cmdg_applyno  
                    left join nv_codetbl_brand on nv_codetbl_brand.ct_bd_id = cmdg_carbrand --廠牌
                    join nv_codetbl_carbodyshape_g on nv_codetbl_carbodyshape_g.ct_cbsg_id = cmdg_carbodyshape
                    left join nv_codetbl_gearmethod on ct_gm_id=cpm_transmissiontypetype                
                    where cmdg_applyno = @cmdg_applyno and cpm_carcompomodelno=@carcompomodelno";
        }
        
        return sql;
    }
    public DataTable Set_Vehicle_Car_Information(string cmdg_applyno, string carcompomodelno,string vechiletype)
    {
        DataSet ds = new DataSet();
        DataTable dt = new DataTable();
        string sql = Set_Vehicle_Car_sql(vechiletype);

        SqlCommand cmd = new SqlCommand(sql);
        cmd.Parameters.AddWithValue("@cmdg_applyno", cmdg_applyno);
        cmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
        ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
        dt = ds.Tables[0];
        return dt;

    }





     /// <summary>
     /// 查詢測定報告
     /// </summary>
     /// <returns></returns>
     /// 
     [WebMethod]
     public DataTable Set_Measurement_Report(string reportno)
     {
         DataSet ds = new DataSet();
         DataTable dt = new DataTable();
         string sql = @"select * from nv_noisetestrpt where ntr_testrptno=@reportno";

         SqlCommand cmd = new SqlCommand(sql);
         cmd.Parameters.AddWithValue("@reportno", reportno);

         ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
         dt = ds.Tables[0];
         return dt;

     }

    /// <summary>
    /// 刪除車型資料
    /// </summary>
    /// <returns></returns>
     [WebMethod]
    public bool Del_Vehicle_Car_Information(string cmdg_applyno, string cmdg_carcompomodelno, string cmdg_carmodelno)
    {
        bool success = false;
        string sql = @"delete  from nv_carmodeldata where cmdg_applyno=@cmdg_applyno and cmdg_carcompomodelno=@cmdg_carcompomodelno and cmdg_carmodelno=@cmdg_carmodelno
                       delete  from nv_gearratio  where gr_applyno=@gr_applyno and gr_carcompomodelno=@gr_carcompomodelno and gr_carmodelno = @gr_carmodelno ";

        SqlCommand cmd = new SqlCommand(sql);
        cmd.Parameters.AddWithValue("@cmdg_applyno", cmdg_applyno);
        cmd.Parameters.AddWithValue("@gr_applyno", cmdg_applyno);

        cmd.Parameters.AddWithValue("@cmdg_carcompomodelno", cmdg_carcompomodelno);
        cmd.Parameters.AddWithValue("@gr_carcompomodelno", cmdg_carcompomodelno);

        cmd.Parameters.AddWithValue("@cmdg_carmodelno", cmdg_carmodelno);
        cmd.Parameters.AddWithValue("@gr_carmodelno", cmdg_carmodelno);

        try
        {

            Common.Data_noisevalidation.runParaCmd1(cmd);
            success = true;
        }
        catch (Exception ex)
        {
            throw ex;

        }
        finally
        {
            cmd.Dispose();
        }




        return success;

    }

    /// <summary>
    /// 編輯車身號碼
    /// </summary>
    /// <param name="cmdg_applyno"></param>
    /// <param name="cmdg_carcompomodelno"></param>
    /// <param name="mode"></param>
    /// <returns></returns>
     public DataTable Edit_chassis_number(string cmdg_applyno, string cmdg_carcompomodelno, string mode,string vechiletype)
     {
         DataSet ds = new DataSet();
         DataTable dt = new DataTable();
         string sql = Set_Vehicle_Car_sql(vechiletype) + "{0}";

         string Strvalue = string.Empty;



         switch (mode.Trim())
         {
             default:
                 Strvalue = string.Empty;
                 break;

             case "2": //沿用
             case "5": //沿用及修改
                 Strvalue = " and cmdg_berepresentativevehicle=@cmdg_berepresentativevehicle";
                 break;

             case "3": //延伸
                 Strvalue = " and cmdg_berepresentativevehicle=@cmdg_berepresentativevehicle and cmdg_beextendmodel=@cmdg_beextendmodel";

                 break;

         }

         sql = string.Format(sql, Strvalue);

         SqlCommand cmd = new SqlCommand(sql);
         cmd.Parameters.AddWithValue("@cmdg_applyno", cmdg_applyno);
         cmd.Parameters.AddWithValue("@carcompomodelno", cmdg_carcompomodelno);
                                       
         cmd.Parameters.AddWithValue("@cmdg_berepresentativevehicle", "Y");
         cmd.Parameters.AddWithValue("@cmdg_beextendmodel", "1");

         ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
         dt = ds.Tables[0];
         return dt;
     }

    /// <summary>
    /// 判斷資料有無重複
    /// </summary>
    /// <param name="abn_applyno"></param>
    /// <param name="abn_carcompomodelno"></param>
    /// <param name="abn_carmodelno"></param>
    /// <param name="abn_bodyno"></param>
    /// <returns></returns>
     public bool chassis_number_Exist(string abn_applyno, string abn_carcompomodelno, string abn_carmodelno, string abn_bodyno)
     {
         bool success = false;
           DataSet ds = new DataSet();
         DataTable dt = new DataTable();
         string sql = @"select * from nv_additionalbodyno 
                        where abn_applyno=@abn_applyno and abn_carcompomodelno=@abn_carcompomodelno and
                        abn_carmodelno=@abn_carmodelno and abn_bodyno=@abn_bodyno";

         SqlCommand cmd = new SqlCommand(sql);
         cmd.Parameters.AddWithValue("@abn_applyno", abn_applyno);
         cmd.Parameters.AddWithValue("@abn_carcompomodelno", abn_carcompomodelno);
         cmd.Parameters.AddWithValue("@abn_carmodelno", abn_carmodelno);
         cmd.Parameters.AddWithValue("@abn_bodyno", abn_bodyno);

         ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
         dt = ds.Tables[0];

         if (dt.Rows.Count<=0)
         {
             success = true;
         }
         return success;
     }

    /// <summary>
    /// 查詢車型編號
    /// </summary>
    /// <param name="applyno"></param>
    /// <param name="carcompomodelno"></param>
    /// <param name="carmodelno"></param>
    /// <param name="ID"></param>
    /// <param name="PWD"></param>
    /// <returns></returns>
//     public string Get_Model_number_data(string applyno, string carcompomodelno)
//     {
//         DataSet ds_carmodel_data = new DataSet();
        
//             string sql = @"select top 1 cmdg_carmodelno from nv_carmodeldata where cmdg_applyno=@cmdg_applyno and cmdg_carcompomodelno=@cmdg_carcompomodelno
//                            order by cmdg_createdate desc";

//             SqlCommand cmd = new SqlCommand(sql);
//             cmd.Parameters.AddWithValue("@cmdg_applyno", applyno);
//             cmd.Parameters.AddWithValue("@cmdg_carcompomodelno", carcompomodelno);

//             ds_carmodel_data = Common.Data_noisevalidation.runParaCmdDS(cmd);
//             return ds_carmodel_data.Tables[0].Rows[0]["cmdg_carmodelno"].ToString();
         
       
//     }

     /// <summary>
     /// 新增車身號碼資料
     /// </summary>
     /// <param name="cmdg_applyno"></param>
     /// <param name="cmdg_carcompomodelno"></param>
     /// <param name="mode"></param>
     /// <returns></returns>
     public bool insert_chassis_number(string applyno, string carcompomodelno, string carmodelno, string bodyno, string engineno, string modaccount)
     {
         bool success = false;
         DataSet ds = new DataSet();
         DataTable dt = new DataTable();
         string sql = @"
                        SET XACT_ABORT ON
                        begin transaction
                           
                            delete from nv_additionalbodyno where abn_applyno=@applyno and abn_carcompomodelno=@carcompomodelno and abn_carmodelno=@carmodelno

                            insert nv_additionalbodyno(abn_applyno,abn_carcompomodelno,abn_carmodelno,abn_bodyno,abn_engineno,abn_modaccount,abn_moddate)
                            values(@applyno,@carcompomodelno,@carmodelno,@bodyno,@engineno,@modaccount,getdate())

                        if (@@ERROR<>0)
                        rollback tran;
                        else
                        commit tran;
                    ";
         SqlCommand cmd = new SqlCommand(sql);
         cmd.Parameters.AddWithValue("@applyno", applyno);
         cmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
         cmd.Parameters.AddWithValue("@carmodelno", carmodelno);
         cmd.Parameters.AddWithValue("@bodyno", bodyno);
         cmd.Parameters.AddWithValue("@engineno", engineno);
         cmd.Parameters.AddWithValue("@modaccount", modaccount);
         try
         {

             Common.Data_noisevalidation.runParaCmd1(cmd);
             success = true;
         }
         catch (Exception ex)
         {
             throw ex;

         }
         finally
         {
             cmd.Dispose();
         }
         return success;
     }
     /// <summary>
     /// 更新車身號碼資料
     /// </summary>
     /// <param name="cmdg_applyno"></param>
     /// <param name="cmdg_carcompomodelno"></param>
     /// <param name="mode"></param>
     /// <returns></returns>
     public bool update_chassis_number(string applyno, string carcompomodelno, string carmodelno, string bodyno, string engineno, string modaccount)
     {
         bool success = false;
         DataSet ds = new DataSet();
         DataTable dt = new DataTable();
         string sql = @"update nv_additionalbodyno
                        set abn_engineno=@engineno,abn_modaccount=@modaccount,abn_moddate=getdate() 
                        where abn_applyno=@applyno and abn_carcompomodelno=@carcompomodelno and abn_carmodelno=@carmodelno and abn_bodyno=@bodyno";
         SqlCommand cmd = new SqlCommand(sql);
         cmd.Parameters.AddWithValue("@applyno", applyno);
         cmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
         cmd.Parameters.AddWithValue("@carmodelno", carmodelno);
         cmd.Parameters.AddWithValue("@bodyno", bodyno);
         cmd.Parameters.AddWithValue("@engineno", engineno);
         cmd.Parameters.AddWithValue("@modaccount", modaccount);
         try
         {

             Common.Data_noisevalidation.runParaCmd1(cmd);
             success = true;
         }
         catch (Exception ex)
         {
             throw ex;

         }
         finally
         {
             cmd.Dispose();
         }
         return success;
     }

     public bool Isberepresentativevehicle(string applyno, string carcompomodelno)
     {
         SqlCommand sqlCmd = new SqlCommand(@"

            if exists(select cmdg_applyno,cmdg_carcompomodelno,cmdg_berepresentativevehicle from nv_carmodeldata where cmdg_applyno=@applyno and cmdg_carcompomodelno=@carcompomodelno and cmdg_berepresentativevehicle='Y')
            begin
	            select 1
	            return
            end
            select 0
            ");
         sqlCmd.Parameters.AddWithValue("@applyno", applyno);
         sqlCmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
         object obj = Common.Data_noisevalidation.runScalar(sqlCmd);
         return obj.ToString() == "1" ? true : false; //1有;0沒有
     }

     public void update_carmodelnoisetestrpt(string applyno, string carcompomodelno, string carmodelno, string ID, string PWD)
     {
         if (wsCheckCarCompoModelValid(ID, PWD))
         {
             SqlCommand sqlCmd = new SqlCommand(@"

            --有變更代表車 才修改測試報告
            declare @old_carmodelno nvarchar(36)
            if exists(select ntr_carmodelno from nv_noisetestrpt where ntr_carcompomodelno=@carcompomodelno )
            begin
	            select @old_carmodelno=ntr_carmodelno from nv_noisetestrpt where ntr_carcompomodelno=@carcompomodelno 
                    if (@old_carmodelno <> @new_carmodelno )
                    begin
	                       SET XACT_ABORT ON
                            begin transaction
                                Update nv_noisetestrpt set ntr_carmodelno=@new_carmodelno where ntr_carcompomodelno=@carcompomodelno and ntr_carmodelno=@old_carmodelno
                                Update nv_noisetestrpt_remark set ntrr_carmodelno=@new_carmodelno where ntrr_carcompomodelno=@carcompomodelno and ntrr_carmodelno=@old_carmodelno
                                Update nv_testrptspeedupinfo set trsui_carmodelno=@new_carmodelno where trsui_carcompomodelno=@carcompomodelno and trsui_carmodelno=@old_carmodelno                               

                            if (@@ERROR<>0)
                            rollback tran;
                            else
                            commit tran;
                    end
            end
       
            ");
             sqlCmd.Parameters.AddWithValue("@applyno", applyno);
             sqlCmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
             sqlCmd.Parameters.AddWithValue("@new_carmodelno", carmodelno);
            
             Common.Data_noisevalidation.runParaCmd1(sqlCmd);
         }
     }
    
}
