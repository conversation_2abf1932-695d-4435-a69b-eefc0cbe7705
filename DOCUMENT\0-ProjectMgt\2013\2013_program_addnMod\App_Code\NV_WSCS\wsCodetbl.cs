﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Services;
using System.Data;
using System.Data.SqlClient;

/// <summary>
/// Summary description for wsCodetbl
/// </summary>
[WebService(Namespace = "http://tempuri.org/")]
[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
// To allow this Web Service to be called from script, using ASP.NET AJAX, uncomment the following line. 
// [System.Web.Script.Services.ScriptService]
public class wsCodetbl : System.Web.Services.WebService
{
    public wsCodetbl () {

        //Uncomment the following line if using designed components 
        //InitializeComponent(); 
    }    

    /// <summary>
    /// 取得帳號作業聯絡人清單
    /// </summary>
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    /// <returns></returns>
    [WebMethod]
    public DataTable getAccountContactList(string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd_get = new SqlCommand(@"SELECT ct_cat_id
                                ,ct_cat_desc,ct_cat_beshow,ct_cat_order
                                FROM nv_codetbl_contactaccount
                                where ct_cat_beshow='1'");
            return Common.Data_noisevalidation.runParaCmdDS(cmd_get).Tables[0];
        } return dt;
    }

    [WebMethod]
    public DataSet getAccountStatus(string ID, string PD)
    {
        DataSet ds_accountstatus = new DataSet();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd_status = new SqlCommand(@"SELECT ct_as_id,ct_as_desc,ct_as_beshow
                                    FROM nv_codetbl_accountstatus where ct_as_beshow='1'");
            return Common.Data_noisevalidation.runParaCmdDS(cmd_status);
        } return ds_accountstatus;
    }

    [WebMethod]
    public DataSet getCityCode(string ID, string PD)
    {
        DataSet ds_city = new DataSet();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd_city = new SqlCommand(@"select * from nv_codetbl_city");
            return Common.Data_noisevalidation.runParaCmdDS(cmd_city);
        }
        else return ds_city;
    }

    [WebMethod]
    public DataSet getZipCode(string cityid, string ID, string PD)
    {
        DataSet ds_zip = new DataSet();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd_zip = new SqlCommand(@"select * from nv_codetbl_zipcode where ct_zipcode_city=@cityid");
            cmd_zip.Parameters.AddWithValue("@cityid", cityid);
            return Common.Data_noisevalidation.runParaCmdDS(cmd_zip);
        }
        else return ds_zip;
    }

    [WebMethod]
    public string getCityName(string cityid, string ID, string PD)
    {
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd_city = new SqlCommand(@"select * from nv_codetbl_city where ct_city_id=@cityid");
            cmd_city.Parameters.AddWithValue("@cityid", cityid);
            DataView dv_city = Common.Data_noisevalidation.runParaCmd(cmd_city);
            if (dv_city.Count == 0)
                return "";
            else
                return dv_city[0]["ct_city_desc"].ToString();
        }
        else return "";
    }

    [WebMethod]
    public string getZipName(string cityid, string zipid, string ID, string PD)
    {
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd_zip = new SqlCommand(@"select * from nv_codetbl_zipcode where ct_zipcode_city=@cityid and ct_zipcode_id=@zipid");
            cmd_zip.Parameters.AddWithValue("@cityid", cityid);
            cmd_zip.Parameters.AddWithValue("@zipid", zipid);
            DataView dv_zip = Common.Data_noisevalidation.runParaCmd(cmd_zip);
            if (dv_zip.Count == 0)
                return "";
            else
                return dv_zip[0]["ct_zipcode_desc"].ToString();
        }
        else return "";
    }
    //增壓器
    public DataSet getHaveTurboMode(string ID, string PD)
    {
        DataSet dsHaveTurboMode = new DataSet();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd_HaveTurboMode = new SqlCommand(@"select ct_htm_id,ct_htm_desc from nv_codetbl_haveturbomode where ct_htm_beshow=1 order by ct_htm_order");
            return Common.Data_noisevalidation.runParaCmdDS(cmd_HaveTurboMode);
        }
        else return dsHaveTurboMode;
    }
    //---------------------Peter.H-----v-----
    //交通工具種類
    [WebMethod]
    public DataSet getCarType(string ID, string PD)
    {
        DataSet dsCarType = new DataSet();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd_CarType = new SqlCommand(@"select ct_ct_id,ct_ct_desc from nv_codetbl_cartype where ct_ct_beshow=1 order by ct_ct_order");
            return Common.Data_noisevalidation.runParaCmdDS(cmd_CarType);
        }
        else return dsCarType;
    }

    //適用期別
    [WebMethod]
    public DataSet getStandardDate(string ID, string PD)
    {
        DataSet dsStandardDate = new DataSet();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd_StandardDate = new SqlCommand(@"select ct_sd_id,ct_sd_desc  from nv_codetbl_standarddate where ct_sd_beshow=1");
            return Common.Data_noisevalidation.runParaCmdDS(cmd_StandardDate);
        }
        else return dsStandardDate;
    }

    //兩證合一申請方式
    [WebMethod]
    public DataSet getTwoCertType(string ID, string PD)
    {
        DataSet dsTwoCertType = new DataSet();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd_TwoCertType = new SqlCommand(@"select ct_twct_id,ct_twct_desc from dbo.nv_codetbl_twocerttype where ct_twct_beshow=1 order by ct_twct_order");
            return Common.Data_noisevalidation.runParaCmdDS(cmd_TwoCertType);
        }
        else return dsTwoCertType;
    }

    //動力來源
    [WebMethod]
    public DataSet getPowerFrom(string ID, string PD)
    {
        DataSet dsPowerFrom = new DataSet();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd_PowerFrom = new SqlCommand(@"select ct_pf_id,ct_pf_desc from nv_codetbl_powerfrom where ct_pf_beshow=1 order by ct_pf_order");
            return Common.Data_noisevalidation.runParaCmdDS(cmd_PowerFrom);
        }
        else return dsPowerFrom;
    }

    //申請狀態
    [WebMethod]
    public DataSet getApplyStatus(string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select ct_aps_id,ct_aps_desc from  nv_codetbl_applystatus where ct_aps_beshow=1 order by ct_aps_order");
            ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
        }
        return ds;
    }

    //申請狀態by-代碼編號
    [WebMethod]
    public string getApplyStatusById(string strCtId, string ID, string PD)
    {
        string strDesc = "";
        DataSet ds = new DataSet();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select ct_aps_id,ct_aps_desc from  nv_codetbl_applystatus where ct_aps_beshow=1 and ct_aps_id=@ct_id order by ct_aps_order");
            cmd.Parameters.AddWithValue("@ct_id", strCtId);
            ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
            if (ds.Tables[0].Rows.Count > 0)
                strDesc = ds.Tables[0].Rows[0]["ct_aps_desc"].ToString();
        }
        return strDesc;
    }

    //車型組-申請型式
    [WebMethod]
    public DataSet getCarCompoModelApplyType(string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select ct_at_id,ct_at_desc from nv_codetbl_applytype where ct_at_beshow=1 order by ct_at_order");
            ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
        }
        return ds;
    }

    //車型組-傳動方式
    [WebMethod]
    public DataSet getCarDriveType(string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select ct_dt_id,ct_dt_desc from nv_codetbl_drivetype where ct_dt_beshow=1 order by ct_dt_order");
            ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
        }
        return ds;
    }
    //車型組-驅動軸數
    [WebMethod]
    public DataSet getNumberOfAxes(string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select ct_na_id,ct_na_desc from nv_codetbl_numofaxes where ct_na_beshow=1 order by ct_na_order");
            ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
        }
        return ds;
    }

    //車型組-使用燃料
    [WebMethod]
    public DataSet getFuelType(string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select ct_ft_id,ct_ft_desc from nv_codetbl_fueltype where ct_ft_beshow=1 order by ct_ft_order");
            ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
        }
        return ds;
    }

    //車型組-供油方式
    [WebMethod]
    public DataSet getProvideOilMethod(string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select ct_le_id,ct_le_desc from nv_codetbl_lubricationtype where ct_le_beshow=1 order by ct_le_order");
            ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
        }
        return ds;
    }   

    //車型組-冷卻系統型式
    [WebMethod]
    public DataSet getCoolSystem(string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select ct_cs_id,ct_cs_desc from  nv_codetbl_coolsystem where ct_cs_beshow=1 order by ct_cs_order");
            ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
        }
        return ds;
    }    

    //車型組-國產/進口
    [WebMethod]
    public DataSet getIsImport(string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select ct_im_id,ct_im_desc from  nv_codetbl_isimport where ct_im_beshow=1 order by ct_im_order");
            ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
        }
        return ds;
    }

    //車型組-以歐盟合格證申請
    [WebMethod]
    public DataSet getUseEurocert(string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select ct_eu_id,ct_eu_desc from  nv_codetbl_useeurocert where ct_eu_beshow=1 order by ct_eu_order");
            ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
        }
        return ds;
    }

    //車型組-排檔型式
    [WebMethod]
    public DataSet getGearMethod(string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select ct_gm_id,ct_gm_desc from nv_codetbl_gearmethod where ct_gm_beshow=1 order by ct_gm_order");
            ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
        }
        return ds;
    }

    //車型組-引擎安裝位置
    [WebMethod]
    public DataSet getEnginePos(string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select ct_ep_id_g,ct_ep_desc_g from nv_codetbl_enginepos where ct_ep_beshow_g=1 order by ct_ep_order_g");
            ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
        }
        return ds;
    }

    //車型組-檢測廠
    [WebMethod]
    public DataSet getTestFactory(string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select ct_tf_id,ct_tf_desc from nv_codetbl_testfactory where ct_tf_beshow=1 order by ct_tf_order");
            ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
        }
        return ds;
    }

    //車型組-進口國
    [WebMethod]
    public DataSet getCountry(string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select ct_cy_id,ct_cy_desc from nv_codetbl_country where ct_cy_beshow=1 order by ct_cy_order");
            ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
        }
        return ds;
    }

    //製造地區
    [WebMethod]
    public DataSet getProdCountry(string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select ct_pcy_id,ct_pcy_desc from nv_codetbl_prodcountry where ct_pcy_beshow=1 order by ct_pcy_order");
            ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
        }
        return ds;
    }

    //車型組-燃燒循環
    [WebMethod]
    public DataSet getEngineBurnMethod(string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select ct_ebm_id,ct_ebm_desc from nv_codetbl_engineburnmethod where ct_ebm_beshow =1 order by ct_ebm_order");
            ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
        }
        return ds;
    }

    //車型組-汽缸排列(機車)
    [WebMethod]
    public DataSet getCylinderPos_M(string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select ct_cp_id_m,ct_cp_desc_m from nv_codetbl_cylinderpos_m where ct_cp_beshow_m=1 order by ct_cp_order_m");
            ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
        }
        return ds;
    }
    //加速選定條件
    [WebMethod]
    public DataSet getSpeedupCondi(string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select ct_supc_id,ct_supc_desc from nv_codetbl_speedupcondi where ct_supc_beshow=1 order by ct_supc_order");
            ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
        }
        return ds;
    }

    //原地設定條件
    [WebMethod]
    public DataSet getStayrpmCondi(string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select ct_srpmc_id,ct_srpmc_desc from nv_codetbl_stayrpmcondi where ct_srpmc_beshow=1 order by ct_srpmc_order");
            ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
        }
        return ds;
    }
    //---------------------Peter.H-----^-----

    //---------------------Olivia------v-----
    // 取得車型分類-查詢使用
    [WebMethod]
    public DataTable getTransportTypeSearch(string ID, string PD, string cartype)
    {
        DataTable dt = new DataTable();
        if (wsCheckCodetbluntValid(ID, PD))
        {

          //  SqlCommand cmd = new SqlCommand(@"select ct_nvc_id,ct_nvc_desc from nv_codetbl_noisevechilecategory where ct_nvc_beshow=1 order by ct_nvc_order");
           // string SQL = " select * from nv_codetbl_transporttype where ct_trans_beshow='1' Query  order by ct_trans_order";
            string SQL = "select ct_nvc_id,ct_nvc_desc from nv_codetbl_noisevechilecategory where ct_nvc_beshow='1' Query order by ct_nvc_order";
            if (cartype == "G")
                SQL = SQL.Replace("Query", " and ct_nvc_id in ('1','2','3','4')");
            else if (cartype == "D")
                SQL = SQL.Replace("Query", " and ct_nvc_id in ('1','2','3','4')");
            else if (cartype == "M")
                SQL = SQL.Replace("Query", " and ct_nvc_id in ('5','6','7','8')");
            else
                SQL = SQL.Replace("Query", "");

            SqlCommand cmd = new SqlCommand(SQL);
            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        } return dt;
    }
   
    /// <summary>
    /// 取得代碼檔的SORT_NAME
    /// </summary>
    /// <param name="codetype"></param>
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    /// <returns></returns>
    private string gettypeSortName(string codetype, string ID, string PD)
    {
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd_getName = new SqlCommand("select * from nv_codeinfo where ci_beshow='1' and ci_codetype=@codetype");
            cmd_getName.Parameters.AddWithValue("@codetype", codetype);
            DataTable dt_Name = Common.Data_noisevalidation.runParaCmdDS(cmd_getName).Tables[0];
            return dt_Name.Rows[0]["ci_typesort"].ToString();
        } return "";
    }
    /// <summary>
    /// 取得代碼檔的Filed-ID
    /// </summary>
    /// <param name="codetype"></param>
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    /// <returns></returns>
    private string gettypeIDName(string codetype, string ID, string PD)
    {
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd_getName = new SqlCommand("select * from nv_codeinfo where ci_beshow='1' and ci_codetype=@codetype");
            cmd_getName.Parameters.AddWithValue("@codetype", codetype);
            DataTable dt_Name = Common.Data_noisevalidation.runParaCmdDS(cmd_getName).Tables[0];
            return dt_Name.Rows[0]["ci_typeid"].ToString();
        } return "";
    }
    /// <summary>
    /// 取得代碼檔TABLENAME
    /// </summary>
    /// <param name="codetype"></param>
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    /// <returns></returns>
    private string gettypeTableName(string codetype, string ID, string PD)
    {
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd_getName = new SqlCommand("select * from nv_codeinfo where ci_beshow='1' and ci_codetype=@codetype");
            cmd_getName.Parameters.AddWithValue("@codetype", codetype);
            DataTable dt_Name = Common.Data_noisevalidation.runParaCmdDS(cmd_getName).Tables[0];
            return dt_Name.Rows[0]["ci_typetable"].ToString();
        } return "";
    }
    /// <summary>
    /// 取得所有代碼檔所有細項清單
    /// </summary>
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    /// <returns></returns>
    [WebMethod]
    public DataTable getAllCodetbl(string ID, string PD)
    {
        DataTable dtAll = new DataTable();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmdAll = new SqlCommand(@"select * from vw_codetbllist");
            return Common.Data_noisevalidation.runParaCmdDS(cmdAll).Tables[0];
        } return dtAll;
    }
    /// <summary>
    /// 取得所有顯示的代碼檔清單
    /// </summary>
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    /// <returns></returns>
    [WebMethod]
    public DataTable getCodetblList(string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd_getName = new SqlCommand("select * from nv_codeinfo where ci_beshow='1'");
            return Common.Data_noisevalidation.runParaCmdDS(cmd_getName).Tables[0];
        } return dt;
    }
    /// <summary>
    /// 新增代碼檔明細資料
    /// </summary>
    /// <param name="codetype"></param>
    /// <param name="typeID"></param>
    /// <param name="typeDesc"></param>
    /// <param name="typeSort"></param>
    /// <param name="typebeshow"></param>
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    [WebMethod]
    public void InsCodeTblValue(string codetype, string typeID, string typeDesc, string typeSort, string typebeshow, string ID, string PD)
    {
        if (wsCheckCodetbluntValid(ID, PD))
        {
            string tableName = gettypeTableName(codetype, ID, PD);
            string SortName = gettypeSortName(codetype, ID, PD);
            string SQL = string.Format(@"
                                        update {0} set {1}={1}+1 where {1}>={2};
                                        insert into {0} select '{3}','{4}','{5}','{2}' "
                , tableName, SortName, typeSort, typeID, typeDesc, typebeshow);
            SqlCommand cmd_upd = new SqlCommand(SQL);
            Common.Data_noisevalidation.runParaCmd1(cmd_upd);
        }
    }
    //新增代碼檔明細資料-郵遞區號
    [WebMethod]
    public void InsZipCodeTblValue(string codetype, string typeID, string typeDesc, string typeCity, string ID, string PD)
    {
        if (wsCheckCodetbluntValid(ID, PD))
        {
            string tableName = gettypeTableName(codetype, ID, PD);          
            string SQL = string.Format(@"                                       
                                        insert into {0} select '{1}','{2}','{3}'"
                , tableName, typeID, typeDesc,typeCity);
            SqlCommand cmd_upd = new SqlCommand(SQL);
            Common.Data_noisevalidation.runParaCmd1(cmd_upd);
        }
    }
    //新增代碼檔明細資料-公司地址縣市別
    [WebMethod]
    public void InsCityCodeTblValue(string codetype, string typeID, string typeDesc, string ID, string PD)
    {
        if (wsCheckCodetbluntValid(ID, PD))
        {
            string tableName = gettypeTableName(codetype, ID, PD);           
            string SQL = string.Format(@"                                       
                                        insert into {0} select '{1}','{2}'"
                , tableName, typeID, typeDesc);
            SqlCommand cmd_upd = new SqlCommand(SQL);
            Common.Data_noisevalidation.runParaCmd1(cmd_upd);
        }
    }
    /// <summary>
    /// 取得某代碼檔清單
    /// </summary>
    /// <param name="codetype"></param>
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    /// <returns></returns>
    [WebMethod]
    public DataTable getSingleCodeList(string codetype, string ID, string PD)
    {
        DataTable dt_list = new DataTable();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            string selQuery = "ctType='" + codetype + "'";
            DataRow[] dRow = getAllCodetbl(ID, PD).Select(selQuery, "ctOrder");
            dt_list = getAllCodetbl(ID, PD).Clone();
            foreach (DataRow dr in dRow)
            {
                dt_list.ImportRow(dr);
            }
            return dt_list;
        } return dt_list;
    }
    /// <summary>
    /// 取得某代碼長度
    /// </summary>
    /// <param name="codetype"></param>
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    /// <returns></returns>
    [WebMethod]
    public DataTable getCodeIdLen(string codetype, string ID, string PD)
    {
        DataTable dt_list = new DataTable();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            string tableName = gettypeTableName(codetype, ID, PD);
            string tableID = gettypeIDName(codetype, ID, PD);
            string SQL = string.Format(@"                                       
                                        SELECT COL_LENGTH('{0}','{1}')AS 'IDLen'"
                                         , tableName, tableID);
            SqlCommand cmd_upd = new SqlCommand(SQL);
            return Common.Data_noisevalidation.runParaCmdDS(cmd_upd).Tables[0]; ;                      
        } return dt_list;
    }
    /// <summary>
    /// 修改代碼檔明細資料
    /// </summary>
    /// <param name="codetype"></param>
    /// <param name="typeID"></param>
    /// <param name="typeDesc"></param>
    /// <param name="typeSort"></param>
    /// <param name="typebeshow"></param>
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    [WebMethod]
    public void UpdateCodeTblValue(string codetype, string typeID, string typeDesc, string typeSort, string typebeshow, string ID, string PD)
    {
        if (wsCheckCodetbluntValid(ID, PD))
        {
            string tableName = gettypeTableName(codetype, ID, PD);
            string tableID = gettypeIDName(codetype, ID, PD);
            string SortName = gettypeSortName(codetype, ID, PD);
            string SQL = string.Format(@"
                                        declare @oldsort smallint
                                        select @oldsort={0} from {1} where {2}='{3}';
                                        if(exists(select * from {1} where {2}='{3}'))
                                        begin
                                            if @oldsort > {4}  --原本的值>新的值
                                            begin
                                                update {1} set {0}={0}+1 
	                                            where {0}<@oldsort and {0}>={4};
                                            end
                                            else --原本的值<新的值
                                            if @oldsort < {4}  begin 
                                                update {1} set {0}={0}-1 
	                                            where {0}>@oldsort and {0}<={4};	    
                                            end
                                            delete from {1} where {2}='{3}';
                                            insert into {1} select '{3}','{5}','{6}','{4}';
                                        end
                                        else begin
                                            update {1} set {0}={0}+1 where {0}>{4};
                                            delete from {1} where {2}='{3}';
                                            insert into {1} select '{3}','{5}','{6}','{4}';
                                        end
                                         "
                , SortName, tableName, tableID, typeID, typeSort, typeDesc, typebeshow);
            SqlCommand cmd_upd = new SqlCommand(SQL);
            Common.Data_noisevalidation.runParaCmd1(cmd_upd);
        }
    }
    //修改代碼檔明細資料-郵遞區號
    [WebMethod]
    public void UpdateZipCodeTblValue(string codetype, string typeID, string typeDesc, string typeCity, string ID, string PD)
    {
        if (wsCheckCodetbluntValid(ID, PD))
        {
            string tableName = gettypeTableName(codetype, ID, PD);
            string tableID = gettypeIDName(codetype, ID, PD);
            string SQL = string.Format(@"update {0} set ct_zipcode_desc='{3}',ct_zipcode_city='{4}' where {1}='{2}';"
                , tableName, tableID, typeID, typeDesc, typeCity);
            SqlCommand cmd_upd = new SqlCommand(SQL);
            Common.Data_noisevalidation.runParaCmd1(cmd_upd);
        }
    }
    //修改代碼檔明細資料-公司地址縣市別
    [WebMethod]
    public void UpdateCityCodeTblValue(string codetype, string typeID, string typeDesc, string ID, string PD)
    {
        if (wsCheckCodetbluntValid(ID, PD))
        {
            string tableName = gettypeTableName(codetype, ID, PD);
            string tableID = gettypeIDName(codetype, ID, PD);           
            string SQL = string.Format(@"update {0} set ct_city_desc='{3}' where {1}='{2}';"
                , tableName, tableID, typeID, typeDesc);
            SqlCommand cmd_upd = new SqlCommand(SQL);
            Common.Data_noisevalidation.runParaCmd1(cmd_upd);
        }
    }
    public DataTable CheckIdExist(string codetype,string typeID, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsCheckCodetbluntValid(ID, PD))
        {                      
            string tableName = gettypeTableName(codetype, ID, PD);
            string tableID = gettypeIDName(codetype, ID, PD);
            string SQL = string.Format(@"SELECT * FROM {0} WHERE {1}='{2}';"
                , tableName, tableID, typeID);
            SqlCommand cmd_upd = new SqlCommand(SQL);

            return Common.Data_noisevalidation.runParaCmdDS(cmd_upd).Tables[0];
        }
        return dt;
    }
    //車型-車種分類
    [WebMethod]
    public DataSet get_noisevechilecategory(string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select ct_nvc_id,ct_nvc_desc from nv_codetbl_noisevechilecategory where ct_nvc_beshow=1 order by ct_nvc_order");
            ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
        }
        return ds;
    }

    //車型-汽車車身式樣
    [WebMethod]
    public DataSet get_CarBodyShape_g(string ID, string PD,string mode)
    {
        DataSet ds = new DataSet();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            string sql;
            if (mode.ToString().ToLower() == "g" || mode.ToString().ToLower() == "d")
            {
                sql = "select ct_cbsg_id as value,ct_cbsg_desc as text from nv_codetbl_carbodyshape_g where ct_cbsg_beshow=1 order by ct_cbsg_order";
            }
            else
            {
                sql = "select ct_cbsm_id as value,ct_cbsm_desc as text from nv_codetbl_carbodyshape_m where ct_cbsm_beshow=1 order by ct_cbsm_order";
            }

            SqlCommand cmd = new SqlCommand(sql);
            ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
        }
        return ds;
    }

    //驅動方式
    [WebMethod]
    public DataSet get_Driven_approach(string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            string sql;

            sql = @"select ct_dt_id as value,ct_dt_desc as text from nv_codetbl_drivetype";
           

            SqlCommand cmd = new SqlCommand(sql);
            ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
        }
        return ds;
    }

    //車型-機車車身式樣
    [WebMethod]
    public DataSet get_CarBodyShape_m(string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select ct_cbsm_id,ct_cbsm_desc from nv_codetbl_carbodyshape_m where ct_cbsm_beshow=1 order by ct_cbsm_order");
            ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
        }
        return ds;
    }

    //車型-門數
    [WebMethod]
    public DataSet get_CarDoorCount(string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select ct_vr_id,ct_vr_desc from nv_codetbl_vehicledoortype where ct_vr_beshow=1 order by ct_vr_order");
            ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
        }
        return ds;
    }

    //車型-廠牌
    [WebMethod]
    public DataSet get_carbrand(string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select ct_bd_id ,ct_bd_desc from nv_codetbl_brand where ct_bd_beshow=1 order by ct_bd_order");
            ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
        }
        return ds;
    }

    //車型-進氣方式
    [WebMethod]
    public DataSet get_InletMode(string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select ct_im_id,ct_im_desc from nv_codetbl_inletmode where ct_im_beshow=1 order by ct_im_order");
            ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
        }
        return ds;
    }
    //車型-高低檔
    [WebMethod]
    public DataSet get_HighLowGear(string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select ct_hlg_id,ct_hlg_desc from nv_codetbl_highlowgear where ct_hlg_beshow=1 order by ct_hlg_order");
            ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
        }
        return ds;
    }
    //代碼維護-郵遞區號
    [WebMethod]
    public DataTable get_Zipcode(string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"SELECT ct_zipcode_id AS ctID,ct_zipcode_desc AS ctDesc,ct_zipcode_city AS ctOrder FROM nv_codetbl_zipcode");           
            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;
    }
    //代碼維護-公司地址縣市別
    [WebMethod]
    public DataTable get_Citycode(string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"SELECT ct_city_id AS ctID,ct_city_desc AS ctDesc FROM nv_codetbl_city");
            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;
    }
    //初審認領人
    [WebMethod]
    public DataSet getChargeAccount(string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsCheckCodetbluntValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select dbo.fn_CastAccountCn(abg_account)'ChargeAccount',abg_account from dbo.nv_accountbelonggroup
                                              join nv_accountinfo on ai_account=abg_account
                                              where abg_grpid='02' ");
            ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
        }
        return ds;
    }

    //---------------------Olivia------^-----   
    private bool wsCheckCodetbluntValid(string ID, string PD)
    {
        string[][] x = new string[7][];
        x[0] = new string[2] { "CDWS1", "ijsOU867" };
        x[1] = new string[2] { "CDWS2", "dji67GGT" };
        x[2] = new string[2] { "CDWS3", "dsdf2dvB" };
        x[3] = new string[2] { "CDWS4", "KJIUH6GTR" };
        x[4] = new string[2] { "CDWS5", "nbu70BfR" };
        x[5] = new string[2] { "CDWS6", "hosIOiD" };
        x[6] = new string[2] { "CDWS7", "jcis654JK" };

        for (int i = 0; i < 7; i++)
        {
            if (x[i][0].ToString() == ID && x[i][1].ToString() == PD)
            {
                return true;
            }
        }
        return false;
    }
    
}
