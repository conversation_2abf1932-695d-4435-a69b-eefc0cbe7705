<?xml version="1.0" encoding="utf-8"?>
<Report xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner" xmlns="http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition">
  <DataSources>
    <DataSource Name="noisedefenseplanDataSet">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>69ec5743-d7a5-442d-ac2a-b818e7231384</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="DataSet_noisedefenseplan">
      <Fields>
        <Field Name="ndp_applyno">
          <DataField>ndp_applyno</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ndp_carcompomodelno">
          <DataField>ndp_carcompomodelno</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ndp_defenseplanid">
          <DataField>ndp_defenseplanid</DataField>
          <rd:TypeName>System.Int64</rd:TypeName>
        </Field>
        <Field Name="ndp_position">
          <DataField>ndp_position</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ndp_defenseeqpname">
          <DataField>ndp_defenseeqpname</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ndp_eqpmaterial">
          <DataField>ndp_eqpmaterial</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ndp_eqpheight">
          <DataField>ndp_eqpheight</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ndp_equremark">
          <DataField>ndp_equremark</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ndp_createaccount">
          <DataField>ndp_createaccount</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ndp_createdate">
          <DataField>ndp_createdate</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="ndp_modaccount">
          <DataField>ndp_modaccount</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ndp_moddate">
          <DataField>ndp_moddate</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>noisedefenseplanDataSet</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <rd:DataSetInfo>
        <rd:DataSetName>noisedefenseplanDataSet</rd:DataSetName>
        <rd:SchemaPath>D:\Olivia\noisevalidation\PROGRAM\webRoot\App_Code\DataSet\noisedefenseplanDataSet1.xsd</rd:SchemaPath>
        <rd:TableName>nv_noisedefenseplan</rd:TableName>
        <rd:TableAdapterFillMethod>Fill</rd:TableAdapterFillMethod>
        <rd:TableAdapterGetDataMethod>GetData</rd:TableAdapterGetDataMethod>
        <rd:TableAdapterName>nv_noisedefenseplanTableAdapter</rd:TableAdapterName>
      </rd:DataSetInfo>
    </DataSet>
    <DataSet Name="DataSet_applystatus">
      <Fields>
        <Field Name="abd_applyno">
          <DataField>abd_applyno</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="cpm_carcompomodelno">
          <DataField>cpm_carcompomodelno</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ct_aps_desc">
          <DataField>ct_aps_desc</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>noisedefenseplanDataSet</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <rd:DataSetInfo>
        <rd:DataSetName>noisedefenseplanDataSet</rd:DataSetName>
        <rd:SchemaPath>D:\Olivia\noisevalidation\PROGRAM\webRoot\App_Code\DataSet\noisedefenseplanDataSet1.xsd</rd:SchemaPath>
        <rd:TableName>pr_get_applystatus</rd:TableName>
        <rd:TableAdapterFillMethod>Fill</rd:TableAdapterFillMethod>
        <rd:TableAdapterGetDataMethod>GetData</rd:TableAdapterGetDataMethod>
        <rd:TableAdapterName>pr_get_applystatusTableAdapter</rd:TableAdapterName>
      </rd:DataSetInfo>
    </DataSet>
    <DataSet Name="DataSet_carmodelnames">
      <Fields>
        <Field Name="cmdg_carmodelno">
          <DataField>cmdg_carmodelno</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="cpm_carcompomodelno">
          <DataField>cpm_carcompomodelno</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="cmdg_carchnmodelname">
          <DataField>cmdg_carchnmodelname</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="cmdg_carengmodelname">
          <DataField>cmdg_carengmodelname</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="cmdg_berepresentativevehicle">
          <DataField>cmdg_berepresentativevehicle</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="fullcarstylename">
          <DataField>fullcarstylename</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>noisedefenseplanDataSet</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <rd:DataSetInfo>
        <rd:DataSetName>noisevalidationDataSet</rd:DataSetName>
        <rd:SchemaPath>D:\Olivia\noisevalidation\PROGRAM\webRoot\App_Code\DataSet\noisevalidationDataSet.xsd</rd:SchemaPath>
        <rd:TableName>pr_get_carmodelnames</rd:TableName>
        <rd:TableAdapterFillMethod>Fill</rd:TableAdapterFillMethod>
        <rd:TableAdapterGetDataMethod>GetData</rd:TableAdapterGetDataMethod>
        <rd:TableAdapterName>pr_get_carmodelnamesTableAdapter</rd:TableAdapterName>
      </rd:DataSetInfo>
    </DataSet>
    <DataSet Name="DataSet_get_applybasebarcode">
      <Fields>
        <Field Name="abd_applyno">
          <DataField>abd_applyno</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="abd_databelongcompidno">
          <DataField>abd_databelongcompidno</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="abd_barcodeno">
          <DataField>abd_barcodeno</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="abd_barcodeimg">
          <DataField>abd_barcodeimg</DataField>
          <rd:TypeName>System.Byte[]</rd:TypeName>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>noisedefenseplanDataSet</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <rd:DataSetInfo>
        <rd:DataSetName>representativevehiclenoteDataSet</rd:DataSetName>
        <rd:SchemaPath>D:\Olivia\noisevalidation\PROGRAM\webRoot\App_Code\DataSet\representativevehiclenoteDataSet.xsd</rd:SchemaPath>
        <rd:TableName>nv_applybasedata</rd:TableName>
        <rd:TableAdapterFillMethod>Fill</rd:TableAdapterFillMethod>
        <rd:TableAdapterGetDataMethod>GetData</rd:TableAdapterGetDataMethod>
        <rd:TableAdapterName>nv_applybasedataTableAdapter</rd:TableAdapterName>
      </rd:DataSetInfo>
    </DataSet>
  </DataSets>
  <Body>
    <ReportItems>
      <Tablix Name="Tablix2">
        <TablixBody>
          <TablixColumns>
            <TablixColumn>
              <Width>0.03125in</Width>
            </TablixColumn>
          </TablixColumns>
          <TablixRows>
            <TablixRow>
              <Height>0.26527in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox164">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox164</rd:DefaultName>
                      <Visibility>
                        <Hidden>true</Hidden>
                      </Visibility>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>Black</Color>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Black</Color>
                        </BottomBorder>
                        <LeftBorder>
                          <Style>None</Style>
                        </LeftBorder>
                        <RightBorder>
                          <Color>Black</Color>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.26527in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="cmdg_carchnmodelname">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>cmdg_carchnmodelname</rd:DefaultName>
                      <Visibility>
                        <Hidden>true</Hidden>
                      </Visibility>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Color>Black</Color>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Black</Color>
                        </BottomBorder>
                        <LeftBorder>
                          <Style>None</Style>
                        </LeftBorder>
                        <RightBorder>
                          <Color>Black</Color>
                        </RightBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
          </TablixRows>
        </TablixBody>
        <TablixColumnHierarchy>
          <TablixMembers>
            <TablixMember />
          </TablixMembers>
        </TablixColumnHierarchy>
        <TablixRowHierarchy>
          <TablixMembers>
            <TablixMember>
              <TablixHeader>
                <Size>1.49018in</Size>
                <CellContents>
                  <Textbox Name="Textbox162">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>申請編號</Value>
                            <Style>
                              <FontStyle>Normal</FontStyle>
                              <FontSize>12pt</FontSize>
                              <FontWeight>Normal</FontWeight>
                              <TextDecoration>None</TextDecoration>
                              <Color>#000000</Color>
                            </Style>
                          </TextRun>
                          <TextRun>
                            <Value>：</Value>
                            <Style>
                              <FontStyle>Normal</FontStyle>
                              <FontFamily>Verdana</FontFamily>
                              <FontSize>12pt</FontSize>
                              <FontWeight>Normal</FontWeight>
                              <TextDecoration>None</TextDecoration>
                              <Color>#000000</Color>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style />
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox162</rd:DefaultName>
                    <Style>
                      <Border>
                        <Style>Solid</Style>
                      </Border>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </CellContents>
              </TablixHeader>
              <TablixMembers>
                <TablixMember>
                  <TablixHeader>
                    <Size>3.37888in</Size>
                    <CellContents>
                      <Textbox Name="Textbox172">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>=First(Fields!abd_applyno.Value, "DataSet_applystatus")</Value>
                                <Style>
                                  <FontSize>12pt</FontSize>
                                </Style>
                              </TextRun>
                            </TextRuns>
                            <Style />
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox172</rd:DefaultName>
                        <Style>
                          <Border>
                            <Style>Solid</Style>
                          </Border>
                          <VerticalAlign>Middle</VerticalAlign>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                  <TablixMembers>
                    <TablixMember>
                      <TablixHeader>
                        <Size>1.84516in</Size>
                        <CellContents>
                          <Textbox Name="Textbox166">
                            <CanGrow>true</CanGrow>
                            <KeepTogether>true</KeepTogether>
                            <Paragraphs>
                              <Paragraph>
                                <TextRuns>
                                  <TextRun>
                                    <Value>資料狀態</Value>
                                    <Style>
                                      <FontStyle>Normal</FontStyle>
                                      <FontSize>12pt</FontSize>
                                      <FontWeight>Normal</FontWeight>
                                      <TextDecoration>None</TextDecoration>
                                      <Color>#000000</Color>
                                    </Style>
                                  </TextRun>
                                  <TextRun>
                                    <Value>：</Value>
                                    <Style>
                                      <FontStyle>Normal</FontStyle>
                                      <FontFamily>Verdana</FontFamily>
                                      <FontSize>12pt</FontSize>
                                      <FontWeight>Normal</FontWeight>
                                      <TextDecoration>None</TextDecoration>
                                      <Color>#000000</Color>
                                    </Style>
                                  </TextRun>
                                </TextRuns>
                                <Style>
                                  <TextAlign>Right</TextAlign>
                                </Style>
                              </Paragraph>
                            </Paragraphs>
                            <rd:DefaultName>Textbox166</rd:DefaultName>
                            <Style>
                              <Border>
                                <Style>Solid</Style>
                              </Border>
                              <VerticalAlign>Middle</VerticalAlign>
                              <PaddingLeft>2pt</PaddingLeft>
                              <PaddingRight>2pt</PaddingRight>
                              <PaddingTop>2pt</PaddingTop>
                              <PaddingBottom>2pt</PaddingBottom>
                            </Style>
                          </Textbox>
                        </CellContents>
                      </TablixHeader>
                      <TablixMembers>
                        <TablixMember>
                          <TablixHeader>
                            <Size>2.52306in</Size>
                            <CellContents>
                              <Textbox Name="Textbox168">
                                <CanGrow>true</CanGrow>
                                <KeepTogether>true</KeepTogether>
                                <Paragraphs>
                                  <Paragraph>
                                    <TextRuns>
                                      <TextRun>
                                        <Value>=First(Fields!ct_aps_desc.Value, "DataSet_applystatus")</Value>
                                        <Style>
                                          <FontStyle>Normal</FontStyle>
                                          <FontSize>12pt</FontSize>
                                          <FontWeight>Normal</FontWeight>
                                          <TextDecoration>None</TextDecoration>
                                          <Color>#000000</Color>
                                        </Style>
                                      </TextRun>
                                    </TextRuns>
                                    <Style />
                                  </Paragraph>
                                </Paragraphs>
                                <rd:DefaultName>Textbox168</rd:DefaultName>
                                <Style>
                                  <Border>
                                    <Style>Solid</Style>
                                  </Border>
                                  <RightBorder>
                                    <Style>None</Style>
                                  </RightBorder>
                                  <VerticalAlign>Middle</VerticalAlign>
                                  <PaddingLeft>2pt</PaddingLeft>
                                  <PaddingRight>2pt</PaddingRight>
                                  <PaddingTop>2pt</PaddingTop>
                                  <PaddingBottom>2pt</PaddingBottom>
                                </Style>
                              </Textbox>
                            </CellContents>
                          </TablixHeader>
                        </TablixMember>
                      </TablixMembers>
                    </TablixMember>
                  </TablixMembers>
                </TablixMember>
              </TablixMembers>
            </TablixMember>
            <TablixMember>
              <TablixHeader>
                <Size>1.49018in</Size>
                <CellContents>
                  <Textbox Name="Textbox165">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>車型名稱</Value>
                            <Style>
                              <FontStyle>Normal</FontStyle>
                              <FontSize>12pt</FontSize>
                              <FontWeight>Normal</FontWeight>
                              <TextDecoration>None</TextDecoration>
                              <Color>#000000</Color>
                            </Style>
                          </TextRun>
                          <TextRun>
                            <Value>：</Value>
                            <Style>
                              <FontStyle>Normal</FontStyle>
                              <FontFamily>Verdana</FontFamily>
                              <FontSize>12pt</FontSize>
                              <FontWeight>Normal</FontWeight>
                              <TextDecoration>None</TextDecoration>
                              <Color>#000000</Color>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style />
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox165</rd:DefaultName>
                    <Style>
                      <Border>
                        <Style>Solid</Style>
                      </Border>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </CellContents>
              </TablixHeader>
              <TablixMembers>
                <TablixMember>
                  <TablixHeader>
                    <Size>7.7471in</Size>
                    <CellContents>
                      <Tablix Name="Tablix3">
                        <TablixBody>
                          <TablixColumns>
                            <TablixColumn>
                              <Width>7.7471in</Width>
                            </TablixColumn>
                          </TablixColumns>
                          <TablixRows>
                            <TablixRow>
                              <Height>0.26527in</Height>
                              <TablixCells>
                                <TablixCell>
                                  <CellContents>
                                    <Textbox Name="fullcarstylename">
                                      <CanGrow>true</CanGrow>
                                      <KeepTogether>true</KeepTogether>
                                      <Paragraphs>
                                        <Paragraph>
                                          <TextRuns>
                                            <TextRun>
                                              <Value>=Fields!fullcarstylename.Value</Value>
                                              <Style>
                                                <FontSize>12pt</FontSize>
                                              </Style>
                                            </TextRun>
                                          </TextRuns>
                                          <Style />
                                        </Paragraph>
                                      </Paragraphs>
                                      <rd:DefaultName>fullcarstylename</rd:DefaultName>
                                      <Style>
                                        <Border>
                                          <Style>None</Style>
                                        </Border>
                                        <TopBorder>
                                          <Color>Black</Color>
                                        </TopBorder>
                                        <LeftBorder>
                                          <Color>White</Color>
                                        </LeftBorder>
                                        <PaddingLeft>2pt</PaddingLeft>
                                        <PaddingRight>2pt</PaddingRight>
                                        <PaddingTop>2pt</PaddingTop>
                                        <PaddingBottom>2pt</PaddingBottom>
                                      </Style>
                                    </Textbox>
                                  </CellContents>
                                </TablixCell>
                              </TablixCells>
                            </TablixRow>
                          </TablixRows>
                        </TablixBody>
                        <TablixColumnHierarchy>
                          <TablixMembers>
                            <TablixMember />
                          </TablixMembers>
                        </TablixColumnHierarchy>
                        <TablixRowHierarchy>
                          <TablixMembers>
                            <TablixMember>
                              <Group Name="Details1" />
                            </TablixMember>
                          </TablixMembers>
                        </TablixRowHierarchy>
                        <DataSetName>DataSet_carmodelnames</DataSetName>
                        <ZIndex>3</ZIndex>
                        <Style>
                          <Border>
                            <Style>None</Style>
                          </Border>
                        </Style>
                      </Tablix>
                    </CellContents>
                  </TablixHeader>
                </TablixMember>
              </TablixMembers>
            </TablixMember>
          </TablixMembers>
        </TablixRowHierarchy>
        <RepeatColumnHeaders>true</RepeatColumnHeaders>
        <DataSetName>DataSet_carmodelnames</DataSetName>
        <Top>0.82931cm</Top>
        <Left>0.00002cm</Left>
        <Height>1.34757cm</Height>
        <Width>23.54207cm</Width>
        <Style>
          <Border>
            <Style>Solid</Style>
            <Width>1.25pt</Width>
          </Border>
          <FontSize>12pt</FontSize>
        </Style>
      </Tablix>
      <Textbox Name="Textbox1">
        <CanGrow>true</CanGrow>
        <KeepTogether>true</KeepTogether>
        <Paragraphs>
          <Paragraph>
            <TextRuns>
              <TextRun>
                <Value>=Parameters!P_title.Value</Value>
                <Style>
                  <FontFamily>KaiTi</FontFamily>
                  <FontSize>16pt</FontSize>
                </Style>
              </TextRun>
            </TextRuns>
            <Style>
              <TextAlign>Center</TextAlign>
            </Style>
          </Paragraph>
        </Paragraphs>
        <rd:DefaultName>Textbox1</rd:DefaultName>
        <Left>6.49921cm</Left>
        <Height>0.75875cm</Height>
        <Width>12.12841cm</Width>
        <ZIndex>1</ZIndex>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
          <VerticalAlign>Middle</VerticalAlign>
          <PaddingLeft>2pt</PaddingLeft>
          <PaddingRight>2pt</PaddingRight>
          <PaddingTop>2pt</PaddingTop>
          <PaddingBottom>2pt</PaddingBottom>
        </Style>
      </Textbox>
      <Tablix Name="Tablix1">
        <TablixBody>
          <TablixColumns>
            <TablixColumn>
              <Width>1.2111in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1.99337in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1.99337in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1.99337in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>2.02858in</Width>
            </TablixColumn>
          </TablixColumns>
          <TablixRows>
            <TablixRow>
              <Height>0.23622in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox52">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>位</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox52</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <BottomBorder>
                          <Style>None</Style>
                        </BottomBorder>
                        <BackgroundColor>#e0eabf</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox353">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>隔吸音構造</Value>
                              <Style>
                                <FontSize>12pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox352</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>#e0eabf</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                    <ColSpan>4</ColSpan>
                  </CellContents>
                </TablixCell>
                <TablixCell />
                <TablixCell />
                <TablixCell />
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.23622in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox46">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>置</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox45</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <TopBorder>
                          <Style>None</Style>
                        </TopBorder>
                        <BackgroundColor>#e0eabf</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox364">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>設施(品名)</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox363</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>#e0eabf</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox41">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>材質</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox39</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>#e0eabf</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox47">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>厚度</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox41</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>#e0eabf</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox48">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>備註</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox43</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>#e0eabf</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.23622in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="ndp_position">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!ndp_position.Value</Value>
                              <Style>
                                <FontSize>12pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>ndp_position</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="ndp_defenseeqpname1">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!ndp_defenseeqpname.Value</Value>
                              <Style>
                                <FontSize>12pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>ndp_defenseeqpname1</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="ndp_eqpmaterial1">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!ndp_eqpmaterial.Value</Value>
                              <Style>
                                <FontSize>12pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>ndp_eqpmaterial1</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="ndp_eqpheight1">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!ndp_eqpheight.Value</Value>
                              <Style>
                                <FontSize>12pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>ndp_eqpheight1</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="ndp_equremark1">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!ndp_equremark.Value</Value>
                              <Style>
                                <FontSize>12pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>ndp_equremark1</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
          </TablixRows>
        </TablixBody>
        <TablixColumnHierarchy>
          <TablixMembers>
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
          </TablixMembers>
        </TablixColumnHierarchy>
        <TablixRowHierarchy>
          <TablixMembers>
            <TablixMember>
              <KeepWithGroup>After</KeepWithGroup>
            </TablixMember>
            <TablixMember>
              <KeepWithGroup>After</KeepWithGroup>
            </TablixMember>
            <TablixMember>
              <Group Name="Details" />
            </TablixMember>
          </TablixMembers>
        </TablixRowHierarchy>
        <DataSetName>DataSet_noisedefenseplan</DataSetName>
        <SortExpressions>
          <SortExpression>
            <Value>=Fields!ndp_position.Value</Value>
          </SortExpression>
        </SortExpressions>
        <Top>2.66383cm</Top>
        <Left>0.04445cm</Left>
        <Height>1.8cm</Height>
        <Width>23.41826cm</Width>
        <ZIndex>2</ZIndex>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
        </Style>
      </Tablix>
    </ReportItems>
    <Height>1.8199in</Height>
    <Style />
  </Body>
  <ReportParameters>
    <ReportParameter Name="P_title">
      <DataType>String</DataType>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
  </ReportParameters>
  <Width>9.31267in</Width>
  <Page>
    <PageHeight>21cm</PageHeight>
    <PageWidth>29.7cm</PageWidth>
    <LeftMargin>1cm</LeftMargin>
    <RightMargin>1cm</RightMargin>
    <TopMargin>1cm</TopMargin>
    <BottomMargin>1cm</BottomMargin>
    <ColumnSpacing>0.13cm</ColumnSpacing>
    <Style />
  </Page>
  <rd:ReportID>1ba65159-6158-4a5c-973a-af60733f262f</rd:ReportID>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
</Report>