﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.IO;

public partial class accountmt_accountvalidate_compdatachange : BaseAdminPage
{
    wsCompany wscompany = new wsCompany();
    wsAccountInfo wsaccount = new wsAccountInfo();
    wsCodetbl wscodetbl = new wsCodetbl();
    wsApplyDeny wsapplydeny = new wsApplyDeny();
    wsFileInfo wsfile = new wsFileInfo();
    protected void Page_Load(object sender, EventArgs e)
    {
        #region TextBox在Textmode=MultiLine狀態下MaxLength的字數限制無效
        txt_denybackreason.Attributes.Add("maxlength", "120");
        txt_denybackreason.Attributes.Add("onkeyup", "return ismaxlength(this)");

        #endregion
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = 'index.aspx';</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" && (GetAgentAccountGrpList().Contains("fis_adm") || GetAgentAccountGrpList().Contains("pg_adm") || GetAgentAccountGrpList().Contains("sys_adm")))
            {
                if (!IsPostBack)
                {
                    if (Request["status"] == null || Request["type"] == null || Request["account"] == null || Request["compidno"] == null)
                    {
                        Response.Write(ConfigurationManager.AppSettings["Msg.參數正確性"].ToString());
                        Response.End();
                        return;
                    }
                    else
                    {
                        string type = DeCode(Request["type"].ToString());
                        string status = DeCode(Request["status"].ToString());
                        if (type != "公司資料異動")
                        {
                            Response.Write(ConfigurationManager.AppSettings["Msg.無對應資料"].ToString());
                            Response.End();
                            return;
                        }
                        else
                        {
                            if (status == "申請中")
                            {
                                txt_denybackreason.Enabled = true;
                                txt_denybackreason.ReadOnly = false;
                                txt_compshortname.Enabled = true;
                                txt_stampcompno_d.Enabled = true;
                                txt_stampcompno_g.Enabled = true;
                                txt_stampcompno_m.Enabled = true;
                                txt_compshortname.ReadOnly = false;
                                txt_stampcompno_d.ReadOnly = false;
                                txt_stampcompno_g.ReadOnly = false;
                                txt_stampcompno_m.ReadOnly = false;
                                btn_OK.Visible = true;
                                btn_NO.Visible = true;

                                lbl_loginname.Text = GetAgentName();
                                lbl_logindate.Text = DateTime.Now.ToString("yyyy/MM/dd");
                            }
                            else
                            {
                                txt_denybackreason.Enabled = false;
                                txt_denybackreason.ReadOnly = true;
                                txt_compshortname.Enabled = false;
                                txt_stampcompno_d.Enabled = false;
                                txt_stampcompno_g.Enabled = false;
                                txt_stampcompno_m.Enabled = false;
                                txt_compshortname.ReadOnly = true;
                                txt_stampcompno_d.ReadOnly = true;
                                txt_stampcompno_g.ReadOnly = true;
                                txt_stampcompno_m.ReadOnly = true;
                                btn_OK.Visible = false;
                                btn_NO.Visible = false;
                            }

                            if (status == "退件")
                            {
                                //BindCompany();
                                BindDenyCustImportChg();
                            }
                            else
                            {
                                //BindCompany();
                                BindCustImportChg();
                            }
                        }
                    }
                }
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }
    }
    private void BindCompany()
    {
        //string[] radom_C = wsCompanyValidate(getRadNum());
        //string[] radom_Code = wsCodetblValidate(getRadNum());

        //DataSet ds_company = wscompany.getCompanyBase(Request["compidno"].ToString(), radom_C[0].ToString(), radom_C[1].ToString());
        //lbl_compidno.Text = DeCode(Request["compidno"].ToString());
        //lbl_chargeman.Text = ds_company.Tables[0].Rows[0]["ci_chargeman"].ToString();
        //lbl_comptel.Text = ds_company.Tables[0].Rows[0]["ci_comptel"].ToString();
        //lbl_compfax.Text = (ds_company.Tables[0].Rows[0]["ci_compfax"].ToString() == "") ? "&nbsp;" : ds_company.Tables[0].Rows[0]["ci_compfax"].ToString();
        //lbl_compaddr.Text = ds_company.Tables[0].Rows[0]["ci_compaddrpostno"].ToString()
        //    + "  " + wscodetbl.getCityName(ds_company.Tables[0].Rows[0]["ci_compaddrcity"].ToString(), radom_Code[0].ToString(), radom_Code[1].ToString())
        //    + "  " + wscodetbl.getZipName(ds_company.Tables[0].Rows[0]["ci_compaddrcity"].ToString(), ds_company.Tables[0].Rows[0]["ci_compaddrlocalarea"].ToString(), radom_Code[0].ToString(), radom_Code[1].ToString())
        //    + "  " + ds_company.Tables[0].Rows[0]["ci_compaddr"].ToString();

        //string[] radom_A = wsAccountValidate(getRadNum());
        //int issuedaccountcount = wsaccount.hIssuedAccountbyComp(Request["compidno"].ToString(), radom_A[0].ToString(), radom_A[1].ToString());
        //lbl_appyliedcount.Text = issuedaccountcount.ToString();
    }

    private void BindCustImportChg()
    {
        string[] radom_C = wsCompanyValidate(getRadNum());
        string[] radom_Code = wsCodetblValidate(getRadNum());
        string id = DeCode(Request["account"].ToString());
        DataTable dtBase = wscompany.getComapnyChangeBaseData(radom_C[0].ToString(), radom_C[1].ToString(), id);
        if (dtBase.Rows.Count > 0)
        {
            lbl_compidno.Text = DeCode(Request["compidno"].ToString());
            lbl_compname.Text = dtBase.Rows[0]["cci_compname"].ToString();
            lbl_compename.Text = dtBase.Rows[0]["cci_compename"].ToString();
            lbl_chargeman.Text = dtBase.Rows[0]["cci_chargeman"].ToString();
            lbl_comptel.Text = dtBase.Rows[0]["cci_comptel"].ToString();
            lbl_compfax.Text = (dtBase.Rows[0]["cci_compfax"].ToString() == "") ? "&nbsp;" : dtBase.Rows[0]["cci_compfax"].ToString();
            lbl_compaddr.Text = dtBase.Rows[0]["cci_compaddrpostno"].ToString()
                + "  " + wscodetbl.getCityName(dtBase.Rows[0]["cci_compaddrcity"].ToString(), radom_Code[0].ToString(), radom_Code[1].ToString())
                + "  " + wscodetbl.getZipName(dtBase.Rows[0]["cci_compaddrcity"].ToString(), dtBase.Rows[0]["cci_compaddrlocalarea"].ToString(), radom_Code[0].ToString(), radom_Code[1].ToString())
                + "  " + dtBase.Rows[0]["cci_compaddr"].ToString();
            lbl_certaddr.Text = dtBase.Rows[0]["cci_certaddrpostno"].ToString()
                + "  " + wscodetbl.getCityName(dtBase.Rows[0]["cci_certaddrcity"].ToString(), radom_Code[0].ToString(), radom_Code[1].ToString())
                + "  " + wscodetbl.getZipName(dtBase.Rows[0]["cci_certaddrcity"].ToString(), dtBase.Rows[0]["cci_certaddrlocalarea"].ToString(), radom_Code[0].ToString(), radom_Code[1].ToString())
                + "  " + dtBase.Rows[0]["cci_certaddr"].ToString();
            string[] radom_A = wsAccountValidate(getRadNum());
            int issuedaccountcount = wsaccount.hIssuedAccountbyComp(Request["compidno"].ToString(), radom_A[0].ToString(), radom_A[1].ToString());
            lbl_appyliedcount.Text = issuedaccountcount.ToString();
            txt_stampcompno_g.Text = dtBase.Rows[0]["cci_stampcompno_g"].ToString().Trim();
            txt_stampcompno_m.Text = dtBase.Rows[0]["cci_stampcompno_m"].ToString().Trim();
            txt_stampcompno_d.Text = dtBase.Rows[0]["cci_stampcompno_d"].ToString().Trim();

            lab_compename.Text = dtBase.Rows[0]["cci_compename"].ToString().Trim();
            lab_chargeman.Text = dtBase.Rows[0]["cci_chargeman"].ToString().Trim();


            string[] radom_F = wsFileValidate(getRadNum());
            DataTable dtFile = wsfile.getCompanyChangeFileList(radom_F[0].ToString(), radom_F[1].ToString(), Request["compidno"].ToString());
            rpt_fileList.DataSource = dtFile;
            rpt_fileList.DataBind();
        }
    }

    private void BindDenyCustImportChg()
    {
        string[] radom_ap = wsApplyDenyValidate(getRadNum());
        string[] radom_Code = wsCodetblValidate(getRadNum());
        string id = DeCode(Request["account"].ToString());
        DataTable dtBase = wsapplydeny.getCompanyChangeDeny(radom_ap[0].ToString(), radom_ap[1].ToString(), id);
        if (dtBase.Rows.Count > 0)
        {
            lbl_compidno.Text = DeCode(Request["compidno"].ToString());
            lbl_compname.Text = dtBase.Rows[0]["cci_ad_compname"].ToString();
            lbl_chargeman.Text = dtBase.Rows[0]["cci_ad_chargeman"].ToString();
            lbl_comptel.Text = dtBase.Rows[0]["cci_ad_comptel"].ToString();
            lbl_compfax.Text = (dtBase.Rows[0]["cci_ad_compfax"].ToString() == "") ? "&nbsp;" : dtBase.Rows[0]["cci_ad_compfax"].ToString();
            lbl_compaddr.Text = dtBase.Rows[0]["cci_ad_compaddrpostno"].ToString()
                + "  " + wscodetbl.getCityName(dtBase.Rows[0]["cci_ad_compaddrcity"].ToString(), radom_Code[0].ToString(), radom_Code[1].ToString())
                + "  " + wscodetbl.getZipName(dtBase.Rows[0]["cci_ad_compaddrcity"].ToString(), dtBase.Rows[0]["cci_ad_compaddrlocalarea"].ToString(), radom_Code[0].ToString(), radom_Code[1].ToString())
                + "  " + dtBase.Rows[0]["cci_ad_compaddr"].ToString();
            lbl_certaddr.Text = dtBase.Rows[0]["cci_ad_certaddrpostno"].ToString()
                + "  " + wscodetbl.getCityName(dtBase.Rows[0]["cci_ad_certaddrcity"].ToString(), radom_Code[0].ToString(), radom_Code[1].ToString())
                + "  " + wscodetbl.getZipName(dtBase.Rows[0]["cci_ad_certaddrcity"].ToString(), dtBase.Rows[0]["cci_ad_certaddrlocalarea"].ToString(), radom_Code[0].ToString(), radom_Code[1].ToString())
                + "  " + dtBase.Rows[0]["cci_ad_certaddr"].ToString();

            string[] radom_A = wsAccountValidate(getRadNum());
            int issuedaccountcount = wsaccount.hIssuedAccountbyComp(Request["compidno"].ToString(), radom_A[0].ToString(), radom_A[1].ToString());
            lbl_appyliedcount.Text = issuedaccountcount.ToString();


            string[] radom_F = wsFileValidate(getRadNum());
            DataTable dtFile = wsfile.getCompanyChangeFileList(radom_F[0].ToString(), radom_F[1].ToString(), Request["compidno"].ToString());
            rpt_fileList.DataSource = dtFile;
            rpt_fileList.DataBind();
        }
    }
    protected void btn_OK_Click(object sender, EventArgs e)
    {
        if (txt_denybackreason.Text.Trim() != "")
        {
            MessageBox.Show("無需填寫退件原因");
            return;
        }
        if (txt_compshortname.Text.Trim() == "")
        {
            MessageBox.Show("公司簡稱不可空值");
            return;
        }
        if ((txt_stampcompno_d.Text.Trim() + txt_stampcompno_g.Text.Trim() + txt_stampcompno_m.Text.Trim()) == "")
        {
            MessageBox.Show("廠商代碼至少擇一填入");
            return;
        }

        #region Accountmt特殊字元判斷-有MasterPage
        ContentPlaceHolder mpContentPlaceHolder;
        mpContentPlaceHolder = (ContentPlaceHolder)Master.FindControl("ContentPlaceHolder1");
        foreach (object ctrl in mpContentPlaceHolder.Controls)
        {
            if (ctrl is TextBox)
            {
                TextBox objTextBox = default(TextBox);
                objTextBox = (TextBox)ctrl;
                if ((tbWord(objTextBox.Text)))
                {
                    MessageBox.Show(objTextBox.Text + ":含有系統不允許的特殊字元");
                    return;
                }
            }
        }
        #endregion

        string id = DeCode(Request["account"].ToString());
        string[] radom_C = wsCompanyValidate(getRadNum());
        string[] radom_a = wsAccountValidate(getRadNum());
        string compidnoE = Request["compidno"];
        DataTable dtBase = wscompany.getComapnyChangeBaseData(radom_C[0].ToString(), radom_C[1].ToString(), id);
        DataTable dtAccountBase = wsaccount.getAccountbyCompanyAndAccountname(radom_a[0].ToString(), radom_a[1].ToString(), compidnoE, dtBase.Rows[0]["cci_lastchangename"].ToString());
        string[] radom_A = wsAccountValidate(getRadNum());
        string[] radom_CarCert = wsCarCertValid(Convert.ToInt16(DateTime.Now.DayOfWeek));
        DataSet ds_company = wscompany.getCompanyBase(Session["compidno"].ToString(), radom_C[0].ToString(), radom_C[1].ToString());
        DataSet ds_Account = wsaccount.getAccountBase(DeCode(Session["account"].ToString()), radom_A[0].ToString(), radom_A[1].ToString());


        SendMail mail = new SendMail();
        //mail
        string[] _mailTo = new string[1];
        _mailTo[0] = dtAccountBase.Rows[0]["ai_email"].ToString();//收件人                
        string[] _mailCc = new string[1];//副本
        _mailCc[0] = "";
        string[] _mailBcc = new string[1];//密件
        _mailBcc[0] = "";
        if (Convert.ToBoolean(ConfigurationManager.AppSettings["DEBUGMode"].ToString()))
        {
            //測試
            _mailTo[0] = ConfigurationManager.AppSettings["MailTestTo"].ToString();
        }
        if (mail.Mail_ValidateCompChg_OK(_mailTo, _mailCc, _mailBcc, lbl_compname.Text.Trim(), dtBase.Rows[0]["cci_lastchangename"].ToString()))
        {
            wscompany.execCompChangeApplyOK(radom_C[0].ToString(), radom_C[1].ToString(), compidnoE, id, txt_compshortname.Text.Trim(), txt_stampcompno_d.Text.Trim(), txt_stampcompno_m.Text.Trim(), txt_stampcompno_g.Text.Trim(), GetAgentAccount(), GetIP());

            connectCarCert.CarCert CarCert = new connectCarCert.CarCert();
            #region Check CarCert驗證 UpdateCustomer
            //判斷此公司是否存在於CarCert；若存在 則更新資料
            if (doCarCertCompanyCheck() && ds_company.Tables[0].Rows.Count > 0 && ds_Account.Tables[0].Rows.Count > 0)
            {
                CarCert.UpdateCustomer(compidnoE, EnCode(lbl_compname.Text.Trim()), EnCode(ds_company.Tables[0].Rows[0]["ci_compname"].ToString()), EnCode(ds_company.Tables[0].Rows[0]["ci_compename"].ToString()),
                       EnCode(txt_compshortname.Text.Trim()), EnCode(DateTime.Now.Date.ToString("yyyyMMdd")), EnCode(lbl_comptel.Text.Trim()), EnCode(lbl_compfax.Text.Trim()), EnCode(ds_company.Tables[0].Rows[0]["ci_certaddrcity"].ToString()), EnCode(ds_company.Tables[0].Rows[0]["ci_certaddrlocalarea"].ToString()),
                      EnCode(ds_company.Tables[0].Rows[0]["ci_certaddrpostno"].ToString()), EnCode(ds_company.Tables[0].Rows[0]["ci_certaddr"].ToString()), EnCode(ds_Account.Tables[0].Rows[0]["ai_username"].ToString()), EnCode(ds_Account.Tables[0].Rows[0]["ai_email"].ToString()), EnCode(ds_Account.Tables[0].Rows[0]["ai_usertel"].ToString()), EnCode(ds_Account.Tables[0].Rows[0]["ai_usercellphone"].ToString()),
                      EnCode(ds_Account.Tables[0].Rows[0]["ai_userfax"].ToString()), EnCode(txt_stampcompno_g.Text.Trim()), EnCode(txt_stampcompno_m.Text.Trim()), EnCode("1"), radom_CarCert[0].ToString(), radom_CarCert[1].ToString());
            }
            #endregion
            ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('系統已寄發核發通知');
                                                   window.location = 'applyaccountvalidaionlist.aspx';</script>");
        }
        else
        {
            MessageBox.Show("寄件失敗");
        }
    }
    protected void btn_NO_Click(object sender, EventArgs e)
    {
        if (txt_denybackreason.Text.Trim().Length > 200)
        {
            MessageBox.Show("字數超出200，請確認");
            return;
        }
        if (txt_denybackreason.Text.Trim() != "")
        {

            #region Accountmt特殊字元判斷-有MasterPage
            ContentPlaceHolder mpContentPlaceHolder;
            mpContentPlaceHolder = (ContentPlaceHolder)Master.FindControl("ContentPlaceHolder1");
            foreach (object ctrl in mpContentPlaceHolder.Controls)
            {
                if (ctrl is TextBox)
                {
                    TextBox objTextBox = default(TextBox);
                    objTextBox = (TextBox)ctrl;
                    if ((tbWord(objTextBox.Text)))
                    {
                        MessageBox.Show(objTextBox.Text + ":含有系統不允許的特殊字元");
                        return;
                    }
                }
            }
            #endregion

            string id = DeCode(Request["account"].ToString());
            string[] radom_AD = wsApplyDenyValidate(getRadNum());
            string compidnoE = Request["compidno"];

            string[] radom_C = wsCompanyValidate(getRadNum());
            string[] radom_a = wsAccountValidate(getRadNum());
            DataTable dtBase = wscompany.getComapnyChangeBaseData(radom_C[0].ToString(), radom_C[1].ToString(), id);
            DataTable dtAccountBase = wsaccount.getAccountbyCompanyAndAccountname(radom_a[0].ToString(), radom_a[1].ToString(), compidnoE, dtBase.Rows[0]["cci_lastchangename"].ToString());

            SendMail mail = new SendMail();
            //mail
            string[] _mailTo = new string[1];
            _mailTo[0] = dtAccountBase.Rows[0]["ai_email"].ToString();//收件人                
            string[] _mailCc = new string[1];//副本
            _mailCc[0] = "";
            string[] _mailBcc = new string[1];//密件
            _mailBcc[0] = "";
            if (Convert.ToBoolean(ConfigurationManager.AppSettings["DEBUGMode"].ToString()))
            {
                //測試
                _mailTo[0] = ConfigurationManager.AppSettings["MailTestTo"].ToString();
            }
            if (mail.Mail_ValidateCompChg_NG(_mailTo, _mailCc, _mailBcc, lbl_compname.Text.Trim(), dtBase.Rows[0]["cci_lastchangename"].ToString(), txt_denybackreason.Text.Trim()))
            {
                wsapplydeny.DenyCompanyChange(radom_AD[0].ToString(), radom_AD[1].ToString(), id, txt_denybackreason.Text.Trim(), GetAgentAccount(), GetIP(), Request["compidno"].ToString());
                ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('系統已寄發退件通知');
                                                    window.location = 'applyaccountvalidaionlist.aspx';</script>");
            }
            else
            {
                MessageBox.Show("寄件失敗");
            }
        }
        else
        {
            MessageBox.Show("請輸入退件原因");
        }
    }
    protected void btn_Cancel_Click(object sender, EventArgs e)
    {
        Response.Redirect("applyaccountvalidaionlist.aspx", false);
    }
    protected void rpt_fileList_ItemDataBound(object sender, RepeaterItemEventArgs e)
    {
        FileInfo file;
        if (e.Item.ItemType == ListItemType.Item || e.Item.ItemType == ListItemType.AlternatingItem)
        {
            string afi_fileid = DataBinder.Eval(e.Item.DataItem, "afi_fileid").ToString();
            string afi_filepath = DataBinder.Eval(e.Item.DataItem, "afi_filepath").ToString();
            string afi_sysfilename = DataBinder.Eval(e.Item.DataItem, "afi_sysfilename").ToString();
            string afi_userfilename = DataBinder.Eval(e.Item.DataItem, "afi_userfilename").ToString();
            file = new FileInfo(afi_filepath + afi_sysfilename);
            HyperLink hlk_file = (HyperLink)e.Item.FindControl("hlk_file");
            string ci_ufi_filestatus = DataBinder.Eval(e.Item.DataItem, "ci_ufi_filestatus").ToString();
            if (ci_ufi_filestatus == "C")
                hlk_file.Text = afi_userfilename + "_申請中";
            else hlk_file.Text = afi_userfilename;
            if (file.Exists)
            {
                hlk_file.NavigateUrl = "../FileDownloadSeal.aspx?File=" + Server.UrlEncode(EnCode(afi_fileid));
            }
        }
    }

    #region Check CarCert 公司驗證
    private bool doCarCertCompanyCheck()
    {

        connectCarCert.CarCert CarCert = new connectCarCert.CarCert();
        string[] radom_CarCert = wsCarCertValid(Convert.ToInt16(DateTime.Now.DayOfWeek));
        //公司是否存在 CarCert
        bool isCompanyCarCert = CarCert.CheckCustomer(EnCode(GetAgentIDNo()), radom_CarCert[0].ToString(), radom_CarCert[1].ToString());


        if (isCompanyCarCert)
            return true;
        else
            return false;
    }
    #endregion
}