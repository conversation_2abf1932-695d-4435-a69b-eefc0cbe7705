<?xml version="1.0" encoding="utf-8"?>
<Report xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner" xmlns="http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition">
  <DataSources>
    <DataSource Name="fixcatalogDataSet">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>c21e785e-a794-4e36-951f-901f46f21122</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="DataSet_fixcatalog2">
      <Fields>
        <Field Name="Itno">
          <DataField>Itno</DataField>
          <rd:TypeName>System.Int64</rd:TypeName>
        </Field>
        <Field Name="ct_at_desc">
          <DataField>ct_at_desc</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="abd_applydate">
          <DataField>abd_applydate</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="fc_applyno">
          <DataField>fc_applyno</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="fc_carcompomodelno">
          <DataField>fc_carcompomodelno</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="fc_fixid">
          <DataField>fc_fixid</DataField>
          <rd:TypeName>System.Int64</rd:TypeName>
        </Field>
        <Field Name="fc_fixitem">
          <DataField>fc_fixitem</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="fc_beforefix">
          <DataField>fc_beforefix</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="fc_afterfix">
          <DataField>fc_afterfix</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="fc_createaccount">
          <DataField>fc_createaccount</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="fc_createdate">
          <DataField>fc_createdate</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="fc_modaccount">
          <DataField>fc_modaccount</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="fc_moddate">
          <DataField>fc_moddate</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="fc_oriapplyno">
          <DataField>fc_oriapplyno</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>fixcatalogDataSet</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <rd:DataSetInfo>
        <rd:DataSetName>fixcatalogDataSet</rd:DataSetName>
        <rd:SchemaPath>D:\Olivia\noisevalidation\PROGRAM\webRoot\App_Code\DataSet\fixcatalogDataSet.xsd</rd:SchemaPath>
        <rd:TableName>pr_get_fixcatalog_list2</rd:TableName>
        <rd:TableAdapterFillMethod>Fill</rd:TableAdapterFillMethod>
        <rd:TableAdapterGetDataMethod>GetData</rd:TableAdapterGetDataMethod>
        <rd:TableAdapterName>pr_get_fixcatalog_list2TableAdapter</rd:TableAdapterName>
      </rd:DataSetInfo>
    </DataSet>
  </DataSets>
  <Body>
    <ReportItems>
      <Tablix Name="Tablix2">
        <TablixBody>
          <TablixColumns>
            <TablixColumn>
              <Width>0.7874in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1.1811in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1.1811in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>2.08661in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>2.18684in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>2.08661in</Width>
            </TablixColumn>
          </TablixColumns>
          <TablixRows>
            <TablixRow>
              <Height>0.264in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Itno">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Itno.Value</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Itno</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="ct_at_desc">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!ct_at_desc.Value</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>ct_at_desc</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="abd_applydate">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!abd_applydate.Value</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>abd_applydate</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="fc_fixitem">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!fc_fixitem.Value</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>fc_fixitem</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="fc_beforefix">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!fc_beforefix.Value</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>fc_beforefix</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="fc_afterfix">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!fc_afterfix.Value</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>fc_afterfix</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
          </TablixRows>
        </TablixBody>
        <TablixColumnHierarchy>
          <TablixMembers>
            <TablixMember>
              <TablixHeader>
                <Size>0.48632in</Size>
                <CellContents>
                  <Textbox Name="Textbox20">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>項次</Value>
                            <Style>
                              <FontStyle>Normal</FontStyle>
                              <FontSize>12pt</FontSize>
                              <FontWeight>Normal</FontWeight>
                              <TextDecoration>None</TextDecoration>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox19</rd:DefaultName>
                    <Style>
                      <Border>
                        <Color>LightGrey</Color>
                        <Style>Solid</Style>
                      </Border>
                      <BackgroundColor>#e0eabf</BackgroundColor>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </CellContents>
              </TablixHeader>
              <TablixMembers>
                <TablixMember />
              </TablixMembers>
            </TablixMember>
            <TablixMember>
              <TablixHeader>
                <Size>0.48632in</Size>
                <CellContents>
                  <Textbox Name="Textbox22">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>申請型式</Value>
                            <Style>
                              <FontStyle>Normal</FontStyle>
                              <FontSize>12pt</FontSize>
                              <FontWeight>Normal</FontWeight>
                              <TextDecoration>None</TextDecoration>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox21</rd:DefaultName>
                    <Style>
                      <Border>
                        <Color>LightGrey</Color>
                        <Style>Solid</Style>
                      </Border>
                      <BackgroundColor>#e0eabf</BackgroundColor>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </CellContents>
              </TablixHeader>
              <TablixMembers>
                <TablixMember />
              </TablixMembers>
            </TablixMember>
            <TablixMember>
              <TablixHeader>
                <Size>0.48632in</Size>
                <CellContents>
                  <Textbox Name="Textbox24">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>申請日期</Value>
                            <Style>
                              <FontStyle>Normal</FontStyle>
                              <FontSize>12pt</FontSize>
                              <FontWeight>Normal</FontWeight>
                              <TextDecoration>None</TextDecoration>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox23</rd:DefaultName>
                    <Style>
                      <Border>
                        <Color>LightGrey</Color>
                        <Style>Solid</Style>
                      </Border>
                      <BackgroundColor>#e0eabf</BackgroundColor>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </CellContents>
              </TablixHeader>
              <TablixMembers>
                <TablixMember />
              </TablixMembers>
            </TablixMember>
            <TablixMember>
              <TablixHeader>
                <Size>0.24316in</Size>
                <CellContents>
                  <Textbox Name="Textbox50">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>修正項目</Value>
                            <Style>
                              <FontStyle>Normal</FontStyle>
                              <FontSize>12pt</FontSize>
                              <FontWeight>Normal</FontWeight>
                              <TextDecoration>None</TextDecoration>
                            </Style>
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox50</rd:DefaultName>
                    <Style>
                      <Border>
                        <Color>LightGrey</Color>
                        <Style>Solid</Style>
                      </Border>
                      <BackgroundColor>#e0eabf</BackgroundColor>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </CellContents>
              </TablixHeader>
              <TablixMembers>
                <TablixMember>
                  <TablixHeader>
                    <Size>0.24316in</Size>
                    <CellContents>
                      <Textbox Name="Textbox26">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>修改項目</Value>
                                <Style>
                                  <FontStyle>Normal</FontStyle>
                                  <FontSize>12pt</FontSize>
                                  <FontWeight>Normal</FontWeight>
                                  <TextDecoration>None</TextDecoration>
                                </Style>
                              </TextRun>
                            </TextRuns>
                            <Style>
                              <TextAlign>Center</TextAlign>
                            </Style>
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox25</rd:DefaultName>
                        <Style>
                          <Border>
                            <Color>LightGrey</Color>
                            <Style>Solid</Style>
                          </Border>
                          <BackgroundColor>#e0eabf</BackgroundColor>
                          <VerticalAlign>Middle</VerticalAlign>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                  <TablixMembers>
                    <TablixMember />
                  </TablixMembers>
                </TablixMember>
                <TablixMember>
                  <TablixHeader>
                    <Size>0.24316in</Size>
                    <CellContents>
                      <Textbox Name="Textbox28">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>修改前</Value>
                                <Style>
                                  <FontStyle>Normal</FontStyle>
                                  <FontSize>12pt</FontSize>
                                  <FontWeight>Normal</FontWeight>
                                  <TextDecoration>None</TextDecoration>
                                </Style>
                              </TextRun>
                            </TextRuns>
                            <Style>
                              <TextAlign>Center</TextAlign>
                            </Style>
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox27</rd:DefaultName>
                        <Style>
                          <Border>
                            <Color>LightGrey</Color>
                            <Style>Solid</Style>
                          </Border>
                          <BackgroundColor>#e0eabf</BackgroundColor>
                          <VerticalAlign>Middle</VerticalAlign>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                  <TablixMembers>
                    <TablixMember />
                  </TablixMembers>
                </TablixMember>
                <TablixMember>
                  <TablixHeader>
                    <Size>0.24316in</Size>
                    <CellContents>
                      <Textbox Name="Textbox30">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>修改後</Value>
                                <Style>
                                  <FontStyle>Normal</FontStyle>
                                  <FontSize>12pt</FontSize>
                                  <FontWeight>Normal</FontWeight>
                                  <TextDecoration>None</TextDecoration>
                                </Style>
                              </TextRun>
                            </TextRuns>
                            <Style>
                              <TextAlign>Center</TextAlign>
                            </Style>
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox29</rd:DefaultName>
                        <Style>
                          <Border>
                            <Color>LightGrey</Color>
                            <Style>Solid</Style>
                          </Border>
                          <BackgroundColor>#e0eabf</BackgroundColor>
                          <VerticalAlign>Middle</VerticalAlign>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                  <TablixMembers>
                    <TablixMember />
                  </TablixMembers>
                </TablixMember>
              </TablixMembers>
            </TablixMember>
          </TablixMembers>
        </TablixColumnHierarchy>
        <TablixRowHierarchy>
          <TablixMembers>
            <TablixMember>
              <Group Name="Details1" />
            </TablixMember>
          </TablixMembers>
        </TablixRowHierarchy>
        <RepeatColumnHeaders>true</RepeatColumnHeaders>
        <RepeatRowHeaders>true</RepeatRowHeaders>
        <FixedColumnHeaders>true</FixedColumnHeaders>
        <FixedRowHeaders>true</FixedRowHeaders>
        <DataSetName>DataSet_fixcatalog2</DataSetName>
        <Top>2.04639cm</Top>
        <Left>0.04445cm</Left>
        <Height>1.90581cm</Height>
        <Width>24.15454cm</Width>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
        </Style>
      </Tablix>
      <Textbox Name="Textbox32">
        <CanGrow>true</CanGrow>
        <KeepTogether>true</KeepTogether>
        <Paragraphs>
          <Paragraph>
            <TextRuns>
              <TextRun>
                <Value>=Parameters!P_title.Value</Value>
                <Style>
                  <FontFamily>KaiTi</FontFamily>
                  <FontSize>16pt</FontSize>
                </Style>
              </TextRun>
            </TextRuns>
            <Style>
              <TextAlign>Center</TextAlign>
            </Style>
          </Paragraph>
        </Paragraphs>
        <rd:DefaultName>Textbox32</rd:DefaultName>
        <Top>0.03528cm</Top>
        <Left>7.26726cm</Left>
        <Height>0.75875cm</Height>
        <Width>10cm</Width>
        <ZIndex>1</ZIndex>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
          <VerticalAlign>Middle</VerticalAlign>
          <PaddingLeft>2pt</PaddingLeft>
          <PaddingRight>2pt</PaddingRight>
          <PaddingTop>2pt</PaddingTop>
          <PaddingBottom>2pt</PaddingBottom>
        </Style>
      </Textbox>
      <Tablix Name="Tablix3">
        <TablixBody>
          <TablixColumns>
            <TablixColumn>
              <Width>2.37742in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>2.37742in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>2.37742in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>2.37742in</Width>
            </TablixColumn>
          </TablixColumns>
          <TablixRows>
            <TablixRow>
              <Height>0.23622in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox34">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>申請編號：</Value>
                              <Style>
                                <FontSize>12pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox34</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="fc_applyno">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!fc_applyno.Value</Value>
                              <Style>
                                <FontSize>12pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Left</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>fc_applyno</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>None</Style>
                        </Border>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox38">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>資料狀態：</Value>
                              <Style>
                                <FontSize>12pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox38</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox39">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Parameters!P_status.Value</Value>
                              <Style>
                                <FontSize>12pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Left</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox39</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
          </TablixRows>
        </TablixBody>
        <TablixColumnHierarchy>
          <TablixMembers>
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
          </TablixMembers>
        </TablixColumnHierarchy>
        <TablixRowHierarchy>
          <TablixMembers>
            <TablixMember />
          </TablixMembers>
        </TablixRowHierarchy>
        <DataSetName>DataSet_fixcatalog2</DataSetName>
        <Top>0.82931cm</Top>
        <Left>0.04445cm</Left>
        <Height>0.6cm</Height>
        <Width>24.15459cm</Width>
        <ZIndex>2</ZIndex>
        <Style>
          <Border>
            <Style>Solid</Style>
          </Border>
          <FontSize>12pt</FontSize>
        </Style>
      </Tablix>
    </ReportItems>
    <Height>1.64269in</Height>
    <Style />
  </Body>
  <ReportParameters>
    <ReportParameter Name="P_status">
      <DataType>String</DataType>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="P_title">
      <DataType>String</DataType>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
  </ReportParameters>
  <Width>9.56622in</Width>
  <Page>
    <PageHeight>21cm</PageHeight>
    <PageWidth>29.7cm</PageWidth>
    <LeftMargin>2cm</LeftMargin>
    <RightMargin>2cm</RightMargin>
    <TopMargin>2cm</TopMargin>
    <BottomMargin>2cm</BottomMargin>
    <ColumnSpacing>0.13cm</ColumnSpacing>
    <Style />
  </Page>
  <rd:ReportID>82844b47-e155-423b-96d7-50ced32831ad</rd:ReportID>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
</Report>