﻿<%@ Page Title="" Language="C#" MasterPageFile="~/masterpage/MasterPage.master" AutoEventWireup="true"
    CodeFile="carcompomodel_readnoly.aspx.cs" Inherits="applydatamt_carcompomodel_readnoly" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <asp:ScriptManager ID="ScriptManager1" runat="server">
    </asp:ScriptManager>
    <div class="fullheader">
        <table width="100%" cellspacing="0" cellpadding="0" class="fullheaderinfo">
            <tr>
                <td valign="top">
                    <b>噪音合格證明申請-車型組基本資料</b>
                </td>
                <td align="right" class="font_loginInfo">
                </td>
            </tr>
        </table>
    </div>
    <!--{* fullheader end *}-->
    <div class="fullcontent">
        <div class="formstyle">
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="font_fullcontent">
                <tr>
                    <td align="right">
                        申請編號：
                    </td>
                    <td>
                        <asp:Label ID="lbl_applyno" runat="server"></asp:Label>
                        <asp:Label ID="lbl_vechiletype" runat="server" Visible="False"></asp:Label>
                    </td>
                    <td align="right">
                        申請狀態：
                    </td>
                    <td>
                        <asp:Label ID="lbl_applystatus" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        引擎族：
                    </td>
                    <td>
                        <asp:Label ID="lbl_enginefamily" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        車型年：
                    </td>
                    <td>
                        <asp:Label ID="lbl_carstyleyear" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        車型組編號：
                    </td>
                    <td colspan="3">
                        <asp:Label ID="lbl_carcompomodelno" runat="server"></asp:Label>
                    </td>
                </tr>
            </table>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="font_fullcontent">
                <tr>
                    <td colspan="4" align="left">
                        <font style="font-size: medium; font-style: normal; font-weight: bold;">基本引擎</font>
                    </td>
                </tr>
            </table>
            <div id="div_G" runat="server" style="display: none;">
                <%--汽車--%>
                <table border="0" cellspacing="0" cellpadding="0" width="100%" class="font_fullcontent">
                    <tr>
                        <td align="right">
                            申請型式：
                        </td>
                        <td colspan="3">
                            <asp:Label ID="lbl_ct_at_desc_G" runat="server"></asp:Label>
                        </td>
                    </tr>
                    <tr>
                        <td align="right">
                            進口國：
                        </td>
                        <td>
                            <asp:Label ID="lbl_importarea_G" runat="server"></asp:Label>
                        </td>
                        <td align="right">
                            以歐盟合格證申請：
                        </td>
                        <td>
                            <asp:Label ID="lbl_useeurocert_G" runat="server"></asp:Label>
                        </td>
                    </tr>
                    <tr>
                        <td align="right">
                            使用燃料：
                        </td>
                        <td>
                            <asp:Label ID="lbl_gasolinetype_G" runat="server"></asp:Label>
                        </td>
                        <td align="right">
                            排檔型式：
                        </td>
                        <td>
                            <asp:Label ID="lbl_transmissiontypetype_G" runat="server"></asp:Label>
                        </td>
                    </tr>
                    <tr>
                        <td align="right">
                            供油方式：
                        </td>
                        <td>
                            <asp:Label ID="lbl_provideoilmethod_G" runat="server"></asp:Label>
                        </td>
                        <td align="right">
                            引擎安裝位置：
                        </td>
                        <td>
                            <asp:Label ID="lbl_fituptype_G" runat="server"></asp:Label>
                        </td>
                        <%--                        <td align="right">
                            驅動方式：
                        </td>
                        <td>
                            <asp:Label ID="lbl_drivetype_G" runat="server"></asp:Label>
                        </td>--%>
                    </tr>
                    <tr>
                        <td align="right">
                            汽缸數：
                        </td>
                        <td>
                            <asp:Label ID="lbl_cylindernums_G" runat="server"></asp:Label>
                        </td>
                        <td align="right">
                            冷卻系統型式：
                        </td>
                        <td colspan="3">
                            <asp:Label ID="lbl_coolsystem_G" runat="server"></asp:Label>
                        </td>
                    </tr>
                </table>
            </div>
            <div id="div_M" runat="server" style="display: none;">
                <%--機車--%>
                <table border="0" cellspacing="0" cellpadding="0" width="100%" class="font_fullcontent">
                    <tr>
                        <td align="right">
                            申請型式：
                        </td>
                        <td colspan="3">
                            <asp:Label ID="lbl_ct_at_desc_M" runat="server"></asp:Label>
                        </td>
                    </tr>
                    <tr>
                        <td align="right">
                            排檔型式：
                        </td>
                        <td>
                            <asp:Label ID="lbl_transmissiontypetype_M" runat="server"></asp:Label>
                        </td>
                        <td align="right">
                            以歐盟合格證申請：
                        </td>
                        <td>
                            <asp:Label ID="lbl_useeurocert_M" runat="server"></asp:Label>
                        </td>
                    </tr>
                    <tr>
                        <td align="right">
                            使用燃料：
                        </td>
                        <td>
                            <asp:Label ID="lbl_gasolinetype_M" runat="server"></asp:Label>
                        </td>
                        <td align="right">
                            燃燒循環：
                        </td>
                        <td>
                            <asp:Label ID="lbl_powerexportway_M" runat="server"></asp:Label>
                        </td>
                    </tr>
                    <tr>
                        <td align="right">
                            供油方式：
                        </td>
                        <td>
                            <asp:Label ID="lbl_provideoilmethod_M" runat="server"></asp:Label>
                        </td>
                        <td align="right">
                            總排氣量：
                        </td>
                        <td>
                            <asp:Label ID="lbl_exhuast_M" runat="server"></asp:Label>
                        </td>
                    </tr>
                    <tr>
                        <td align="right">
                            汽缸缸數及排列：
                        </td>
                        <td>
                            缸數：<asp:Label ID="lbl_cylindernums_M" runat="server"></asp:Label><br />
                            汽缸排列：<asp:Label ID="lbl_cylindertype_M" runat="server"></asp:Label>
                        </td>
                        <td align="right">
                            引擎安裝位置及方式：
                        </td>
                        <td>
                            <asp:Label ID="lbl_enginesetupposnmethod_M" runat="server"></asp:Label>
                        </td>
                    </tr>
                    <tr>
                        <td align="right">
                            冷卻系統型式：
                        </td>
                        <td colspan="3">
                            <asp:Label ID="lbl_coolsystem_M" runat="server"></asp:Label>
                        </td>
                    </tr>
                    <tr>
                        <td align="right">
                            進口國：
                        </td>
                        <td colspan="3">
                            <asp:Label ID="lbl_importarea_M" runat="server"></asp:Label>
                        </td>
                    </tr>
                </table>
            </div>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="font_fullcontent">
                <tr>
                    <td align="left">
                        <font style="font-size: medium; font-style: normal; font-weight: bold;">車型資料</font>
                    </td>
                </tr>
                <tr>
                    <td>
                        <div class="tableoutcome">
                            <asp:GridView ID="gv_data" runat="server" AutoGenerateColumns="false" Width="100%"
                                CellSpacing="0" CellPadding="0" BorderWidth="0" GridLines="None" CssClass="font_fullcontent"
                                OnRowDataBound="gv_data_RowDataBound" EmptyDataText="查無資料">
                                <Columns>
                                    <asp:BoundField HeaderText="序號" DataField="item_no" ItemStyle-CssClass="lineleft"
                                        HeaderStyle-CssClass="header" ItemStyle-HorizontalAlign="Center" HeaderStyle-HorizontalAlign="Center"
                                        HeaderStyle-Width="80px" />
                                    <asp:TemplateField HeaderText="車型名稱" ItemStyle-CssClass="lineleft" HeaderStyle-CssClass="header"
                                        ItemStyle-HorizontalAlign="Center" HeaderStyle-HorizontalAlign="Center">
                                        <ItemTemplate>
                                            <asp:HyperLink ID="hlk_fullcarstylename" runat="server"></asp:HyperLink>
                                        </ItemTemplate>
                                    </asp:TemplateField>
                                    <asp:BoundField HeaderText="最大馬力" DataField="cmdg_maxhorsepower" ItemStyle-CssClass="lineleft"
                                        HeaderStyle-CssClass="header" ItemStyle-HorizontalAlign="Center" HeaderStyle-HorizontalAlign="Center"
                                        HeaderStyle-Width="80px" />
                                    <asp:BoundField HeaderText="轉速" DataField="cmdg_maxhorsepowerspeed" ItemStyle-CssClass="lineleft"
                                        HeaderStyle-CssClass="header" ItemStyle-HorizontalAlign="Center" HeaderStyle-HorizontalAlign="Center"
                                        HeaderStyle-Width="80px" />
                                    <asp:TemplateField HeaderText="代表車" ItemStyle-CssClass="lineleft" HeaderStyle-CssClass="header"
                                        ItemStyle-HorizontalAlign="Center" HeaderStyle-HorizontalAlign="Center" HeaderStyle-Width="110px">
                                        <ItemTemplate>
                                            <asp:Label ID="lbl_berepresentativevehicle" runat="server"></asp:Label>
                                        </ItemTemplate>
                                        <HeaderStyle CssClass="lineright"></HeaderStyle>
                                        <ItemStyle CssClass="lineright"></ItemStyle>
                                    </asp:TemplateField>
                                    <asp:TemplateField HeaderText="測定報告" ItemStyle-CssClass="lineleft" HeaderStyle-CssClass="header"
                                        ItemStyle-HorizontalAlign="Center" HeaderStyle-HorizontalAlign="Center" HeaderStyle-Width="110px">
                                        <ItemTemplate>
                                            <asp:HyperLink ID="hlk_testrptno" runat="server"></asp:HyperLink>
                                            <asp:Label ID="lbl_noise" runat="server"></asp:Label>
                                        </ItemTemplate>
                                        <HeaderStyle CssClass="lineright"></HeaderStyle>
                                        <ItemStyle CssClass="lineright"></ItemStyle>
                                    </asp:TemplateField>
                                </Columns>
                                <EmptyDataRowStyle ForeColor="Red" />
                            </asp:GridView>
                        </div>
                    </td>
                </tr>
            </table>
        </div>
    </div>
    <!--{* fullcontent end *}-->
    <div class="fullfooter">
    </div>
    <!--{* content end *}-->
</asp:Content>
