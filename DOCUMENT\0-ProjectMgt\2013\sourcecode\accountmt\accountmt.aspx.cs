﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;

public partial class accountmt_accountmt : BaseAdminPage
{
    wsAccountInfo wsaccount = new wsAccountInfo();
    protected void Page_Load(object sender, EventArgs e)
    {
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = 'index.aspx';</script>");
            return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2")
            {
                if (!IsPostBack)
                {
                    BindCity();
                    BindDataBase();
                }
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;

            }
        }
    }

    private void BindDataBase()
    {
        string[] radom_A = wsAccountValidate(getRadNum());
        DataSet ds_accountbase = wsaccount.getAccountBase(DeCode(Session["account"].ToString()), radom_A[0].ToString(), radom_A[1].ToString());
        if (ds_accountbase.Tables[0].Rows.Count > 0)
        {
            lbl_username.Text = ds_accountbase.Tables[0].Rows[0]["ai_username"].ToString();
            txt_usertel.Text = ds_accountbase.Tables[0].Rows[0]["ai_usertel"].ToString();
            txt_userfax.Text = ds_accountbase.Tables[0].Rows[0]["ai_userfax"].ToString();
            txt_cellphone.Text = ds_accountbase.Tables[0].Rows[0]["ai_usercellphone"].ToString();
            txt_useremail.Text = ds_accountbase.Tables[0].Rows[0]["ai_email"].ToString();
            ddl_useraddrcity.SelectedValue = ds_accountbase.Tables[0].Rows[0]["ai_useraddrcity"].ToString();
            BindZip();
            ddl_useraddrlocalarea.SelectedValue = ds_accountbase.Tables[0].Rows[0]["ai_useraddrlocalarea"].ToString();
            lbl_useraddrpostno.Text = ds_accountbase.Tables[0].Rows[0]["ai_useraddrpostno"].ToString();
            txt_useraddr.Text = ds_accountbase.Tables[0].Rows[0]["ai_useraddr"].ToString();
            lbl_lastchangedate.Text = ds_accountbase.Tables[0].Rows[0]["ai_lastchangedate"].ToString();
        }
        //顯示同步按鈕- 公司存在於CarCert,但帳號尚未建立於CarCert
        if (doCarCertCompanyCheck() && (!doCarCertReaccountCheck()))
        {
            btn_Sync.Enabled = true;
        }
        else
        {
            btn_Sync.Enabled = false;
        }
    }

    private void BindCity()
    {
        wsCodetbl wscodetbl = new wsCodetbl();
        string[] arr = wsCodetblValidate(getRadNum());
        DataSet ds_city = wscodetbl.getCityCode(arr[0].ToString(), arr[1].ToString());
        ddl_useraddrcity.DataSource = ds_city;
        ddl_useraddrcity.DataTextField = "ct_city_desc";
        ddl_useraddrcity.DataValueField = "ct_city_id";
        ddl_useraddrcity.DataBind();
        ddl_useraddrcity.Items.Insert(0, new ListItem("請選擇", "000"));
    }

    private void BindZip()
    {
        wsCodetbl wscodetbl = new wsCodetbl();
        string[] arr = wsCodetblValidate(getRadNum());
        if (ddl_useraddrcity.SelectedValue != "000")
        {
            DataSet ds_zip_account = wscodetbl.getZipCode(ddl_useraddrcity.SelectedValue, arr[0].ToString(), arr[1].ToString());
            ddl_useraddrlocalarea.DataSource = ds_zip_account;
            ddl_useraddrlocalarea.DataTextField = "ct_zipcode_desc";
            ddl_useraddrlocalarea.DataValueField = "ct_zipcode_id";
            ddl_useraddrlocalarea.DataBind();
        }
        else
        {
            ddl_useraddrlocalarea.Items.Clear();
            lbl_useraddrpostno.Text = "";
        }
    }
    protected void ddl_useraddrcity_SelectedIndexChanged(object sender, EventArgs e)
    {
        BindZip();
        lbl_useraddrpostno.Text = ddl_useraddrlocalarea.SelectedValue;
    }
    protected void ddl_useraddrlocalarea_SelectedIndexChanged(object sender, EventArgs e)
    {
        lbl_useraddrpostno.Text = ddl_useraddrlocalarea.SelectedValue;
    }
    protected void btn_Save_Click(object sender, EventArgs e)
    {

        if (ddl_useraddrcity.SelectedValue == "000")
            MessageBox.Show("請選擇縣市別");
        else
        {
            #region Accountmt特殊字元判斷-有MasterPage
            ContentPlaceHolder mpContentPlaceHolder;
            mpContentPlaceHolder = (ContentPlaceHolder)Master.FindControl("ContentPlaceHolder1");
            foreach (object ctrl in mpContentPlaceHolder.Controls)
            {
                if (ctrl is TextBox)
                {
                    TextBox objTextBox = default(TextBox);
                    objTextBox = (TextBox)ctrl;
                    if ((tbWord(objTextBox.Text)))
                    {
                        MessageBox.Show(objTextBox.Text + ":含有系統不允許的特殊字元");
                        return;
                    }
                }
            }
            #endregion

            string submit_result = "";
            string[] radom_A = wsAccountValidate(getRadNum());
            wsaccount.UpdateAccountBase(DeCode(Session["account"].ToString()), lbl_username.Text.Trim(), txt_usertel.Text.Trim(), txt_userfax.Text.Trim()
                , txt_cellphone.Text.Trim(), txt_useremail.Text.Trim(), ddl_useraddrcity.SelectedValue, ddl_useraddrlocalarea.SelectedValue, lbl_useraddrpostno.Text
                , txt_useraddr.Text.Trim(), radom_A[0].ToString(), radom_A[1].ToString());

            #region Check CarCert驗證 UpdateAccount
            connectCarCert.CarCert CarCert = new connectCarCert.CarCert();
            string[] radom_CarCert = wsCarCertValid(Convert.ToInt16(DateTime.Now.DayOfWeek));
            //帳號是否存在 CarCert
            if (doCarCertReaccountCheck())
            {
                submit_result = CarCert.UpdateAccount(Session["account"].ToString(), EnCode(lbl_username.Text.Trim()), EnCode(txt_usertel.Text.Trim()), EnCode(txt_useremail.Text.Trim()), radom_CarCert[0].ToString(), radom_CarCert[1].ToString());
                if (submit_result.Equals("修改帳號成功"))
                    submit_result = " ;同步修改資料成功!";
                else
                    submit_result = " ;同步修改資料失敗!";
            }
            #endregion

            BindDataBase();
            MessageBox.Show("使用者資訊更新已完成!" + submit_result);
        }
    }

    #region Check CarCert 公司驗證
    private bool doCarCertCompanyCheck()
    {
        connectCarCert.CarCert CarCert = new connectCarCert.CarCert();
        string[] radom_CarCert = wsCarCertValid(Convert.ToInt16(DateTime.Now.DayOfWeek));
        //公司是否存在 CarCert
        bool isCompanyCarCert = CarCert.CheckCustomer(EnCode(GetAgentIDNo()), radom_CarCert[0].ToString(), radom_CarCert[1].ToString());

        if (isCompanyCarCert)
            return true;
        else
            return false;
    }
    #endregion

    #region Check CarCert 帳號驗證
    private bool doCarCertReaccountCheck()
    {

        connectCarCert.CarCert CarCert = new connectCarCert.CarCert();
        string[] radom_CarCert = wsCarCertValid(Convert.ToInt16(DateTime.Now.DayOfWeek));
        //帳號是否存在 CarCert
        bool isReaccountCarCert = CarCert.CheckUserID(EnCode(GetAgentAccount()), radom_CarCert[0].ToString(), radom_CarCert[1].ToString());


        if (isReaccountCarCert)
            return true;
        else
            return false;
    }
    #endregion

    #region CarCert同步按鈕
    protected void btn_Sync_Click(object sender, EventArgs e)
    {
        try
        {
            string compidnoE = Session["compidno"].ToString();
            string compidno = DeCode(compidnoE);
            string submit_result = "";
            connectCarCert.CarCert CarCert = new connectCarCert.CarCert();
            string[] radom_CarCert = wsCarCertValid(Convert.ToInt16(DateTime.Now.DayOfWeek));

            //同步-新增CarCert帳號資料            
            submit_result = CarCert.InsertAccount(Session["account"].ToString(), EnCode(lbl_username.Text.Trim()), compidnoE, EnCode(txt_usertel.Text.Trim()), EnCode(txt_useremail.Text.Trim()), GetAgentPassword(), radom_CarCert[0].ToString(), radom_CarCert[1].ToString());
            //同步-CarCert個資
            if (submit_result.Equals("新增帳號成功"))
            {
                #region 信件通知CarCert

                string mailCC = ConfigurationManager.AppSettings["MailCC"].ToString();

                string[] _mailTo = new string[1];
                _mailTo[0] = ConfigurationManager.AppSettings["population_contactmail"].ToString();//ds_Account.Tables[0].Rows[0]["ai_email"].ToString();//收件人                
                string[] _mailCc = new string[1];//副本
                _mailCc[0] = "";
                string[] _mailBcc = new string[1];//密件
                _mailBcc[0] = "";
                if (Convert.ToBoolean(ConfigurationManager.AppSettings["DEBUGMode"].ToString()))
                {
                    //測試
                    _mailTo[0] = ConfigurationManager.AppSettings["MailTestTo"].ToString();
                }
                //信件通知
                SendMail sendmail = new SendMail();

                string CompName = GetAgentIDName();
                string Account = DeCode(Session["account"].ToString());

                sendmail.Mail_ApplyCarCertAccount(_mailTo, _mailCc, _mailBcc, CompName, Account, lbl_username.Text.Trim());
                #endregion
                submit_result = "同步新增帳號成功";
            }
            else
            {
                submit_result = "同步新增帳號失敗";
            }
            MessageBox.Show(submit_result);
            BindDataBase();
            return;
        }
        catch (Exception ex)
        {
            ex.Message.ToString();
            MessageBox.Show("資料庫錯誤");
            return;
        }
    }
    #endregion
}