﻿<%@ Page Title="" Language="C#" MasterPageFile="~/masterpage/MasterPage.master" AutoEventWireup="true"
    CodeFile="fixcatalog_readnoly.aspx.cs" Inherits="applydatamt_fixcatalog_readnoly" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="fullheader">
        <table width="100%" cellspacing="0" cellpadding="0" class="fullheaderinfo">
            <tr>
                <td valign="top">
                    <b>車型組(<asp:Label ID="lbl_carcompomodelno" runat="server"></asp:Label>)-修正目錄</b>
                </td>
                <td align="right" valign="top" class="font_loginInfo">
                </td>
            </tr>
        </table>
    </div>
    <div class="fullcontent">
        <div id="div_maintain" runat="server" class="formstyle">
            <table border="0" cellspacing="0" cellpadding="0" width="100%" class="font_fullcontent">
                <tr>
                    <td align="right">
                        申請編號：
                    </td>
                    <td>
                        <asp:Label ID="lbl_applyno" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        資料狀態：
                    </td>
                    <td>
                        <asp:Label ID="lbl_applystatus" runat="server"></asp:Label>
                    </td>
                </tr>
            </table>
        </div>
        <div class="tableoutcome">
            <asp:GridView ID="gv_data" runat="server" AutoGenerateColumns="false" Width="100%"
                CellSpacing="0" CellPadding="0" BorderWidth="0" GridLines="None" CssClass="font_fullcontent"
                AllowPaging="True" EmptyDataText="查無資料" 
                OnPageIndexChanging="gv_data_PageIndexChanging">
                <Columns>
                    <asp:BoundField HeaderText="項次" DataField="Itno" ItemStyle-CssClass="lineleft"
                        HeaderStyle-CssClass="header" HeaderStyle-Width="60px" ItemStyle-HorizontalAlign="Center"
                        HeaderStyle-HorizontalAlign="Center">
                        <HeaderStyle CssClass="header"></HeaderStyle>
                        <ItemStyle CssClass="lineleft"></ItemStyle>
                    </asp:BoundField>
                    <asp:BoundField HeaderText="申請型式" DataField="ct_at_desc" ItemStyle-CssClass="lineleft"
                        HeaderStyle-CssClass="header" ItemStyle-HorizontalAlign="Center" HeaderStyle-HorizontalAlign="Center">
                        <HeaderStyle CssClass="header"></HeaderStyle>
                        <ItemStyle CssClass="lineleft"></ItemStyle>
                    </asp:BoundField>
                    <asp:BoundField HeaderText="申請日期" DataField="abd_applydate" DataFormatString="{0:d}"
                        ItemStyle-CssClass="lineleft" HeaderStyle-CssClass="header" ItemStyle-HorizontalAlign="Center"
                        HeaderStyle-HorizontalAlign="Center">
                        <HeaderStyle CssClass="header"></HeaderStyle>
                        <ItemStyle CssClass="lineleft"></ItemStyle>
                    </asp:BoundField>
                    <asp:BoundField HeaderText="修改項目" DataField="fc_fixitem" ItemStyle-CssClass="lineleft"
                        HeaderStyle-CssClass="header" ItemStyle-HorizontalAlign="Center" HeaderStyle-HorizontalAlign="Center">
                        <HeaderStyle CssClass="header"></HeaderStyle>
                        <ItemStyle CssClass="lineleft"></ItemStyle>
                    </asp:BoundField>
                    <asp:BoundField HeaderText="修改前" DataField="fc_beforefix" ItemStyle-CssClass="lineleft"
                        HeaderStyle-CssClass="header" ItemStyle-HorizontalAlign="Center" HeaderStyle-HorizontalAlign="Center">
                        <HeaderStyle CssClass="header"></HeaderStyle>
                        <ItemStyle CssClass="lineleft"></ItemStyle>
                    </asp:BoundField>
                    <asp:BoundField HeaderText="修改後" DataField="fc_afterfix" ItemStyle-CssClass="lineleft"
                        HeaderStyle-CssClass="header" ItemStyle-HorizontalAlign="Center" HeaderStyle-HorizontalAlign="Center">
                        <HeaderStyle CssClass="lineright"></HeaderStyle>
                        <ItemStyle CssClass="lineright"></ItemStyle>
                    </asp:BoundField>
                </Columns>
                <EmptyDataRowStyle ForeColor="Red" />
                <PagerStyle HorizontalAlign="Center" Font-Bold="true" CssClass="S_PageCss" />
            </asp:GridView>
        </div>
        <div id="div_footer" runat="server">
            <table border="0" cellspacing="0" cellpadding="0" width="100%" class="font_fullcontent">
                <tr>
                    <td align="right">
                        資料最後修改人：
                    </td>
                    <td>
                        <asp:Label ID="lbl_modaccount" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        資料最後修改日期：
                    </td>
                    <td>
                        <asp:Label ID="lbl_moddate" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="center" colspan="4">
                        <asp:Button ID="btn_Print" runat="server" Text="列印" CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'"
                            onmouseout="this.className='btn_mouseout'" CausesValidation="False" OnClick="btn_Print_Click" />
                    </td>
                </tr>
            </table>
        </div>
    </div>
    <!--{* fullcontent end *}-->
    <div class="fullfooter">
    </div>
    <!--{* content end *}-->
</asp:Content>
