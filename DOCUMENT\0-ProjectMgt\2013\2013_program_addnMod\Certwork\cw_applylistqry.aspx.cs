﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.IO;
using System.Text;

public partial class Certwork_cw_applylistqry : BaseAdminPage
{
    protected void Page_Load(object sender, EventArgs e)
    {
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = '../accountmt/index.aspx';</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" && (GetAgentAccountGrpList().Contains("epa_adm") || GetAgentAccountGrpList().Contains("ac_adm") || GetAgentAccountGrpList().Contains("tc_adm") || GetAgentAccountGrpList().Contains("rv_adm") || GetAgentAccountGrpList().Contains("rs_adm") || GetAgentAccountGrpList().Contains("fis_adm") || GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("pg_adm")))
            {
                if (!IsPostBack)
                {
                    BindData();
                    BindDataStauts();
                    BindCarrType();
                    BindAdaptStandarddata();
                    BindTransportType();
                }
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }
    }

    public void BindData()
    {
        Cryptography crypy = new Cryptography();
        int keynum = crypy.getRadNum();
        string[] arr = crypy.wsApplylistQryValid(keynum);
        wsApplylistQry oALQ = new wsApplylistQry();
        DataTable dtList = oALQ.get_applylistqryno_list(txt_compname.Text, txt_applyno.Text, ddl_applystatus.SelectedValue, txt_enginefamily.Text, txt_factoryname.Text, txt_carstyleno.Text, ddl_cartype.SelectedValue, ddl_standarddate.SelectedValue, ddl_transporttype.SelectedValue, cal_applydate_S.GetDate, cal_applydate_E.GetDate, cal_validdate_S.GetDate, cal_validdate_E.GetDate, arr[0].ToString(), arr[1].ToString());

        gv_data.DataSource = dtList;
        gv_data.DataBind();
    }
    private void BindDataStauts()
    {
        ddl_applystatus.Items.Clear();
        wsCodetbl wscode = new wsCodetbl();
        string[] radom_cd = wsCodetblValidate(getRadNum());
        ddl_applystatus.DataSource = wscode.getApplyStatus(radom_cd[0].ToString(), radom_cd[1].ToString());
        ddl_applystatus.DataTextField = "ct_aps_desc";
        ddl_applystatus.DataValueField = "ct_aps_id";
        ddl_applystatus.DataBind();

        ddl_applystatus.Items.Insert(0, new ListItem("全部", ""));
    }

    private void BindCarrType()
    {
        ddl_cartype.Items.Clear();
        wsCodetbl wscode = new wsCodetbl();
        string[] radom_cd = wsCodetblValidate(getRadNum());
        ddl_cartype.DataSource = wscode.getCarType(radom_cd[0].ToString(), radom_cd[1].ToString());
        ddl_cartype.DataTextField = "ct_ct_desc";
        ddl_cartype.DataValueField = "ct_ct_id";
        ddl_cartype.DataBind();

        ddl_cartype.Items.Insert(0, new ListItem("全部", ""));
    }

    private void BindAdaptStandarddata()
    {
        ddl_standarddate.Items.Clear();
        wsCodetbl wscode = new wsCodetbl();
        string[] radom_cd = wsCodetblValidate(getRadNum());
        string Query = string.Empty;

        ddl_standarddate.DataSource = wscode.getStandardDate(radom_cd[0].ToString(), radom_cd[1].ToString());
        ddl_standarddate.DataTextField = "ct_sd_desc";
        ddl_standarddate.DataValueField = "ct_sd_id";
        ddl_standarddate.DataBind();
        ddl_standarddate.Items.Insert(0, new ListItem("全部", ""));
    }
    private void BindTransportType()
    {
        ddl_transporttype.Items.Clear();
        wsCodetbl wscode = new wsCodetbl();
        string[] radom_cd = wsCodetblValidate(getRadNum());
        ddl_transporttype.DataSource = wscode.getTransportTypeSearch(radom_cd[0].ToString(), radom_cd[1].ToString(), ddl_cartype.SelectedValue);
        ddl_transporttype.DataTextField = "ct_nvc_desc";
        ddl_transporttype.DataValueField = "ct_nvc_id";
        ddl_transporttype.DataBind();
        ddl_transporttype.Items.Insert(0, new ListItem("全部", ""));
    }
    protected void ddl_cartype_SelectedIndexChanged(object sender, EventArgs e)
    {
        // BindAdaptStandarddata();
        BindTransportType();

    }

    protected void gv_data_RowDataBound(object sender, GridViewRowEventArgs e)
    {

        string[] radom_AB = wsApplyInfoValidate(getRadNum());

        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            //#region 讓GridView不會自動壓縮文字
            //for (int i = 0; i < e.Row.Cells.Count; i++)
            //{
            //    e.Row.Cells[i].Width = Unit.Pixel(50);
            //    e.Row.Cells[i].Wrap = false;
            //}
            //#endregion
            HyperLink hlk_abd_applyno = (HyperLink)e.Row.FindControl("hlk_abd_applyno");
            Label lbl_applystatus = (Label)e.Row.FindControl("lbl_applystatus");
            Label lbl_adaptstandarddate = (Label)e.Row.FindControl("lbl_adaptstandarddate");
            Label lbl_vechiletype = (Label)e.Row.FindControl("lbl_vechiletype");

            string sApplystatuscode = DataBinder.Eval(e.Row.DataItem, "abd_applystatus").ToString();
            string applyno = DataBinder.Eval(e.Row.DataItem, "abd_applyno").ToString();
            string applystatus = DataBinder.Eval(e.Row.DataItem, "ct_aps_desc").ToString();
            string adaptstandarddate = DataBinder.Eval(e.Row.DataItem, "abd_adaptstandarddate").ToString();
            string str_vechiletype = DataBinder.Eval(e.Row.DataItem, "abd_vechiletype").ToString().TrimEnd(',');
            str_vechiletype = str_vechiletype.Replace("G", "汽車").Replace("M", "機車").Replace("D", "柴油車");
            adaptstandarddate = adaptstandarddate.Replace("1", "第一期").Replace("2", "第二期").Replace("3", "第三期").Replace("4", "第四期");
            HyperLink hlk_carcompomodellist = (HyperLink)e.Row.FindControl("hlk_carcompomodellist");

            hlk_carcompomodellist.NavigateUrl = "../applydatamt/applybasedata_detail.aspx?applyno=" + Server.UrlEncode(EnCode(applyno));

            hlk_carcompomodellist.ImageUrl = "../images/show.gif";
            lbl_applystatus.Text = applystatus;
            lbl_vechiletype.Text = str_vechiletype;

            lbl_adaptstandarddate.Text = adaptstandarddate;// == "1" ? "一期" : adaptstandarddate == "2" ? "二期" : adaptstandarddate == "3" ? "三期" : "四期";
            hlk_abd_applyno.Attributes["ApplyID"] = DataBinder.Eval(e.Row.DataItem, "abd_id").ToString().Trim();
            hlk_abd_applyno.Text = applyno;

            hlk_abd_applyno.NavigateUrl = "../applydatamt/applybasedata_detail.aspx?applyno=" + Server.UrlEncode(EnCode(applyno));


            //資料狀態

            if (sApplystatuscode == Library.code_status.Exe_Status.初審中.GetHashCode().ToString()
                || sApplystatuscode == Library.code_status.Exe_Status.複審中.GetHashCode().ToString()
                || sApplystatuscode == Library.code_status.Exe_Status.核閱中.GetHashCode().ToString()
                || sApplystatuscode == Library.code_status.Exe_Status.核決核閱退回.GetHashCode().ToString()
                || sApplystatuscode == Library.code_status.Exe_Status.複審核閱退回.GetHashCode().ToString())
            {
                lbl_applystatus.Text = "審核中";
            }
            else
            {
                lbl_applystatus.Text = Library.code_status.Exe_Status.填單中.GetHashCode().ToString();
            }
            lbl_applystatus.Text = applystatus;

            //審核表
            HyperLink hlk_auditsheet = (HyperLink)e.Row.FindControl("hlk_auditsheet");
            if (new FirstProcessControl().IsValidationStatus(applyno, "21") || new FirstProcessControl().IsValidationStatus(applyno, "30") || new FirstProcessControl().IsValidationStatus(applyno, "31") || new FirstProcessControl().IsValidationStatus(applyno, "32") || new FirstProcessControl().IsValidationStatus(applyno, "33") || new FirstProcessControl().IsValidationStatus(applyno, "34") || new FirstProcessControl().IsValidationStatus(applyno, "35") || new FirstProcessControl().IsValidationStatus(applyno, "36") || new FirstProcessControl().IsValidationStatus(applyno, "40") || new FirstProcessControl().IsValidationStatus(applyno, "50") || new FirstProcessControl().IsValidationStatus(applyno, "55") || new FirstProcessControl().IsValidationStatus(applyno, "70") || new FirstProcessControl().IsValidationStatus(applyno, "91") || new FirstProcessControl().IsValidationStatus(applyno, "92") || new FirstProcessControl().IsValidationStatus(applyno, "93") || new FirstProcessControl().IsValidationStatus(applyno, "99"))
            {
                hlk_auditsheet.NavigateUrl = "cw_auditsheet.aspx?p=" + Server.UrlEncode(EnCode(applyno));
                hlk_auditsheet.ImageUrl = "../images/show.gif";
            }
            //重傳合格證          
            HyperLink hlk_recert = (HyperLink)e.Row.FindControl("hlk_recert");
            if (new FirstProcessControl().IsValidationStatus(applyno, "50") || new FirstProcessControl().IsValidationStatus(applyno, "55")) //已上傳平台;歸檔
            {
                hlk_recert.NavigateUrl = "cw_filling.aspx?p=" + Server.UrlEncode(EnCode(applyno));
                hlk_recert.ImageUrl = "../images/show.gif";
            }
        }
    }
    private string compname
    {
        get
        {
            return txt_compname.Text == null ? string.Empty : DeCode(txt_compname.Text.Trim());
        }
    }
    private string applyno
    {
        get
        {
            return txt_applyno.Text == null ? string.Empty : DeCode(txt_applyno.Text.Trim());
        }
    }
    protected void btn_Search_Click(object sender, EventArgs e)
    {
        #region 特殊字元判斷-沒有MasterPage
        foreach (Control ctl in Page.Form.Controls)
        {
            Control content = (Control)ctl;
            foreach (Control c in content.Controls)
            {
                if (c is TextBox)
                {
                    TextBox objTextBox = default(TextBox);
                    objTextBox = (TextBox)c;
                    if ((tbWord(objTextBox.Text)))
                    {
                        MessageBox.Show(objTextBox.Text + ":含有系統不允許的特殊字元");
                        return;
                    }
                }
            }
        }
        #endregion

        #region 特殊字元判斷-日期
        if (tbWord(cal_applydate_S.GetDate) || tbWord(cal_applydate_E.GetDate) || tbWord(cal_validdate_S.GetDate) || tbWord(cal_validdate_E.GetDate))
        {
            MessageBox.Show("日期選項含有系統不允許的特殊字元");
            return;
        }
        #endregion
        BindData();

    }
    protected void btn_AddApply_Click(object sender, EventArgs e)
    {
        Response.Redirect("../applydatamt/applybasedata.aspx?applyno=", false);
    }
    protected void gv_data_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        gv_data.PageIndex = e.NewPageIndex;
        BindData();
    }
    protected void btn_Export_Click(object sender, EventArgs e)
    {
        //到匯出選項畫面 
        Button btn_Export = (Button)sender;
        Cryptography crypy = new Cryptography();
        int keynum = crypy.getRadNum();
        string scriptString = string.Empty;
        string s_compname = txt_compname.Text == "" ? "" : txt_compname.Text.Trim();
        string s_applyno = txt_applyno.Text == "" ? "" : txt_applyno.Text.Trim();
        string s_applystatus = ddl_applystatus.SelectedValue == "" ? "" : ddl_applystatus.SelectedValue.Trim();
        string s_enginefamily = txt_enginefamily.Text == "" ? "" : txt_enginefamily.Text.Trim();
        string s_factoryname = txt_factoryname.Text == "" ? "" : txt_factoryname.Text.Trim();
        string s_carstyleno = txt_carstyleno.Text == "" ? "" : txt_carstyleno.Text.Trim();
        string s_cartype = ddl_cartype.SelectedValue == "" ? "" : ddl_cartype.SelectedValue.Trim();
        string s_standarddate = ddl_standarddate.SelectedValue == "" ? "" : ddl_standarddate.SelectedValue.Trim();
        string s_transporttype = ddl_transporttype.SelectedValue == "" ? "" : ddl_transporttype.SelectedValue.Trim();
        string s_applydate_SDate = cal_applydate_S.GetDate == "" ? "" : cal_applydate_S.GetDate.Trim();
        string s_applydate_EDate = cal_applydate_E.GetDate == "" ? "" : cal_applydate_E.GetDate.Trim();
        string s_validdate_SDate = cal_validdate_S.GetDate == "" ? "" : cal_validdate_S.GetDate.Trim();
        string s_validdate_EDate = cal_validdate_E.GetDate == "" ? "" : cal_validdate_E.GetDate.Trim();

        scriptString = string.Format(@"var Mleft = (screen.width - 980) / 2;var Mtop = (screen.height - 800) / 2;
        window.open('cw_exportpick.aspx?compname={0}&applyno={1}&applystatus={2}&enginefamily={3}&factoryname={4}&carstyleno={5}&cartype={6}&standarddate={7}&transporttype={8}&applydate_SDate={9}&applydate_EDate={10}&validdate_SDate={11}&validdate_EDate={12}',10,'height=700,width=960,scrollbars=yes,top='+Mtop+',left='+Mleft+''); ",
            Server.UrlEncode(crypy.EnCode(s_compname)), Server.UrlEncode(crypy.EnCode(s_applyno)), Server.UrlEncode(crypy.EnCode(s_applystatus)), Server.UrlEncode(crypy.EnCode(s_enginefamily)), Server.UrlEncode(crypy.EnCode(s_factoryname)), Server.UrlEncode(crypy.EnCode(s_carstyleno)), Server.UrlEncode(crypy.EnCode(s_cartype)), Server.UrlEncode(crypy.EnCode(s_standarddate)), Server.UrlEncode(crypy.EnCode(s_transporttype)), Server.UrlEncode(crypy.EnCode(s_applydate_SDate)), Server.UrlEncode(crypy.EnCode(s_applydate_EDate)), Server.UrlEncode(crypy.EnCode(s_validdate_SDate)), Server.UrlEncode(crypy.EnCode(s_validdate_EDate)));
        this.ClientScript.RegisterStartupScript(this.GetType(), "", scriptString, true);


    }
}