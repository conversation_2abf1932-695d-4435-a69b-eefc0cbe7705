﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class accountmt_enabledpollutionlogin : System.Web.UI.Page
{
    Cryptography crypt = new Cryptography();
    protected void Page_Load(object sender, EventArgs e)
    {

    }
    protected void btn_Login_Click(object sender, EventArgs e)
    {
        try
        {
            Library.ExamineProcedure ep = new Library.ExamineProcedure();
            Session.RemoveAll();	//結束目前 Session.
            
            string compidno = txt_compidno.Text.Trim();
            string account = txt_account.Text.Trim();

            #region Accountmt特殊字元判斷-有MasterPage
            ContentPlaceHolder mpContentPlaceHolder;
            mpContentPlaceHolder = (ContentPlaceHolder)Master.FindControl("ContentPlaceHolder1");
            BaseAdminPage egpadmin = new BaseAdminPage();
            foreach (object ctrl in mpContentPlaceHolder.Controls)
            {
                if (ctrl is TextBox)
                {
                    TextBox objTextBox = default(TextBox);
                    objTextBox = (TextBox)ctrl;
                    if ((egpadmin.tbWord(objTextBox.Text)))
                    {
                        MessageBox.Show(objTextBox.Text + ":含有系統不允許的特殊字元");
                        return;
                    }
                }
            }
            #endregion

            if (ep.danger_word(compidno) || ep.danger_word(account) || ep.danger_word(txt_password.Text.Trim()))
            {
                MessageBox.Show("不可輸入 ' -- / * 等字元");
                txt_account.Text = "";
                txt_compidno.Text = "";
                txt_password.Text = "";
                return;
            }
            if (account != "" && compidno != "" && txt_password.Text.Trim() != "")
            {
                wsLoginCheck wslogin = new wsLoginCheck();
                string[] radom_L = crypt.wsCheckLogin(crypt.getRadNum());
                string password = crypt.GetPAWDHash(txt_password.Text.Trim());
                string compidnoE = crypt.EnCode(compidno);
                string accountE = crypt.EnCode(account);
                if (wslogin.AccountCheckByPollution(radom_L[0].ToString(), radom_L[1].ToString(), account, password, compidnoE))
                {
                    Session["encryfunc"] = crypt.GetEnCodeMode();
                    switch (Session["encryfunc"].ToString())
                    {
                        case "M":
                            Session["Vkey"] = crypt.GetMD5(account + crypt.GetIP());
                            break;
                        case "H":
                            Session["Vkey"] = crypt.GetHtmlEnCode(account + crypt.GetIP());
                            break;
                        case "B":
                            Session["Vkey"] = crypt.GetBig(account + crypt.GetIP());
                            break;
                        case "U":
                            Session["Vkey"] = crypt.GetUTF(account + crypt.GetIP());
                            break;
                        default:
                            break;
                    }
                    Response.Redirect(string.Format("pollutionfileupload.aspx?account={0}&compidno={1}", Server.UrlEncode(accountE), Server.UrlEncode(compidnoE)), false);
                }
                else
                {
                    MessageBox.Show("無權限使用");
                }
            }
        }
        catch
        {
        }
    }
}