﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;

public partial class accountmt_AccountCheckNG : BaseAdminPage
{
    protected void Page_Load(object sender, EventArgs e)
    {
        #region TextBox在Textmode=MultiLine狀態下MaxLength的字數限制無效
        txt_reason.Attributes.Add("maxlength", "50");
        txt_reason.Attributes.Add("onkeyup", "return ismaxlength(this)");
       
        #endregion
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.close();</script>");
            return;
        }
        else
        {
            if (Request["account"] == null || Request["username"] == null)
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.參數正確性"].ToString());
                Response.End();
                return;
            }
            if (!GetAgentAccountGrpList().Contains("cam_adm"))
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
            if (GetAgentName() == "")
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }
    }
    protected void btn_Save_Click(object sender, EventArgs e)
    {
        #region Accountmt特殊字元判斷-沒有MasterPage
        foreach (Control ctl in Page.Form.Controls)
        {
            if (ctl is TextBox)
                {
                    TextBox objTextBox = default(TextBox);
                    objTextBox = (TextBox)ctl;
                    if ((tbWord(objTextBox.Text)))
                    {
                        MessageBox.Show(objTextBox.Text + ":含有系統不允許的特殊字元");
                        return;
                    }
                }            
        }
        #endregion

        if (Session["compidno"].ToString() == "")
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.close();</script>");
            return;
        }
        if (txt_reason.Text.Length > 50)
        {
            MessageBox.Show("字數超出50個字，請確認");
            return;
        }
        if (txt_reason.Text.Trim() == "")
        {
            MessageBox.Show("請填寫原因");
            return;
        }
        wsAccountInfo wsaccount = new wsAccountInfo();
        string account = Request["account"].ToString();
        string username = Request["username"].ToString();

        if (!string.IsNullOrEmpty(account))
        {
            wsGroupInfo wsgroup = new wsGroupInfo();
            string[] radom_A = wsAccountValidate(getRadNum());
            string[] radom_G = wsGroupValidate(getRadNum());
            DataSet dsAccount = wsaccount.getAccountBase(account, radom_A[0].ToString(), radom_A[1].ToString());
            
            //email
            string[] _mailTo = new string[1];
            _mailTo[0] = dsAccount.Tables[0].Rows[0]["ai_email"].ToString();//收件人                
            string[] _mailCc = new string[1];//副本
            _mailCc[0] = "";
            string[] _mailBcc = new string[1];//密件
            _mailBcc[0] = "";
            if (Convert.ToBoolean(ConfigurationManager.AppSettings["DEBUGMode"].ToString()))
            {
                //測試
                _mailTo[0] = ConfigurationManager.AppSettings["MailTestTo"].ToString();
            }

            SendMail mail = new SendMail();
            if (mail.Mail_CheckAccountNG(_mailTo, _mailCc, _mailBcc, username, account, GetAgentName()))
            {
                //wsaccount.UpdateStatusAndCheckData(account, GetAgentAccount(), "9", txt_reason.Text.Trim(), radom_A[0].ToString(), radom_A[1].ToString());
                //******** Gemma 確定有誤，就直接將帳號退到退回TABLE
                wsApplyDeny wsapplydeny = new wsApplyDeny();
                string[] radom_D = wsApplyDenyValidate(getRadNum());
                wsapplydeny.DenyAgain(EnCode(account), GetAgentAccount(), GetIP(), txt_reason.Text.Trim(), radom_D[0].ToString(), radom_D[1].ToString());

                ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('帳號退回確認完成');
                                                    window.close();</script>");
            }
        }
    }
}