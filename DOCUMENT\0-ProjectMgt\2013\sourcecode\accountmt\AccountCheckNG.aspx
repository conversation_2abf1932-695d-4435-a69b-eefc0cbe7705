﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="AccountCheckNG.aspx.cs" Inherits="accountmt_AccountCheckNG" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <base target="_self">
    </base>
    <script type="text/javascript">
        function ismaxlength(obj) {
            var mlength = obj.getAttribute ? parseInt(obj.getAttribute("maxlength")) : "";
            if (obj.getAttribute && obj.value.length > mlength)
                obj.value = obj.value.substring(0, mlength);
        }
    </script>
    <title>帳號申請審核退件原因</title>
    <link href="../css/style.css" rel="stylesheet" type="text/css" />
</head>
<body>
    <form id="form1" runat="server">
    <div class="fullcontent">
        <div class="formstyle">
            <table border="0" cellspacing="0" cellpadding="0" class="font_fullcontent">
                <tr>
                    <td>
                        <span class="font_fulltitle"><b><span style="color: Red;">*</span>請輸入有誤原因：</b></span>
                    </td>
                </tr>
                <tr>
                    <td>
                        <asp:TextBox ID="txt_reason" runat="server" Height="56px" Style="margin-bottom: 0px"
                            TextMode="MultiLine" Width="431px" MaxLength="50"></asp:TextBox>
                        (上限50個字)
                        <asp:RequiredFieldValidator ID="rfv_reason" runat="server" ControlToValidate="txt_reason"
                            Display="None" ErrorMessage="不可空白"></asp:RequiredFieldValidator>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        <asp:Button ID="btn_Save" runat="server" Text="送出" OnClick="btn_Save_Click" CssClass="btn_mouseout"
                            onmouseover="this.className='btn_mouseover'" onmouseout="this.className='btn_mouseout'" />&nbsp;&nbsp;
                        <asp:Button ID="btn_Can" runat="server" Text="取消" OnClientClick="window.close();return false;"
                            CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'" onmouseout="this.className='btn_mouseout'" />
                    </td>
                </tr>
            </table>
        </div>
    </div>
    <asp:ValidationSummary ID="ValidationSummary1" runat="server" ShowMessageBox="True"
        ShowSummary="False" />
    </form>
</body>
</html>
