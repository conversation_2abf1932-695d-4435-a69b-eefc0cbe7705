﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Xml;
using Common;
using System.IO;

public partial class applydatamt_otherfileupload_readnoly : BaseAdminPage
{
    public DataTable dtList
    {
        get { return (DataTable)ViewState["dtList"]; }
        set { ViewState["dtList"] = value; }
    }
    protected void Page_Load(object sender, EventArgs e)
    {
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = '../accountmt/index.aspx';</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" &&
            (
            (
            GetAgentAccountGrpList().Contains("epa_adm") || GetAgentAccountGrpList().Contains("ac_adm") || GetAgentAccountGrpList().Contains("tc_adm") || GetAgentAccountGrpList().Contains("rv_adm") || GetAgentAccountGrpList().Contains("rs_adm") || GetAgentAccountGrpList().Contains("fis_adm") || GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("pg_adm")
            )
            ||
            (
              (GetAgentAccountGrpList().Contains("cga_adm") || GetAgentAccountGrpList().Contains("cam_adm")) && CheckSingleApplyDataAccessRight(DeCode(Request["applyno"].ToString()))
            )
            )
            )
            {
                if (!IsPostBack)
                {
                    if (Request["applyno"].ToString() != "")
                    {
                        Cryptography crypy = new Cryptography();
                        int keynum = crypy.getRadNum();
                        string[] Warr = crypy.wsApplybasedataDetailValid(keynum);
                        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
                        DataTable dt_applydatadetail = wsApplyData.get_applybasedate_detail(crypy.DeCode(Request["applyno"].ToString()), Warr[0].ToString(), Warr[1].ToString());
                        if (dt_applydatadetail.Rows.Count > 0)
                        {
                            //if (dt_applydatadetail.Rows[0]["abd_databelongcompidno"].ToString() == Session["compidno"].ToString())
                            //{
                            BindData();

                            dt_applydatadetail.Dispose();
                            //}
                            //else
                            //{
                            //    dt_applydatadetail.Dispose();
                            //    Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                            //    Response.End();
                            //}
                        }
                        else
                        {
                            dt_applydatadetail.Dispose();
                            Response.Write("發生取不到申請編號錯誤，系統將導回首頁！！");
                            Response.Redirect("../index.aspx");
                        }
                    }
                    else
                    {
                        Response.Write("發生取不到申請編號錯誤，系統將導回首頁！！");
                        Response.Redirect("../index.aspx");
                    }
                }
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }
    }

    #region 更改的麵包屑傳入的參數
    protected void Page_LoadComplete(object sender, EventArgs e)
    {
        ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text = ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text.Replace("applydatamt/applybasedata_detail.aspx", "applydatamt/applybasedata_detail.aspx?applyno=" + Server.UrlEncode(Request["applyno"].ToString()));
    }
    #endregion

    public void BindData()
    {
        wsOtherFileupload oOFD = new wsOtherFileupload();
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        Cryptography crypy = new Cryptography();
        int keynum = crypy.getRadNum();
        string[] arr = crypy.wsOtherFileuploadValid(keynum);
        string[] Warr = crypy.wsApplybasedataDetailValid(keynum);
        string applyno = DeCode(Request["applyno"].ToString());
        string carcompomodelno = DeCode(Request["carcompomodelno"].ToString());

        lab_applyno.Text = applyno;
        lab_carcompomodelno.Text = carcompomodelno;

        DataTable dtItem = oOFD.get_carcompomodel_Item(applyno.ToString().Trim(), carcompomodelno, arr[0].ToString(), arr[1].ToString());
        DataTable dt_carmodelnames = wsApplyData.get_Nv_carmodelnames(applyno, carcompomodelno, Warr[0].ToString(), Warr[1].ToString());
        if (dt_carmodelnames.Rows.Count > 0)
        {
            foreach (DataRow dr in dt_carmodelnames.Rows)
            {
                if (dr["cmdg_berepresentativevehicle"].ToString().Trim().Equals("Y", StringComparison.CurrentCultureIgnoreCase))
                {
                    lab_carengmodelname.Text = dr["fullcarstylename"].ToString();
                }
            }
        }
        DataTable dt_applystatus = wsApplyData.get_applystatus(applyno, carcompomodelno, Warr[0].ToString(), Warr[1].ToString());
        if (dt_applystatus.Rows.Count > 0)
        {
            lab_applystatus.Text = dt_applystatus.Rows[0]["ct_aps_desc"].ToString();
        }
        dtList = oOFD.get_otherfileupload_list(dtItem.Rows[0]["abd_isimport"].ToString(), dtItem.Rows[0]["abd_vechiletype"].ToString(), dtItem.Rows[0]["abd_powerfrom"].ToString(), dtItem.Rows[0]["cpm_applytype"].ToString(), arr[0].ToString(), arr[1].ToString());
        gv_data.DataSource = dtList;
        gv_data.DataBind();


    }
    protected void gv_data_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        wsOtherFileupload oOFD = new wsOtherFileupload();
        Cryptography crypy = new Cryptography();
        int keynum = crypy.getRadNum();
        string[] Carr = crypy.wsOtherFileuploadValid(keynum);
        string applyno = DeCode(Request["applyno"].ToString());
        string carcompomodelno = DeCode(Request["carcompomodelno"].ToString());
        Repeater rpt_file = (Repeater)e.Row.FindControl("rpt_file");

        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            string sfiletype = string.Empty;
            sfiletype = DataBinder.Eval(e.Row.DataItem, "vil_filetype").ToString().Trim();
            DataTable dt_otherfilename = oOFD.get_otherfilenameForReadnoly(applyno, carcompomodelno, sfiletype, Carr[0].ToString(), Carr[1].ToString());
            rpt_file.DataSource = dt_otherfilename;
            rpt_file.DataBind();
        }
    }
    protected void rpt_file_ItemDataBound(object sender, RepeaterItemEventArgs e)
    {
        LinkButton lbtn_file = (LinkButton)e.Item.FindControl("lbtn_file");
        Label lbl_fileDate = (Label)e.Item.FindControl("lbl_fileDate");

        if (e.Item.ItemType == ListItemType.Item || e.Item.ItemType == ListItemType.AlternatingItem)
        {
            lbtn_file.Text = DataBinder.Eval(e.Item.DataItem, "afi_userfilename").ToString().Trim();
            lbtn_file.Attributes["afi_fileid"] = DataBinder.Eval(e.Item.DataItem, "afi_fileid").ToString().Trim();
            lbtn_file.Attributes.Add("onclick", "javascript:window.open('../FileDownloadSeal.aspx?File=" + Server.UrlEncode(EnCode(DataBinder.Eval(e.Item.DataItem, "afi_fileid").ToString().Trim())) + "','_blank');");
            lbl_fileDate.Text = " [" + DateTime.Parse(DataBinder.Eval(e.Item.DataItem, "afi_uploaddate").ToString()).ToString("yyyy/MM/dd hh:mm") + "]";
        }
    }

    protected void gv_data_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        gv_data.PageIndex = e.NewPageIndex;
        BindData();
    }
}