﻿<%@ Page Title="" Language="C#" MasterPageFile="~/masterpage/MasterPage.master" AutoEventWireup="true" CodeFile="applyaccountvalidaionlist.aspx.cs" Inherits="accountmt_applyaccountvalidaionlist" %>

<%@ Register src="../usercontrol/GetCalender.ascx" tagname="GetCalender" tagprefix="uc1" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" Runat="Server">
<script type="text/javascript" language="javascript">
    function KeyPressCheckCap() {
        if (event.keyCode < 65 || event.keyCode > 90) {
            if (event.keyCode >= 97 && event.keyCode <= 122)
                event.keyCode = event.keyCode - 32;
        }
    }
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
 <div class="fullheader">
        <table width="100%" cellspacing="0" cellpadding="0" class="fullheaderinfo">
            <tr>
                <td valign="top">
                    <b>使用者帳號審核查詢</b>
                </td>
                <td align="right" valign="top" class="font_loginInfo">
                </td>
            </tr>
        </table>
    </div>
    <!--{* fullheader end *}-->
    <div class="fullcontent">
        <table border="0" cellspacing="0" cellpadding="0" width="100%" class="font_fullcontent">
            <tr>
                <td align="right">
                    統一編號：
                </td>
                <td>
                    <asp:TextBox ID="txt_compidno" runat="server" Width="160px"></asp:TextBox>
                </td>
                <td align="right">
                    狀態：
                </td>
                <td colspan="2">
                    <asp:DropDownList ID="ddl_accountstatus" runat="server">
                    </asp:DropDownList>
                   
                    </td>
            </tr>
            <tr>
                <td align="right">
                    申請日期：
                </td>
                <td>            
                    <uc1:GetCalender ID="cal_applyS" runat="server" DateFormat="yyyyMMdd" />~
                    <uc1:GetCalender ID="cal_applyE" runat="server" DateFormat="yyyyMMdd" />
                </td>
                <td align="right">
                    公司名稱：
                </td>
                <td>
                    <asp:TextBox ID="txt_compname" runat="server" Width="268px"></asp:TextBox>
                </td>
                <td>
                    <asp:Button ID="btn_Search" runat="server" Text="查詢" OnClick="btn_Search_Click" 
                    CssClass="btn_mouseout"
                        onmouseover="this.className='btn_mouseover'" onmouseout="this.className='btn_mouseout'"/>
                </td>
            </tr>
        </table>
        <div class="tableoutcome">
        <asp:GridView ID="gv_Data" runat="server" AutoGenerateColumns="false" AllowPaging="True"
            AllowSorting="True" PageSize="10" OnRowDataBound="gv_Data_RowDataBound" OnSorting="gv_Data_Sorting"
            OnPageIndexChanging="gv_Data_PageIndexChanging" Width="100%" CellSpacing="0" CellPadding="0" BorderWidth="0" GridLines="None"
                CssClass="font_fullcontent">
            <Columns>
                <asp:TemplateField HeaderText="編輯">
                    <ItemTemplate>
                        <asp:Button ID="btn_Edit" runat="server" Text="編輯" OnClick="Edit_Click" CssClass="btn_mouseout"
                        onmouseover="this.className='btn_mouseover'" onmouseout="this.className='btn_mouseout'"/>
                    </ItemTemplate>
                    <ItemStyle CssClass="content" />
                </asp:TemplateField>
                <asp:TemplateField HeaderText="統一編號" SortExpression="compidno" HeaderStyle-Width="80px" ItemStyle-CssClass="lineleft"  HeaderStyle-CssClass="header">
                    <ItemTemplate>
                        <asp:Label ID="lbl_compidno" runat="server"></asp:Label>
                    </ItemTemplate>
                </asp:TemplateField>
                <asp:BoundField HeaderText="申請日期" DataField="applydate" SortExpression="applydate" ItemStyle-CssClass="lineleft"  HeaderStyle-CssClass="header"/>
                <asp:BoundField HeaderText="申請種類" DataField="kindtype" ItemStyle-CssClass="lineleft"  HeaderStyle-CssClass="header"/>
                <asp:BoundField HeaderText="公司名稱" DataField="compname" ItemStyle-CssClass="lineleft"  HeaderStyle-CssClass="header" HeaderStyle-Width="200px"/>
                <asp:BoundField HeaderText="連絡人姓名" DataField="namedata" ItemStyle-CssClass="lineleft"  HeaderStyle-CssClass="header"/>
                <asp:BoundField HeaderText="連絡人電話" DataField="teldata" ItemStyle-CssClass="lineleft"  HeaderStyle-CssClass="header"/>
                <asp:BoundField HeaderText="狀態" DataField="statusdesc" ItemStyle-CssClass="lineleft"  HeaderStyle-CssClass="header"/>
                <asp:BoundField HeaderText="核可/退件時間" DataField="enableddate" SortExpression="enableddate" ItemStyle-CssClass="lineleft"  HeaderStyle-CssClass="header"/>
            </Columns>
            <PagerStyle HorizontalAlign="Center" Font-Bold="true" CssClass="S_PageCss" />
        </asp:GridView>
        </div>
    </div>
    <!--{* fullcontent end *}-->
    <div class="fullfooter">
    </div>
    <!--{* content end *}-->
</asp:Content>

