﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;

public partial class Certwork_cw_resupplydoc : BaseAdminPage
{
    protected void Page_Load(object sender, EventArgs e)
    {
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = '../accountmt/index.aspx';</script>"); return;
        }
        else
        {
            bool access_flag = false;

            Cryptography crypy = new Cryptography();
            int keynum = crypy.getRadNum();
            wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
            string strApplyno = crypy.DeCode(Request["p"].ToString());
            string[] arr = crypy.wsApplybasedataDetailValid(keynum);

            if ((GetAgentAccountGrpList().Contains("cam_adm") || GetAgentAccountGrpList().Contains("cga_adm")))
            {
                DataTable dt_applydatadetail = wsApplyData.get_applybasedate_detail(strApplyno, arr[0].ToString(), arr[1].ToString());
                //需檢查是否為登入USER公司的資料
                if (dt_applydatadetail.Rows[0]["abd_databelongcompidno"].ToString() == Session["compidno"].ToString()) access_flag = true;
            }

            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" && (GetAgentAccountGrpList().Contains("epa_adm") || GetAgentAccountGrpList().Contains("ac_adm") || GetAgentAccountGrpList().Contains("tc_adm") || GetAgentAccountGrpList().Contains("rv_adm") || GetAgentAccountGrpList().Contains("rs_adm") || GetAgentAccountGrpList().Contains("fis_adm") || GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("pg_adm") || access_flag))
            {
                if (!IsPostBack)
                {
                    reLoadData();
                    lbl_CarCompoNo.Text = CarCompoNo;
                }
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }
    }

    private string RepairNo
    {
        get
        {
            return Request["r"] == null ? string.Empty : DeCode(Request["r"].ToString());
        }
    }

    private string CarCompoNo
    {
        get
        {
            return Request["c"] == null ? string.Empty : DeCode(Request["c"].ToString());
        }
    }

    private void reLoadData()
    {
        gv_data.DataSource = new ResupplyControl().ListResupplyDetail(RepairNo, CarCompoNo);
        gv_data.DataBind();
    }
    protected void gv_data_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            Label lbl_no = (Label)e.Row.FindControl("lbl_no");
            string sOpinion = DataBinder.Eval(e.Row.DataItem, "Opinion").ToString();
            string sItemstatus = DataBinder.Eval(e.Row.DataItem, "vcn_validationitemstatus").ToString();
            Label lbl_Opinion = (Label)e.Row.FindControl("lbl_Opinion");
            if (Convert.ToInt32(lbl_no.Text) <= 17)
            {
                lbl_Opinion.Text = sOpinion.Trim().Equals("") ? sItemstatus : sOpinion;
            }
        }
    }
}