﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="forgetpwd.aspx.cs" Inherits="accountmt_forgetpwd" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>忘記密碼</title>
    <link href="../css/style.css" rel="stylesheet" type="text/css" />
</head>
<body>
    <form id="form1" runat="server">
    <div class="fullcontent">
        <div class="formstyle">
            <table border="0" cellspacing="0" cellpadding="0" class="font_fullcontent">
                <tr>
                    <td colspan="2">
                        <span style="color: DarkRed;">請輸入使用者帳號與驗證碼，系統將重新寄發新密碼至您的電子郵件信箱。</span>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        請輸入使用者帳號：
                    </td>
                    <td>
                        <asp:TextBox ID="txt_account" runat="server" Width="200px" MaxLength="20"></asp:TextBox>
                        <asp:RequiredFieldValidator ID="rfAccount" runat="server" Display="None" ControlToValidate="txt_account"
                            ErrorMessage="使用者帳號不可空白"></asp:RequiredFieldValidator>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        請輸入驗證碼：
                    </td>
                    <td>
                        <asp:TextBox ID="txt_ValidateCode" runat="server" MaxLength="4" Width="72px"></asp:TextBox>
                        <asp:RequiredFieldValidator ID="rfValidateCode" runat="server" Display="None" ControlToValidate="txt_ValidateCode"
                            ErrorMessage="驗證碼不可空白"></asp:RequiredFieldValidator>
                        <br />
                        ＊請將下方顯示的驗證碼四位數字輸入到此欄位
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        驗證碼：
                    </td>
                    <td>
                        <img src="../Sub_Sys/validatecode.aspx" alt="驗證碼" />
                        <asp:LinkButton ID="lbtn_refresh" runat="server" Text="重取驗證碼" 
                            CausesValidation="False"></asp:LinkButton>
                    </td>
                </tr>
                <tr>
                    <td colspan="2" align="center">
                        <asp:Button ID="btn_Send" runat="server" Text="送出" OnClick="btn_Send_Click"  CssClass="btn_mouseout"
                            onmouseover="this.className='btn_mouseover'" onmouseout="this.className='btn_mouseout'"/>&nbsp;&nbsp;
                        <asp:Button ID="btn_Cancel" runat="server" Text="取消" OnClientClick="window.close();return false;"  CssClass="btn_mouseout"
                            onmouseover="this.className='btn_mouseover'" onmouseout="this.className='btn_mouseout'"/>
                    </td>
                </tr>
            </table>
        </div>
    </div>
    <asp:ValidationSummary ID="ValidationSummary1" runat="server" ShowMessageBox="True"
        ShowSummary="False" />
    </form>
</body>
</html>
