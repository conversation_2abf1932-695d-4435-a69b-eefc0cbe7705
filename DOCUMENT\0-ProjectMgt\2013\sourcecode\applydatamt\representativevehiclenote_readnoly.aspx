﻿<%@ Page Title="" Language="C#" MasterPageFile="~/masterpage/MasterPage.master" AutoEventWireup="true"
    CodeFile="representativevehiclenote_readnoly.aspx.cs" Inherits="applydatamt_representativevehiclenote_readnoly" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">

</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="fullheader">
        <table width="100%" cellspacing="0" cellpadding="0" class="fullheaderinfo">
            <tr>
                <td valign="top">
                    <b>車型組(<asp:Label ID="lbl_carcompomodelno" runat="server"></asp:Label>)-代表車選定審查表</b>
                </td>
                <td align="right" valign="top" class="font_loginInfo">
                </td>
            </tr>
        </table>
    </div>
    <!--{* fullheader end *}-->
    <div class="fullcontent">
        <div id="div_maintain" runat="server" class="formstyle">
            <table border="0" cellspacing="0" cellpadding="0" width="100%" class="font_fullcontent">
                <tr>
                    <td align="right">
                        申請編號：
                    </td>
                    <td>
                        <asp:Label ID="lbl_applyno" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        資料狀態：
                    </td>
                    <td>
                        <asp:Label ID="lbl_applystatus" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        車型年：
                    </td>
                    <td>
                        <asp:Label ID="lbl_carstyleyear" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        申請廠商：
                    </td>
                    <td>
                        <asp:Label ID="lbl_databelongcompname" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        引擎族：
                    </td>
                    <td>
                        <asp:Label ID="lbl_enginefamily" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        製造地區：
                    </td>
                    <td>
                        <asp:Label ID="lbl_contacttel" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        廠牌名稱：
                    </td>
                    <td>
                        <asp:Label ID="lbl_carbrand" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        製造廠名稱：
                    </td>
                    <td>
                        <asp:Label ID="lbl_factoryname" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        進口地區：
                    </td>
                    <td>
                        <asp:Label ID="lbl_importarea" runat="server"></asp:Label>
                    </td>
                </tr>
            </table>
        </div>
        <div class="tableoutcome">
            <asp:GridView ID="gv_carmodelrpt" runat="server" AutoGenerateColumns="false" AllowPaging="True"
                PageSize="20" Width="100%" CellSpacing="0" CellPadding="0" BorderWidth="0" GridLines="None"
                CssClass="font_fullcontent" EmptyDataText="查無資料"
                OnPageIndexChanging="gv_carmodelrpt_PageIndexChanging" 
                onrowdatabound="gv_carmodelrpt_RowDataBound">
                <Columns>
                    <asp:BoundField HeaderText="車型名稱" DataField="fullcarstylename" ItemStyle-CssClass="lineleft"
                        HeaderStyle-CssClass="header" HeaderStyle-Width="80px" />
                    <asp:TemplateField HeaderText="引擎最大馬力(kw)" ItemStyle-CssClass="lineleft" HeaderStyle-CssClass="header"
                        ItemStyle-HorizontalAlign="Center" HeaderStyle-HorizontalAlign="Center" HeaderStyle-Width="80px">
                        <ItemTemplate>
                            <asp:Label ID="lbl_maxhorsepower" runat="server"></asp:Label>
                        </ItemTemplate>
                        <HeaderStyle CssClass="lineright"></HeaderStyle>
                        <ItemStyle CssClass="lineright"></ItemStyle>
                    </asp:TemplateField> 
                    <asp:BoundField HeaderText="受驗車重(kg)" DataField="cmdg_testweight" ItemStyle-CssClass="lineleft"
                        ItemStyle-HorizontalAlign="Center" HeaderStyle-CssClass="header" HeaderStyle-Width="80px" />
                    <asp:BoundField HeaderText="測試檔位總減速比" DataField="cmdg_testgearreductionratio" ItemStyle-CssClass="lineleft"
                        ItemStyle-HorizontalAlign="Center" HeaderStyle-CssClass="header" HeaderStyle-Width="80px" />
                    <asp:BoundField HeaderText="冷卻風扇驅動方式" DataField="cmdg_coolingdrivermethod" ItemStyle-CssClass="lineleft"
                        ItemStyle-HorizontalAlign="Center" HeaderStyle-CssClass="header" HeaderStyle-Width="80px" />
                    <asp:BoundField HeaderText="輪胎數量(不含備胎)" DataField="cmdg_tiresnum" ItemStyle-CssClass="lineleft"
                        ItemStyle-HorizontalAlign="Center" HeaderStyle-CssClass="header" HeaderStyle-Width="80px" />
                    <asp:BoundField HeaderText="輪胎寬度" DataField="cmdg_tirewidth" ItemStyle-CssClass="lineleft"
                        ItemStyle-HorizontalAlign="Center" HeaderStyle-CssClass="header" HeaderStyle-Width="80px" />
                    <asp:BoundField HeaderText="排氣管開口數量" DataField="cmdg_exhaustpipenum" ItemStyle-CssClass="lineleft"
                        ItemStyle-HorizontalAlign="Center" HeaderStyle-CssClass="header" HeaderStyle-Width="80px" />
                    <asp:BoundField HeaderText="進氣方式" DataField="cmdg_inletmode" ItemStyle-CssClass="lineright"
                        ItemStyle-HorizontalAlign="Center" HeaderStyle-CssClass="lineright" HeaderStyle-Width="80px" />
                </Columns>
                <EmptyDataRowStyle ForeColor="Red" />
                <PagerStyle HorizontalAlign="Center" Font-Bold="true" CssClass="S_PageCss" />
            </asp:GridView>
        </div>
        <div id="div_subcontent" runat="server" class="formstyle">
            <table border="0" cellspacing="0" cellpadding="0" width="100%" class="font_fullcontent">
                <tr>
                    <td align="right">
                        代表車型名稱：
                    </td>
                    <td colspan="3">
                        <asp:Label ID="lbl_carengmodelname" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        車身號碼：
                    </td>
                    <td>
                        <asp:Label ID="lbl_carbodyno" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        引擎號碼：
                    </td>
                    <td>
                        <asp:Label ID="lbl_engineno" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        加速進入檔位：
                    </td>
                    <td>
                        <div>
                            <asp:Repeater ID="rpt_speedupgear" runat="server" OnItemDataBound="rpt_speedupgear_ItemDataBound">
                                <ItemTemplate>
                                    <asp:Label runat="server" ID="lbl_speedupgear"></asp:Label>
                                </ItemTemplate>
                                <SeparatorTemplate>
                                    <asp:Label ID="lbl_speedupgear_dot" runat="server" Text=";"></asp:Label>
                                </SeparatorTemplate>
                            </asp:Repeater>
                        </div>
                    </td>
                    <td align="right">
                        原地引擎轉速設定：
                    </td>
                    <td>
                        <asp:Label ID="lbl_stayrpmparam" runat="server"></asp:Label><b>rpm</b>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        加速進場車速：
                    </td>
                    <td>
                        <div>
                            <asp:Repeater ID="rpt_speed" runat="server" OnItemDataBound="rpt_speed_ItemDataBound">
                                <ItemTemplate>
                                    <asp:Label runat="server" ID="lbl_speed"></asp:Label>
                                </ItemTemplate>
                                <SeparatorTemplate>
                                    <asp:Label ID="lbl_speed_dot" runat="server" Text=";"></asp:Label>
                                </SeparatorTemplate>
                            </asp:Repeater>
                        </div>
                    </td>
                    <td align="right">
                        原地設定條件：
                    </td>
                    <td>
                        <asp:Label ID="lbl_stayrpmcondi" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        加速選定條件：
                    </td>
                    <td colspan="3">
                        <asp:Label ID="lbl_speedupcondi" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="center" colspan="4">
                        <asp:Button ID="btn_Print" runat="server" Text="列印" CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'"
                            onmouseout="this.className='btn_mouseout'" CausesValidation="False" OnClick="btn_Print_Click" />
                    </td>
                </tr>
            </table>
        </div>
    </div>
    <!--{* fullcontent end *}-->
    <div class="fullfooter">
    </div>
    <!--{* content end *}-->
</asp:Content>
