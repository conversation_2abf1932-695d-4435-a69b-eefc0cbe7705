﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Xml;
using Common;
using System.IO;

public partial class applydatamt_fixcatalog_readnoly : BaseAdminPage
{
    
    public DataTable dt_list
    {
        get { return (DataTable)ViewState["_dt_list"]; }
        set { ViewState["_dt_list"] = value; }
    }
    protected void Page_Load(object sender, EventArgs e)
    {
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = '../accountmt/index.aspx';</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" &&
                (
                (
                GetAgentAccountGrpList().Contains("epa_adm") || GetAgentAccountGrpList().Contains("ac_adm") || GetAgentAccountGrpList().Contains("tc_adm") || GetAgentAccountGrpList().Contains("rv_adm") || GetAgentAccountGrpList().Contains("rs_adm") || GetAgentAccountGrpList().Contains("fis_adm") || GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("pg_adm")
                )
                ||
                (
                  (GetAgentAccountGrpList().Contains("cga_adm") || GetAgentAccountGrpList().Contains("cam_adm")) && CheckSingleApplyDataAccessRight(DeCode(Request["applyno"].ToString()))
                )
                )
                )
            {
                if (!IsPostBack)
                {
                    if (Request["applyno"].ToString() != "")
                    {
                        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
                        Cryptography crypy = new Cryptography();
                        int keynum = crypy.getRadNum();
                        string[] arr = crypy.wsApplybasedataDetailValid(keynum);
                        string applyno = DeCode(Request["applyno"].ToString());
                        string carcompomodelno = DeCode(Request["carcompomodelno"].ToString());

                        DataTable dt_applydatadetail = wsApplyData.get_applybasedate_detail(crypy.DeCode(Request["applyno"].ToString()), arr[0].ToString(), arr[1].ToString());
                        if (dt_applydatadetail.Rows.Count > 0)
                        {
                                BindData();                                
                                dt_applydatadetail.Dispose();
                        }
                        else
                        {
                            dt_applydatadetail.Dispose();
                            Response.Write("發生取不到申請編號錯誤，系統將導回首頁！！");
                            Response.Redirect("../index.aspx");
                        }
                    }
                    else
                    {
                        Response.Write("發生取不到申請編號錯誤，系統將導回首頁！！");
                        Response.Redirect("../index.aspx");
                    }
                }
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }
    }

    #region 更改的麵包屑傳入的參數
    protected void Page_LoadComplete(object sender, EventArgs e)
    {
        ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text = ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text.Replace("applydatamt/applybasedata_detail.aspx", "applydatamt/applybasedata_detail.aspx?applyno=" + Server.UrlEncode(Request["applyno"].ToString()));
    }
    #endregion

    public void BindData()
    {
       
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        Cryptography crypy = new Cryptography();
        int keynum = crypy.getRadNum();
        string[] arr = crypy.wsApplybasedataDetailValid(keynum);
        string applyno = DeCode(Request["applyno"].ToString());
        string carcompomodelno = DeCode(Request["carcompomodelno"].ToString());
      
        lbl_applyno.Text = applyno;
        lbl_carcompomodelno.Text = carcompomodelno;

        DataTable dt_applystatus = wsApplyData.get_applystatus(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());

        if (dt_applystatus.Rows.Count > 0)
        {
            lbl_applystatus.Text = dt_applystatus.Rows[0]["ct_aps_desc"].ToString();
        }

        dt_list = wsApplyData.get_fix_catalog2(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
        if (dt_list.Rows.Count > 0)
        {
            gv_data.DataSource = dt_list;
            gv_data.DataBind();
            lbl_modaccount.Text = dt_list.Rows[0]["fc_modaccount"].ToString();
            lbl_moddate.Text = dt_list.Rows[0]["fc_moddate"].ToString(); 
        }
        else {
            lbl_modaccount.Text = "-";
            lbl_moddate.Text = "-";
        }
    }
   
    protected void gv_data_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        gv_data.PageIndex = e.NewPageIndex;
        BindData();
    }
    protected void btn_Print_Click(object sender, EventArgs e)
    {

        //到列印畫面
        string scriptString = "";
        scriptString = string.Format(@"var newwin=window.open('fixcatalog_rpt.aspx?applyno={0}&carcompomodelno={1}','winFC','scrollbars=yes');newwin.resizeTo(screen.width-20,screen.height-20) ;newwin.moveTo(0,0); ", Server.UrlEncode(Request["applyno"].ToString()), Server.UrlEncode(Request["carcompomodelno"].ToString()));
        this.ClientScript.RegisterStartupScript(this.GetType(), "", scriptString, true);

    }
}