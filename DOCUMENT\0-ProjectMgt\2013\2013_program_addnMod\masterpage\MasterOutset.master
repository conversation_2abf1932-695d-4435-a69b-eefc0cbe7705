﻿<%@ Master Language="C#" AutoEventWireup="true" CodeFile="MasterOutset.master.cs"
    Inherits="masterpage_MasterOutset" %>

<%@ Register Src="../usercontrol/uc_banner.ascx" TagName="uc_banner" TagPrefix="uc1" %>
<%@ Register Src="../usercontrol/uc_footer.ascx" TagName="uc_footer" TagPrefix="uc3" %>
<%@ Register Src="../usercontrol/uc_menu.ascx" TagName="uc_menu" TagPrefix="uc2" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>機動車輛噪音審驗電子化作業與網路申請系統</title>
    <asp:ContentPlaceHolder ID="head" runat="server">
    </asp:ContentPlaceHolder>
    <link href="../css/style.css" rel="stylesheet" type="text/css" />
    <link href="../css/navigation.css" rel="stylesheet" type="text/css" />
    <link href="../css/SelfStyle.css" rel="stylesheet" type="text/css" />
    <script type="text/javascript" src="../js/jquery-1.6.4.min.js"></script>
    <script type="text/javascript" src="../js/navigation.js"></script>
    <script type="text/javascript">
        document.oncontextmenu = function () { return false; } 
    </script>
</head>
<body>
    <form id="form1" runat="server">
    <div id="main">
        <div id="header">
            <div class="logo">
                <a href="../accountmt/index.aspx" target="_self">
                    <img src="../images/logo.png" border="0" /></a></div>
            <div id="menu">
                <div id="nav">
                    <uc2:uc_menu ID="uc_menu1" runat="server" />
                </div>
            </div>
            <!--{* menu end *}-->
            <br />
            <div class="info font-gray">
                <uc1:uc_banner ID="uc_banner1" runat="server" />
            </div>
            <!--{* info end *}-->
            <br />
            <br />
            <br />
            <br />
            <br />
            <br />
            <br />
            <br />
            <span class="font_loginInfo" style="position: absolute; margin-top: 3px; margin-left: 15px;">
                <asp:Label ID="lbl_Breadcrumbs" runat="server"></asp:Label>
            </span>
        </div>
        <!--{* header end *}-->
        <br />
        <asp:ContentPlaceHolder ID="ContentPlaceHolder1" runat="server">
        </asp:ContentPlaceHolder>
        <uc3:uc_footer ID="uc_footer1" runat="server" />
    </div>
    </form>
</body>
</html>
