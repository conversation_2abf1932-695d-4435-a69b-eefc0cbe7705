﻿<%@ Page Title="" Language="C#" MasterPageFile="~/masterpage/MasterOutset.master" AutoEventWireup="true" CodeFile="newslist.aspx.cs" Inherits="accountmt_newslist" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" Runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
 <!--{* content start *}-->
    <div class="fullheader">
        <table width="100%" cellspacing="0" cellpadding="0" class="fullheaderinfo">
            <tr>
                <td valign="top">
                    <b>
                        <asp:Label ID="lbl_Title" runat="server"></asp:Label></b>
                </td>
            </tr>
        </table>
    </div>
    <!--{* fullheader end *}-->
    <div class="fullcontent">
        <p>
            <!--{* table start *}-->
            <div class="tableoutcome">
                <asp:GridView ID="gv_news" runat="server" AutoGenerateColumns="false" Width="100%"
                    CellSpacing="0" CellPadding="0" BorderWidth="0" GridLines="None" CssClass="font_fullcontent"
                    OnRowDataBound="gv_news_RowDataBound" AllowPaging="True" PageSize="10" 
                    onpageindexchanging="gv_news_PageIndexChanging">
                    <Columns>
                        <asp:TemplateField HeaderText="日期">
                            <ItemTemplate>
                                <div class="content">
                                    <asp:Label ID="lbl_newsdate" runat="server"></asp:Label></div>
                            </ItemTemplate>
                            <ItemStyle HorizontalAlign="Center" />
                            <HeaderStyle Width="100px" />
                        </asp:TemplateField>
                        <asp:TemplateField HeaderText="標題">
                            <ItemTemplate>
                                <div class="lineleft">
                                    <asp:Label ID="lbl_title" runat="server"></asp:Label></div>
                            </ItemTemplate>
                            <HeaderStyle Width="250px" CssClass="header" />
                            <ItemStyle HorizontalAlign="Center" />
                        </asp:TemplateField>
                        <asp:TemplateField HeaderText="內容">
                            <ItemTemplate>
                                <div class="lineleft">
                                    <asp:Label ID="lbl_content" runat="server"></asp:Label></div>
                            </ItemTemplate>
                            <HeaderStyle CssClass="header" />
                        </asp:TemplateField>
                    </Columns>
                    <PagerStyle HorizontalAlign="Center" Font-Bold="true" CssClass="S_PageCss" />
                </asp:GridView>                
                <asp:GridView ID="gv_Brief" runat="server" AutoGenerateColumns="false" Width="100%"
                    CellSpacing="0" CellPadding="0" BorderWidth="0" GridLines="None" CssClass="font_fullcontent"
                    OnRowDataBound="gv_Brief_RowDataBound" AllowPaging="True" PageSize="10" 
                    onpageindexchanging="gv_Brief_PageIndexChanging">
                    <Columns>
                        <asp:TemplateField HeaderText="日期">
                            <ItemTemplate>
                                <div class="content">
                                    <asp:Label ID="lbl_newsdate" runat="server"></asp:Label></div>
                            </ItemTemplate>
                            <ItemStyle HorizontalAlign="Center" />
                            <HeaderStyle Width="100px" />
                        </asp:TemplateField>
                        <asp:TemplateField HeaderText="檔案">
                            <ItemTemplate>
                                <div class="lineleft">
                                    <asp:HyperLink ID="hlk_title" runat="server" Target="_blank"></asp:HyperLink></div>
                            </ItemTemplate>
                            <HeaderStyle CssClass="header" />
                            <ItemStyle HorizontalAlign="Center" />
                        </asp:TemplateField>
                    </Columns>
                    <PagerStyle HorizontalAlign="Center" Font-Bold="true" CssClass="S_PageCss" />
                </asp:GridView>
            </div>
            <!--{* table end *}-->
        </p>
        <br />
        <center>
            <span class="font_fullcontent">
                <img src="../images/icon_top.gif" />
                <asp:LinkButton ID="lbtn_TOP" runat="server" Text="回首頁" OnClick="lbtn_TOP_Click"></asp:LinkButton>
            </span>
        </center>
    </div>
    <!--{* fullcontent end *}-->
    <div class="fullfooter">
    </div>
    <!--{* content end *}-->
</asp:Content>

