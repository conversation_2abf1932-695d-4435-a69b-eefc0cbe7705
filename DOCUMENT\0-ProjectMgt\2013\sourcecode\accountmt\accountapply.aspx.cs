﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using System.Configuration;
using System.Drawing;
using System.IO;

public partial class accountmt_accountapply : Rac_Example.RAC_BasePage
{
    //CarCert 驗證
    wsCodetbl wscodetbl = new wsCodetbl();
    wsAccountInfo wsaccount = new wsAccountInfo();
    wsCompany wscompany = new wsCompany();
    Cryptography crypy = new Cryptography();
    wsFileInfo wsfile = new wsFileInfo();
    wsIntegrateAirPollution wsiap = new wsIntegrateAirPollution();

    public string _mailFrom, _notifyResult;
    protected void Page_Load(object sender, EventArgs e)
    {
        #region 註冊轉成大寫的 javascript
        //統一編號	
        //this.txt_compidno.Attributes["onkeypress"] = "KeyPressCheckCap();";
        txt_compidno.Attributes.Add("onkeypress", "javascript:this.value = this.value.toUpperCase();");
        txt_compidno.Attributes.Add("onblur", "javascript:this.value = this.value.toUpperCase();");
        #endregion
        if (!IsPostBack)
        {
            BindCity();
            BindCityUse();
            BindZip();
            BindZipUse();
            BindCityCertaddr();
            BindZipCertaddr();
        }
    }

    #region 取得資料
    private void BindCity()
    {
        int keynum = crypy.getRadNum();
        string[] arr = crypy.wsCodetblValidate(keynum);
        DataSet ds_city = wscodetbl.getCityCode(arr[0].ToString(), arr[1].ToString());
        //廠商端
        ddl_compaddrcity.DataSource = ds_city;
        ddl_compaddrcity.DataTextField = "ct_city_desc";
        ddl_compaddrcity.DataValueField = "ct_city_id";
        ddl_compaddrcity.DataBind();
        ddl_compaddrcity.Items.Insert(0, new ListItem("請選擇", "000"));
    }

    private void BindCityUse()
    {
        int keynum = crypy.getRadNum();
        string[] arr = crypy.wsCodetblValidate(keynum);
        DataSet ds_city = wscodetbl.getCityCode(arr[0].ToString(), arr[1].ToString());
        //帳號端
        ddl_useraddrcity.DataSource = ds_city;
        ddl_useraddrcity.DataTextField = "ct_city_desc";
        ddl_useraddrcity.DataValueField = "ct_city_id";
        ddl_useraddrcity.DataBind();
        ddl_useraddrcity.Items.Insert(0, new ListItem("請選擇", "000"));
    }

    private void BindCityCertaddr()
    {
        int keynum = crypy.getRadNum();
        string[] arr = crypy.wsCodetblValidate(keynum);
        DataSet ds_city = wscodetbl.getCityCode(arr[0].ToString(), arr[1].ToString());
        //合格證
        ddl_certaddrCity.DataSource = ds_city;
        ddl_certaddrCity.DataTextField = "ct_city_desc";
        ddl_certaddrCity.DataValueField = "ct_city_id";
        ddl_certaddrCity.DataBind();
        ddl_certaddrCity.Items.Insert(0, new ListItem("請選擇", "000"));
    }

    private void BindZip()
    {
        int keynum = crypy.getRadNum();
        string[] arr = crypy.wsCodetblValidate(keynum);
        if (ddl_compaddrcity.SelectedValue != "000")
        {
            DataSet ds_zip_company = wscodetbl.getZipCode(ddl_compaddrcity.SelectedValue, arr[0].ToString(), arr[1].ToString());
            ddl_compaddrlocalarea.DataSource = ds_zip_company;
            ddl_compaddrlocalarea.DataTextField = "ct_zipcode_desc";
            ddl_compaddrlocalarea.DataValueField = "ct_zipcode_id";
            ddl_compaddrlocalarea.DataBind();
            lbl_compaddrpostno.Text = ddl_compaddrlocalarea.SelectedValue;
        }
        else
        {
            ddl_compaddrlocalarea.Items.Clear();
            lbl_compaddrpostno.Text = "";
        }
    }

    private void BindZipCertaddr()
    {
        int keynum = crypy.getRadNum();
        string[] arr = crypy.wsCodetblValidate(keynum);
        if (ddl_certaddrCity.SelectedValue != "000")
        {
            DataSet ds_zip_account = wscodetbl.getZipCode(ddl_certaddrCity.SelectedValue, arr[0].ToString(), arr[1].ToString());

            ddl_certaddrlocalarea.DataSource = ds_zip_account;
            ddl_certaddrlocalarea.DataTextField = "ct_zipcode_desc";
            ddl_certaddrlocalarea.DataValueField = "ct_zipcode_id";
            ddl_certaddrlocalarea.DataBind();
            lbl_certaddrpostno.Text = ddl_certaddrlocalarea.SelectedValue;
        }
        else
        {
            ddl_certaddrlocalarea.Items.Clear();
            lbl_certaddrpostno.Text = "";
        }
    }

    private void BindZipUse()
    {
        wsCodetbl wscodetbl = new wsCodetbl();
        int keynum = crypy.getRadNum();
        string[] arr = crypy.wsCodetblValidate(keynum);
        if (ddl_useraddrcity.SelectedValue != "000")
        {
            DataSet ds_zip_account = wscodetbl.getZipCode(ddl_useraddrcity.SelectedValue, arr[0].ToString(), arr[1].ToString());
            ddl_useraddrlocalarea.DataSource = ds_zip_account;
            ddl_useraddrlocalarea.DataTextField = "ct_zipcode_desc";
            ddl_useraddrlocalarea.DataValueField = "ct_zipcode_id";
            ddl_useraddrlocalarea.DataBind();
            lbl_useraddrpostno.Text = ddl_useraddrlocalarea.SelectedValue;
        }
        else
        {
            ddl_useraddrlocalarea.Items.Clear();
            lbl_useraddrpostno.Text = "";
        }
    }
    #endregion

    protected void btn_apply_Click(object sender, EventArgs e)
    {
        #region Accountmt特殊字元判斷-有MasterPage
        ContentPlaceHolder mpContentPlaceHolder;
        mpContentPlaceHolder = (ContentPlaceHolder)Master.FindControl("ContentPlaceHolder1");
        BaseAdminPage egpadmin = new BaseAdminPage();
        foreach (object ctrl in mpContentPlaceHolder.Controls)
        {
            if (ctrl is TextBox)
            {
                TextBox objTextBox = default(TextBox);
                objTextBox = (TextBox)ctrl;
                if ((egpadmin.tbWord(objTextBox.Text)))
                {
                    MessageBox.Show(objTextBox.Text + ":含有系統不允許的特殊字元");
                    return;
                }
            }
        }
        #endregion

        #region 辨識文件類型
        if (fu_compDoc.FileName.Length > 0)
        {
            string subFileName = System.IO.Path.GetExtension(fu_compDoc.PostedFile.FileName).Substring(1);
            if (!subFileName.Equals("pdf", StringComparison.CurrentCultureIgnoreCase))
            {
                MessageBox.Show("您上傳的檔案為" + subFileName + "，請改上傳PDF檔\n");
                return;
            }

        }
        if (fu_compReg.FileName.Length > 0)
        {
            string subFileName = System.IO.Path.GetExtension(fu_compReg.PostedFile.FileName).Substring(1);
            if (!subFileName.Equals("pdf", StringComparison.CurrentCultureIgnoreCase))
            {
                MessageBox.Show("您上傳的檔案為" + subFileName + "，請改上傳PDF檔\n");
                return;
            }
        }
        if (fu_proxyLetter.FileName.Length > 0)
        {
            string subFileName = System.IO.Path.GetExtension(fu_proxyLetter.PostedFile.FileName).Substring(1);
            if (!subFileName.Equals("pdf", StringComparison.CurrentCultureIgnoreCase))
            {
                MessageBox.Show("您上傳的檔案為" + subFileName + "，請改上傳PDF檔\n");
                return;
            }
        }
        if (fu_importersDoc.FileName.Length > 0)
        {
            string subFileName = System.IO.Path.GetExtension(fu_importersDoc.PostedFile.FileName).Substring(1);
            if (!subFileName.Equals("pdf", StringComparison.CurrentCultureIgnoreCase))
            {
                MessageBox.Show("您上傳的檔案為" + subFileName + "，請改上傳PDF檔\n");
                return;
            }
        }
        #endregion

        #region 辨識個資聲明
        if (cbx_isperson.Checked == false)
        {
            MessageBox.Show("您未勾選個資同意聲明\n");
            return;
        }
        #endregion
        string[] radom_A = crypy.wsAccountValidate(crypy.getRadNum());
        if (!wsaccount.OnlyAndNotApprovebyComp(crypy.EnCode(txt_compidno.Text.Trim()), radom_A[0].ToString(), radom_A[1].ToString()))
        {
            MessageBox.Show("此統一編號已申請過，但尚未有「已核發」帳號，不可再申請新帳號，待帳號核發完成!");
            txt_compidno.Text = "";
            objectAttributes("2");
            return;
        }
        if (CheckData())
        {
            string password = crypy.GetRandomPassword(6);
            string[] radom_C = crypy.wsCompanyValidate(crypy.getRadNum());
            string[] radom_F = crypy.wsFileValidate(crypy.getRadNum());
            string[] radom_G = crypy.wsGroupValidate(crypy.getRadNum());
            string[] radom_iap = crypy.wsIntegrateAirPollutionValid(crypy.getRadNum());
            try
            {
                if (ddl_compaddrcity.Enabled)
                {
                    //新增公司和帳號
                    //尚未有公司基本資料
                    bool isReCompany = wscompany.CheckhReCompany(crypy.EnCode(txt_compidno.Text.Trim()), radom_C[0].ToString(), radom_C[1].ToString());
                    //判斷是否已有公司資料 
                    if (!isReCompany)
                    {
                        //公司編號寫入時需要加密
                        wscompany.InsertCompany(crypy.EnCode(txt_compidno.Text.Trim()), txt_compname.Text.Trim(), txt_compename.Text.Trim()
                           , txt_comptel.Text.Trim(), txt_chargeman.Text.Trim(), txt_compfax.Text.Trim()
                           , ddl_compaddrcity.SelectedValue, ddl_compaddrlocalarea.SelectedValue
                           , lbl_compaddrpostno.Text, txt_compaddr.Text.Trim()
                           , ddl_certaddrCity.SelectedValue, ddl_certaddrlocalarea.SelectedValue, lbl_certaddrpostno.Text.Trim(), txt_certaddr.Text.Trim()
                           , radom_C[0].ToString(), radom_C[1].ToString());


                        string filepath = ConfigurationManager.AppSettings["FilePath"].ToString();
                        string strUpLoadPath = filepath + "Company\\" + txt_compidno.Text.Trim() + "\\";
                        //如果上傳路徑中沒有該目錄，則自動新增
                        if (!Directory.Exists(strUpLoadPath.Substring(0, strUpLoadPath.LastIndexOf("\\"))))
                        {
                            Directory.CreateDirectory(strUpLoadPath.Substring(0, strUpLoadPath.LastIndexOf("\\")));
                        }
                        string[] strFileNm;
                        string filename1 = "";
                        string filename2 = "";
                        string newfile = "";
                        //公司證明文件
                        //原始檔名
                        strFileNm = fu_compDoc.PostedFile.FileName.Split('\\');
                        filename1 = strFileNm[strFileNm.GetUpperBound(0)];
                        //系統名稱
                        filename2 = txt_compidno.Text.Trim() + "_1" + "_" + DateTime.Now.ToString("yyyyMMddHHmmss") + "_" + filename1;
                        wsfile.InsertCompanyFile("1", wsfile.getFileSNByFileType(radom_F[0].ToString(), radom_F[1].ToString(), "1"), filename1, filename2, strUpLoadPath, "公司證明文件", txt_account.Text.Trim()
                            , crypy.EnCode(txt_compidno.Text.Trim()), radom_F[0].ToString(), radom_F[1].ToString(), "N");
                        //實體檔案上傳
                        newfile = strUpLoadPath + filename2;
                        fu_compDoc.PostedFile.SaveAs(newfile);
                        //工廠登記證   
                        if (fu_compReg.FileName.Length > 0)
                        {
                            strFileNm = fu_compReg.PostedFile.FileName.Split('\\');
                            filename1 = strFileNm[strFileNm.GetUpperBound(0)];
                            filename2 = txt_compidno.Text.Trim() + "_2" + "_" + DateTime.Now.ToString("yyyyMMddHHmmss") + "_" + filename1;
                            wsfile.InsertCompanyFile("2", wsfile.getFileSNByFileType(radom_F[0].ToString(), radom_F[1].ToString(), "2"), filename1, filename2, strUpLoadPath, "工廠登記證", txt_account.Text.Trim()
                                , crypy.EnCode(txt_compidno.Text.Trim()), radom_F[0].ToString(), radom_F[1].ToString(), "N");
                            newfile = strUpLoadPath + filename2;
                            fu_compReg.PostedFile.SaveAs(newfile);
                        }
                        //代理授權證明函
                        if (fu_proxyLetter.FileName.Length > 0)
                        {
                            strFileNm = fu_proxyLetter.PostedFile.FileName.Split('\\');
                            filename1 = strFileNm[strFileNm.GetUpperBound(0)];
                            filename2 = txt_compidno.Text.Trim() + "_3" + "_" + DateTime.Now.ToString("yyyyMMddHHmmss") + "_" + filename1;
                            wsfile.InsertCompanyFile("3", wsfile.getFileSNByFileType(radom_F[0].ToString(), radom_F[1].ToString(), "3"), filename1, filename2, strUpLoadPath, "代理授權證明函", txt_account.Text.Trim()
                                , crypy.EnCode(txt_compidno.Text.Trim()), radom_F[0].ToString(), radom_F[1].ToString(), "N");
                            newfile = strUpLoadPath + filename2;
                            fu_proxyLetter.PostedFile.SaveAs(newfile);
                        }
                        //進口商證明文件
                        if (fu_importersDoc.FileName.Length > 0)
                        {
                            strFileNm = fu_importersDoc.PostedFile.FileName.Split('\\');
                            filename1 = strFileNm[strFileNm.GetUpperBound(0)];
                            filename2 = txt_compidno.Text.Trim() + "_4" + "_" + DateTime.Now.ToString("yyyyMMddHHmmss") + "_" + filename1;
                            wsfile.InsertCompanyFile("4", wsfile.getFileSNByFileType(radom_F[0].ToString(), radom_F[1].ToString(), "4"), filename1, filename2, strUpLoadPath, "進口商證明文件", txt_account.Text.Trim()
                                , crypy.EnCode(txt_compidno.Text.Trim()), radom_F[0].ToString(), radom_F[1].ToString(), "N");
                            newfile = strUpLoadPath + filename2;
                            fu_importersDoc.PostedFile.SaveAs(newfile);
                        }
                    }
                }
                //密碼申請時寫入原始密碼(隨機)，而是在審核成功時才將密碼改成編碼過的值
                string useGMsystem = "0";
                foreach (ListItem item in ckb_usesystem.Items)
                {
                    if (item.Selected && item.Value == "1")
                        useGMsystem = "1";
                }
                wsaccount.InsertAccount(txt_account.Text.Trim(), password
                        , txt_username.Text.Trim(), txt_usertel.Text.Trim(), txt_userfax.Text.Trim(), txt_usercellphone.Text.Trim()
                        , txt_email.Text.Trim(), ddl_useraddrcity.SelectedValue, ddl_useraddrlocalarea.SelectedValue
                        , lbl_useraddrpostno.Text, txt_useraddr.Text.Trim(), "A", crypy.EnCode(txt_compidno.Text.Trim())
                        , useGMsystem, (ddl_compaddrcity.Enabled) ? "1" : "0", radom_A[0].ToString(), radom_A[1].ToString());
                //個資            
                wsaccount.InsertPersonalInfodeclare(txt_account.Text.Trim(), radom_A[0].ToString(), radom_A[1].ToString());

                //email
                string mailCC = ConfigurationManager.AppSettings["MailCC"].ToString();

                string[] _mailTo = new string[1];
                _mailTo[0] = txt_email.Text;//收件人                
                string[] _mailCc = new string[1];//副本
                _mailCc[0] = "";
                string[] _mailBcc = new string[1];//密件
                _mailBcc[0] = "";
                if (Convert.ToBoolean(ConfigurationManager.AppSettings["DEBUGMode"].ToString()))
                {
                    //測試
                    _mailTo[0] = ConfigurationManager.AppSettings["MailTestTo"].ToString();
                }

                SendMail sendmail = new SendMail();
                //公司管理者Email
                string sCompAdminEmail = wsaccount.getCompAdminEmail(radom_A[0].ToString(), radom_A[1].ToString(), crypy.EnCode(txt_compidno.Text.Trim()));
//                if (sCompAdminEmail.Length > 0)
//                {
//                    _mailCc[0] = sCompAdminEmail;
//                }
//                if (sendmail.Mail_ApplyAccount(_mailTo, _mailCc, _mailBcc, crypy.EnCode(txt_compidno.Text.Trim())))
//                {
//                    if (ddl_compaddrcity.Enabled)
//                    {
//                        ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('帳號申請已送出');
//                                                                    window.location = 'index.aspx';</script>");
//                    }
//                    else
//                    {
//                        ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('新帳號申請已送出');
//                                                                    window.location = 'index.aspx';</script>");
//                    }
//                }
//                else
//                {
//                    if (ddl_compaddrcity.Enabled)
//                    {
//                        wscompany.DelInsComp(crypy.EnCode(txt_compidno.Text.Trim()), radom_C[0].ToString(), radom_C[1].ToString());
//                        wsfile.DelInsFile(crypy.EnCode(txt_compidno.Text.Trim()), radom_F[0].ToString(), radom_F[1].ToString());
//                        //刪實體檔案
//                        DataTable dtFile = wsfile.getCompanyFileList(radom_F[0].ToString(), radom_F[1].ToString(), crypy.EnCode(txt_compidno.Text.Trim()));
//                        for (int i = 0; i < dtFile.Rows.Count; i++)
//                        {
//                            string sysfilename = dtFile.Rows[i]["afi_sysfilename"].ToString();
//                            string filepath = dtFile.Rows[i]["afi_filepath"].ToString();
//                            System.IO.File.Delete(filepath + sysfilename);
//                        }
//                    }
//                    wsaccount.DelInsAccount(txt_account.Text.Trim(), radom_A[0].ToString(), radom_A[1].ToString());
//                    MessageBox.Show("寄件失敗");
//                }
            }
            catch
            {
                if (ddl_compaddrcity.Enabled)
                {
                    wscompany.DelInsComp(crypy.EnCode(txt_compidno.Text.Trim()), radom_C[0].ToString(), radom_C[1].ToString());
                    wsfile.DelInsFile(crypy.EnCode(txt_compidno.Text.Trim()), radom_F[0].ToString(), radom_F[1].ToString());
                    //刪實體檔案
                    DataTable dtFile = wsfile.getCompanyFileList(radom_F[0].ToString(), radom_F[1].ToString(), crypy.EnCode(txt_compidno.Text.Trim()));
                    for (int i = 0; i < dtFile.Rows.Count; i++)
                    {
                        string sysfilename = dtFile.Rows[i]["afi_sysfilename"].ToString();
                        string filepath = dtFile.Rows[i]["afi_filepath"].ToString();
                        System.IO.File.Delete(filepath + sysfilename);
                    }
                }
                wsaccount.DelInsAccount(txt_account.Text.Trim(), radom_A[0].ToString(), radom_A[1].ToString());
                MessageBox.Show("新增失敗");
            }
        }
    }

    private bool CheckData()
    {
        StringBuilder sb = new StringBuilder();
        int LimitApplyAccount = Convert.ToInt32(ConfigurationManager.AppSettings["LimitApplyAccount"].ToString());


        if (lbl_appliedcount.Text != "" && Convert.ToInt32(lbl_appliedcount.Text.Trim()) >= LimitApplyAccount)
        {
            sb.Append("帳號數已達到可申請上限，不可再申請新帳號");
        }
        else
        {
            if (p_fileUpload.Visible)
            {
                //********改為非必上傳
                //if (fu_compDoc.FileName.ToString().Length == 0)
                //    sb.Append("公司證明文件為必上傳檔案!\n");
                //else
                if (fu_compDoc.FileName.ToString().Length != 0)
                {
                    string[] strFileType = fu_compDoc.PostedFile.FileName.Split('.');
                    string subFileName = strFileType[strFileType.GetUpperBound(0)];
                    if (subFileName.ToUpper() != "PDF")
                    {
                        sb.Append("檔案格式不正確\n");
                    }
                }
                if (ddl_compaddrcity.SelectedValue == "000")
                    sb.Append("請選擇公司地址縣市別\n");
            }

            if (ddl_certaddrCity.SelectedValue == "000")
                sb.Append("請選擇合格證寄發地址縣市別\n");
            if (ddl_useraddrcity.SelectedValue == "000")
                sb.Append("請選擇申請人郵寄地址縣市別\n");

            string[] radom_A = crypy.wsAccountValidate(crypy.getRadNum());

            bool isReaccount = wsaccount.CheckhReAccount(txt_account.Text.Trim(), radom_A[0].ToString(), radom_A[1].ToString());

            #region Check CarCert 驗證
            connectCarCert.CarCert CarCert = new connectCarCert.CarCert();
            string[] radom_CarCert = crypy.wsCarCertValid(Convert.ToInt16(DateTime.Now.DayOfWeek));
            #endregion
            //帳號是否存在 CarCert
            bool isReaccountCarCert = CarCert.CheckUserID(crypy.EnCode(txt_account.Text.Trim()), radom_CarCert[0].ToString(), radom_CarCert[1].ToString());
            if (isReaccount || isReaccountCarCert)
            {
                sb.Append(string.Format("{0}：此帳號已被使用，請重新輸入!\n", txt_account.Text.Trim()));
                txt_account.Text = "";
            }
        }

        if (sb.Length > 0)
        {
            MessageBox.Show(sb.ToString());
            return false;
        }
        else
        {
            return true;
        }
    }

    #region 地址
    protected void ddl_compaddrcity_SelectedIndexChanged(object sender, EventArgs e)
    {
        BindZip();
        lbl_compaddrpostno.Text = ddl_compaddrlocalarea.SelectedValue;
    }
    protected void ddl_compaddrlocalarea_SelectedIndexChanged(object sender, EventArgs e)
    {
        lbl_compaddrpostno.Text = ddl_compaddrlocalarea.SelectedValue;
    }
    protected void ddl_useraddrcity_SelectedIndexChanged(object sender, EventArgs e)
    {
        BindZipUse();
        lbl_useraddrpostno.Text = ddl_useraddrlocalarea.SelectedValue;
    }
    protected void ddl_useraddrlocalarea_SelectedIndexChanged(object sender, EventArgs e)
    {
        lbl_useraddrpostno.Text = ddl_useraddrlocalarea.SelectedValue;
    }
    protected void ddl_certaddrCity_SelectedIndexChanged(object sender, EventArgs e)
    {
        BindZipCertaddr();
        lbl_certaddrpostno.Text = ddl_certaddrlocalarea.SelectedValue;
    }
    protected void ddl_certaddrlocalarea_SelectedIndexChanged(object sender, EventArgs e)
    {
        lbl_certaddrpostno.Text = ddl_certaddrlocalarea.SelectedValue;
    }
    protected void cbx_isthesame_CheckedChanged(object sender, EventArgs e)
    {
        if (cbx_isthesame.Checked)
        {
            wsCodetbl wscodetbl = new wsCodetbl();
            int keynum = crypy.getRadNum();
            string[] arr = crypy.wsCodetblValidate(keynum);

            ddl_useraddrcity.SelectedValue = ddl_compaddrcity.SelectedValue;

            if (ddl_useraddrlocalarea.SelectedValue != "000")
            {
                DataSet ds_zip_account = wscodetbl.getZipCode(ddl_useraddrcity.SelectedValue, arr[0].ToString(), arr[1].ToString());
                ddl_useraddrlocalarea.DataSource = ds_zip_account;
                ddl_useraddrlocalarea.DataTextField = "ct_zipcode_desc";
                ddl_useraddrlocalarea.DataValueField = "ct_zipcode_id";
                ddl_useraddrlocalarea.DataBind();

                ddl_useraddrlocalarea.SelectedValue = ddl_compaddrlocalarea.SelectedValue;
                txt_useraddr.Text = txt_compaddr.Text;
                lbl_useraddrpostno.Text = lbl_compaddrpostno.Text;
            }
        }
    }
    protected void cb_certaddrthesame_CheckedChanged(object sender, EventArgs e)
    {
        if (cb_certaddrthesame.Checked)
        {
            wsCodetbl wscodetbl = new wsCodetbl();
            int keynum = crypy.getRadNum();
            string[] arr = crypy.wsCodetblValidate(keynum);

            ddl_certaddrCity.SelectedValue = ddl_compaddrcity.SelectedValue;
            if (ddl_certaddrlocalarea.SelectedValue != "000")
            {
                DataSet ds_zip_account = wscodetbl.getZipCode(ddl_certaddrCity.SelectedValue, arr[0].ToString(), arr[1].ToString());
                ddl_certaddrlocalarea.DataSource = ds_zip_account;
                ddl_certaddrlocalarea.DataTextField = "ct_zipcode_desc";
                ddl_certaddrlocalarea.DataValueField = "ct_zipcode_id";
                ddl_certaddrlocalarea.DataBind();

                ddl_certaddrlocalarea.SelectedValue = ddl_compaddrlocalarea.SelectedValue;
                txt_certaddr.Text = txt_compaddr.Text;
                lbl_certaddrpostno.Text = lbl_compaddrpostno.Text;
            }
        }
    }
    #endregion

    protected void txt_compidno_TextChanged(object sender, EventArgs e)
    {
        //string[] radom_iap = crypy.wsCarCertValid(crypy.getRadNum());
        //wsiap.InsertAccount(crypy.EnCode("o5426667"), "o5426667", crypy.EnCode("testname"), crypy.EnCode("*********"), crypy.EnCode("*********"), crypy.EnCode("**********")
        //, crypy.EnCode("<EMAIL>"), crypy.EnCode("310"), crypy.EnCode("242"), crypy.EnCode("242"), crypy.EnCode("新泰路1段43號")
        //, "e1122665", crypy.EnCode("0"), crypy.EnCode("1"), radom_iap[0].ToString(), radom_iap[1].ToString());



        if (txt_compidno.Text.Trim().Length != 8)
        {
            MessageBox.Show("統一編號不正確，請確認!");
            txt_compidno.Text = "";
            return;
        }
        else
        {
            if (txt_compidno.Text.Trim() == "********" || txt_compidno.Text.Trim() == "01802247x")
            {
                MessageBox.Show("此統一編號不可使用，請重新輸入");
                txt_compidno.Text = "";
                return;
            }
            else
            {
                string[] radom_A = crypy.wsAccountValidate(crypy.getRadNum());
                string[] radom_C = crypy.wsCompanyValidate(crypy.getRadNum());
                string[] radom_CB = crypy.wsCodetblValidate(crypy.getRadNum());

                #region Check CarCert 驗證
                connectCarCert.CarCert CarCert = new connectCarCert.CarCert();
                string[] radom_CarCert = crypy.wsCarCertValid(Convert.ToInt16(DateTime.Now.DayOfWeek));

                #endregion
                bool isCompanyCarCert = CarCert.CheckCustomer(crypy.EnCode(txt_compidno.Text.Trim()), radom_CarCert[0].ToString(), radom_CarCert[1].ToString());

                bool isReCompany = wscompany.CheckhReCompany(crypy.EnCode(txt_compidno.Text.Trim()), radom_C[0].ToString(), radom_C[1].ToString());
                bool isaccount = wsaccount.isExistsCompidno(radom_A[0].ToString(), radom_A[1].ToString(), crypy.EnCode(txt_compidno.Text.Trim()));

                if (isReCompany && isaccount)//是否已有公司資料，且有正常流程的帳號資料
                {
                    //若公司帳號只有一組，但尚未已核發已啟用，不可再申請另一組帳號
                    if (wsaccount.OnlyAndNotApprovebyComp(crypy.EnCode(txt_compidno.Text.Trim()), radom_A[0].ToString(), radom_A[1].ToString()))
                    {
                        //取得該公司或統一編號已有核發之帳號的筆數
                        int issuedaccountcount = wsaccount.hIssuedAccountbyComp(crypy.EnCode(txt_compidno.Text.Trim()), radom_A[0].ToString(), radom_A[1].ToString());
                        //已申請帳號數
                        lbl_appliedcount.Text = issuedaccountcount.ToString();

                        //取得原始廠商基本資料
                        DataSet ds_company = wscompany.getCompanyBase(crypy.EnCode(txt_compidno.Text.Trim()), radom_C[0].ToString(), radom_C[1].ToString());
                        txt_compname.Text = ds_company.Tables[0].Rows[0]["ci_compname"].ToString();
                        txt_compename.Text = ds_company.Tables[0].Rows[0]["ci_compename"].ToString();
                        txt_chargeman.Text = ds_company.Tables[0].Rows[0]["ci_chargeman"].ToString();
                        txt_comptel.Text = ds_company.Tables[0].Rows[0]["ci_comptel"].ToString();
                        txt_compfax.Text = ds_company.Tables[0].Rows[0]["ci_compfax"].ToString();
                        //公司地址
                        ddl_compaddrcity.SelectedValue = ds_company.Tables[0].Rows[0]["ci_compaddrcity"].ToString();
                        DataSet ds_zip_company = wscodetbl.getZipCode(ddl_compaddrcity.SelectedValue, radom_CB[0].ToString(), radom_CB[1].ToString());
                        ddl_compaddrlocalarea.DataSource = ds_zip_company;
                        ddl_compaddrlocalarea.DataTextField = "ct_zipcode_desc";
                        ddl_compaddrlocalarea.DataValueField = "ct_zipcode_id";
                        ddl_compaddrlocalarea.DataBind();
                        ddl_compaddrlocalarea.SelectedValue = ds_company.Tables[0].Rows[0]["ci_compaddrlocalarea"].ToString();
                        lbl_compaddrpostno.Text = ds_company.Tables[0].Rows[0]["ci_compaddrpostno"].ToString();
                        txt_compaddr.Text = ds_company.Tables[0].Rows[0]["ci_compaddr"].ToString();
                        //合格證地址
                        ddl_certaddrCity.SelectedValue = ds_company.Tables[0].Rows[0]["ci_certaddrcity"].ToString();
                        DataSet ds_zip_cert = wscodetbl.getZipCode(ddl_certaddrCity.SelectedValue, radom_CB[0].ToString(), radom_CB[1].ToString());
                        ddl_certaddrlocalarea.DataSource = ds_zip_cert;
                        ddl_certaddrlocalarea.DataTextField = "ct_zipcode_desc";
                        ddl_certaddrlocalarea.DataValueField = "ct_zipcode_id";
                        ddl_certaddrlocalarea.DataBind();
                        ddl_certaddrlocalarea.SelectedValue = ds_company.Tables[0].Rows[0]["ci_certaddrlocalarea"].ToString();
                        lbl_certaddrpostno.Text = ds_company.Tables[0].Rows[0]["ci_certaddrpostno"].ToString();
                        txt_certaddr.Text = ds_company.Tables[0].Rows[0]["ci_certaddr"].ToString();

                        //檔案
                        string[] radom_F = crypy.wsFileValidate(crypy.getRadNum());
                        DataTable dtFile = wsfile.getCompanyFileList(radom_F[0].ToString(), radom_F[1].ToString(), crypy.EnCode(txt_compidno.Text.Trim()));
                        rpt_fileList.DataSource = dtFile;
                        rpt_fileList.DataBind();

                        objectAttributes("1");
                    }
                    else
                    {
                        MessageBox.Show("此統一編號已申請過，但尚未有「已核發」帳號，不可再申請新帳號，待帳號核發完成!");
                        txt_compidno.Text = "";
                        objectAttributes("2");
                    }

                }
                else
                {
                    if (!isCompanyCarCert) //CarCert 不存在此公司資料
                    {
                        txt_compname.Text = "";
                        txt_compename.Text = ""; txt_chargeman.Text = "";
                        txt_comptel.Text = "";
                        txt_compfax.Text = "";
                        txt_compaddr.Text = "";
                        ddl_compaddrcity.SelectedValue = "000";
                        BindZip();

                        ddl_certaddrCity.SelectedValue = "000";
                        BindZipCertaddr();
                        objectAttributes("2");
                    }
                    else
                    {

                        MessageBox.Show("貴公司資料已存在污染審驗系統，請到污染審驗系統執行同步功能!");
                        txt_compidno.Text = "";
                        return;
                    }
                }

            }
        }
    }

    private void objectAttributes(string type)
    {
        if (type == "1")
        {
            //已有原公司資料
            txt_compname.ReadOnly = true;
            txt_compname.Enabled = false;
            txt_compename.ReadOnly = true;
            txt_compename.Enabled = false;
            txt_chargeman.ReadOnly = true;
            txt_chargeman.Enabled = false;
            txt_comptel.ReadOnly = true;
            txt_comptel.Enabled = false;
            txt_compfax.ReadOnly = true;
            txt_compfax.Enabled = false;

            ddl_compaddrcity.Enabled = false;
            ddl_compaddrlocalarea.Enabled = false;
            txt_compaddr.ReadOnly = true;
            txt_compaddr.Enabled = false;

            ddl_certaddrCity.Enabled = false;
            ddl_certaddrlocalarea.Enabled = false;
            txt_certaddr.ReadOnly = true;
            txt_certaddr.Enabled = false;

            //已申請帳號數
            div_applied1.Attributes.Add("style", "display:inline;");
            div_applied2.Attributes.Add("style", "display:inline;");

            //檔案
            p_fileUpload.Visible = false;
            p_filedownload.Visible = true;
        }
        else
        {
            txt_compname.Attributes.Remove("ReadOnly");
            txt_compname.Enabled = true;
            txt_compename.Attributes.Remove("ReadOnly");
            txt_compename.Enabled = true;
            txt_chargeman.Attributes.Remove("ReadOnly");
            txt_chargeman.Enabled = true;
            txt_comptel.Attributes.Remove("ReadOnly");
            txt_comptel.Enabled = true;
            txt_compfax.Attributes.Remove("ReadOnly");
            txt_compfax.Enabled = true;
            ddl_compaddrcity.Enabled = true;
            ddl_compaddrlocalarea.Enabled = true;
            txt_compaddr.Attributes.Remove("ReadOnly");
            txt_compaddr.Enabled = true;

            ddl_certaddrCity.Enabled = true;
            ddl_certaddrlocalarea.Enabled = true;
            txt_certaddr.Attributes.Remove("ReadOnly");
            txt_certaddr.Enabled = true;

            div_applied1.Attributes.Add("style", "display:none;");
            div_applied2.Attributes.Add("style", "display:none;");

            //檔案
            p_fileUpload.Visible = true;
            p_filedownload.Visible = false;

            if (txt_compidno.Text.Trim() == "")
            {
                txt_compname.Text = "";
                txt_compename.Text = "";
                txt_chargeman.Text = "";
                txt_comptel.Text = "";
                txt_compfax.Text = "";
                txt_compaddr.Text = "";
                ddl_compaddrcity.SelectedValue = "000";

                ddl_certaddrCity.SelectedValue = "000";
                txt_certaddr.Text = "";
            }
        }
    }
    protected void rpt_fileList_ItemDataBound(object sender, RepeaterItemEventArgs e)
    {
        FileInfo file;
        if (e.Item.ItemType == ListItemType.Item || e.Item.ItemType == ListItemType.AlternatingItem)
        {
            string afi_fileid = DataBinder.Eval(e.Item.DataItem, "afi_fileid").ToString();
            string afi_filepath = DataBinder.Eval(e.Item.DataItem, "afi_filepath").ToString();
            string afi_sysfilename = DataBinder.Eval(e.Item.DataItem, "afi_sysfilename").ToString();
            string afi_userfilename = DataBinder.Eval(e.Item.DataItem, "afi_userfilename").ToString();
            file = new FileInfo(afi_filepath + afi_sysfilename);
            HyperLink hlk_file = (HyperLink)e.Item.FindControl("hlk_file");
            hlk_file.Text = afi_userfilename;
            if (file.Exists)
            {
                hlk_file.NavigateUrl = "../FileDownloadSeal.aspx?File=" + Server.UrlEncode(crypy.EnCode(afi_fileid));
            }
        }
    }

    protected void btn_Disagree_Click(object sender, EventArgs e)
    {
        Response.Redirect("index.aspx" , false);
    }
}