﻿ALTER TABLE [dbo].[nv_applybasedata] ALTER COLUMN [abd_adaptstandarddate] VARCHAR (5) NULL;
ALTER TABLE [dbo].[nv_carcompomodel] ALTER COLUMN [cpm_adaptstandarddate] VARCHAR (5) NULL;
ALTER TABLE [dbo].[nv_carcompomodel] ALTER COLUMN [cpm_importarea] VARCHAR (50) NULL;
ALTER TABLE [dbo].[nv_carcompomodel]
    ADD [cpm_vechilekind]      VARCHAR (3)    NULL,
        [cpm_silencersystem]   NVARCHAR (500) NULL,
        [cpm_engineompartment] NVARCHAR (500) NULL;
ALTER TABLE [dbo].[nv_carmodeldata]
    ADD [cmdg_lockgeerratiomode]       VARCHAR (2)    NULL,
        [cmdg_lockgeerratiomodedesc]   NVARCHAR (500) NULL,
        [cmdg_lockgeerratiomodefileid] BIGINT         NULL,
        [cmdg_silencersystemfileid]    BIGINT         NULL,
        [cmdg_engineompartmentfileid]  BIGINT         NULL;


GO
PRINT N'開始重建資料表 [dbo].[nv_codetbl_gdm_vechilecategory]...';
BEGIN TRANSACTION;
SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;
SET XACT_ABORT ON;
CREATE TABLE [dbo].[tmp_ms_xx_nv_codetbl_gdm_vechilecategory] (
    [ct_vc_gdm]        VARCHAR (1)   NOT NULL,
    [ct_vc_categoryid] VARCHAR (3)   NOT NULL,
    [ct_vc_sdid]       VARCHAR (5)   NOT NULL,
    [ct_vc_beshow]     VARCHAR (1)   NOT NULL,
    [ct_vc_order]      SMALLINT      NOT NULL,
    [ct_vc_type]       CHAR (1)      NOT NULL,
    [ct_vc_carclass1]  NVARCHAR (15) NULL,
    [ct_vc_carclass2]  NVARCHAR (50) NULL,
    CONSTRAINT [tmp_ms_xx_constraint_PK_nv_codetbl_gdm_vechilecategory1] PRIMARY KEY CLUSTERED ([ct_vc_gdm] ASC, [ct_vc_categoryid] ASC, [ct_vc_sdid] ASC, [ct_vc_type] ASC)
);

IF EXISTS (SELECT TOP 1 1 
           FROM   [dbo].[nv_codetbl_gdm_vechilecategory])
    BEGIN
        INSERT INTO [dbo].[tmp_ms_xx_nv_codetbl_gdm_vechilecategory] ([ct_vc_gdm], [ct_vc_categoryid], [ct_vc_sdid], [ct_vc_type], [ct_vc_beshow], [ct_vc_order])
        SELECT   [ct_vc_gdm],
                 [ct_vc_categoryid],
                 [ct_vc_sdid],
                 [ct_vc_type],
                 [ct_vc_beshow],
                 [ct_vc_order]
        FROM     [dbo].[nv_codetbl_gdm_vechilecategory]
        ORDER BY [ct_vc_gdm] ASC, [ct_vc_categoryid] ASC, [ct_vc_sdid] ASC, [ct_vc_type] ASC;
    END

DROP TABLE [dbo].[nv_codetbl_gdm_vechilecategory];
EXECUTE sp_rename N'[dbo].[tmp_ms_xx_nv_codetbl_gdm_vechilecategory]', N'nv_codetbl_gdm_vechilecategory';
EXECUTE sp_rename N'[dbo].[tmp_ms_xx_constraint_PK_nv_codetbl_gdm_vechilecategory1]', N'PK_nv_codetbl_gdm_vechilecategory', N'OBJECT';
COMMIT TRANSACTION;
SET TRANSACTION ISOLATION LEVEL READ COMMITTED;


GO
PRINT N'開始重建資料表 [dbo].[nv_codetbl_speedupcondi]...';
GO
BEGIN TRANSACTION;
SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;
SET XACT_ABORT ON;
CREATE TABLE [dbo].[tmp_ms_xx_nv_codetbl_speedupcondi] (
    [ct_supc_id]     VARCHAR (5)    NOT NULL,
    [ct_supc_desc]   NVARCHAR (250) NULL,
    [ct_supc_beshow] CHAR (1)       NULL,
    [ct_supc_order]  SMALLINT       NULL,
    CONSTRAINT [tmp_ms_xx_constraint_PK_nv_codetbl_speedupcondi1] PRIMARY KEY CLUSTERED ([ct_supc_id] ASC)
);
IF EXISTS (SELECT TOP 1 1 
           FROM   [dbo].[nv_codetbl_speedupcondi])
    BEGIN
        INSERT INTO [dbo].[tmp_ms_xx_nv_codetbl_speedupcondi] ([ct_supc_id], [ct_supc_desc], [ct_supc_beshow], [ct_supc_order])
        SELECT   [ct_supc_id],
                 [ct_supc_desc],
                 [ct_supc_beshow],
                 [ct_supc_order]
        FROM     [dbo].[nv_codetbl_speedupcondi]
        ORDER BY [ct_supc_id] ASC;
    END
DROP TABLE [dbo].[nv_codetbl_speedupcondi];
EXECUTE sp_rename N'[dbo].[tmp_ms_xx_nv_codetbl_speedupcondi]', N'nv_codetbl_speedupcondi';
EXECUTE sp_rename N'[dbo].[tmp_ms_xx_constraint_PK_nv_codetbl_speedupcondi1]', N'PK_nv_codetbl_speedupcondi', N'OBJECT';
COMMIT TRANSACTION;
SET TRANSACTION ISOLATION LEVEL READ COMMITTED;


GO
PRINT N'開始重建資料表 [dbo].[nv_codetbl_standarddate]...';
GO
BEGIN TRANSACTION;
SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;
SET XACT_ABORT ON;
CREATE TABLE [dbo].[tmp_ms_xx_nv_codetbl_standarddate] (
    [ct_sd_id]     VARCHAR (5)   NOT NULL,
    [ct_sd_desc]   NVARCHAR (20) NULL,
    [ct_sd_beshow] CHAR (1)      NULL,
    [ct_sd_order]  SMALLINT      NULL,
    PRIMARY KEY CLUSTERED ([ct_sd_id] ASC)
);

IF EXISTS (SELECT TOP 1 1 
           FROM   [dbo].[nv_codetbl_standarddate])
    BEGIN
        INSERT INTO [dbo].[tmp_ms_xx_nv_codetbl_standarddate] ([ct_sd_id], [ct_sd_desc], [ct_sd_beshow], [ct_sd_order])
        SELECT   [ct_sd_id],
                 [ct_sd_desc],
                 [ct_sd_beshow],
                 [ct_sd_order]
        FROM     [dbo].[nv_codetbl_standarddate]
        ORDER BY [ct_sd_id] ASC;
    END

DROP TABLE [dbo].[nv_codetbl_standarddate];
EXECUTE sp_rename N'[dbo].[tmp_ms_xx_nv_codetbl_standarddate]', N'nv_codetbl_standarddate';
COMMIT TRANSACTION;
SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
GO

PRINT N'開始重建資料表 [dbo].[nv_codetbl_standarddatelawinfo]...';
GO
BEGIN TRANSACTION;

SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;

SET XACT_ABORT ON;

CREATE TABLE [dbo].[tmp_ms_xx_nv_codetbl_standarddatelawinfo] (
    [ct_sdli_id]          VARCHAR (5)    NOT NULL,
    [ct_sdli_date]        VARCHAR (8)    NOT NULL,
    [ct_sdli_desc]        NVARCHAR (100) NULL,
    [ct_sdli_beshow]      CHAR (1)       NULL,
    [ct_sdli_order]       SMALLINT       NULL,
    [ct_sdli_vechiletype] CHAR (1)       NOT NULL,
    CONSTRAINT [tmp_ms_xx_constraint_PK_nv_codetbl_standarddatelawinfo1] PRIMARY KEY CLUSTERED ([ct_sdli_id] ASC, [ct_sdli_vechiletype] ASC)
);

IF EXISTS (SELECT TOP 1 1 
           FROM   [dbo].[nv_codetbl_standarddatelawinfo])
    BEGIN
        INSERT INTO [dbo].[tmp_ms_xx_nv_codetbl_standarddatelawinfo] ([ct_sdli_id], [ct_sdli_vechiletype], [ct_sdli_date], [ct_sdli_desc], [ct_sdli_beshow], [ct_sdli_order])
        SELECT   [ct_sdli_id],
                 [ct_sdli_vechiletype],
                 [ct_sdli_date],
                 [ct_sdli_desc],
                 [ct_sdli_beshow],
                 [ct_sdli_order]
        FROM     [dbo].[nv_codetbl_standarddatelawinfo]
        ORDER BY [ct_sdli_id] ASC, [ct_sdli_vechiletype] ASC;
    END

DROP TABLE [dbo].[nv_codetbl_standarddatelawinfo];
EXECUTE sp_rename N'[dbo].[tmp_ms_xx_nv_codetbl_standarddatelawinfo]', N'nv_codetbl_standarddatelawinfo';
EXECUTE sp_rename N'[dbo].[tmp_ms_xx_constraint_PK_nv_codetbl_standarddatelawinfo1]', N'PK_nv_codetbl_standarddatelawinfo', N'OBJECT';
COMMIT TRANSACTION;
SET TRANSACTION ISOLATION LEVEL READ COMMITTED;


GO
PRINT N'開始重建資料表 [dbo].[nv_codetbl_testrptremark]...';
GO
BEGIN TRANSACTION;
SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;
SET XACT_ABORT ON;
CREATE TABLE [dbo].[tmp_ms_xx_nv_codetbl_testrptremark] (
    [ct_trr_id]          VARCHAR (3)    NOT NULL,
    [ct_trr_desc]        NVARCHAR (250) NULL,
    [ct_trr_beshow]      CHAR (1)       NULL,
    [ct_trr_order]       SMALLINT       NULL,
    [ct_trr_sdid]        VARCHAR (5)    NOT NULL,
    [ct_trr_vechiletype] VARCHAR (1)    NOT NULL,
    CONSTRAINT [tmp_ms_xx_constraint_PK_nv_codetbl_testrptremark1] PRIMARY KEY CLUSTERED ([ct_trr_id] ASC, [ct_trr_sdid] ASC, [ct_trr_vechiletype] ASC)
);
IF EXISTS (SELECT TOP 1 1 
           FROM   [dbo].[nv_codetbl_testrptremark])
    BEGIN
        INSERT INTO [dbo].[tmp_ms_xx_nv_codetbl_testrptremark] ([ct_trr_id], [ct_trr_sdid], [ct_trr_desc], [ct_trr_beshow], [ct_trr_order])
        SELECT   [ct_trr_id],
                 [ct_trr_sdid],
                 [ct_trr_desc],
                 [ct_trr_beshow],
                 [ct_trr_order]
        FROM     [dbo].[nv_codetbl_testrptremark]
        ORDER BY [ct_trr_id] ASC, [ct_trr_sdid] ASC;
    END
DROP TABLE [dbo].[nv_codetbl_testrptremark];
EXECUTE sp_rename N'[dbo].[tmp_ms_xx_nv_codetbl_testrptremark]', N'nv_codetbl_testrptremark';
EXECUTE sp_rename N'[dbo].[tmp_ms_xx_constraint_PK_nv_codetbl_testrptremark1]', N'PK_nv_codetbl_testrptremark', N'OBJECT';
COMMIT TRANSACTION;
SET TRANSACTION ISOLATION LEVEL READ COMMITTED;


GO
PRINT N'開始重建資料表 [dbo].[nv_codetbl_testweightformula]...';
GO
BEGIN TRANSACTION;
SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;
SET XACT_ABORT ON;
CREATE TABLE [dbo].[tmp_ms_xx_nv_codetbl_testweightformula] (
    [ct_twf_id]     VARCHAR (5)   NOT NULL,
    [ct_twf_desc]   NVARCHAR (50) NULL,
    [ct_twf_beshow] CHAR (1)      NULL,
    [ct_twf_order]  INT           NULL,
    CONSTRAINT [tmp_ms_xx_constraint_PK_nv_codetbl_testweightformula1] PRIMARY KEY CLUSTERED ([ct_twf_id] ASC)
);
IF EXISTS (SELECT TOP 1 1 
           FROM   [dbo].[nv_codetbl_testweightformula])
    BEGIN
        INSERT INTO [dbo].[tmp_ms_xx_nv_codetbl_testweightformula] ([ct_twf_id], [ct_twf_desc], [ct_twf_beshow], [ct_twf_order])
        SELECT   [ct_twf_id],
                 [ct_twf_desc],
                 [ct_twf_beshow],
                 [ct_twf_order]
        FROM     [dbo].[nv_codetbl_testweightformula]
        ORDER BY [ct_twf_id] ASC;
    END
DROP TABLE [dbo].[nv_codetbl_testweightformula];
EXECUTE sp_rename N'[dbo].[tmp_ms_xx_nv_codetbl_testweightformula]', N'nv_codetbl_testweightformula';
EXECUTE sp_rename N'[dbo].[tmp_ms_xx_constraint_PK_nv_codetbl_testweightformula1]', N'PK_nv_codetbl_testweightformula', N'OBJECT';
COMMIT TRANSACTION;
SET TRANSACTION ISOLATION LEVEL READ COMMITTED;


GO
PRINT N'正在更改 [dbo].[nv_noisestandarddata]...';
ALTER TABLE [dbo].[nv_noisestandarddata] ALTER COLUMN [nsd_standarddate] VARCHAR (5) NOT NULL;
ALTER TABLE [dbo].[nv_noisetestrpt] ALTER COLUMN [ntr_adaptstandarddate] VARCHAR (5) NULL;

PRINT N'正在建立 [dbo].[combine_statistical]...';
CREATE TABLE [dbo].[combine_statistical] (
    [FristValidDays]            INT            NULL,
    [vp_applyno]                VARCHAR (15)   NOT NULL,
    [abd_powerfrom]             VARCHAR (2)    NULL,
    [abd_vechiletype]           VARCHAR (1)    NULL,
    [ApplyStatusCn]             NVARCHAR (250) NULL,
    [vp_execdate]               DATETIME       NULL,
    [cpm_carcompomodelno]       VARCHAR (15)   NOT NULL,
    [ai_username]               NVARCHAR (50)  NULL,
    [abd_validatechargeaccount] VARCHAR (1)    NOT NULL
);


GO
PRINT N'正在建立 [dbo].[nv_codetbl_statisticalcode]...';
CREATE TABLE [dbo].[nv_codetbl_statisticalcode] (
    [ct_sc_id]      VARCHAR (3)    NOT NULL,
    [ct_sc_cartype] VARCHAR (3)    NOT NULL,
    [ct_sc_desc]    NVARCHAR (250) NULL,
    [ct_sc_beshow]  CHAR (1)       NULL,
    [ct_sc_order]   SMALLINT       NULL,
    CONSTRAINT [PK_nv_codetbl_statisticalcode] PRIMARY KEY CLUSTERED ([ct_sc_id] ASC)
);


GO
PRINT N'正在建立 [dbo].[nv_codetbl_vehiclekind]...';
CREATE TABLE [dbo].[nv_codetbl_vehiclekind] (
    [ct_vk_id]      VARCHAR (3)    NOT NULL,
    [ct_vk_cartype] VARCHAR (3)    NOT NULL,
    [ct_vk_desc]    NVARCHAR (250) NULL,
    [ct_vk_beshow]  CHAR (1)       NULL,
    [ct_vk_order]   SMALLINT       NULL,
    CONSTRAINT [PK_nv_codetbl_vehiclekind] PRIMARY KEY CLUSTERED ([ct_vk_id] ASC)
);


GO
PRINT N'正在更改 [dbo].[v_applydata_for_artc_oldsystem]...';
GO
ALTER view [dbo].[v_applydata_for_artc_oldsystem] as
--案號、檢測廠、車輛種類、車型年、委託客戶統編	委託客戶名稱、委託客戶簡稱、進口/國產、進口國   
select abd_applyno,cpm_testfactory,abd_vechiletype,abd_carstyleyear,abd_databelongcompidno 'compidno',abd_databelongcompname, ci_compshortname,abd_mailcertidocaddr,case abd_isimport when '1' then '進口' else '國產' end as 'abd_isimport', ct_cy_desc, 
--廠牌	中文車型	英文車型	車型代碼	排氣量	排擋方式	排檔數	車門數	其他(車型名稱補充)	車身式樣
ct_bd_desc,cmdg_carchnmodelname,cmdg_carengmodelname,cmdg_carmodelcode,replace(cpm_exhuast,'.00','') cpm_exhuast,ct_gm_desc 'cpm_transmissiontypetype',cmdg_transmissionnum,case abd_vechiletype when 'M' then '' else ct_vr_desc end as 'cmdg_cardoorcount',cmdg_carmodeladd,ct_cbsg_desc,
--噪音車型組代號	車身號碼、引擎號碼、最大馬力、最大馬力轉速、受測轉速、加速噪音值、原地噪音值、期別、車種分類、、
cpm_carcompomodelno,'' carbodyno,'' enginebodyno, cmdg_maxhorsepower,cmdg_maxhorsepowerspeed, '' testrpm,ntr_speedupnoise,ntr_staynoise,ct_sd_desc,ct_nvc_desc,
--引擎流水碼、製造廠名稱、基本引擎、負載車重、變速系統、引擎族、兩證合一申請方式
abd_enginesn,abd_factoryname,cmdg_baseenginename,cmdg_loadingweight,cmdg_transmissionmodel,abd_enginefamily,ct_at_desc,abd_twocertmethod,
--備註、車型序號、核准日期、合格證檔案ID、合格證檔案位置、是否為代表車
'' remark,'' carsn,abd_validateddate,abd_certdocfileid,'' certdocfilepath,cmdg_berepresentativevehicle

from nv_applybasedata
join nv_carcompomodel on cpm_applyno=abd_applyno
join nv_carmodeldata on cmdg_applyno=cpm_applyno and cmdg_carcompomodelno=cpm_carcompomodelno
join nv_codetbl_brand on ct_bd_id=cmdg_carbrand
join nv_companyinfo on ci_compidno=abd_databelongcompidno
left join nv_noisetestrpt on ntr_carmodelno = cmdg_carmodelno
join nv_codetbl_standarddatelawinfo on cpm_adaptstandarddate=ct_sdli_id and abd_vechiletype=ct_sdli_vechiletype
join nv_codetbl_vehicledoortype on ct_vr_id=cmdg_cardoorcount --車門
left join nv_codetbl_country on ct_cy_id = cpm_importarea -- 進口國ct_cy_desc
left join nv_codetbl_applytype on ct_at_id = cpm_applytype --申請型式ct_at_desc
left join nv_codetbl_standarddate ON ct_sd_id = cpm_adaptstandarddate --適用期別
left join nv_codetbl_carbodyshape_g on ct_cbsg_id = cmdg_carbodyshape --車身樣式
left join nv_codetbl_gearmethod on ct_gm_id = cmdg_transmissiontype --排檔型式 ct_gm_desc
left join nv_codetbl_noisevechilecategory on ct_nvc_id =cmdg_cartype --車輛種類 ct_ct_desc 
where abd_applystatus='50'
GO

PRINT N'正在重新整理 [dbo].[vw_addrbarcode]...';
GO
EXECUTE sp_refreshsqlmodule N'[dbo].[vw_addrbarcode]';
GO

PRINT N'正在更改 [dbo].[vw_codetbllist]...';
GO



PRINT N'正在更改 [dbo].[fn_FullCarstyleNameStr]...';

GO
--車型名稱，10個欄位組合而成 (廠牌+中文車型+英文車型＋車型代碼＋排氣量＋排檔方式＋排檔數＋車門數(機車沒有)＋其他＋車身式樣)
ALTER function [dbo].[fn_FullCarstyleNameStr](@applyno varchar(9),@carcompomodelno varchar(15),@carmodelno varchar(36))
returns nvarchar(250)
begin
	declare @cn nvarchar(250)
	declare @powerfrom varchar(1)
	select @powerfrom = abd_powerfrom from nv_applybasedata where abd_applyno=@applyno
	
if(exists(select abd_applyno from nv_applybasedata where abd_applyno = @applyno and abd_vechiletype = 'G'))
begin 

	select @cn=replace(replace(ct_bd_desc + ' '+ nv_carmodeldata.cmdg_carchnmodelname+ ' '+nv_carmodeldata.cmdg_carengmodelname+ ' '+nv_carmodeldata.cmdg_carmodelcode+ ' '+ case ISNULL(@powerfrom,'') when '3' then '' else ISNULL(convert(varchar,nv_carcompomodel.cpm_exhuast),' ') +'c.c.' end+' '+ct_gm_desc+case ISNULL(ct_gm_desc,'') when 'CVT' then '' else convert(varchar,nv_carmodeldata.cmdg_transmissionnum) end+' '+ct_vr_desc +' '+nv_carmodeldata.cmdg_carmodeladd+' '+CASE WHEN ct_cbsg_desc = '其他' THEN replace(ct_cbsg_desc,'其他','') +' '+nv_carmodeldata.cmdg_carbodyshapedesc ELSE replace(ct_cbsg_desc,'-','') END,'  ',' '),'.00','')
	from nv_carmodeldata
	join nv_carcompomodel on cmdg_applyno=cpm_applyno and cmdg_carcompomodelno = cpm_carcompomodelno 
	join nv_codetbl_brand on cmdg_carbrand = ct_bd_id --廠牌
	join nv_codetbl_vehicledoortype on cmdg_cardoorcount =ct_vr_id     --車門數
	join dbo.nv_codetbl_carbodyshape_g on ct_cbsg_id = cmdg_carbodyshape	--車身樣式
	join nv_codetbl_gearmethod on cmdg_transmissiontype = ct_gm_id  --排檔型式ct_gm_desc
	where cmdg_applyno=@applyno and cmdg_carcompomodelno=@carcompomodelno and cmdg_carmodelno=@carmodelno
end
if(exists(select abd_applyno from nv_applybasedata where abd_applyno = @applyno and abd_vechiletype= 'D' ))
begin 

	select @cn=replace(replace(ct_bd_desc + ' '+ nv_carmodeldata.cmdg_carchnmodelname+ ' '+nv_carmodeldata.cmdg_carengmodelname+ ' '+nv_carmodeldata.cmdg_carmodelcode+ ' '+ case ISNULL(@powerfrom,'') when '3' then '' else ISNULL(convert(varchar,nv_carcompomodel.cpm_exhuast),' ') +'c.c.' end+' '+ct_gm_desc+case ISNULL(ct_gm_desc,'') when 'CVT' then '' else convert(varchar,nv_carmodeldata.cmdg_transmissionnum) end+' '+ct_vr_desc +' '+nv_carmodeldata.cmdg_carmodeladd+' '+CASE WHEN ct_cbsd_desc = '其他' THEN replace(ct_cbsd_desc,'其他','') +' '+nv_carmodeldata.cmdg_carbodyshapedesc ELSE replace(ct_cbsd_desc,'-','') END,'  ',' '),'.00','')
	from nv_carmodeldata
	join nv_carcompomodel on cmdg_applyno=cpm_applyno and cmdg_carcompomodelno = cpm_carcompomodelno 
	join nv_codetbl_brand on cmdg_carbrand = ct_bd_id --廠牌
	join nv_codetbl_vehicledoortype on cmdg_cardoorcount =ct_vr_id     --車門數
	join dbo.nv_codetbl_carbodyshape_d on ct_cbsd_id = cmdg_carbodyshape	--車身樣式
	join nv_codetbl_gearmethod on cmdg_transmissiontype = ct_gm_id  --排檔型式ct_gm_desc
	where cmdg_applyno=@applyno and cmdg_carcompomodelno=@carcompomodelno and cmdg_carmodelno=@carmodelno
end
if(exists(select abd_applyno from nv_applybasedata where abd_applyno = @applyno and abd_vechiletype = 'M' ))
begin 

	select @cn= replace(replace(ct_bd_desc + ' '+ nv_carmodeldata.cmdg_carchnmodelname+ ' '+nv_carmodeldata.cmdg_carengmodelname+ ' '+nv_carmodeldata.cmdg_carmodelcode+ ' '+case ISNULL(@powerfrom,'') when '3' then '' else ISNULL(convert(varchar,nv_carcompomodel.cpm_exhuast),' ') +'c.c.' end+ ' '+ct_gm_desc+ case ISNULL(ct_gm_desc,'') when 'CVT' then '' else convert(varchar,nv_carmodeldata.cmdg_transmissionnum) end +' '+nv_carmodeldata.cmdg_carmodeladd+' '+CASE WHEN ct_cbsm_desc = '其他' THEN replace(ct_cbsm_desc,'其他','') +' '+nv_carmodeldata.cmdg_carbodyshapedesc ELSE replace(ct_cbsm_desc,'-','') END,'  ',' '),'.00','') 
	from nv_carmodeldata
	join nv_carcompomodel on cmdg_applyno=cpm_applyno and cmdg_carcompomodelno = cpm_carcompomodelno 
	join nv_codetbl_brand on cmdg_carbrand = ct_bd_id	--廠牌
	join nv_codetbl_gearmethod on cmdg_transmissiontype = ct_gm_id  --排檔型式ct_gm_desc
	join dbo.nv_codetbl_carbodyshape_m on ct_cbsm_id = cmdg_carbodyshape --車身樣式
	where cmdg_applyno=@applyno and cmdg_carcompomodelno=@carcompomodelno and cmdg_carmodelno=@carmodelno 
end

return isnull(@cn,space(0))
end
GO
PRINT N'正在更改 [dbo].[pr_provide_common_data_for_population_G]...';


GO

--供汙染從噪音複製資料使用  汽車
ALTER procedure [dbo].[pr_provide_common_data_for_population_G] @databelongcompidno nvarchar(100),@vechiletype char(1),@beimport char(1),@carstyleyear varchar(4),@engineamily varchar(30)
as
declare @applyno varchar(9)
set @applyno=''
--set @applyno = '*********'
select top 1  @applyno= abd_applyno from nv_applybasedata where abd_databelongcompidno=@databelongcompidno and abd_enginefamily=@engineamily and abd_carstyleyear=@carstyleyear and abd_vechiletype=@vechiletype and abd_applystatus not in ('10','60','70','90','99') order by abd_applyno desc

create table #tmp_gearratio
(
tmp_carmodelno varchar(36),
tmp_gearratio varchar(800)
)

declare @carmodelno varchar(36)
declare @gearratio nvarchar(800)
set @gearratio=''
declare @cursor_gearratio cursor
set @cursor_gearratio = cursor for select distinct gr_carmodelno from nv_gearratio where gr_applyno=@applyno
open @cursor_gearratio
Fetch Next From @cursor_gearratio
Into @carmodelno
while (@@fetch_status = 0)
  begin
	select @gearratio = @gearratio+convert(varchar,gr_gearratio)+',' from nv_gearratio where gr_carmodelno=@carmodelno and gr_applyno=@applyno order by gr_gearnum
	insert into #tmp_gearratio values(@carmodelno,substring(@gearratio,1,LEN(@gearratio)-1))
	set @gearratio=''
	Fetch Next From @cursor_gearratio
	Into @carmodelno
  end
--select * from #tmp_gearratio
--G 汽車
--abd_mailcertidocaddr--合格證地址
--ct_pf_desc--動力來源
--nv_codetbl_isimport.ct_im_desc--進口/國產
--abd_factoryname--車輛製造者
--cmdg_maxhorsepower--引擎最大功率
--cmdg_maxhorsepowerspeed--轉速
--cmdg_baseenginename--基本引擎
--cmdg_transmissionmodel--變數系統
--ct_cs_desc--冷卻系統
--cpm_cylindernums--汽缸數
--cmdg_cylindermeters--缸徑
--cmdg_cylinderstroke--衝程
--cmdg_compressionratio--壓縮比
--inletmode--供氣方式
--cmdg_drivetype驅動輪(數字)
--ct_dt_desc--驅動輪(中文)
--ct_bd_desc--廠牌
--cmdg_carchnmodelname--中文車型名
--cmdg_carengmodelname--英文車型名
--cmdg_carmodelcode--車型代碼
--cpm_exhuast--排氣量
--ct_gm_desc--排檔方式
--cmdg_transmissionnum--排檔數
--ct_vr_desc--門數
--cmdg_carmodeladd--其他
--cmdg_differentialgearratio--最後驅動比
--tmp_gearratio--齒比
--fullcarstylename--車型名稱
select 
abd_applyno,abd_mailcertidocaddr,ct_pf_desc,nv_codetbl_isimport.ct_im_desc,abd_factoryname,cmdg_maxhorsepower,cmdg_maxhorsepowerspeed,
cmdg_baseenginename,cmdg_transmissionmodel,ct_cs_desc,cpm_cylindernums,cmdg_cylindermeters,cmdg_cylinderstroke,cmdg_compressionratio,nv_codetbl_inletmode.ct_im_desc as 'inletmode' ,cmdg_drivetype,ct_dt_desc,ct_bd_desc,
cmdg_carchnmodelname,cmdg_carengmodelname,cmdg_carmodelcode,replace(ISNULL(convert(varchar,nv_carcompomodel.cpm_exhuast),' '),'.00','') 'cpm_exhuast',ct_gm_desc, cmdg_transmissionnum,ct_vr_desc,cmdg_carmodeladd,
CASE WHEN ct_cbsg_desc = '其他' THEN replace(ct_cbsg_desc,'其他','') +nv_carmodeldata.cmdg_carbodyshapedesc ELSE replace(ct_cbsg_desc,'-','') END as 'carbodyshapedesc',
dbo.fn_FullCarstyleNameStr(@applyno,nv_carmodeldata.cmdg_carcompomodelno,nv_carmodeldata.cmdg_carmodelno) as 'fullcarstylename',
cmdg_differentialgearratio, tmp_gearratio
from nv_applybasedata
join nv_carcompomodel on abd_applyno=cpm_applyno
join nv_carmodeldata on abd_applyno=cmdg_applyno and cpm_carcompomodelno=cmdg_carcompomodelno
join nv_codetbl_vehicledoortype on ct_vr_id=cmdg_cardoorcount --車門
join nv_codetbl_brand on ct_bd_id = cmdg_carbrand --廠牌 ct_bd_desc
join nv_codetbl_isimport on nv_codetbl_isimport.ct_im_id = abd_isimport --進出口 ct_im_desc
join nv_codetbl_powerfrom on ct_pf_id = abd_powerfrom --動力來源 ct_pf_desc
join nv_codetbl_inletmode on nv_codetbl_inletmode.ct_im_id=cmdg_inletmode --進氣方式ct_im_desc
--join nv_codetbl_fueltype on ct_ft_id = cpm_gasolinetype --使用燃料ct_ft_desc
join nv_codetbl_gearmethod on ct_gm_id = cmdg_transmissiontype --排檔型式ct_gm_desc
--join nv_codetbl_lubricationtype on ct_le_id=cpm_provideoilmethod --供油方式ct_le_desc
join nv_codetbl_drivetype on ct_dt_id = cmdg_drivetype --驅動方式ct_dt_desc
--join nv_codetbl_enginepos on ct_ep_id_g=cpm_fituptype --引擎安裝位置ct_ep_desc_g
join nv_codetbl_coolsystem on ct_cs_id=cpm_coolsystem --冷卻系統ct_cs_desc
join nv_codetbl_carbodyshape_g on ct_cbsg_id = cmdg_carbodyshape --車身樣式
left join #tmp_gearratio on tmp_carmodelno=cmdg_carmodelno
where abd_applyno=@applyno and cpm_applytype not in ('9') -- 不異動,撤銷 不顯示

--exec pr_provide_common_data_for_population_G '6B3DU+Zqs/QCcC0yRN4Heg==','G','2','2012','G1-991S-12'
--exec pr_provide_common_data_for_population_G '6B3DU+Zqs/QCcC0yRN4Heg==','G','2','2013','U1234567_1222'
--exec pr_provide_common_data_for_population_G '6B3DU+Zqs/QCcC0yRN4Heg==','G','1','2013','U1234567_1222'
--exec pr_provide_common_data_for_population_G '6B3DU+Zqs/QCcC0yRN4Heg==','G','1','2013','A'

--exec pr_provide_common_data_for_population_G '6B3DU+Zqs/QCcC0yRN4Heg==','G','1','2011','G1-2013-08'
--SELECT abd_carstyleyear,* from nv_applybasedata where  abd_applyno='102070002'--abd_carstyleyear='2012' and abd_vechiletype='G' and abd_applystatus not in ('10','60','70','90','99') order by abd_applyno desc
GO
PRINT N'正在更改 [dbo].[pr_get_InfoOfWorkDays]...';


GO

--取得審驗作業相關天數 資料 (收件日期、辦理期限、剩餘審驗天數、已補件天數、剩餘補件天數)
ALTER procedure [dbo].[pr_get_InfoOfWorkDays] 
@ApplyNo varchar(9)=''

as 

select cal_thisday,cal_beholiday,'' validstatus into #tmp_tbl_calworkdays from dbo.nv_calendar where convert(varchar,cal_thisday,112)>=(select convert(varchar,MIN(vp_execdate),112) from nv_validationprocess where vp_applyno=@ApplyNo and vp_applystatus='80') and cal_thisday<=GETDATE()


declare @lastaction varchar(2)
declare @lastactiondate datetime
declare @applystatus varchar(2)
declare @execdate datetime
set @lastactiondate=GETDATE()
set @lastaction=''

/*判斷是否有 補件重送  補件40 送件80*/
if ( exists(select * from nv_validationprocess where vp_applystatus='40' and vp_applyno=@ApplyNo) and exists(select * from nv_validationprocess where vp_applystatus='80' and vp_applyno=@ApplyNo))
begin
select top 1 @lastaction=vp_applystatus,@lastactiondate=vp_execdate from nv_validationprocess where vp_applyno=@ApplyNo  and vp_applystatus in ('40','80') and vp_execdate >=(select MIN(vp_execdate) from nv_validationprocess where vp_applyno=@ApplyNo and vp_applystatus='40') order by vp_execdate desc

end
else
begin

select top 1 @lastaction=vp_applystatus,@lastactiondate=vp_execdate from nv_validationprocess where vp_applyno=@ApplyNo and vp_applystatus ='80' order by vp_execdate desc 

end

declare @startdate datetime
declare @enddate datetime 
declare @resupplydateperiod_cursor cursor

--drop table #tmptbl_resupplydateperiod
/*判斷是否有 補件重送  補件40 送件80*/
if ( exists(select * from nv_validationprocess where vp_applystatus='40' and vp_applyno=@ApplyNo) and exists(select * from nv_validationprocess where vp_applystatus='80' and vp_applyno=@ApplyNo))
begin

		set @resupplydateperiod_cursor = cursor for
			  select vp_applystatus,vp_execdate from nv_validationprocess where vp_applyno=@ApplyNo and vp_applystatus in ('40','80') and vp_execdate >=(select MIN(vp_execdate) from nv_validationprocess where vp_applyno=@ApplyNo and vp_applystatus='40') order by vp_execdate    
		open @resupplydateperiod_cursor
		Fetch Next From @resupplydateperiod_cursor
		Into @applystatus,@execdate
		while (@@fetch_status = 0)
		  begin
			if (@applystatus = '40')
			begin
				set @startdate = @execdate
			end
			if (@applystatus = '80')
			begin
				set @enddate = @execdate			
				update #tmp_tbl_calworkdays set validstatus='1' where convert(varchar,cal_thisday,112)>=convert(varchar,@startdate,112) and convert(varchar,cal_thisday,112)<= convert(varchar,@enddate,112)
			end	
			Fetch Next From @resupplydateperiod_cursor Into @applystatus,@execdate
		  end
		  if (@lastaction = '40')
		  begin
			update #tmp_tbl_calworkdays set validstatus='1' where convert(varchar,cal_thisday,112)>=convert(varchar,@startdate,112) and convert(varchar,cal_thisday,112)<= convert(varchar,GETDATE(),112)
		  end
end

declare @been_resupplydays int  --已補件天數
declare @been_validationdays int --已審驗天數
set @been_resupplydays=0
set @been_validationdays=0
select @been_resupplydays=count(*) from #tmp_tbl_calworkdays where validstatus='1' and cal_beholiday='0'  /*cal_beholiday=0 非假日,validstatus=1 作業天*/
select @been_validationdays=count(*) from #tmp_tbl_calworkdays where validstatus<>'1' and cal_beholiday='0'

declare @validation_deadline nvarchar(10)
set @validation_deadline = '已超過審驗期限'

if ((20-@been_validationdays) > 0)
begin

    select top (20-@been_validationdays) * into #tmp_abd from nv_calendar where convert(varchar,cal_thisday,112)>convert(varchar,GETDATE(),112) and cal_beholiday='0' order by cal_thisday
    select top 1 @validation_deadline=convert(varchar,cal_thisday,111) from #tmp_abd order by cal_thisday desc   
    drop table #tmp_abd
end
else
begin
    set @been_validationdays=0
end


select 
abd_applyno ApplyNo --申請編號
,abd_databelongcompname Compname --申請者名稱
,dbo.fn_GetApplyTypeStr(abd_applyno) ApplyTypeStr --申請內容
,abd_contactname ContactName --聯絡人
,abd_contacttel ContactTel --聯絡人電話
,abd_mailcertidocaddr CertidocAddr--合格證地址
,ct_cs_desc as CertStatus --合格證製發狀況
,(select top 1 vp_execdate from nv_validationprocess where vp_applyno= @ApplyNo and vp_applystatus='80' order by vp_execdate ) ChargeDate --收件日期
,@validation_deadline WorkDays --辦理期限
,(20 - @been_validationdays) RemnantValidDays --剩餘審驗天數
,@been_resupplydays RePairDays --已補件天數
,(30- @been_resupplydays) RemnantRePairDays --剩餘補件天數
,dbo.fn_CastApplyStatusCn(abd_applystatus) ApplyStatusCn --資料狀態
,abd_enginefamily EngineFamily --引擎族
,abd_carstyleyear CarYear --車型年
,abd_factoryname FactoryName --製造廠
,dbo.fn_CastCarTypeCn(abd_vechiletype) CarType --交通工具種類
,dbo.fn_CastStandardDateCn(abd_adaptstandarddate) ApplicationDate --適用期別
,dbo.fn_CastTwoCertTypeCn(abd_twocertmethod) TwoCertMethod --兩證合一申請方式
,abd_officedocdate OfficeDocDate --申請函發文日期
,abd_officedocno OfficeDocNo --申請函發文字號
,abd_userdocfileid OfficeFileId --申請函發文字PDF
,dbo.fn_GetPayDataStr(abd_applyno) PayDataStr --繳費資料
,dbo.fn_GetValidProcessStr(abd_applyno,'31') FirstProcess --初審中
,dbo.fn_GetCarCompoCountGroup(abd_applyno) ApplyTypeCount--車型組數
,dbo.fn_GetCarCompoCount(abd_applyno) CarCompoCount--車型數
,dbo.fn_GetValidProcessStr(abd_applyno,'32') SecondProcess --複審中
,dbo.fn_GetValidProcessStr(abd_applyno,'33') ClassBProcess --二級主管核閱
,dbo.fn_GetValidProcessStr(abd_applyno,'34') ClassAProcess --一級主管核閱
,isnull(abd_validatechargeaccount,'') ChargeAccount
from dbo.nv_applybasedata
left join nv_codetbl_certstatus on ct_cs_id=abd_certstatus 
where abd_applyno=@ApplyNo

drop table #tmp_tbl_calworkdays

--EXEC pr_get_InfoOfWorkDays '*********'
GO
PRINT N'正在更改 [dbo].[pr_provide_common_data_for_population_D]...';


GO


--供汙染從噪音複製資料使用  汽車
ALTER procedure [dbo].[pr_provide_common_data_for_population_D] @databelongcompidno nvarchar(100),@vechiletype char(1),@beimport char(1),@carstyleyear varchar(4),@engineamily varchar(32)
as
declare @applyno varchar(9)
set @applyno=''
--set @applyno = '*********'
if (@vechiletype ='D') --柴油車(比對-前字串)
	begin                                       
		set	@engineamily= left(@engineamily,(len(@engineamily)-ISNULL(NULLIF(CHARINDEX('-',REVERSE(@engineamily)),0),0))) + '%'                           		     		
		select top 1 @applyno= abd_applyno from nv_applybasedata where abd_databelongcompidno=@databelongcompidno and abd_enginefamily LIKE @engineamily and abd_carstyleyear=@carstyleyear and abd_vechiletype=@vechiletype and abd_applystatus not in ('10','60','70','90','99') order by abd_applyno desc
	end
	else
		begin                                       		
		select top 1 @applyno= abd_applyno from nv_applybasedata where abd_databelongcompidno=@databelongcompidno and abd_enginefamily = @engineamily and abd_carstyleyear=@carstyleyear and abd_vechiletype=@vechiletype and abd_applystatus not in ('10','60','70','90','99') order by abd_applyno desc
	end

create table #tmp_gearratio
(
tmp_carmodelno varchar(36),
tmp_gearratio varchar(800)
)

declare @carmodelno varchar(36)
declare @gearratio nvarchar(800)
set @gearratio=''
declare @cursor_gearratio cursor
set @cursor_gearratio = cursor for select distinct gr_carmodelno from nv_gearratio where gr_applyno=@applyno
open @cursor_gearratio
Fetch Next From @cursor_gearratio
Into @carmodelno
while (@@fetch_status = 0)
  begin
	select @gearratio = @gearratio+convert(varchar,gr_gearratio)+',' from nv_gearratio where gr_carmodelno=@carmodelno and gr_applyno=@applyno order by gr_gearnum
	insert into #tmp_gearratio values(@carmodelno,substring(@gearratio,1,LEN(@gearratio)-1))
	set @gearratio=''
	Fetch Next From @cursor_gearratio
	Into @carmodelno
  end

--D 柴油車
--abd_mailcertidocaddr--合格證地址
--ct_pf_desc--動力來源
--nv_codetbl_isimport.ct_im_desc--進口/國產
--abd_factoryname--車輛製造者
--cmdg_maxhorsepower--引擎最大功率
--cmdg_maxhorsepowerspeed--轉速
--cmdg_baseenginename--基本引擎
--cmdg_transmissionmodel--變數系統
--ct_cs_desc--冷卻系統
--cpm_cylindernums--汽缸數
--cmdg_cylindermeters--缸徑
--cmdg_cylinderstroke--衝程
--cmdg_compressionratio--壓縮比
--inletmode--供氣方式
--cmdg_drivetype驅動輪(數字)
--ct_dt_desc--驅動輪(中文)
--ct_bd_desc--廠牌
--cmdg_carchnmodelname--中文車型名
--cmdg_carengmodelname--英文車型名
--cmdg_carmodelcode--車型代碼
--cpm_exhuast--排氣量
--ct_gm_desc--排檔方式
--cmdg_transmissionnum--排檔數
--ct_vr_desc--門數
--cmdg_carmodeladd--其他
--cmdg_differentialgearratio--最後驅動比
--tmp_gearratio--齒比
--fullcarstylename--車型名稱

--cmdg_carbodyweight--車體總重量
--cmdg_numofaxes_f--傳動系統 軸數 前軸 單/雙
--cmdg_numofaxes_b--傳動系統 軸數 後軸 單/雙
--cmdg_increasepowerstyle--傳動系統 加力箱型式
--cmdg_increasepowerratio--傳動系統 加力箱齒比
--cmdg_backgearnums--後退檔數
--cmdg_highlowgear--高低半檔  高半檔/低半檔
--cmdg_climbratio--爬坡齒比
--cmdg_suspensionsystem_supplement--懸吊系統-補
--cmdg_maxhorsepower_hb--馬達最大馬力
--cmdg_maxhorsepowerspeed_hb--馬達最大馬力轉速
--cmdg_torque_hb--馬達最大扭力
--cmdg_torquespeed_hb--馬達最大扭力轉速
--cmdg_producercountry-- 製造國
--cmdg_forwardgearnums--前進檔數
select 
abd_applyno,cmdg_carcompomodelno,abd_mailcertidocaddr,ct_pf_desc,nv_codetbl_isimport.ct_im_desc,abd_factoryname,cmdg_maxhorsepower,cmdg_maxhorsepowerspeed,cmdg_torque,cmdg_torquespeed,
cmdg_baseenginename,cmdg_transmissionmodel,ct_cs_desc,cpm_cylindernums,cmdg_cylindermeters,cmdg_cylinderstroke,cmdg_compressionratio,nv_codetbl_inletmode.ct_im_desc as 'inletmode' ,cmdg_drivetype,ct_dt_desc,ct_bd_desc,
cmdg_carchnmodelname,cmdg_carengmodelname,cmdg_carmodelcode,replace(ISNULL(convert(varchar,nv_carcompomodel.cpm_exhuast),' '),'.00','') 'cpm_exhuast',ct_gm_desc, cmdg_transmissionnum,ct_vr_desc,cmdg_carmodeladd,
CASE WHEN ct_cbsd_desc = '其他' THEN replace(ct_cbsd_desc,'其他','') +nv_carmodeldata.cmdg_carbodyshapedesc ELSE replace(ct_cbsd_desc,'-','') END as 'carbodyshapedesc',
dbo.fn_FullCarstyleNameStr(abd_applyno,nv_carmodeldata.cmdg_carcompomodelno,nv_carmodeldata.cmdg_carmodelno) as 'fullcarstylename',
cmdg_differentialgearratio, tmp_gearratio,
cmdg_carbodyweight,case ISNULL(cmdg_numofaxes_f,'') when 'O' then '單' else '雙' end 'numofaxes_f',case ISNULL(cmdg_numofaxes_b,'') when 'O' then '單' else '雙' end 'numofaxes_b',cmdg_increasepowerstyle,cmdg_increasepowerratio,cmdg_backgearnums,ct_hlg_desc 'highlowgear',cmdg_climbratio,cmdg_suspensionsystem_supplement,cmdg_maxhorsepower_hb,cmdg_maxhorsepowerspeed_hb,
cmdg_torque_hb,cmdg_torquespeed_hb,cmdg_producercountry 'producercountry',cmdg_forwardgearnums
from nv_applybasedata
join nv_carcompomodel on abd_applyno=cpm_applyno
join nv_carmodeldata on abd_applyno=cmdg_applyno and cpm_carcompomodelno=cmdg_carcompomodelno
join nv_codetbl_vehicledoortype on ct_vr_id=cmdg_cardoorcount --車門
join nv_codetbl_brand on ct_bd_id = cmdg_carbrand --廠牌 ct_bd_desc
join nv_codetbl_isimport on nv_codetbl_isimport.ct_im_id = abd_isimport --進出口 ct_im_desc
join nv_codetbl_powerfrom on ct_pf_id = abd_powerfrom --動力來源 ct_pf_desc
join nv_codetbl_inletmode on nv_codetbl_inletmode.ct_im_id=cmdg_inletmode --進氣方式ct_im_desc
--join nv_codetbl_fueltype on ct_ft_id = cpm_gasolinetype --使用燃料ct_ft_desc
join nv_codetbl_gearmethod on ct_gm_id = cmdg_transmissiontype --排檔型式ct_gm_desc
--left join nv_codetbl_lubricationtype on ct_le_id=cpm_provideoilmethod --供油方式ct_le_desc
join nv_codetbl_drivetype on ct_dt_id = cmdg_drivetype --驅動方式ct_dt_desc
--join nv_codetbl_enginepos on ct_ep_id_g=cpm_fituptype --引擎安裝位置ct_ep_desc_g
join nv_codetbl_coolsystem on ct_cs_id=cpm_coolsystem --冷卻系統ct_cs_desc
join nv_codetbl_carbodyshape_d on ct_cbsd_id = cmdg_carbodyshape --車身樣式
left join nv_codetbl_highlowgear on ct_hlg_id = cmdg_highlowgear -- 高低檔
left join #tmp_gearratio on tmp_carmodelno=cmdg_carmodelno
where abd_applyno=@applyno and cpm_applytype not in ('9') -- 不異動,撤銷 不顯示

--exec pr_provide_common_data_for_population_D 'KH6U+PnJgSWnXT3EzSOroA==','D','2','2014','DCECH089E5X-B4'
--SELECT * from nv_applybasedata where  abd_applyno='*********'--abd_carstyleyear='2012' and abd_vechiletype='G' and abd_applystatus not in ('10','60','70','90','99') order by abd_applyno desc



--exec pr_provide_common_data_for_population_D 'vO/YnMQoYqVUAPvemsVj+w==','D','2','2015','DCECH067E5X-B5'
GO
PRINT N'正在更改 [dbo].[pr_self_data_for_population_G]...';


GO

--汽車
ALTER procedure [dbo].[pr_self_data_for_population_G] @applyno varchar(9)
as

--set @applyno = '*********'
-- select top 1  @applyno= abd_applyno from nv_applybasedata where abd_databelongcompidno=@databelongcompidno and abd_enginefamily=@engineamily and abd_carstyleyear=@carstyleyear and abd_vechiletype=@vechiletype and abd_applystatus not in ('10','60','70','90','99') order by abd_applyno desc

create table #tmp_gearratio
(
tmp_carmodelno varchar(36),
tmp_gearratio varchar(800)
)

declare @carmodelno varchar(36)
declare @gearratio nvarchar(800)
set @gearratio=''
declare @cursor_gearratio cursor
set @cursor_gearratio = cursor for select distinct gr_carmodelno from nv_gearratio where gr_applyno=@applyno
open @cursor_gearratio
Fetch Next From @cursor_gearratio
Into @carmodelno
while (@@fetch_status = 0)
  begin
	select @gearratio = @gearratio+convert(varchar,gr_gearratio)+',' from nv_gearratio where gr_carmodelno=@carmodelno and gr_applyno=@applyno order by gr_gearnum
	insert into #tmp_gearratio values(@carmodelno,substring(@gearratio,1,LEN(@gearratio)-1))
	set @gearratio=''
	Fetch Next From @cursor_gearratio
	Into @carmodelno
  end
--select * from #tmp_gearratio
--G 汽車
--abd_mailcertidocaddr--合格證地址
--ct_pf_desc--動力來源
--nv_codetbl_isimport.ct_im_desc--進口/國產
--abd_factoryname--車輛製造者
--cmdg_maxhorsepower--引擎最大功率
--cmdg_maxhorsepowerspeed--轉速
--cmdg_baseenginename--基本引擎
--cmdg_transmissionmodel--變數系統
--ct_cs_desc--冷卻系統
--cpm_cylindernums--汽缸數
--cmdg_cylindermeters--缸徑
--cmdg_cylinderstroke--衝程
--cmdg_compressionratio--壓縮比
--inletmode--供氣方式
--cmdg_drivetype驅動輪(數字)
--ct_dt_desc--驅動輪(中文)
--ct_bd_desc--廠牌
--cmdg_carchnmodelname--中文車型名
--cmdg_carengmodelname--英文車型名
--cmdg_carmodelcode--車型代碼
--cpm_exhuast--排氣量
--ct_gm_desc--排檔方式
--cmdg_transmissionnum--排檔數
--ct_vr_desc--門數
--cmdg_carmodeladd--其他
--cmdg_differentialgearratio--最後驅動比
--tmp_gearratio--齒比
--fullcarstylename--車型名稱
select 
abd_applyno,abd_mailcertidocaddr,ct_pf_desc,nv_codetbl_isimport.ct_im_desc,abd_factoryname,cmdg_maxhorsepower,cmdg_maxhorsepowerspeed,
cmdg_baseenginename,cmdg_transmissionmodel,ct_cs_desc,cpm_cylindernums,cmdg_cylindermeters,cmdg_cylinderstroke,cmdg_compressionratio,nv_codetbl_inletmode.ct_im_desc as 'inletmode' ,cmdg_drivetype,ct_dt_desc,ct_bd_desc,
cmdg_carchnmodelname,cmdg_carengmodelname,cmdg_carmodelcode,replace(ISNULL(convert(varchar,nv_carcompomodel.cpm_exhuast),' '),'.00','') 'cpm_exhuast',ct_gm_desc, cmdg_transmissionnum,ct_vr_desc,cmdg_carmodeladd,
CASE WHEN ct_cbsg_desc = '其他' THEN replace(ct_cbsg_desc,'其他','') +nv_carmodeldata.cmdg_carbodyshapedesc ELSE replace(ct_cbsg_desc,'-','') END as 'carbodyshapedesc',
dbo.fn_FullCarstyleNameStr(abd_applyno,nv_carmodeldata.cmdg_carcompomodelno,nv_carmodeldata.cmdg_carmodelno) as 'fullcarstylename',
cmdg_differentialgearratio, tmp_gearratio
from nv_applybasedata
join nv_carcompomodel on abd_applyno=cpm_applyno
join nv_carmodeldata on abd_applyno=cmdg_applyno and cpm_carcompomodelno=cmdg_carcompomodelno
join nv_codetbl_vehicledoortype on ct_vr_id=cmdg_cardoorcount --車門
join nv_codetbl_brand on ct_bd_id = cmdg_carbrand --廠牌 ct_bd_desc
join nv_codetbl_isimport on nv_codetbl_isimport.ct_im_id = abd_isimport --進出口 ct_im_desc
join nv_codetbl_powerfrom on ct_pf_id = abd_powerfrom --動力來源 ct_pf_desc
join nv_codetbl_inletmode on nv_codetbl_inletmode.ct_im_id=cmdg_inletmode --進氣方式ct_im_desc
--join nv_codetbl_fueltype on ct_ft_id = cpm_gasolinetype --使用燃料ct_ft_desc
join nv_codetbl_gearmethod on ct_gm_id = cmdg_transmissiontype --排檔型式ct_gm_desc
--join nv_codetbl_lubricationtype on ct_le_id=cpm_provideoilmethod --供油方式ct_le_desc
join nv_codetbl_drivetype on ct_dt_id = cmdg_drivetype --驅動方式ct_dt_desc
--join nv_codetbl_enginepos on ct_ep_id_g=cpm_fituptype --引擎安裝位置ct_ep_desc_g
join nv_codetbl_coolsystem on ct_cs_id=cpm_coolsystem --冷卻系統ct_cs_desc
join nv_codetbl_carbodyshape_g on ct_cbsg_id = cmdg_carbodyshape --車身樣式
left join #tmp_gearratio on tmp_carmodelno collate Chinese_Taiwan_Stroke_BIN=cmdg_carmodelno COLLATE Chinese_Taiwan_Stroke_BIN
where abd_applyno=@applyno and cpm_applytype not in ('9') -- 不異動,撤銷 不顯示

drop table #tmp_gearratio

--exec pr_provide_common_data_for_population_G '6B3DU+Zqs/QCcC0yRN4Heg==','G','2','2012','G1-991S-12'
--exec pr_provide_common_data_for_population_G '6B3DU+Zqs/QCcC0yRN4Heg==','G','2','2013','U1234567_1222'
--exec pr_provide_common_data_for_population_G '6B3DU+Zqs/QCcC0yRN4Heg==','G','1','2013','U1234567_1222'
--exec pr_provide_common_data_for_population_G '6B3DU+Zqs/QCcC0yRN4Heg==','G','1','2013','A'

--exec pr_provide_common_data_for_population_G '6B3DU+Zqs/QCcC0yRN4Heg==','G','1','2011','G1-2013-08'
--SELECT abd_carstyleyear,* from nv_applybasedata where  abd_applyno='102070002'--abd_carstyleyear='2012' and abd_vechiletype='G' and abd_applystatus not in ('10','60','70','90','99') order by abd_applyno desc
GO
PRINT N'正在更改 [dbo].[pr_get_carspectabledata]...';


GO

--取得單一車型規格表資料
--author:880346
ALTER procedure [dbo].[pr_get_carspectabledata] @applyno varchar(9),@carcompomodelno varchar(15),@carmodelno varchar(36),@vechiletype varchar(1)
as
declare @compname nvarchar(150)
declare @carstyleyear varchar(4)
declare @adaptstandarddate varchar(5)
declare @representedcar int
set @representedcar=0
set @compname=''
set @carstyleyear=''
select @compname=abd_databelongcompname,@carstyleyear=abd_carstyleyear from nv_applybasedata where abd_applyno=@applyno

select @adaptstandarddate=cpm_adaptstandarddate from nv_carcompomodel where cpm_applyno=@applyno and cpm_carcompomodelno=@carcompomodelno
create table #tmp_carcompodata
(
cpm_applyno varchar(9),
cpm_carcompomodelno varchar(15),
cpm_applytype  varchar(3),
cpm_baseenginename nvarchar(50),
cpm_exhuast nvarchar(50),
abd_enginefamily nvarchar(50),
abd_carstyleyear nvarchar(4),
abd_databelongcompname nvarchar(200),--廠商名稱
ct_aps_desc nvarchar(50),
ct_at_desc nvarchar(50),
ct_cy_desc nvarchar(50),
ct_twct_desc nvarchar(50),
ct_ft_desc nvarchar(50),
ct_ebm_desc  nvarchar(50), --motor
ct_le_desc nvarchar(50),
cpm_cylindernums nvarchar(50),
ct_ep_desc nvarchar(50),
cpm_enginesetupposnmethod nvarchar(50), --motor
ct_cs_desc nvarchar(50),
abd_powerfrom varchar(2),
)


if (CONVERT(DECIMAL(9,2) ,@adaptstandarddate) <= 5)
begin

	if (@vechiletype='G')
	begin	
		insert into #tmp_carcompodata
		SELECT cpm_applyno,cpm_carcompomodelno,cpm_applytype,cpm_baseenginename,cpm_exhuast,abd_enginefamily,abd_carstyleyear,abd_databelongcompname,
		ct_aps_desc,ct_at_desc,ct_cy_desc,ct_twct_desc,ct_ft_desc,'',ct_le_desc,cpm_cylindernums,ct_ep_desc_g ,'',ct_cs_desc,abd_powerfrom
		FROM nv_carcompomodel
		left join nv_applybasedata on abd_applyno=cpm_applyno
		left join nv_codetbl_applystatus on ct_aps_id = abd_applystatus --申請型式 ct_aps_desc
		left join nv_codetbl_applytype on ct_at_id = cpm_applytype --申請狀態ct_at_desc
		left join nv_codetbl_country on ct_cy_id = cpm_importarea -- 進口國ct_cy_desc
		left join nv_codetbl_twocerttype  on ct_twct_id = cpm_useeurocert --以歐盟合格申請ct_twct_desc
		left join nv_codetbl_fueltype on ct_ft_id = cpm_gasolinetype --使用燃料ct_ft_desc
		
		left join nv_codetbl_lubricationtype on ct_le_id=cpm_provideoilmethod --供油方式ct_le_desc
		left join nv_codetbl_enginepos on ct_ep_id_g=cpm_fituptype --引擎安裝位置ct_ep_desc_g
		left join nv_codetbl_coolsystem on ct_cs_id=cpm_coolsystem --冷卻系統ct_cs_desc
		WHERE cpm_applyno=@applyno and cpm_carcompomodelno=@carcompomodelno
		
		select cpm_adaptstandarddate,nv_carmodeldata.*,ct_hlg_desc 'highlowgear',ct_im_desc,
		dbo.fn_FullCarstyleNameStr(@applyno,@carcompomodelno,nv_carmodeldata.cmdg_carmodelno)as 'fullcarstylename',
		ct_cbsg_desc 'ct_cbs_desc',ct_vr_desc,ct_bd_desc,'' AS 'speetuptype',nvcO.ct_nvc_desc,ct_htm_desc,ct_dt_desc,@representedcar 'berepresentativevehicle_num',ai_username,cmdg_transmissiontype,#tmp_carcompodata.*,ct_gm_desc
		,cpm_silencersystem,cpm_engineompartment
		from nv_carmodeldata
		join nv_carcompomodel on nv_carmodeldata.cmdg_applyno=cpm_applyno and nv_carmodeldata.cmdg_carcompomodelno = cpm_carcompomodelno		
		join nv_codetbl_carbodyshape_g on ct_cbsg_id=cmdg_carbodyshape --車身式樣
		join nv_codetbl_vehicledoortype on ct_vr_id=cmdg_cardoorcount --車門
		join nv_codetbl_brand on ct_bd_id = cmdg_carbrand --廠牌
		join nv_codetbl_noisevechilecategory as nvcO on nvcO.ct_nvc_id = cmdg_cartype --車種分類	原地		
		left join nv_codetbl_haveturbomode on ct_htm_id = cmdg_turborchargers --渦輪增壓器
		join nv_codetbl_drivetype on ct_dt_id=cmdg_drivetype  --驅動方式
		join nv_accountinfo on ai_account=cmdg_modaccount
		join nv_codetbl_inletmode on ct_im_id=cmdg_inletmode
		left join nv_codetbl_highlowgear on ct_hlg_id = cmdg_highlowgear -- 高低檔
		left join nv_codetbl_gearmethod on ct_gm_id = cmdg_transmissiontype --排檔型式ct_gm_desc		
		join #tmp_carcompodata on cmdg_carcompomodelno=#tmp_carcompodata.cpm_carcompomodelno and cmdg_applyno=#tmp_carcompodata.cpm_applyno
		where cmdg_applyno=@applyno and cmdg_carcompomodelno=@carcompomodelno and cmdg_carmodelno=@carmodelno 
	end

	if (@vechiletype='D')
	begin	
		insert into #tmp_carcompodata
		SELECT cpm_applyno,cpm_carcompomodelno,cpm_applytype,cpm_baseenginename,cpm_exhuast,abd_enginefamily,abd_carstyleyear,abd_databelongcompname,
		ct_aps_desc,ct_at_desc,ct_cy_desc,ct_twct_desc,ct_ft_desc,'',ct_le_desc,cpm_cylindernums,ct_ep_desc_g ,'',ct_cs_desc,abd_powerfrom
		FROM nv_carcompomodel
		left join nv_applybasedata on abd_applyno=cpm_applyno
		left join nv_codetbl_applystatus on ct_aps_id = abd_applystatus --申請型式 ct_aps_desc
		left join nv_codetbl_applytype on ct_at_id = cpm_applytype --申請狀態ct_at_desc
		left join nv_codetbl_country on ct_cy_id = cpm_importarea -- 進口國ct_cy_desc
		left join nv_codetbl_twocerttype  on ct_twct_id = cpm_useeurocert --以歐盟合格申請ct_twct_desc
		left join nv_codetbl_fueltype on ct_ft_id = cpm_gasolinetype --使用燃料ct_ft_desc
		
		left join nv_codetbl_lubricationtype on ct_le_id=cpm_provideoilmethod --供油方式ct_le_desc
		left join nv_codetbl_enginepos on ct_ep_id_g=cpm_fituptype --引擎安裝位置ct_ep_desc_g
		left join nv_codetbl_coolsystem on ct_cs_id=cpm_coolsystem --冷卻系統ct_cs_desc
		WHERE cpm_applyno=@applyno and cpm_carcompomodelno=@carcompomodelno
		
		select cpm_adaptstandarddate,nv_carmodeldata.*,ct_hlg_desc 'highlowgear',ct_im_desc,
		dbo.fn_FullCarstyleNameStr(@applyno,@carcompomodelno,nv_carmodeldata.cmdg_carmodelno)as 'fullcarstylename',
		ct_cbsd_desc 'ct_cbs_desc',ct_vr_desc,ct_bd_desc,'' AS 'speetuptype',nvcO.ct_nvc_desc,ct_htm_desc,ct_dt_desc,@representedcar 'berepresentativevehicle_num',ai_username,cmdg_transmissiontype,#tmp_carcompodata.*,ct_gm_desc
		,cpm_silencersystem,cpm_engineompartment
		from nv_carmodeldata
		join nv_carcompomodel on nv_carmodeldata.cmdg_applyno=cpm_applyno and nv_carmodeldata.cmdg_carcompomodelno = cpm_carcompomodelno		
		join nv_codetbl_carbodyshape_d on ct_cbsd_id=cmdg_carbodyshape --車身式樣
		join nv_codetbl_vehicledoortype on ct_vr_id=cmdg_cardoorcount --車門
		join nv_codetbl_brand on ct_bd_id = cmdg_carbrand --廠牌
		join nv_codetbl_noisevechilecategory as nvcO on nvcO.ct_nvc_id = cmdg_cartype --車種分類	原地	
		join nv_codetbl_haveturbomode on ct_htm_id = cmdg_turborchargers --渦輪增壓器
		join nv_codetbl_drivetype on ct_dt_id=cmdg_drivetype  --驅動方式
		join nv_accountinfo on ai_account=cmdg_modaccount
		join nv_codetbl_inletmode on ct_im_id=cmdg_inletmode
		left join nv_codetbl_highlowgear on ct_hlg_id = cmdg_highlowgear -- 高低檔
		left join nv_codetbl_gearmethod on ct_gm_id = cmdg_transmissiontype --排檔型式ct_gm_desc

		join #tmp_carcompodata on cmdg_carcompomodelno=#tmp_carcompodata.cpm_carcompomodelno and cmdg_applyno=#tmp_carcompodata.cpm_applyno
		where cmdg_applyno=@applyno and cmdg_carcompomodelno=@carcompomodelno and cmdg_carmodelno=@carmodelno 
	end	

	if (@vechiletype='M')
	begin
		insert into #tmp_carcompodata
		SELECT cpm_applyno,cpm_carcompomodelno,cpm_applytype,cpm_baseenginename,cpm_exhuast,abd_enginefamily,abd_carstyleyear,abd_databelongcompname,
		ct_aps_desc,ct_at_desc,ct_cy_desc,ct_twct_desc,ct_ft_desc,ct_ebm_desc,ct_le_desc,cpm_cylindernums,ct_cp_desc_m,cpm_enginesetupposnmethod,ct_cs_desc,abd_powerfrom
		FROM nv_carcompomodel
		left join nv_applybasedata on abd_applyno=cpm_applyno
		left join nv_codetbl_applystatus on ct_aps_id = abd_applystatus --申請型式 ct_aps_desc
		left join nv_codetbl_applytype on ct_at_id = cpm_applytype --申請狀態ct_at_desc		
		left join nv_codetbl_twocerttype  on ct_twct_id = cpm_useeurocert --以歐盟合格申請ct_twct_desc
		left join nv_codetbl_fueltype on ct_ft_id = cpm_gasolinetype --使用燃料ct_ft_desc
		left join nv_codetbl_engineburnmethod on ct_ebm_id = cpm_powerexportway --燃燒循環ct_ebm_desc
		left join nv_codetbl_lubricationtype on ct_le_id=cpm_provideoilmethod --供油方式ct_le_desc
		left join nv_codetbl_cylinderpos_m on ct_cp_id_m = cpm_cylindertype --汽缸排列ct_cp_desc_m
		left join nv_codetbl_coolsystem on ct_cs_id=cpm_coolsystem --冷卻系統ct_cs_desc
		left join nv_codetbl_country on ct_cy_id = cpm_importarea -- 進口國ct_cy_desc
		WHERE cpm_applyno=@applyno and cpm_carcompomodelno=@carcompomodelno
		
		select cpm_adaptstandarddate,nv_carmodeldata.*,ct_im_desc,
		dbo.fn_FullCarstyleNameStr(@applyno,@carcompomodelno,nv_carmodeldata.cmdg_carmodelno) as 'fullcarstylename',
		ct_cbsm_desc 'ct_cbs_desc','' ct_vr_desc,ct_bd_desc,'' AS 'speetuptype',nvcO.ct_nvc_desc,ct_htm_desc,'' 'ct_dt_desc' ,@representedcar 'berepresentativevehicle_num',ai_username,cmdg_transmissiontype,#tmp_carcompodata.* ,ct_gm_desc
		,cpm_silencersystem,cpm_engineompartment
		from nv_carmodeldata
		join nv_carcompomodel on nv_carmodeldata.cmdg_applyno=cpm_applyno and nv_carmodeldata.cmdg_carcompomodelno = cpm_carcompomodelno
		join nv_codetbl_carbodyshape_m on ct_cbsm_id = nv_carmodeldata.cmdg_carbodyshape --車身式樣
		join nv_codetbl_brand on ct_bd_id = cmdg_carbrand  --廠牌
		join nv_codetbl_noisevechilecategory as nvcO on nvcO.ct_nvc_id = cmdg_cartype --車種分類	原地
		join nv_codetbl_haveturbomode on ct_htm_id = cmdg_turborchargers --渦輪增壓器
		join nv_accountinfo on ai_account=cmdg_modaccount --帳號
		join nv_codetbl_inletmode on ct_im_id=cmdg_inletmode		
		join nv_codetbl_gearmethod on cmdg_transmissiontype = ct_gm_id
		join #tmp_carcompodata on cmdg_carcompomodelno=#tmp_carcompodata.cpm_carcompomodelno and cmdg_applyno=#tmp_carcompodata.cpm_applyno
		where cmdg_applyno=@applyno and cmdg_carcompomodelno=@carcompomodelno and cmdg_carmodelno=@carmodelno
	end

end
else
begin
	if (@vechiletype='G')
	begin	
		insert into #tmp_carcompodata
		SELECT cpm_applyno,cpm_carcompomodelno,cpm_applytype,cpm_baseenginename,cpm_exhuast,abd_enginefamily,abd_carstyleyear,abd_databelongcompname,
		ct_aps_desc,ct_at_desc,ct_cy_desc,ct_twct_desc,ct_ft_desc,'',ct_le_desc,cpm_cylindernums,ct_ep_desc_g ,'',ct_cs_desc,abd_powerfrom
		FROM nv_carcompomodel
		left join nv_applybasedata on abd_applyno=cpm_applyno
		left join nv_codetbl_applystatus on ct_aps_id = abd_applystatus --申請型式 ct_aps_desc
		left join nv_codetbl_applytype on ct_at_id = cpm_applytype --申請狀態ct_at_desc
		left join nv_codetbl_country on ct_cy_id = cpm_importarea -- 進口國ct_cy_desc
		left join nv_codetbl_twocerttype  on ct_twct_id = cpm_useeurocert --以歐盟合格申請ct_twct_desc
		left join nv_codetbl_fueltype on ct_ft_id = cpm_gasolinetype --使用燃料ct_ft_desc
		
		left join nv_codetbl_lubricationtype on ct_le_id=cpm_provideoilmethod --供油方式ct_le_desc
		left join nv_codetbl_enginepos on ct_ep_id_g=cpm_fituptype --引擎安裝位置ct_ep_desc_g
		left join nv_codetbl_coolsystem on ct_cs_id=cpm_coolsystem --冷卻系統ct_cs_desc
		WHERE cpm_applyno=@applyno and cpm_carcompomodelno=@carcompomodelno
		
		select cpm_adaptstandarddate,nv_carmodeldata.*,ct_hlg_desc 'highlowgear',ct_im_desc,
		dbo.fn_FullCarstyleNameStr(@applyno,@carcompomodelno,nv_carmodeldata.cmdg_carmodelno)as 'fullcarstylename',
		ct_cbsg_desc 'ct_cbs_desc',ct_vr_desc,ct_bd_desc,nvcS.ct_nvc_desc AS 'speetuptype',nvcO.ct_nvc_desc,ct_htm_desc,ct_dt_desc,@representedcar 'berepresentativevehicle_num',ai_username,cmdg_transmissiontype,#tmp_carcompodata.*,ct_gm_desc
		,cpm_silencersystem,cpm_engineompartment
		from nv_carmodeldata
		join nv_carcompomodel on nv_carmodeldata.cmdg_applyno=cpm_applyno and nv_carmodeldata.cmdg_carcompomodelno = cpm_carcompomodelno		
		join nv_codetbl_carbodyshape_g on ct_cbsg_id=cmdg_carbodyshape --車身式樣
		join nv_codetbl_vehicledoortype on ct_vr_id=cmdg_cardoorcount --車門
		join nv_codetbl_brand on ct_bd_id = cmdg_carbrand --廠牌
		join nv_codetbl_noisevechilecategory as nvcO on nvcO.ct_nvc_id = cmdg_cartype --車種分類	原地
		join nv_codetbl_noisevechilecategory AS nvcS on nvcS.ct_nvc_id = cmdg_carspeetuptype --車種分類 加速
		left join nv_codetbl_haveturbomode on ct_htm_id = cmdg_turborchargers --渦輪增壓器
		join nv_codetbl_drivetype on ct_dt_id=cmdg_drivetype  --驅動方式
		join nv_accountinfo on ai_account=cmdg_modaccount
		join nv_codetbl_inletmode on ct_im_id=cmdg_inletmode
		left join nv_codetbl_highlowgear on ct_hlg_id = cmdg_highlowgear -- 高低檔
		left join nv_codetbl_gearmethod on ct_gm_id = cmdg_transmissiontype --排檔型式ct_gm_desc
		join #tmp_carcompodata on cmdg_carcompomodelno=#tmp_carcompodata.cpm_carcompomodelno and cmdg_applyno=#tmp_carcompodata.cpm_applyno
		where cmdg_applyno=@applyno and cmdg_carcompomodelno=@carcompomodelno and cmdg_carmodelno=@carmodelno 
	end

	if (@vechiletype='D')
	begin	
		insert into #tmp_carcompodata
		SELECT cpm_applyno,cpm_carcompomodelno,cpm_applytype,cpm_baseenginename,cpm_exhuast,abd_enginefamily,abd_carstyleyear,abd_databelongcompname,
		ct_aps_desc,ct_at_desc,ct_cy_desc,ct_twct_desc,ct_ft_desc,'',ct_le_desc,cpm_cylindernums,ct_ep_desc_g ,'',ct_cs_desc,abd_powerfrom
		FROM nv_carcompomodel
		left join nv_applybasedata on abd_applyno=cpm_applyno
		left join nv_codetbl_applystatus on ct_aps_id = abd_applystatus --申請型式 ct_aps_desc
		left join nv_codetbl_applytype on ct_at_id = cpm_applytype --申請狀態ct_at_desc
		left join nv_codetbl_country on ct_cy_id = cpm_importarea -- 進口國ct_cy_desc
		left join nv_codetbl_twocerttype  on ct_twct_id = cpm_useeurocert --以歐盟合格申請ct_twct_desc
		left join nv_codetbl_fueltype on ct_ft_id = cpm_gasolinetype --使用燃料ct_ft_desc		
		left join nv_codetbl_lubricationtype on ct_le_id=cpm_provideoilmethod --供油方式ct_le_desc
		left join nv_codetbl_enginepos on ct_ep_id_g=cpm_fituptype --引擎安裝位置ct_ep_desc_g
		left join nv_codetbl_coolsystem on ct_cs_id=cpm_coolsystem --冷卻系統ct_cs_desc
		WHERE cpm_applyno=@applyno and cpm_carcompomodelno=@carcompomodelno
		
		select cpm_adaptstandarddate,nv_carmodeldata.*,ct_hlg_desc 'highlowgear',ct_im_desc,
		dbo.fn_FullCarstyleNameStr(@applyno,@carcompomodelno,nv_carmodeldata.cmdg_carmodelno)as 'fullcarstylename',
		ct_cbsd_desc 'ct_cbs_desc',ct_vr_desc,ct_bd_desc,nvcS.ct_nvc_desc AS 'speetuptype',nvcO.ct_nvc_desc,ct_htm_desc,ct_dt_desc,@representedcar 'berepresentativevehicle_num',ai_username,cmdg_transmissiontype,#tmp_carcompodata.*,ct_gm_desc
		,cpm_silencersystem,cpm_engineompartment
		from nv_carmodeldata
		join nv_carcompomodel on nv_carmodeldata.cmdg_applyno=cpm_applyno and nv_carmodeldata.cmdg_carcompomodelno = cpm_carcompomodelno		
		join nv_codetbl_carbodyshape_d on ct_cbsd_id=cmdg_carbodyshape --車身式樣
		join nv_codetbl_vehicledoortype on ct_vr_id=cmdg_cardoorcount --車門
		join nv_codetbl_brand on ct_bd_id = cmdg_carbrand --廠牌
		join nv_codetbl_noisevechilecategory as nvcO on nvcO.ct_nvc_id = cmdg_cartype --車種分類	原地
		left join nv_codetbl_noisevechilecategory AS nvcS on nvcS.ct_nvc_id = cmdg_carspeetuptype --車種分類 加速
		join nv_codetbl_haveturbomode on ct_htm_id = cmdg_turborchargers --渦輪增壓器
		join nv_codetbl_drivetype on ct_dt_id=cmdg_drivetype  --驅動方式
		join nv_accountinfo on ai_account=cmdg_modaccount
		join nv_codetbl_inletmode on ct_im_id=cmdg_inletmode
		left join nv_codetbl_highlowgear on ct_hlg_id = cmdg_highlowgear -- 高低檔
		left join nv_codetbl_gearmethod on ct_gm_id = cmdg_transmissiontype --排檔型式ct_gm_desc

		join #tmp_carcompodata on cmdg_carcompomodelno=#tmp_carcompodata.cpm_carcompomodelno and cmdg_applyno=#tmp_carcompodata.cpm_applyno
		where cmdg_applyno=@applyno and cmdg_carcompomodelno=@carcompomodelno and cmdg_carmodelno=@carmodelno 
	end	

	if (@vechiletype='M')
	begin
		insert into #tmp_carcompodata
		SELECT cpm_applyno,cpm_carcompomodelno,cpm_applytype,cpm_baseenginename,cpm_exhuast,abd_enginefamily,abd_carstyleyear,abd_databelongcompname,
		ct_aps_desc,ct_at_desc,ct_cy_desc,ct_twct_desc,ct_ft_desc,ct_ebm_desc,ct_le_desc,cpm_cylindernums,ct_cp_desc_m,cpm_enginesetupposnmethod,ct_cs_desc,abd_powerfrom
		FROM nv_carcompomodel
		left join nv_applybasedata on abd_applyno=cpm_applyno
		left join nv_codetbl_applystatus on ct_aps_id = abd_applystatus --申請型式 ct_aps_desc
		left join nv_codetbl_applytype on ct_at_id = cpm_applytype --申請狀態ct_at_desc
		left join nv_codetbl_twocerttype  on ct_twct_id = cpm_useeurocert --以歐盟合格申請ct_twct_desc
		left join nv_codetbl_fueltype on ct_ft_id = cpm_gasolinetype --使用燃料ct_ft_desc
		left join nv_codetbl_engineburnmethod on ct_ebm_id = cpm_powerexportway --燃燒循環ct_ebm_desc
		left join nv_codetbl_lubricationtype on ct_le_id=cpm_provideoilmethod --供油方式ct_le_desc
		left join nv_codetbl_cylinderpos_m on ct_cp_id_m = cpm_cylindertype --汽缸排列ct_cp_desc_m
		left join nv_codetbl_coolsystem on ct_cs_id=cpm_coolsystem --冷卻系統ct_cs_desc
		left join nv_codetbl_country on ct_cy_id = cpm_importarea -- 進口國ct_cy_desc
		WHERE cpm_applyno=@applyno and cpm_carcompomodelno=@carcompomodelno
		
		select cpm_adaptstandarddate,nv_carmodeldata.*,ct_im_desc,
		dbo.fn_FullCarstyleNameStr(@applyno,@carcompomodelno,nv_carmodeldata.cmdg_carmodelno) as 'fullcarstylename',
		ct_cbsm_desc 'ct_cbs_desc','' ct_vr_desc,ct_bd_desc,nvcS.ct_nvc_desc AS 'speetuptype',nvcO.ct_nvc_desc,ct_htm_desc,'' 'ct_dt_desc' ,@representedcar 'berepresentativevehicle_num',ai_username,cmdg_transmissiontype,#tmp_carcompodata.* ,ct_gm_desc
		,cpm_silencersystem,cpm_engineompartment
		from nv_carmodeldata
		join nv_carcompomodel on nv_carmodeldata.cmdg_applyno=cpm_applyno and nv_carmodeldata.cmdg_carcompomodelno = cpm_carcompomodelno
		join nv_codetbl_carbodyshape_m on ct_cbsm_id = nv_carmodeldata.cmdg_carbodyshape --車身式樣
		join nv_codetbl_brand on ct_bd_id = cmdg_carbrand  --廠牌
		join nv_codetbl_noisevechilecategory as nvcO on nvcO.ct_nvc_id = cmdg_cartype --車種分類	原地
		left join nv_codetbl_noisevechilecategory AS nvcS on nvcS.ct_nvc_id = cmdg_carspeetuptype --車種分類 加速
		join nv_codetbl_haveturbomode on ct_htm_id = cmdg_turborchargers --渦輪增壓器
		join nv_accountinfo on ai_account=cmdg_modaccount --帳號
		join nv_codetbl_inletmode on ct_im_id=cmdg_inletmode
		left join nv_codetbl_gearmethod on ct_gm_id = cmdg_transmissiontype --排檔型式ct_gm_desc
		join #tmp_carcompodata on cmdg_carcompomodelno=#tmp_carcompodata.cpm_carcompomodelno and cmdg_applyno=#tmp_carcompodata.cpm_applyno
		where cmdg_applyno=@applyno and cmdg_carcompomodelno=@carcompomodelno and cmdg_carmodelno=@carmodelno
	end

end
GO
PRINT N'正在更改 [dbo].[pr_self_data_for_population_M]...';


GO

--供汙染從噪音複製資料使用  機車
ALTER procedure [dbo].[pr_self_data_for_population_M] @applyno varchar(9)
as

create table #tmp_gearratio
(
tmp_carmodelno varchar(36),
tmp_gearratio varchar(800)
)

declare @carmodelno varchar(36)
declare @gearratio nvarchar(800)
set @gearratio=''
declare @cursor_gearratio cursor
set @cursor_gearratio = cursor for select distinct gr_carmodelno from nv_gearratio where gr_applyno=@applyno
open @cursor_gearratio
Fetch Next From @cursor_gearratio
Into @carmodelno
while (@@fetch_status = 0)
  begin
	select @gearratio = @gearratio+convert(varchar,gr_gearratio)+',' from nv_gearratio where gr_carmodelno=@carmodelno and gr_applyno=@applyno order by gr_gearnum
	insert into #tmp_gearratio values(@carmodelno,substring(@gearratio,1,LEN(@gearratio)-1))
	set @gearratio=''
	Fetch Next From @cursor_gearratio
	Into @carmodelno
  end

--M 機車
--abd_mailcertidocaddr--合格證地址
--ct_pf_desc--動力來源
--nv_codetbl_isimport.ct_im_desc--進口/國產
--abd_factoryname--車輛製造者
--cmdg_maxhorsepower--引擎最大功率
--cmdg_maxhorsepowerspeed--轉速
--cmdg_baseenginename--基本引擎
--cmdg_transmissionmodel--變數系統
--ct_ebm_desc--燃燒循環
--ct_cp_desc_m--汽缸體形式
--cpm_cylindernums--汽缸數
--ct_cs_desc--冷卻系統
--cmdg_cylindermeters--缸徑
--cmdg_cylinderstroke--衝程
--cmdg_compressionratio--壓縮比
--ct_bd_desc--廠牌
--cmdg_carchnmodelname--中文車型名
--cmdg_carengmodelname--英文車型名
--cmdg_carmodelcode--車型代碼
--cpm_exhuast--排氣量
--ct_gm_desc--排檔方式
--cmdg_transmissionnum--排檔數
--cardoorcount--門數
--cmdg_carmodeladd--其他
--cmdg_1stReductionratio--一次減速比
--cmdg_2ndReductionratio--二次減速比
--tmp_gearratio--齒比
--carstylename--車型名稱
select 
abd_applyno,cmdg_carcompomodelno,abd_mailcertidocaddr,ct_pf_desc,ct_im_desc,abd_factoryname,cmdg_maxhorsepower,cmdg_maxhorsepowerspeed,
ct_bd_desc,cmdg_carchnmodelname,cmdg_carengmodelname,cmdg_carmodelcode,replace(ISNULL(convert(varchar,nv_carcompomodel.cpm_exhuast),' '),'.00','') 'cpm_exhuast',ct_gm_desc, cmdg_transmissionnum,'' as cardoorcount,cmdg_carmodeladd,
CASE WHEN ct_cbsm_desc = '其他' THEN replace(ct_cbsm_desc,'其他','') +nv_carmodeldata.cmdg_carbodyshapedesc ELSE replace(ct_cbsm_desc,'-','') END as 'carbodyshapedesc',
dbo.fn_FullCarstyleNameStr(abd_applyno,nv_carmodeldata.cmdg_carcompomodelno,nv_carmodeldata.cmdg_carmodelno) as 'fullcarstylename',
cmdg_baseenginename,cmdg_transmissionmodel,ct_ebm_desc,ct_cp_desc_m,ct_cs_desc,cpm_cylindernums,cmdg_cylindermeters,cmdg_cylinderstroke,
cmdg_compressionratio,cmdg_1stReductionratio,cmdg_2ndReductionratio,tmp_gearratio
from nv_applybasedata
join nv_carcompomodel on abd_applyno=cpm_applyno
join nv_carmodeldata on abd_applyno=cmdg_applyno and cpm_carcompomodelno=cmdg_carcompomodelno
join nv_codetbl_brand on ct_bd_id = cmdg_carbrand --廠牌
join nv_codetbl_isimport on nv_codetbl_isimport.ct_im_id = abd_isimport --進出口 ct_im_desc
join nv_codetbl_powerfrom on ct_pf_id = abd_powerfrom --動力來源 ct_pf_desc
join nv_codetbl_gearmethod on ct_gm_id = cmdg_transmissiontype --排檔型式 ct_gm_desc
join nv_codetbl_coolsystem on ct_cs_id=cpm_coolsystem --冷卻系統 ct_cs_desc
join nv_codetbl_carbodyshape_m on ct_cbsm_id = cmdg_carbodyshape--車身樣式
join nv_codetbl_engineburnmethod on ct_ebm_id = cpm_powerexportway --燃燒循環 ct_ebm_desc
join nv_codetbl_cylinderpos_m on ct_cp_id_m = cpm_cylindertype --汽缸排列 ct_cp_desc_m
left join #tmp_gearratio on tmp_carmodelno COLLATE Chinese_Taiwan_Stroke_BIN =cmdg_carmodelno COLLATE Chinese_Taiwan_Stroke_BIN
where abd_applyno=@applyno and cpm_applytype not in ('6','9') -- 不異動,撤銷 不顯示

--exec pr_self_data_for_population_M '101080005'
--SELECT * from nv_applybasedata where  abd_applyno='101120010'
--exec pr_provide_common_data_for_population_M '6B3DU+Zqs/QCcC0yRN4Heg==','M','1','2012','U1234567_m1'
--exec pr_provide_common_data_for_population_M '9hvLq1t7EZ46KXeNLEx6UQ==','M','2','2013','G12517C-12'
GO
PRINT N'正在更改 [dbo].[pr_get_carmodeldatarpt]...';


GO
--@"EXEC pr_get_carmodeldatarpt '107050001','B8G12345601'
--車型組車型資料 + 噪音測定報告
ALTER procedure [dbo].[pr_get_carmodeldatarpt] 
@applyno varchar(9)='',
@carcompomodelno varchar(15)=''

as 
declare @adaptstandarddate money=''

declare @id varchar(3)=''
declare @ReCheck nvarchar(5)=''
select @id=cmdg_carspeetuptype from nv_carmodeldata where cmdg_applyno=@applyno and cmdg_carcompomodelno=@carcompomodelno

if exists(select * from nv_codetbl_noisevechilecategory where ct_nvc_id=@id and
 (
 CHARINDEX('M1', ct_nvc_desc) >0 
 or CHARINDEX('N1', ct_nvc_desc) >0
 or ct_nvc_id='27')
  )
begin   
		set @ReCheck='6-1'
end

else if exists(select * from nv_codetbl_noisevechilecategory where ct_nvc_id=@id and
 (
 CHARINDEX('M3', ct_nvc_desc) >0 
 or CHARINDEX('N2', ct_nvc_desc) >0
 or CHARINDEX('N3', ct_nvc_desc) >0
 or ct_nvc_id in('21','22'))
  )
begin   
		set @ReCheck='6-2'   
end



select @adaptstandarddate =cpm_adaptstandarddate from nv_carcompomodel where cpm_applyno=@applyno and cpm_carcompomodelno=@carcompomodelno

if(@adaptstandarddate>5)
begin 
	if(exists(select abd_applyno from nv_applybasedata where abd_applyno = @applyno and abd_vechiletype ='G' ))
	begin 
		
		if(@ReCheck='6-1')
		begin 
		select cmdg_carspeetuptype,cpm_adaptstandarddate,cmdg_applyno,cmdg_carengmodelname,cmdg_carmodelno,cmdg_maxhorsepower,cmdg_maxhorsepowerspeed,replace(cmdg_testweight,'.00','')cmdg_testweight,cmdg_testgearreductionratio,cmdg_coolingdrivermethod,cmdg_tiresnum,cmdg_tirewidth,cmdg_exhaustpipenum,ct_im_desc as'cmdg_inletmode',cmdg_berepresentativevehicle,ISNULL(ntr_carbodyno,'-') ntr_carbodyno,ISNULL(ntr_engineno,'-') ntr_engineno,	
		dbo.fn_FullCarstyleNameStr(@applyno,@carcompomodelno,nv_carmodeldata.cmdg_carmodelno) as 'fullcarstylename'
		from nv_carmodeldata
		left join nv_noisetestrpt on cmdg_carcompomodelno=ntr_carcompomodelno and ntr_carmodelno=cmdg_carmodelno
		join nv_carcompomodel on cmdg_applyno=cpm_applyno and cmdg_carcompomodelno = cpm_carcompomodelno 
		join nv_codetbl_inletmode on nv_codetbl_inletmode.ct_im_id=cmdg_inletmode --進氣方式ct_im_desc
		where cmdg_applyno=@applyno and cmdg_carcompomodelno=@carcompomodelno 
		order by cmdg_berepresentativevehicle desc,
		cmdg_maxhorsepower ASC ,cmdg_testweight desc,cmdg_testgearreductionratio desc,cmdg_tirewidth desc
	    end
		if(@ReCheck='6-2')
		begin 
		select cmdg_carspeetuptype,cpm_adaptstandarddate,cmdg_applyno,cmdg_carengmodelname,cmdg_carmodelno,cmdg_maxhorsepower,cmdg_maxhorsepowerspeed,replace(cmdg_testweight,'.00','')cmdg_testweight,cmdg_testgearreductionratio,cmdg_coolingdrivermethod,cmdg_tiresnum,cmdg_tirewidth,cmdg_exhaustpipenum,ct_im_desc as'cmdg_inletmode',cmdg_berepresentativevehicle,ISNULL(ntr_carbodyno,'-') ntr_carbodyno,ISNULL(ntr_engineno,'-') ntr_engineno,	
		dbo.fn_FullCarstyleNameStr(@applyno,@carcompomodelno,nv_carmodeldata.cmdg_carmodelno) as 'fullcarstylename'
		from nv_carmodeldata
		left join nv_noisetestrpt on cmdg_carcompomodelno=ntr_carcompomodelno and ntr_carmodelno=cmdg_carmodelno
		join nv_carcompomodel on cmdg_applyno=cpm_applyno and cmdg_carcompomodelno = cpm_carcompomodelno 
		join nv_codetbl_inletmode on nv_codetbl_inletmode.ct_im_id=cmdg_inletmode --進氣方式ct_im_desc
		where cmdg_applyno=@applyno and cmdg_carcompomodelno=@carcompomodelno 
		order by cmdg_berepresentativevehicle desc,cmdg_maxhorsepower desc 
	    end
	end
	if(exists(select abd_applyno from nv_applybasedata where abd_applyno = @applyno and abd_vechiletype ='D' ))
	begin 
		if(@ReCheck='6-1')
		begin 
		select cmdg_carspeetuptype,cpm_adaptstandarddate,cmdg_applyno,cmdg_carengmodelname,cmdg_carmodelno,cmdg_maxhorsepower,cmdg_maxhorsepowerspeed,replace(cmdg_testweight,'.00','')cmdg_testweight,cmdg_testgearreductionratio,cmdg_coolingdrivermethod,cmdg_tiresnum,cmdg_tirewidth,cmdg_exhaustpipenum,ct_im_desc as'cmdg_inletmode',cmdg_berepresentativevehicle,ISNULL(ntr_carbodyno,'-') ntr_carbodyno,ISNULL(ntr_engineno,'-') ntr_engineno,	
		dbo.fn_FullCarstyleNameStr(@applyno,@carcompomodelno,nv_carmodeldata.cmdg_carmodelno) as 'fullcarstylename'
		from nv_carmodeldata
		left join nv_noisetestrpt on cmdg_carcompomodelno=ntr_carcompomodelno and ntr_carmodelno=cmdg_carmodelno
		join nv_carcompomodel on cmdg_applyno=cpm_applyno and cmdg_carcompomodelno = cpm_carcompomodelno 
		join nv_codetbl_inletmode on nv_codetbl_inletmode.ct_im_id=cmdg_inletmode --進氣方式ct_im_desc
		where cmdg_applyno=@applyno and cmdg_carcompomodelno=@carcompomodelno 
		order by cmdg_berepresentativevehicle desc,
		cmdg_maxhorsepower ASC ,cmdg_testweight desc,cmdg_testgearreductionratio desc,cmdg_tirewidth desc
	    end
		if(@ReCheck='6-2')
		begin 
		select cmdg_carspeetuptype,cpm_adaptstandarddate,cmdg_applyno,cmdg_carengmodelname,cmdg_carmodelno,cmdg_maxhorsepower,cmdg_maxhorsepowerspeed,replace(cmdg_testweight,'.00','')cmdg_testweight,cmdg_testgearreductionratio,cmdg_coolingdrivermethod,cmdg_tiresnum,cmdg_tirewidth,cmdg_exhaustpipenum,ct_im_desc as'cmdg_inletmode',cmdg_berepresentativevehicle,ISNULL(ntr_carbodyno,'-') ntr_carbodyno,ISNULL(ntr_engineno,'-') ntr_engineno,	
		dbo.fn_FullCarstyleNameStr(@applyno,@carcompomodelno,nv_carmodeldata.cmdg_carmodelno) as 'fullcarstylename'
		from nv_carmodeldata
		left join nv_noisetestrpt on cmdg_carcompomodelno=ntr_carcompomodelno and ntr_carmodelno=cmdg_carmodelno
		join nv_carcompomodel on cmdg_applyno=cpm_applyno and cmdg_carcompomodelno = cpm_carcompomodelno 
		join nv_codetbl_inletmode on nv_codetbl_inletmode.ct_im_id=cmdg_inletmode --進氣方式ct_im_desc
		where cmdg_applyno=@applyno and cmdg_carcompomodelno=@carcompomodelno 
		order by cpm_carcompomodelno ASC,cmdg_berepresentativevehicle desc,cmdg_createdate,fullcarstylename,
		cmdg_maxhorsepower desc ,cmdg_testweight ASC
		end
	end
	if(exists(select abd_applyno from nv_applybasedata where abd_applyno = @applyno and abd_vechiletype = 'M' ))
	begin 
		select cmdg_carspeetuptype,cpm_adaptstandarddate,cmdg_applyno,cmdg_carengmodelname,cmdg_carmodelno,cmdg_maxhorsepower,cmdg_maxhorsepowerspeed,replace(cmdg_testweight,'.00','')cmdg_testweight,cmdg_testgearreductionratio,cmdg_coolingdrivermethod,cmdg_tiresnum,cmdg_tirewidth,cmdg_exhaustpipenum,ct_im_desc as'cmdg_inletmode',cmdg_berepresentativevehicle,ISNULL(ntr_carbodyno,'-') ntr_carbodyno,ISNULL(ntr_engineno,'-') ntr_engineno,	
		dbo.fn_FullCarstyleNameStr(@applyno,@carcompomodelno,nv_carmodeldata.cmdg_carmodelno) as 'fullcarstylename'
		from nv_carmodeldata
		left join nv_noisetestrpt on cmdg_carcompomodelno=ntr_carcompomodelno and ntr_carmodelno=cmdg_carmodelno
		join nv_carcompomodel on cmdg_applyno=cpm_applyno and cmdg_carcompomodelno = cpm_carcompomodelno 
		join nv_codetbl_inletmode on nv_codetbl_inletmode.ct_im_id=cmdg_inletmode --進氣方式ct_im_desc 	
		where cmdg_applyno=@applyno and cmdg_carcompomodelno=@carcompomodelno 
		order by cpm_carcompomodelno ASC,cmdg_berepresentativevehicle desc,cmdg_createdate,fullcarstylename,--order by cmdg_berepresentativevehicle desc,cmdg_createdate,fullcarstylename--cmdg_berepresentativevehicle desc,cmdg_createdate,fullcarstylename
		cmdg_maxhorsepower ASC ,cmdg_testweight desc,cmdg_testgearreductionratio desc,cmdg_tirewidth desc
	end
end

else
begin 
	if(exists(select abd_applyno from nv_applybasedata where abd_applyno = @applyno and abd_vechiletype in ('G','D') ))
	begin 
		select cmdg_carspeetuptype,cpm_adaptstandarddate,cmdg_applyno,cmdg_carengmodelname,cmdg_carmodelno,cmdg_maxhorsepower,cmdg_maxhorsepowerspeed,replace(cmdg_testweight,'.00','')cmdg_testweight,cmdg_testgearreductionratio,cmdg_coolingdrivermethod,cmdg_tiresnum,cmdg_tirewidth,cmdg_exhaustpipenum,ct_im_desc as'cmdg_inletmode',cmdg_berepresentativevehicle,ISNULL(ntr_carbodyno,'-') ntr_carbodyno,ISNULL(ntr_engineno,'-') ntr_engineno,	
		dbo.fn_FullCarstyleNameStr(@applyno,@carcompomodelno,nv_carmodeldata.cmdg_carmodelno) as 'fullcarstylename'
		from nv_carmodeldata
		left join nv_noisetestrpt on cmdg_carcompomodelno=ntr_carcompomodelno and ntr_carmodelno=cmdg_carmodelno
		join nv_carcompomodel on cmdg_applyno=cpm_applyno and cmdg_carcompomodelno = cpm_carcompomodelno 
		join nv_codetbl_inletmode on nv_codetbl_inletmode.ct_im_id=cmdg_inletmode --進氣方式ct_im_desc
		where cmdg_applyno=@applyno and cmdg_carcompomodelno=@carcompomodelno 
		order by cpm_carcompomodelno ASC,cmdg_berepresentativevehicle desc,cmdg_createdate,fullcarstylename		
		--order by cmdg_berepresentativevehicle desc,cmdg_createdate,fullcarstylename
	end
	if(exists(select abd_applyno from nv_applybasedata where abd_applyno = @applyno and abd_vechiletype = 'M' ))
	begin 
		select cmdg_carspeetuptype,cpm_adaptstandarddate,cmdg_applyno,cmdg_carengmodelname,cmdg_carmodelno,cmdg_maxhorsepower,cmdg_maxhorsepowerspeed,replace(cmdg_testweight,'.00','')cmdg_testweight,cmdg_testgearreductionratio,cmdg_coolingdrivermethod,cmdg_tiresnum,cmdg_tirewidth,cmdg_exhaustpipenum,ct_im_desc as'cmdg_inletmode',cmdg_berepresentativevehicle,ISNULL(ntr_carbodyno,'-') ntr_carbodyno,ISNULL(ntr_engineno,'-') ntr_engineno,	
		dbo.fn_FullCarstyleNameStr(@applyno,@carcompomodelno,nv_carmodeldata.cmdg_carmodelno) as 'fullcarstylename'
		from nv_carmodeldata
		left join nv_noisetestrpt on cmdg_carcompomodelno=ntr_carcompomodelno and ntr_carmodelno=cmdg_carmodelno
		join nv_carcompomodel on cmdg_applyno=cpm_applyno and cmdg_carcompomodelno = cpm_carcompomodelno 

		join nv_codetbl_inletmode on nv_codetbl_inletmode.ct_im_id=cmdg_inletmode --進氣方式ct_im_desc 	
		where cmdg_applyno=@applyno and cmdg_carcompomodelno=@carcompomodelno 
		order by cpm_carcompomodelno ASC,cmdg_berepresentativevehicle desc,cmdg_createdate,fullcarstylename--order by cmdg_berepresentativevehicle desc,cmdg_createdate,fullcarstylename--cmdg_berepresentativevehicle desc,cmdg_createdate,fullcarstylename
	
	end

end
GO
PRINT N'正在更改 [dbo].[pr_get_carcompomodel_qry]...';


GO


ALTER procedure [dbo].[pr_get_carcompomodel_qry] @applyno varchar(9)
as
select cmdg_carcompomodelno 'tmp_carcompomodelno',COUNT(*) 'carmodelnums' into #tmp_carcompomodelno from nv_carmodeldata where cmdg_applyno=@applyno group by cmdg_carcompomodelno
create table #tmp_representativevehicle( 
applyno varchar(9),
carcompomodelno varchar(15),
carmodelno varchar(36),
fullcarstylename nvarchar(500),
createdate datetime,
berepresentativevehicle char(1),
carspeetuptype varchar(3)
)
if(exists(select abd_applyno from nv_applybasedata where abd_applyno = @applyno and abd_vechiletype in('G','D') ))
begin 
    insert into #tmp_representativevehicle
	select cmdg_applyno as applyno,cpm_carcompomodelno as carcompomodelno,cmdg_carmodelno as carmodelno,
	dbo.fn_FullCarstyleNameStr(@applyno,cpm_carcompomodelno,nv_carmodeldata.cmdg_carmodelno) as 'fullcarstylename',cmdg_createdate,cmdg_berepresentativevehicle,cmdg_carspeetuptype
	from nv_carmodeldata
	join nv_carcompomodel on cmdg_applyno collate Chinese_Taiwan_Stroke_BIN=cpm_applyno collate Chinese_Taiwan_Stroke_BIN and cmdg_carcompomodelno collate Chinese_Taiwan_Stroke_BIN = cpm_carcompomodelno collate Chinese_Taiwan_Stroke_BIN
	where cmdg_applyno=@applyno and cmdg_berepresentativevehicle ='Y'
end
if(exists(select abd_applyno from nv_applybasedata where abd_applyno = @applyno and abd_vechiletype = 'M' ))
begin
 insert into #tmp_representativevehicle
	select cmdg_applyno as applyno,cpm_carcompomodelno as carcompomodelno,cmdg_carmodelno as carmodelno,
	dbo.fn_FullCarstyleNameStr(@applyno,cpm_carcompomodelno,nv_carmodeldata.cmdg_carmodelno)  as 'fullcarstylename',cmdg_createdate,cmdg_berepresentativevehicle,cmdg_carspeetuptype
	from nv_carmodeldata
	join nv_carcompomodel on cmdg_applyno collate Chinese_Taiwan_Stroke_BIN =cpm_applyno collate Chinese_Taiwan_Stroke_BIN and cmdg_carcompomodelno collate Chinese_Taiwan_Stroke_BIN = cpm_carcompomodelno collate Chinese_Taiwan_Stroke_BIN
	where cmdg_applyno collate Chinese_Taiwan_Stroke_BIN =@applyno and cmdg_berepresentativevehicle collate Chinese_Taiwan_Stroke_BIN='Y'
end

select ct_sd_desc collate Chinese_Taiwan_Stroke_BIN standarddate,berepresentativevehicle collate Chinese_Taiwan_Stroke_BIN berepresentativevehicle,
#tmp_representativevehicle.carmodelno collate Chinese_Taiwan_Stroke_BIN carmodelno,nv_carcompomodel.*,ct_at_id,ct_at_desc,isnull(carmodelnums,0) carmodelnums,fullcarstylename,carspeetuptype collate Chinese_Taiwan_Stroke_BIN carspeetuptype
from nv_carcompomodel 
join nv_codetbl_applytype on cpm_applytype=ct_at_id
left join #tmp_carcompomodelno on tmp_carcompomodelno collate Chinese_Taiwan_Stroke_BIN =cpm_carcompomodelno collate Chinese_Taiwan_Stroke_BIN
left join #tmp_representativevehicle on cpm_applyno collate Chinese_Taiwan_Stroke_BIN = applyno and cpm_carcompomodelno=carcompomodelno collate Chinese_Taiwan_Stroke_BIN
left join nv_codetbl_standarddate on ct_sd_id = cpm_adaptstandarddate --適用期別 ct_sd_desc
where cpm_applyno=@applyno order by cpm_carcompomodelno ASC,berepresentativevehicle desc,createdate,fullcarstylename --and cpm_applytype not in(6)

drop table #tmp_carcompomodelno
drop table #tmp_representativevehicle
GO
PRINT N'正在更改 [dbo].[pr_get_GD_data_for_co2]...';


GO

ALTER procedure [dbo].[pr_get_GD_data_for_co2]
as

--柴油車耗能合格函資料for CO2 計畫
select 
abd_carstyleyear,dbo.fn_FullCarstyleNameStr(abd_applyno,nv_carmodeldata.cmdg_carcompomodelno,nv_carmodeldata.cmdg_carmodelno) as 'fullcarstylename',
abd_databelongcompidno,abd_certdocno,ct_bd_desc,
cmdg_carchnmodelname,cmdg_carengmodelname,cmdg_carmodelcode,replace(ISNULL(convert(varchar,nv_carcompomodel.cpm_exhuast),' '),'.00','') 'cpm_exhuast', cmdg_transmissiontype ct_gm_desc, cmdg_transmissionnum,ct_vr_desc,cmdg_carmodeladd,
cmdg_carbodyshape
--CASE WHEN ct_cbsd_desc = '其他' THEN replace(ct_cbsd_desc,'其他','') +nv_carmodeldata.cmdg_carbodyshapedesc ELSE replace(ct_cbsd_desc,'-','') END as 'carbodyshapedesc'
from nv_applybasedata
join nv_carcompomodel on abd_applyno=cpm_applyno
join nv_carmodeldata on abd_applyno=cmdg_applyno and cpm_carcompomodelno=cmdg_carcompomodelno
join nv_codetbl_vehicledoortype on ct_vr_id=cmdg_cardoorcount --車門
join nv_codetbl_brand on ct_bd_id = cmdg_carbrand --廠牌 ct_bd_desc
join nv_codetbl_isimport on nv_codetbl_isimport.ct_im_id = abd_isimport --進出口 ct_im_desc
join nv_codetbl_powerfrom on ct_pf_id = abd_powerfrom --動力來源 ct_pf_desc
join nv_codetbl_inletmode on nv_codetbl_inletmode.ct_im_id=cmdg_inletmode --進氣方式ct_im_desc
join nv_codetbl_fueltype on ct_ft_id = cpm_gasolinetype --使用燃料ct_ft_desc
join nv_codetbl_gearmethod on ct_gm_id =  cmdg_transmissiontype --排檔型式ct_gm_desc
join nv_codetbl_lubricationtype on ct_le_id=cpm_provideoilmethod --供油方式ct_le_desc
join nv_codetbl_drivetype on ct_dt_id = cmdg_drivetype --驅動方式ct_dt_desc
join nv_codetbl_enginepos on ct_ep_id_g=cpm_fituptype --引擎安裝位置ct_ep_desc_g
join nv_codetbl_coolsystem on ct_cs_id=cpm_coolsystem --冷卻系統ct_cs_desc
join nv_codetbl_carbodyshape_d on ct_cbsd_id = cmdg_carbodyshape --車身樣式
where cpm_applytype not in ('6','9') -- 不異動,撤銷 不顯示
and abd_vechiletype='D'

  
  union
  
select 
abd_carstyleyear,dbo.fn_FullCarstyleNameStr(abd_applyno,nv_carmodeldata.cmdg_carcompomodelno,nv_carmodeldata.cmdg_carmodelno) as 'fullcarstylename',
abd_databelongcompidno,abd_certdocno,ct_bd_desc,
cmdg_carchnmodelname,cmdg_carengmodelname,cmdg_carmodelcode,replace(ISNULL(convert(varchar,nv_carcompomodel.cpm_exhuast),' '),'.00','') 'cpm_exhuast', cmdg_transmissiontype ct_gm_desc, cmdg_transmissionnum,ct_vr_desc,cmdg_carmodeladd,
cmdg_carbodyshape
--CASE WHEN ct_cbsg_desc = '其他' THEN replace(ct_cbsg_desc,'其他','') +nv_carmodeldata.cmdg_carbodyshapedesc ELSE replace(ct_cbsg_desc,'-','') END as 'carbodyshapedesc'
from nv_applybasedata
join nv_carcompomodel on abd_applyno=cpm_applyno
join nv_carmodeldata on abd_applyno=cmdg_applyno and cpm_carcompomodelno=cmdg_carcompomodelno
join nv_codetbl_vehicledoortype on ct_vr_id=cmdg_cardoorcount --車門
join nv_codetbl_brand on ct_bd_id = cmdg_carbrand --廠牌 ct_bd_desc
join nv_codetbl_isimport on nv_codetbl_isimport.ct_im_id = abd_isimport --進出口 ct_im_desc
join nv_codetbl_powerfrom on ct_pf_id = abd_powerfrom --動力來源 ct_pf_desc
join nv_codetbl_inletmode on nv_codetbl_inletmode.ct_im_id=cmdg_inletmode --進氣方式ct_im_desc
join nv_codetbl_fueltype on ct_ft_id = cpm_gasolinetype --使用燃料ct_ft_desc
join nv_codetbl_gearmethod on ct_gm_id =  cmdg_transmissiontype --排檔型式ct_gm_desc
join nv_codetbl_lubricationtype on ct_le_id=cpm_provideoilmethod --供油方式ct_le_desc
join nv_codetbl_drivetype on ct_dt_id = cmdg_drivetype --驅動方式ct_dt_desc
join nv_codetbl_enginepos on ct_ep_id_g=cpm_fituptype --引擎安裝位置ct_ep_desc_g
join nv_codetbl_coolsystem on ct_cs_id=cpm_coolsystem --冷卻系統ct_cs_desc
join nv_codetbl_carbodyshape_g on ct_cbsg_id = cmdg_carbodyshape --車身樣式
where cpm_applytype not in ('6','9') -- 不異動,撤銷 不顯示
and abd_vechiletype='G'
GO
PRINT N'正在更改 [dbo].[pr_generate_certdata_for_towcertsystem]...';


GO
--exec dbo.pr_generate_certdata_for_towcertsystem '106060001'
--exec dbo.pr_generate_certdata_for_towcertsystem '*********'
ALTER procedure [dbo].[pr_generate_certdata_for_towcertsystem] @applyno varchar(9)
as
declare @brandname nvarchar(500)
declare @eachapplytype nvarchar(4)
declare @carcompomodelno varchar(15)	
declare @sub10_carcompomodelno varchar(12) --取前10(電動車11)碼比對 (包括 % 符號)
declare @powerfrom varchar(1)
declare @vechiletype char(1)
select @vechiletype=abd_vechiletype,@powerfrom = abd_powerfrom from nv_applybasedata where abd_applyno=@applyno
/*importxxxx 進口地區 (沒用到一律為"-")、producearea 製造地區 (規格表中的”製造地區”)、cpm_importarea 進口國*/				
				
 --select distinct cpm_carcompomodelno,cpm_oriapplyno,abd_validateddate from nv_carcompomodel join nv_applybasedata on cpm_applyno=abd_applyno where cpm_carcompomodelno like @sub10_carcompomodelno and abd_applystatus in ('50','55') order by abd_validateddate desc
if(@vechiletype = 'M')
begin
	if(exists(select abd_isimport from nv_applybasedata where abd_applyno=@applyno and abd_isimport = '1' ))
	begin
		set @brandname = '中華民國'
	end
	else
	begin
		--set @brandname = '－'
		set @brandname = '-'
	end
end
else
begin
	if(exists(select abd_isimport from nv_applybasedata where abd_applyno=@applyno and abd_isimport = '1' ))
	begin
		set @brandname = '中華民國'
	end
	else
	begin
		select @brandname= cpm_importarea from nv_applybasedata join nv_carcompomodel on cpm_applyno=abd_applyno where abd_applyno=@applyno		
	end
end
--select @brandname
;with combine_towcertsystem as(
--車輛種類	審驗單位	合格證明編號	委託客戶統編	委託客戶名稱	合格證核准日期	核准字號	噪音標準實施日期	進口/國產	車型年	引擎族	
select abd_applyno,abd_applystatus,abd_vechiletype,'2' 'testorg',abd_enginesn,abd_databelongcompidno 'compidno',abd_databelongcompname,case ISNULL(abd_validateddate,'') when '' then (Convert(varchar(10),GETDATE(),112)) else (Convert(varchar(10),abd_validateddate,112)) end as  'certdate','' 'certdocno',ct_sdli_date,
--case abd_isimport when '1' then '2' when '2' then '1' else REPLACE(abd_isimport, '-', '－') end as 'abd_isimport'
case abd_isimport when '1' then '2' when '2' then '1' else REPLACE(ISNULL(abd_isimport,'-'), '－', '-') end as 'abd_isimport'
,abd_carstyleyear,abd_enginefamily,
--廠牌	中文車型	英文車型	車型代碼	排氣量	排擋方式	排檔數	車門數	其他(車型名稱補充)	
ct_bd_desc,cmdg_carchnmodelname,cmdg_carengmodelname,cmdg_carmodelcode,case @powerfrom when '3' then '' else cpm_exhuast end as 'cpm_exhuast',cmdg_transmissiontype,cmdg_transmissionnum,case abd_vechiletype when 'M' then '' else ct_vr_desc end as 'cmdg_cardoorcount',cmdg_carmodeladd,
--進口國	期別	兩證合一申請方式	噪音管制標準	公司地址	製造廠	進口地區	噪音申請型式	基本引擎	變速系統	最大動力	
--REPLACE(ISNULL(ct_cy_desc,'－'), '-', '－')'cpm_importarea',
REPLACE(ISNULL(cpm_importarea,'-'), '－', '-')'cpm_importarea',
cpm_adaptstandarddate,
--case abd_twocertmethod when '1' then '2' when '2' then '1' else REPLACE(abd_twocertmethod, '-', '－') end as 'abd_twocertmethod' 
case abd_twocertmethod when '1' then '2' when '2' then '1' else REPLACE(abd_twocertmethod, '－', '-') end as 'abd_twocertmethod' 
,ct_sdli_desc,abd_mailcertidocaddr 'abd_databelongcompaddr',abd_factoryname,'-' 'importxxxx'
--20180305增加7,8
,case when cpm_applytype in('5' , '7' , '8') then '2' else cpm_applytype end as 'cpm_applytype'
,cmdg_baseenginename,cmdg_transmissionmodel,cmdg_maxhorsepower+'/'+cmdg_maxhorsepowerspeed 'maxpower'
--噪音申請型式-各車型組	製造地區	車身式樣	噪音車型組代號 車型編號 負載車重	噪音值_加速噪音	噪音值_原地噪音	備註 代表車
,case when cpm_applytype in('5' , '7' , '8') then '2' else cpm_applytype end as 'carcompomodel_eachapplytype'
,dbo.fn_CastCountryDateCn(ntr_producercountry) 'tsetCT',cmdg_producercountry 'producearea',
--case abd_vechiletype when 'M' then case DATALENGTH(ct_cbsm_desc) when 0 then '－' else REPLACE(ct_cbsm_desc,'其他',cmdg_carbodyshapedesc) end
case abd_vechiletype when 'M' then case DATALENGTH(ct_cbsm_desc) when 0 then '-' else REPLACE(ct_cbsm_desc,'其他',cmdg_carbodyshapedesc) end
--when 'D' then case DATALENGTH(ct_cbsd_desc) when 0 then '－' else REPLACE(ct_cbsd_desc,'其他',cmdg_carbodyshapedesc) end
when 'D' then case DATALENGTH(ct_cbsd_desc) when 0 then '-' else REPLACE(ct_cbsd_desc,'其他',cmdg_carbodyshapedesc) end
--else case DATALENGTH(ct_cbsg_desc) when 0 then '－' else REPLACE(ct_cbsg_desc,'其他',cmdg_carbodyshapedesc) end 
else case DATALENGTH(ct_cbsg_desc) when 0 then '-' else REPLACE(ct_cbsg_desc,'其他',cmdg_carbodyshapedesc) end 

end as 'carbodychape'
,cpm_carcompomodelno,cmdg_carmodelno,
--REPLACE(ISNULL(cmdg_testweight,'－'), '-', '－') 'cmdg_loadingweight',
REPLACE(ISNULL(cmdg_testweight,'-'), '－', '-') 'cmdg_loadingweight',
--case DATALENGTH(ntr_speedupnoise) when 0 then '－' else REPLACE(ISNULL(ntr_speedupnoise,'－'), '-', '－') end as ntr_speedupnoise,
case DATALENGTH(ntr_speedupnoise) when 0 then '-' else REPLACE(ISNULL(ntr_speedupnoise,'-'), '－', '-') end as ntr_speedupnoise,
--case DATALENGTH(ntr_staynoise) when 0 then '－' else REPLACE(ISNULL(ntr_staynoise,'－'), '-', '－') end as ntr_staynoise,
case DATALENGTH(ntr_staynoise) when 0 then '-' else REPLACE(ISNULL(ntr_staynoise,'-'), '－', '-') end as ntr_staynoise,
 '' remark,cmdg_berepresentativevehicle
--case cpm_applytype when '6' then REPLACE(@eachapplytype, '5', '3') else REPLACE(cpm_applytype, '5', '3') end as 'carcompomodel_eachapplytype'
,cmdg_createdate
from nv_applybasedata
join nv_carcompomodel on cpm_applyno=abd_applyno
join nv_carmodeldata on cmdg_applyno=cpm_applyno and cmdg_carcompomodelno=cpm_carcompomodelno
join nv_codetbl_brand on ct_bd_id=cmdg_carbrand --廠牌 ct_bd_desc
left join nv_noisetestrpt on ntr_carmodelno = cmdg_carmodelno --and ntr_carcompomodelno=cmdg_carcompomodelno
join nv_codetbl_standarddatelawinfo on cpm_adaptstandarddate=ct_sdli_id  and ct_sdli_vechiletype=abd_vechiletype
join nv_codetbl_vehicledoortype on ct_vr_id=cmdg_cardoorcount --車門
left join nv_codetbl_applytype on ct_at_id = cpm_applytype --申請型式ct_at_desc
--left join nv_codetbl_standarddate ON ct_sd_id = cpm_adaptstandarddate --適用期別
left join nv_codetbl_carbodyshape_d on ct_cbsd_id = cmdg_carbodyshape --車身樣式 ct_cbsg_desc (汽車)
left join nv_codetbl_carbodyshape_g on ct_cbsg_id = cmdg_carbodyshape --車身樣式 ct_cbsg_desc (汽車)
left join nv_codetbl_carbodyshape_m on ct_cbsm_id = cmdg_carbodyshape --車身樣式 ct_cbsm_desc (機車)
--join (select max(abd_applyno) applyno from nv_applybasedata group by abd_enginefamily ) a on a.applyno = cpm_applyno
--left join nv_codetbl_gearmethod on ct_gm_id = cmdg_transmissiontype --排檔型式 ct_gm_desc
--left join nv_codetbl_cartype on ct_ct_id =abd_vechiletype --車輛種類 ct_ct_desc 
where abd_applyno=@applyno and cpm_applytype <> '9' --order by abd_applyno,cpm_carcompomodelno ASC,cmdg_berepresentativevehicle desc -- 撤銷 不顯示 --and abd_applystatus='35' 

	)

	SELECT *
	INTO #tmp_table
	FROM combine_towcertsystem 

--噪音申請型式,如是歷史車型組(不異動),請代入該車型組最後一次的申請型式	
if(exists(select cpm_applytype from dbo.nv_carcompomodel where cpm_applyno=@applyno and cpm_applytype ='6' ))
begin			
	create table #tmp_carcompotbl
	(								
	tmp_carcompomodelno varchar(15),
	tmp_applytype varchar(1) ,
	tmp_date datetime
	)
--select cpm_carcompomodelno from #tmp_table where cpm_applytype='6'
	--找出這一次申請型式=不異動 的 歷史車型組(排除不異動) cpm_carcompomodelno,cpm_applytype,abd_validateddate
	declare @cur_tmp_carcompomodel cursor
	set @cur_tmp_carcompomodel = cursor for
		select cpm_carcompomodelno from #tmp_table where cpm_applytype='6' --當次審請不異動的車型組代碼
	open @cur_tmp_carcompomodel
	fetch next from @cur_tmp_carcompomodel into @carcompomodelno
	while (@@fetch_status = 0)
	  begin
		    if (@powerfrom ='3') --電動車
			begin
				set @sub10_carcompomodelno = left(@carcompomodelno,11) + '%'
			end
			else
			begin
				set @sub10_carcompomodelno = left(@carcompomodelno,10) + '%'
			end

			
			--找出這一次申請型式=不異動 的 歷史車型組(排除不異動)存到暫存的table cpm_carcompomodelno,cpm_applytype,abd_validateddate 				
			insert into #tmp_carcompotbl 
			select distinct cpm_carcompomodelno			
			,case when cpm_applytype in('5' , '7' , '8') then '2' else cpm_applytype end as 'cpm_applytype'
			,cpm_createdate from nv_carcompomodel join nv_applybasedata on cpm_applyno=abd_applyno
			where abd_applystatus in ('50','55') 
			and cpm_carcompomodelno like @sub10_carcompomodelno
			and abd_applyno <> @applyno 
			and cpm_applytype <> '6'
			and cpm_createdate =(select MAX(cpm_createdate) cpm_createdate from nv_carcompomodel join nv_applybasedata on cpm_applyno=abd_applyno
									where abd_applystatus in ('50','55') and cpm_carcompomodelno like @sub10_carcompomodelno and abd_applyno <> @applyno and cpm_applytype <> '6') 
			
			--insert into #tmp_carcompotbl 								
			--select distinct cpm_carcompomodelno,cpm_applytype,abd_validateddate from nv_carcompomodel join nv_applybasedata on cpm_applyno=abd_applyno
			--where abd_applystatus in ('50','55') 
			--and cpm_carcompomodelno like @sub10_carcompomodelno
			--and abd_applyno <> @applyno 
			--and cpm_applytype <> '6'
			--and abd_validateddate in(select max(abd_validateddate) abd_validateddate from nv_carcompomodel join nv_applybasedata on cpm_applyno=abd_applyno
			--						 where abd_applystatus in ('50','55') and cpm_carcompomodelno like @sub10_carcompomodelno) 

		fetch next from @cur_tmp_carcompomodel into @carcompomodelno					
	end

--select * from #tmp_carcompotbl 
	--更新資料料狀態為'6'的 carcompomodel_eachapplytype
	declare @cur_carcompomodel cursor
	set @cur_carcompomodel = cursor for
		select tmp_carcompomodelno from #tmp_carcompotbl 
	open @cur_carcompomodel
	fetch next from @cur_carcompomodel into @carcompomodelno
	while (@@fetch_status = 0)
	  begin
		    if (@powerfrom ='3') --電動車
			begin
				set @sub10_carcompomodelno = left(@carcompomodelno,11) + '%'
			end
			else
			begin
				set @sub10_carcompomodelno = left(@carcompomodelno,10) + '%'
			end

		  update #tmp_table set carcompomodel_eachapplytype =(select distinct tmp_applytype from #tmp_carcompotbl where tmp_carcompomodelno= @carcompomodelno) 
		  where carcompomodel_eachapplytype='6' and cpm_carcompomodelno like @sub10_carcompomodelno and abd_applyno=@applyno
		fetch next from @cur_carcompomodel into @carcompomodelno					
	end
end	
	--重新寫入 cpm_applytype
	declare @str nvarchar(max) set @str=''
	declare @cApplyType nvarchar(250)
	declare curData cursor for 
	select distinct 
	case when dbo.fn_CastApplyTypeCn(carcompomodel_eachapplytype) in ('沿用及修改','沿用及延伸','沿用、延伸及修改') then '沿用' else dbo.fn_CastApplyTypeCn(carcompomodel_eachapplytype) end carcompomodel_eachapplytype 	
	from #tmp_table	

	open curData

	fetch next from curData into @cApplyType
	while(@@fetch_status=0)
	begin
		if len(@str)>0 set @str = @str + '、'
		set @str = @str + @cApplyType
		fetch next from curData into @cApplyType		
	end	
	close curData
	deallocate curData
    
    
    
    --20180205 期別修改ApplicationDate 6.2、6.3都是為6期
	--SELECT dbo.fn_FullCarstyleNameStr(abd_applyno,cpm_carcompomodelno,cmdg_carmodelno) as 'fullcarstylename',abd_applystatus, abd_vechiletype, testorg, abd_enginesn, compidno, abd_databelongcompname, certdate, certdocno, ct_sdli_date, abd_isimport, abd_carstyleyear, abd_enginefamily, ct_bd_desc, cmdg_carchnmodelname, cmdg_carengmodelname, cmdg_carmodelcode, cpm_exhuast ,cmdg_transmissiontype, cmdg_transmissionnum, cmdg_cardoorcount, cmdg_carmodeladd,	cpm_importarea,	cpm_adaptstandarddate, abd_twocertmethod, ct_sdli_desc,	abd_databelongcompaddr,	abd_factoryname, importxxxx, @str 'cpm_applytype', cmdg_baseenginename,	cmdg_transmissionmodel,	maxpower,REPLACE(carcompomodel_eachapplytype, '5', '3')	carcompomodel_eachapplytype, tsetCT,producearea,case DATALENGTH(carbodychape) when 0 then '－' else carbodychape end as 'carbodychape',	cpm_carcompomodelno, cmdg_carmodelno, REPLACE(cmdg_loadingweight, '.00', '') cmdg_loadingweight, ntr_speedupnoise,	ntr_staynoise, remark, cmdg_berepresentativevehicle,cmdg_createdate 
	SELECT dbo.fn_FullCarstyleNameStr(abd_applyno,cpm_carcompomodelno,cmdg_carmodelno) as 'fullcarstylename',abd_applystatus, abd_vechiletype, testorg, abd_enginesn, compidno, abd_databelongcompname, certdate, certdocno, ct_sdli_date, abd_isimport, abd_carstyleyear, abd_enginefamily, ct_bd_desc, cmdg_carchnmodelname, cmdg_carengmodelname, cmdg_carmodelcode, cpm_exhuast ,cmdg_transmissiontype, cmdg_transmissionnum, cmdg_cardoorcount, cmdg_carmodeladd,	cpm_importarea
	,case WHEN CHARINDEX('.', cpm_adaptstandarddate) >0  then left(cpm_adaptstandarddate,1) else 
	cpm_adaptstandarddate end as ApplicationDate
	,abd_twocertmethod, ct_sdli_desc,	abd_databelongcompaddr,	abd_factoryname, importxxxx, @str 'cpm_applytype', cmdg_baseenginename,	cmdg_transmissionmodel,	maxpower,REPLACE(carcompomodel_eachapplytype, '5', '3')	carcompomodel_eachapplytype, tsetCT,producearea,case DATALENGTH(carbodychape) when 0 then '-' else carbodychape end as 'carbodychape',	cpm_carcompomodelno, cmdg_carmodelno, REPLACE(cmdg_loadingweight, '.00', '') cmdg_loadingweight, ntr_speedupnoise,	ntr_staynoise, remark, cmdg_berepresentativevehicle,cmdg_createdate 
	FROM #tmp_table 
	order by cpm_carcompomodelno ASC,cmdg_berepresentativevehicle desc,cmdg_createdate,fullcarstylename
GO
PRINT N'正在更改 [dbo].[pr_check_preliminary_producercountry]...';


GO
/*
	初審查 製造地區 是否有填寫
*/
ALTER procedure [dbo].[pr_check_preliminary_producercountry] @applyno varchar(9) 
as
declare @check_result nvarchar(2000)

set @check_result = ''
if exists(select * from nv_applybasedata where abd_applyno=@applyno)
begin

	if exists(select * from nv_carcompomodel where cpm_applyno=@applyno)
	begin
	    
		
		declare @producercountry nvarchar(1000)
		declare @fullcarstylename nvarchar(1000)
		declare @cur_carcompomodel cursor		
		set @cur_carcompomodel = cursor for		
		select cmdg_producercountry,
		dbo.fn_FullCarstyleNameStr(@applyno,cpm_carcompomodelno,nv_carmodeldata.cmdg_carmodelno) as 'fullcarstylename' 
		from nv_carcompomodel 
		join nv_carmodeldata on cpm_applyno = cmdg_applyno and cpm_carcompomodelno = cmdg_carcompomodelno 
		where cpm_applyno=@applyno and cpm_applytype not in ('6','9') --不異動資料不顯示
		set @producercountry = replace(replace(@producercountry,'/','、'),'／','、')
		open @cur_carcompomodel
		fetch next from @cur_carcompomodel into @producercountry,@fullcarstylename
		while (@@fetch_status = 0)
		  begin
		  	 
				set @producercountry = replace(replace(@producercountry,'/','、'),'／','、')
				
				--檢查製造地區
				if not exists (select ct_pcy_desc from nv_codetbl_prodcountry where ct_pcy_desc in (SELECT [Value] FROM [dbo].[fn_split] (@producercountry,'、'))	)	
				begin
					set @check_result=@check_result+@fullcarstylename+' 製造地區( '+@producercountry +' )沒有填寫或有誤、'
				end
								
				fetch next from @cur_carcompomodel into @producercountry,@fullcarstylename
				
		  end
		
	end
	else
	begin
		set @check_result=@check_result+'車型組資料不存在、'
	end
	
	
end
else
begin
	set @check_result='申請編號不存在'
end	

select @check_result


--exec pr_check_preliminary_producercountry '102010007'
--exec pr_check_preliminary_producercountry '101080009'


--declare @applyno varchar(10)='102010007'

--select cmdg_producercountry,
--		dbo.fn_FullCarstyleNameStr(@applyno,cpm_carcompomodelno,nv_carmodeldata.cmdg_carmodelno) as 'fullcarstylename' 
--		from nv_carcompomodel 
--		join nv_carmodeldata on cpm_applyno = cmdg_applyno and cpm_carcompomodelno = cmdg_carcompomodelno 
--		where cpm_applyno=@applyno and cpm_applytype not in ('6','9') --不異動資料不顯示
GO
PRINT N'正在更改 [dbo].[pr_get_copycarcompomodel_qry]...';


GO

/*複製上一次申請資料功能*/
ALTER procedure [dbo].[pr_get_copycarcompomodel_qry] @oldapplyno varchar(9),@applyno varchar(9)
as

--exec pr_get_copycarcompomodel_qry '103110001','103100009'
--/*上一次申請資料 車型數*/
--select cmdg_carcompomodelno 'tmp_carcompomodelno',COUNT(*) 'carmodelnums' 
--into #tmp_carcompomodelno 
--from nv_carmodeldata where cmdg_applyno=@applyno group by cmdg_carcompomodelno

--create table #tmp_representativevehicle( 
--applyno varchar(9),
--carcompomodelno varchar(15),
--carmodelno varchar(36),
--fullcarstylename nvarchar(500),
--createdate datetime,
--berepresentativevehicle char(1)
--)
--/*上一次申請資料 */
-- insert into #tmp_representativevehicle
--	select cmdg_applyno as applyno,cpm_carcompomodelno as carcompomodelno,cmdg_carmodelno as carmodelno,
--	dbo.fn_FullCarstyleNameStr(cpm_applyno,cpm_carcompomodelno,nv_carmodeldata.cmdg_carmodelno)  as 'fullcarstylename',cmdg_createdate,cmdg_berepresentativevehicle
--	from nv_carmodeldata
--	join nv_carcompomodel on cmdg_applyno=cpm_applyno and cmdg_carcompomodelno = cpm_carcompomodelno
--	where cmdg_applyno=@applyno and cmdg_berepresentativevehicle ='Y'


--select berepresentativevehicle,#tmp_representativevehicle.carmodelno,nv_carcompomodel.*,ct_at_id,ct_at_desc,isnull(carmodelnums,0) carmodelnums,fullcarstylename
--from nv_carcompomodel 
--join nv_codetbl_applytype on cpm_applytype=ct_at_id
--left join #tmp_carcompomodelno on tmp_carcompomodelno=cpm_carcompomodelno
--left join #tmp_representativevehicle on cpm_applyno= applyno and cpm_carcompomodelno=carcompomodelno
--where cpm_applyno=@applyno 
--and carcompomodelno not in (select cpm_carcompomodelno from nv_carcompomodel where cpm_applyno=@newapplyno )
--order by cpm_carcompomodelno ASC,berepresentativevehicle desc,createdate,fullcarstylename --and cpm_applytype not in(6)

declare @vechiletype char(1)	
declare @IsExist varchar(2)	
declare @compidno varchar(100)
declare @enginefamily varchar(30)
declare @caryear_condi int
set @caryear_condi=1900


--select @newapplyno
select @vechiletype=abd_vechiletype,@enginefamily=abd_enginefamily,@compidno=abd_databelongcompidno,@caryear_condi=abd_carstyleyear from nv_applybasedata where abd_applyno=@applyno
--select @vechiletype,@IsExist,@compidno,@enginefamily
set	@IsExist='0'

if (@vechiletype ='D') --柴油車(比對-前字串)
begin                                       
	set	@enginefamily= left(@enginefamily,(len(@enginefamily)-ISNULL(NULLIF(CHARINDEX('-',REVERSE(@enginefamily)),0),0))) + '%'                           		     		
	if exists(select * from nv_applybasedata where abd_enginefamily like @enginefamily 
	--and abd_carstyleyear>=(Year(GETDATE())-1) 
	--and abd_carstyleyear<=(Year(GETDATE())+ 1) 
	and abd_carstyleyear>=@caryear_condi-1
	and abd_carstyleyear<=@caryear_condi+1
	and abd_applystatus in ('50','55') 
	and abd_databelongcompidno=@compidno)
	begin                                       
		set	@IsExist='1'
	end
end
else
begin                                       
	set @enginefamily = @enginefamily
	if exists(select * from nv_applybasedata where abd_enginefamily = @enginefamily 
	--and abd_carstyleyear>=(Year(GETDATE())-1) 
	--and abd_carstyleyear<=(Year(GETDATE())+ 1) 
	and abd_carstyleyear>=@caryear_condi-1
	and abd_carstyleyear<=@caryear_condi+1
	and abd_applystatus in ('50','55') 
	and abd_databelongcompidno=@compidno)
	begin                                       
		set	@IsExist='1'
	end
	
	
end

--select @IsExist
if (@IsExist='1')
begin
	create table #tmp_carcompotbl 
	(
	tmp_applyno varchar(9),
	tmp_compomodelno varchar(15)
	)
	
	create table #tmp_applydata
	(
		abd_applyno varchar(9)	,
		abd_carstyleyear varchar(4)
	)
	declare @tmp_applyno varchar(9)
	set @tmp_applyno=''
	declare @powerfrom varchar(1)
	SET @powerfrom=''
	
	declare @caryear varchar(4)
	select @powerfrom = abd_powerfrom,@caryear = abd_carstyleyear from nv_applybasedata where abd_applyno=@applyno
	if (@vechiletype ='D')
	begin		
		insert into #tmp_applydata 			
		select abd_applyno,abd_carstyleyear from nv_applybasedata 
		where abd_enginefamily like @enginefamily and abd_carstyleyear>=convert(varchar,convert(int,@caryear)-1) and abd_carstyleyear<=@caryear
		and abd_applystatus in ('50','55') and abd_databelongcompidno=@compidno
	end
	else
	begin
		insert into #tmp_applydata 			
		select abd_applyno,abd_carstyleyear from nv_applybasedata where abd_enginefamily = @enginefamily 
		and abd_carstyleyear>=convert(varchar,convert(int,@caryear)-1) and abd_carstyleyear<=@caryear		 
		and abd_applystatus in ('50','55') and abd_databelongcompidno=@compidno
	end	  
	
	select case abd_vechiletype when 'D' then left(cpm_carcompomodelno,11) else left(cpm_carcompomodelno,10) end as 'invalid_carcompomodelno' into #invalid_carcompo 
	from nv_carcompomodel 
	join nv_applybasedata on cpm_applyno=abd_applyno
	where cpm_applyno collate Chinese_Taiwan_Stroke_BIN in (select abd_applyno collate Chinese_Taiwan_Stroke_BIN from #tmp_applydata) and cpm_applytype collate Chinese_Taiwan_Stroke_BIN = '9'

	--select abd_applyno into #tmp_applydata from nv_applybasedata where abd_enginefamily like @enginefamily and abd_carstyleyear>=(Year(GETDATE())-1) and abd_carstyleyear<=(Year(GETDATE())+ 1) and abd_applystatus in ('50','55') and abd_databelongcompidno=@compidno
	--車型組編號：新車型的車型年2碼+廠商編號4碼(電動車5碼E+四碼)+”-“車型種類+”流水號2碼”(同一廠商的車型種類流水號)+”-“+沿用車型年”。系統比對時僅能取前10(電動車11)碼比對
	while (select count(*) from #tmp_applydata) > 0
	begin
		set @tmp_applyno = (select top 1 abd_applyno from #tmp_applydata order by abd_carstyleyear desc,abd_applyno desc)
		if (@powerfrom ='3')
		begin
			insert into #tmp_carcompotbl 
			select cpm_applyno,cpm_carcompomodelno from nv_carcompomodel 
			where cpm_applyno collate Chinese_Taiwan_Stroke_BIN = @tmp_applyno 
			and left(cpm_carcompomodelno collate Chinese_Taiwan_Stroke_BIN ,11) not in (select left(tmp_compomodelno collate Chinese_Taiwan_Stroke_BIN,11) tmp_compomodelno from #tmp_carcompotbl) 
			and left(cpm_carcompomodelno collate Chinese_Taiwan_Stroke_BIN ,11) not in (select invalid_carcompomodelno collate Chinese_Taiwan_Stroke_BIN invalid_carcompomodelno from #invalid_carcompo)
			and cpm_applytype <> '9'
		end
		else
		begin
			insert into #tmp_carcompotbl 
			select cpm_applyno,cpm_carcompomodelno from nv_carcompomodel 
			where cpm_applyno collate Chinese_Taiwan_Stroke_BIN= @tmp_applyno 
			and left(cpm_carcompomodelno collate Chinese_Taiwan_Stroke_BIN,10) not in (select left(tmp_compomodelno collate Chinese_Taiwan_Stroke_BIN,10) tmp_compomodelno from #tmp_carcompotbl) 
			and left(cpm_carcompomodelno collate Chinese_Taiwan_Stroke_BIN,10) not in (select invalid_carcompomodelno collate Chinese_Taiwan_Stroke_BIN invalid_carcompomodelno from #invalid_carcompo)
			and cpm_applytype collate Chinese_Taiwan_Stroke_BIN <> '9'
		end
		delete from #tmp_applydata where abd_applyno = @tmp_applyno
	end


/*上一次申請資料 車型數*/
--select cmdg_carcompomodelno 'tmp_carcompomodelno',COUNT(*) 'carmodelnums' 
--into #tmp_carcompomodelno 
--from nv_carmodeldata where cmdg_applyno=@applyno group by cmdg_carcompomodelno

select cmdg_carcompomodelno 'tmp_carcompomodelno',COUNT(*) 'carmodelnums' 
into #tmp_carcompomodelno 
from nv_carmodeldata where cmdg_applyno+'_'+cmdg_carcompomodelno collate Chinese_Taiwan_Stroke_BIN in (select tmp_applyno+'_'+tmp_compomodelno collate Chinese_Taiwan_Stroke_BIN from #tmp_carcompotbl)
group by cmdg_carcompomodelno


create table #tmp_representativevehicle( 
applyno varchar(9),
carcompomodelno varchar(15),
carmodelno varchar(36),
fullcarstylename nvarchar(500),
createdate datetime,
berepresentativevehicle char(1)
)
/*上一次申請資料 */
 insert into #tmp_representativevehicle
	select cmdg_applyno as applyno,cpm_carcompomodelno as carcompomodelno,cmdg_carmodelno as carmodelno,
	dbo.fn_FullCarstyleNameStr(cpm_applyno,cpm_carcompomodelno,nv_carmodeldata.cmdg_carmodelno)  as 'fullcarstylename',cmdg_createdate,cmdg_berepresentativevehicle
	from nv_carmodeldata
	join nv_carcompomodel on cmdg_applyno=cpm_applyno and cmdg_carcompomodelno = cpm_carcompomodelno
	where cmdg_applyno collate Chinese_Taiwan_Stroke_BIN in (select tmp_applyno collate Chinese_Taiwan_Stroke_BIN from #tmp_carcompotbl) and cmdg_berepresentativevehicle collate Chinese_Taiwan_Stroke_BIN ='Y'

--select berepresentativevehicle,#tmp_representativevehicle.carmodelno,nv_carcompomodel.*,ct_at_id,ct_at_desc,isnull(carmodelnums,0) carmodelnums,fullcarstylename
--from nv_carcompomodel 
--join nv_codetbl_applytype on cpm_applytype=ct_at_id
--left join #tmp_carcompomodelno on tmp_carcompomodelno=cpm_carcompomodelno
--left join #tmp_representativevehicle on cpm_applyno= applyno and cpm_carcompomodelno=carcompomodelno
--where cpm_applyno=@applyno 
--and carcompomodelno not in (select cpm_carcompomodelno from nv_carcompomodel where cpm_applyno=@newapplyno )
--order by cpm_carcompomodelno ASC,berepresentativevehicle desc,createdate,fullcarstylename --and cpm_applytype not in(6)

select berepresentativevehicle,#tmp_representativevehicle.carmodelno,nv_carcompomodel.*,ct_at_id,ct_at_desc,isnull(carmodelnums,0) carmodelnums,fullcarstylename
from nv_carcompomodel 
join nv_codetbl_applytype on cpm_applytype=ct_at_id
join #tmp_carcompomodelno on tmp_carcompomodelno collate Chinese_Taiwan_Stroke_BIN=cpm_carcompomodelno collate Chinese_Taiwan_Stroke_BIN
join #tmp_representativevehicle on cpm_applyno collate Chinese_Taiwan_Stroke_BIN= applyno and cpm_carcompomodelno=carcompomodelno collate Chinese_Taiwan_Stroke_BIN
where case @powerfrom when '3' then left(carcompomodelno collate Chinese_Taiwan_Stroke_BIN ,11) else left(carcompomodelno collate Chinese_Taiwan_Stroke_BIN ,10) end not in (select case @powerfrom when '3' then left(cpm_carcompomodelno collate Chinese_Taiwan_Stroke_BIN ,11) else left(cpm_carcompomodelno collate Chinese_Taiwan_Stroke_BIN,10) end from nv_carcompomodel where cpm_applyno=@applyno )
order by cpm_carcompomodelno ASC,berepresentativevehicle desc,createdate,fullcarstylename --and cpm_applytype not in(6)


--select * from #tmp_carcompomodelno
--select * from #tmp_representativevehicle
end
else
begin
	select '' berepresentativevehicle,'' carmodelno,nv_carcompomodel.*,'' ct_at_id,'' ct_at_desc,'' carmodelnums,'' fullcarstylename
	from nv_carcompomodel where 1=2
	
end
GO
PRINT N'正在更改 [dbo].[pr_provide_common_data_for_population_M]...';


GO
--供汙染從噪音複製資料使用  機車
ALTER procedure [dbo].[pr_provide_common_data_for_population_M] @databelongcompidno nvarchar(100),@vechiletype char(1),@beimport char(1),@carstyleyear varchar(4),@engineamily varchar(30)
as
declare @applyno varchar(9)
set @applyno=''
--set @applyno = '101080005'
select top 1  @applyno= abd_applyno from nv_applybasedata where abd_databelongcompidno=@databelongcompidno and abd_enginefamily=@engineamily and abd_carstyleyear=@carstyleyear and abd_vechiletype=@vechiletype and abd_applystatus not in ('10','60','70','90','99') order by abd_applyno desc

create table #tmp_gearratio
(
tmp_carmodelno varchar(36),
tmp_gearratio varchar(800)
)

declare @carmodelno varchar(36)
declare @gearratio nvarchar(800)
set @gearratio=''
declare @cursor_gearratio cursor
set @cursor_gearratio = cursor for select distinct gr_carmodelno from nv_gearratio where gr_applyno=@applyno
open @cursor_gearratio
Fetch Next From @cursor_gearratio
Into @carmodelno
while (@@fetch_status = 0)
  begin
	select @gearratio = @gearratio+convert(varchar,gr_gearratio)+',' from nv_gearratio where gr_carmodelno=@carmodelno and gr_applyno=@applyno order by gr_gearnum
	insert into #tmp_gearratio values(@carmodelno,substring(@gearratio,1,LEN(@gearratio)-1))
	set @gearratio=''
	Fetch Next From @cursor_gearratio
	Into @carmodelno
  end

----G 汽車
--select top 1
--abd_mailcertidocaddr,cmdg_maxhorsepower,cmdg_maxhorsepowerspeed,
--ct_bd_desc , cmdg_carchnmodelname,cmdg_carengmodelname,cmdg_carmodelcode,replace(ISNULL(convert(varchar,nv_carcompomodel.cpm_exhuast),' '),'.00','') 'cpm_exhuast',ct_gm_desc, cmdg_transmissionnum,ct_vr_desc,cmdg_carmodeladd,
--replace(ct_bd_desc + ' '+ nv_carmodeldata.cmdg_carchnmodelname+ ' '+nv_carmodeldata.cmdg_carengmodelname+ ' '+nv_carmodeldata.cmdg_carmodelcode+ ' '+ replace(ISNULL(convert(varchar,nv_carcompomodel.cpm_exhuast),' '),'.00','') +'c.c.'+ ' '+ct_gm_desc+case ISNULL(ct_gm_desc,'') when 'CVT' then '' else convert(varchar,nv_carmodeldata.cmdg_transmissionnum) end+' '+ct_vr_desc +' '+nv_carmodeldata.cmdg_carmodeladd,'  ',' ') as 'carstylename',
--cmdg_baseenginename,cmdg_transmissionmodel,ct_cs_desc,cpm_cylindernums,cmdg_cylindermeters,cmdg_cylinderstroke
--cmdg_compressionratio,'' '進氣方式',ct_dt_desc,cmdg_differentialgearratio,tmp_gearratio
--from nv_applybasedata
--join nv_carcompomodel on abd_applyno=cpm_applyno
--join nv_carmodeldata on abd_applyno=cmdg_applyno and cpm_carcompomodelno=cmdg_carcompomodelno
--join nv_codetbl_vehicledoortype on ct_vr_id=cmdg_cardoorcount --車門
--join nv_codetbl_brand on ct_bd_id = cmdg_carbrand --廠牌
--join nv_codetbl_drivetype on ct_dt_id=cmdg_drivetype  --驅動方式
--join nv_codetbl_gearmethod on ct_gm_id = cpm_transmissiontypetype
--join nv_codetbl_coolsystem on ct_cs_id=cpm_coolsystem
--join #tmp_gearratio on tmp_carmodelno=cmdg_carmodelno
--where abd_applyno=@applyno

--M 機車
--abd_mailcertidocaddr--合格證地址
--ct_pf_desc--動力來源
--nv_codetbl_isimport.ct_im_desc--進口/國產
--abd_factoryname--車輛製造者
--cmdg_maxhorsepower--引擎最大功率
--cmdg_maxhorsepowerspeed--轉速
--cmdg_baseenginename--基本引擎
--cmdg_transmissionmodel--變數系統
--ct_ebm_desc--燃燒循環
--ct_cp_desc_m--汽缸體形式
--cpm_cylindernums--汽缸數
--ct_cs_desc--冷卻系統
--cmdg_cylindermeters--缸徑
--cmdg_cylinderstroke--衝程
--cmdg_compressionratio--壓縮比
--ct_bd_desc--廠牌
--cmdg_carchnmodelname--中文車型名
--cmdg_carengmodelname--英文車型名
--cmdg_carmodelcode--車型代碼
--cpm_exhuast--排氣量
--ct_gm_desc--排檔方式
--cmdg_transmissionnum--排檔數
--cardoorcount--門數
--cmdg_carmodeladd--其他
--cmdg_1stReductionratio--一次減速比
--cmdg_2ndReductionratio--二次減速比
--tmp_gearratio--齒比
--carstylename--車型名稱
select 
abd_applyno,abd_mailcertidocaddr,ct_pf_desc,ct_im_desc,abd_factoryname,cmdg_maxhorsepower,cmdg_maxhorsepowerspeed,
ct_bd_desc,cmdg_carchnmodelname,cmdg_carengmodelname,cmdg_carmodelcode,replace(ISNULL(convert(varchar,nv_carcompomodel.cpm_exhuast),' '),'.00','') 'cpm_exhuast',ct_gm_desc, cmdg_transmissionnum,'' as cardoorcount,cmdg_carmodeladd,
CASE WHEN ct_cbsm_desc = '其他' THEN replace(ct_cbsm_desc,'其他','') +nv_carmodeldata.cmdg_carbodyshapedesc ELSE replace(ct_cbsm_desc,'-','') END as 'carbodyshapedesc',
dbo.fn_FullCarstyleNameStr(@applyno,nv_carmodeldata.cmdg_carcompomodelno,nv_carmodeldata.cmdg_carmodelno) as 'fullcarstylename',
cmdg_baseenginename,cmdg_transmissionmodel,ct_ebm_desc,ct_cp_desc_m,ct_cs_desc,cpm_cylindernums,cmdg_cylindermeters,cmdg_cylinderstroke,
cmdg_compressionratio,cmdg_1stReductionratio,cmdg_2ndReductionratio,tmp_gearratio
from nv_applybasedata
join nv_carcompomodel on abd_applyno=cpm_applyno
join nv_carmodeldata on abd_applyno=cmdg_applyno and cpm_carcompomodelno=cmdg_carcompomodelno
join nv_codetbl_brand on ct_bd_id = cmdg_carbrand --廠牌
join nv_codetbl_isimport on nv_codetbl_isimport.ct_im_id = abd_isimport --進出口 ct_im_desc
join nv_codetbl_powerfrom on ct_pf_id = abd_powerfrom --動力來源 ct_pf_desc
join nv_codetbl_gearmethod on ct_gm_id = cmdg_transmissiontype --排檔型式 ct_gm_desc
join nv_codetbl_coolsystem on ct_cs_id=cpm_coolsystem --冷卻系統 ct_cs_desc
join nv_codetbl_carbodyshape_m on ct_cbsm_id = cmdg_carbodyshape--車身樣式
join nv_codetbl_engineburnmethod on ct_ebm_id = cpm_powerexportway --燃燒循環 ct_ebm_desc
join nv_codetbl_cylinderpos_m on ct_cp_id_m = cpm_cylindertype --汽缸排列 ct_cp_desc_m
left join #tmp_gearratio on tmp_carmodelno=cmdg_carmodelno
where abd_applyno=@applyno and cpm_applytype not in ('9') -- 不異動,撤銷 不顯示

--exec pr_provide_common_data_for_population_M 'hceouOMffefwDR9HIi025Q==','M','2','2013','G1-992S-12'
--SELECT * from nv_applybasedata where  abd_applyno='101120010'
--exec pr_provide_common_data_for_population_M '6B3DU+Zqs/QCcC0yRN4Heg==','M','1','2012','U1234567_m1'
--exec pr_provide_common_data_for_population_M '9hvLq1t7EZ46KXeNLEx6UQ==','M','2','2013','G12517C-12'
GO
PRINT N'正在更改 [dbo].[pr_applysubmit_datahandle]...';


GO
-- =============================================
-- Author:		<880346>
-- Create date: <********>
-- Description:	噪音送件
-- =============================================
ALTER PROCEDURE [dbo].[pr_applysubmit_datahandle] @applyno varchar(9),@applyaccount varchar(20)
AS
BEGIN
	declare @applystatus varchar(2)	
	declare @enginefamily varchar(32)	
	declare @compidno nvarchar(100)	
	declare @vechiletype char(1)

	declare @Applytype varchar(2)	
	select @applystatus=abd_applystatus from nv_applybasedata where abd_applyno=@applyno
	select @enginefamily=abd_enginefamily from nv_applybasedata where abd_applyno=@applyno
	select @compidno=abd_databelongcompidno from nv_applybasedata where abd_applyno=@applyno			
	select @vechiletype=abd_vechiletype from nv_applybasedata where abd_applyno=@applyno
	
	if (@vechiletype ='D') --柴油車(比對-前字串)
	begin                                       
		set	@enginefamily= left(@enginefamily,(len(@enginefamily)-ISNULL(NULLIF(CHARINDEX('-',REVERSE(@enginefamily)),0),0))) + '%'                           		     		
	end

	if (@applystatus = '10' or @applystatus='40' or @applystatus='90')
	begin
		--如果是新車型(1) & 沿用(2) 沿用修改(5) 須自動新增修正目錄	
		--******** 新增78
		SET XACT_ABORT ON
		begin TRANSACTION	
			if exists(select cpm_applyno from dbo.nv_carcompomodel where cpm_applyno=@applyno and cpm_applytype='1')
			begin
				if not exists(select fc_applyno from nv_fixcatalog where fc_applyno=@applyno)
				begin
					INSERT INTO nv_fixcatalog
					select @applyno,cpm_carcompomodelno,'-','-','-',@applyaccount,GETDATE(),@applyaccount,GETDATE(),@applyno from dbo.nv_carcompomodel 
					where cpm_applyno=@applyno and cpm_applytype='1'
				end	
			end
			if exists(select cpm_applyno from dbo.nv_carcompomodel where cpm_applyno=@applyno and cpm_applytype in ('2','5','7','8'))
			begin
					if not exists(select fc_applyno from nv_fixcatalog where fc_applyno=@applyno)
					begin
						INSERT INTO nv_fixcatalog
						select @applyno,cpm_carcompomodelno,'-','-','-',@applyaccount,GETDATE(),@applyaccount,GETDATE(),case cpm_oriapplyno when '' then @applyno else cpm_oriapplyno end from dbo.nv_carcompomodel 
						where cpm_applyno=@applyno and cpm_applytype in ('2','5','7','8')
					end	
			end
			--更新狀態 & insert log
			if (@applystatus = '10' or @applystatus='90')
			begin
				update nv_applybasedata set abd_applystatus='20',abd_applydate=GETDATE() where  abd_applyno=@applyno and abd_applystatus in('10','90')
				exec dbo.pr_insert_validationprocess @applyno,'20','',@applyaccount
			end
			else
			begin
				update nv_applybasedata set abd_applystatus='21',abd_applydate=GETDATE() where  abd_applyno=@applyno and abd_applystatus = '40'
				exec dbo.pr_insert_validationprocess @applyno,'21','',@applyaccount
			end
			
			--如果申請型式為不異動要重新取得資料  
			--******** 單純 沿用 則視為不異動須重新複製資料，但是車型組名稱已被調整，必須被保留
			if exists(select * from nv_carcompomodel where cpm_applyno=@applyno and cpm_applytype in('6','2')  )
			begin

			select @Applytype=cpm_applytype from nv_carcompomodel where cpm_applyno=@applyno		
				
				create table #tmp_carcompotbl
				(
				tmp_applyno varchar(9),
				tmp_compomodelno varchar(15) ,
				tmp_oriapplyno varchar(9)
				)
				insert into #tmp_carcompotbl 
				select cpm_applyno,cpm_carcompomodelno,cpm_oriapplyno from nv_carcompomodel where cpm_applyno = @applyno and cpm_applytype=@Applytype
						
				declare @carcompomodelno varchar(15)		
				declare @cur_carcompomodel cursor
				set @cur_carcompomodel = cursor for
					select tmp_compomodelno from #tmp_carcompotbl where tmp_applyno=@applyno
				open @cur_carcompomodel
				fetch next from @cur_carcompomodel into @carcompomodelno
				while (@@fetch_status = 0)
				  begin
						if (@vechiletype ='D') --柴油車(比對-前字串)
						begin                                       		
							--判斷該引擎族&車型組 是否有已核可資料，才執行刪除與複製
							if exists(
							select * from nv_applybasedata
							join nv_carcompomodel on cpm_applyno=abd_applyno
							where abd_enginefamily like @enginefamily and abd_carstyleyear>=(Year(GETDATE())-1) and abd_carstyleyear<=(Year(GETDATE())+ 1) and abd_applystatus in ('50','55') and abd_databelongcompidno=@compidno
							and cpm_applyno<>@applyno and cpm_carcompomodelno =@carcompomodelno )
							begin
								delete from nv_carcompomodel where cpm_applyno+'_'+cpm_carcompomodelno = (select tmp_applyno+'_'+tmp_compomodelno from #tmp_carcompotbl where tmp_compomodelno=@carcompomodelno)
								delete from nv_carmodeldata where cmdg_applyno+'_'+cmdg_carcompomodelno = (select tmp_applyno+'_'+tmp_compomodelno from #tmp_carcompotbl where tmp_compomodelno=@carcompomodelno)
								delete from nv_gearratio where gr_applyno+'_'+gr_carcompomodelno = (select tmp_applyno+'_'+tmp_compomodelno from #tmp_carcompotbl where tmp_compomodelno=@carcompomodelno)
								delete from nv_noisedefenseplan where ndp_applyno+'_'+ndp_carcompomodelno = (select tmp_applyno+'_'+tmp_compomodelno from #tmp_carcompotbl where tmp_compomodelno=@carcompomodelno)
								delete from nv_noisedefenseplanfiles where ndpf_applyno+'_'+ndpf_carcompomodelno = (select tmp_applyno+'_'+tmp_compomodelno from #tmp_carcompotbl where tmp_compomodelno=@carcompomodelno)
								delete from nv_fixcatalog where fc_applyno+'_'+fc_carcompomodelno+'_'+fc_oriapplyno = (select tmp_applyno+'_'+tmp_compomodelno+'_'+tmp_oriapplyno from #tmp_carcompotbl where tmp_compomodelno=@carcompomodelno)
								delete from nv_applydatafiles where adf_applyno+'_'+adf_carcompomodelno = (select tmp_applyno+'_'+tmp_compomodelno from #tmp_carcompotbl where tmp_compomodelno=@carcompomodelno)
								--1. copy 車型 nv_carcompomodel
								insert into dbo.nv_carcompomodel
								select @applyno
								,cpm_carcompomodelno
								,@Applytype
								,cpm_useeurocert
								,cpm_baseenginename
								,cpm_transmissiontypetype
								,cpm_gasolinetype
								,cpm_powerexportway
								,cpm_provideoilmethod
								,cpm_exhuast
								,cpm_cylindernums
								,cpm_cylindertype
								,cpm_enginesetupposnmethod
								,cpm_fituptype
								,cpm_coolsystem
								,cpm_testfactory
								,cpm_importarea
								,@applyaccount
								,GETDATE()
								,@applyaccount
								,GETDATE()
								,cpm_applyno
								,cpm_adaptstandarddate
								,cpm_vechilekind
								,cpm_silencersystem
								,cpm_engineompartment
								from nv_carcompomodel where cpm_applyno+'_'+cpm_carcompomodelno =( select tmp_oriapplyno+'_'+tmp_compomodelno from #tmp_carcompotbl where tmp_compomodelno=@carcompomodelno) --=  (select MAX(abd_applyno) from nv_applybasedata where abd_enginefamily like @enginefamily  and abd_applystatus in ('50','55') and abd_databelongcompidno=@compidno)+'_'+@carcompomodelno            --( select tmp_oriapplyno+'_'+tmp_compomodelno from #tmp_carcompotbl where tmp_compomodelno=@carcompomodelno)
								--2. copy 車型 nv_carmodeldata車型組清單中，依據 車型組暫存資料表裡的(applyno,carcompomodelno) ，取得車型資料
								insert into dbo.nv_carmodeldata
								SELECT @applyno
								,cmdg_carcompomodelno
								,cmdg_carmodelno
								,cmdg_carbodyshape
								,cmdg_carbodyshapedesc
								,cmdg_berepresentativevehicle
								,cmdg_cartype
								,cmdg_baseenginename
								,cmdg_carbrand
								,cmdg_carchnmodelname
								,cmdg_carengmodelname
								,cmdg_carmodelcode
								,cmdg_transmissionnum
								,cmdg_cardoorcount
								,cmdg_carmodeladd
								,cmdg_carbodylenth
								,cmdg_carbodywidth
								,cmdg_carbodyhieght
								,cmdg_carwheelbase
								,cmdg_carwheelbase_f
								,cmdg_carwheelbase_b
								,cmdg_caremptyweight
								,cmdg_cartotalweight
								,cmdg_passengers
								,cmdg_loadingweight
								,cmdg_cylindermeters
								,cmdg_cylinderstroke
								,cmdg_compressionratio
								,cmdg_maxhorsepower
								,cmdg_maxhorsepowerspeed
								,cmdg_torque
								,cmdg_torquespeed
								,cmdg_turborchargers
								,cmdg_strokeposdirection
								,cmdg_oilcapacity
								,cmdg_929598
								,cmdg_firemethod
								,cmdg_exhaustsystem
								,cmdg_PCV
								,cmdg_EEC
								,cmdg_PM
								,cmdg_CO
								,cmdg_HC
								,cmdg_drivetraintype
								,cmdg_differentialmodel
								,cmdg_differentialgearratio
								,cmdg_1stReductionratio
								,cmdg_2ndReductionratio
								,cmdg_transmissionmodel
								,cmdg_transmissionbackgearratio
								,cmdg_highestspeed
								,cmdg_suspensionsystem_f
								,cmdg_suspensionsystem_b
								,cmdg_tirespec_std_f
								,cmdg_tirespec_std_b
								,cmdg_tirespec_cho_f
								,cmdg_tirespec_cho_b
								,cmdg_testweight
								,cmdg_testgearreductionratio
								,cmdg_coolingdrivermethod
								,cmdg_tiresnum
								,cmdg_exhaustpipenum
								,cmdg_tirewidth
								,cmdg_inletmode
								,cmdg_beextendmodel
								,cmdg_beenmod
								,cmdg_carusedstatus
								,@applyaccount
								,GETDATE()
								,@applyaccount
								,GETDATE()
								,cmdg_drivetype
								,cmdg_remark
								,cmdg_carbodyweight
								,cmdg_numofaxes_f
								,cmdg_numofaxes_b
								,cmdg_increasepowerstyle
								,cmdg_increasepowerratio
								,cmdg_backgearnums
								,cmdg_highlowgear
								,cmdg_climbratio
								,cmdg_suspensionsystem_supplement
								,cmdg_maxhorsepower_hb
								,cmdg_maxhorsepowerspeed_hb
								,cmdg_torque_hb
								,cmdg_torquespeed_hb
								,cmdg_producercountry
								,cmdg_forwardgearnums
								,cmdg_drivetypeotherdesc
								,cmdg_salename_m
								,cmdg_carspeetuptype
								,cmdg_transmissiontype
								,cmdg_lockgeerratiomode
								,cmdg_lockgeerratiomodedesc
								,cmdg_lockgeerratiomodefileid
								,cmdg_silencersystemfileid
								,cmdg_engineompartmentfileid
								FROM nv_carmodeldata where cmdg_applyno+'_'+cmdg_carcompomodelno =( select tmp_oriapplyno+'_'+tmp_compomodelno from #tmp_carcompotbl where tmp_compomodelno=@carcompomodelno) --= (select MAX(abd_applyno) from nv_applybasedata where abd_enginefamily like @enginefamily  and abd_applystatus in ('50','55') and abd_databelongcompidno=@compidno)+'_'+@carcompomodelno
								--2.2排檔齒比資料
								insert into nv_gearratio
								select @applyno,gr_carcompomodelno,gr_carmodelno,gr_gearnum,gr_gearratio
								from dbo.nv_gearratio where gr_applyno+'_'+gr_carcompomodelno = (select MAX(abd_applyno) from nv_applybasedata where abd_enginefamily like @enginefamily  and abd_applystatus in ('50','55') and abd_databelongcompidno=@compidno)+'_'+@carcompomodelno 
								--3. copy 防治對策 nv_noisedefenseplan copy 上一次的申請資料即可 select max(applyno) from nv_applybasedata where abd_applystatus='50' and abd_databelongcompidno=@compidno and abd_enginefamily=@enginefamily
								insert into nv_noisedefenseplan(ndp_applyno,ndp_carcompomodelno,ndp_position,ndp_defenseeqpname,ndp_eqpmaterial,ndp_eqpheight,ndp_equremark,ndp_createaccount,ndp_createdate,ndp_modaccount,ndp_moddate)
								select @applyno,ndp_carcompomodelno
								,ndp_position
								,ndp_defenseeqpname
								,ndp_eqpmaterial
								,ndp_eqpheight
								,ndp_equremark
								,@applyaccount
								,GETDATE()
								,@applyaccount
								,GETDATE()
								from nv_noisedefenseplan where ndp_applyno+'_'+ndp_carcompomodelno =( select tmp_oriapplyno+'_'+tmp_compomodelno from #tmp_carcompotbl where tmp_compomodelno=@carcompomodelno) --= (select MAX(abd_applyno) from nv_applybasedata join nv_carcompomodel on abd_applyno=cpm_applyno and cpm_carcompomodelno=@carcompomodelno where abd_enginefamily like @enginefamily  and abd_applystatus in ('50','55') and abd_databelongcompidno=@compidno)+'_'+@carcompomodelno 
								--4. copy 修正目錄 nv_fixcatalog copy 上一次的申請資料即可
								insert into nv_fixcatalog(fc_applyno,fc_carcompomodelno,fc_fixitem,fc_beforefix,fc_afterfix,fc_createaccount,fc_createdate,fc_modaccount,fc_moddate,fc_oriapplyno)
								select @applyno,fc_carcompomodelno,fc_fixitem,fc_beforefix,fc_afterfix
								,@applyaccount
								,fc_createdate
								,@applyaccount
								,fc_moddate
								,fc_oriapplyno
								from nv_fixcatalog where fc_applyno+'_'+fc_carcompomodelno =( select tmp_oriapplyno+'_'+tmp_compomodelno from #tmp_carcompotbl where tmp_compomodelno=@carcompomodelno) --= (select MAX(abd_applyno) from nv_applybasedata where abd_enginefamily like @enginefamily  and abd_applystatus in ('50','55') and abd_databelongcompidno=@compidno)+'_'+@carcompomodelno 
								--5. copy 檔案上傳
								insert into nv_applydatafiles(adf_applyno,adf_carcompomodelno,adf_filetype,adf_fileid)
								select @applyno
								,adf_carcompomodelno
								,adf_filetype
								,adf_fileid
								from nv_applydatafiles where adf_applyno+'_'+adf_carcompomodelno =( select tmp_oriapplyno+'_'+tmp_compomodelno from #tmp_carcompotbl where tmp_compomodelno=@carcompomodelno) --= (select MAX(abd_applyno) from nv_applybasedata where abd_enginefamily=@enginefamily  and abd_applystatus in ('50','55') and abd_databelongcompidno=@compidno)+'_'+@carcompomodelno 										
							end		
						end
						else
						begin
						--判斷該引擎族&車型組 是否有已核可資料，才執行刪除與複製
							if exists(
							select * from nv_applybasedata
							join nv_carcompomodel on cpm_applyno=abd_applyno
							where abd_enginefamily=@enginefamily and abd_carstyleyear>=(Year(GETDATE())-1) and abd_carstyleyear<=(Year(GETDATE())+ 1) and abd_applystatus in ('50','55') and abd_databelongcompidno=@compidno
							and cpm_applyno<>@applyno and cpm_carcompomodelno =@carcompomodelno )
							begin
								delete from nv_carcompomodel where cpm_applyno+'_'+cpm_carcompomodelno = (select tmp_applyno+'_'+tmp_compomodelno from #tmp_carcompotbl where tmp_compomodelno=@carcompomodelno)
								delete from nv_carmodeldata where cmdg_applyno+'_'+cmdg_carcompomodelno = (select tmp_applyno+'_'+tmp_compomodelno from #tmp_carcompotbl where tmp_compomodelno=@carcompomodelno)
								delete from nv_gearratio where gr_applyno+'_'+gr_carcompomodelno = (select tmp_applyno+'_'+tmp_compomodelno from #tmp_carcompotbl where tmp_compomodelno=@carcompomodelno)
								delete from nv_noisedefenseplan where ndp_applyno+'_'+ndp_carcompomodelno = (select tmp_applyno+'_'+tmp_compomodelno from #tmp_carcompotbl where tmp_compomodelno=@carcompomodelno)
								delete from nv_noisedefenseplanfiles where ndpf_applyno+'_'+ndpf_carcompomodelno = (select tmp_applyno+'_'+tmp_compomodelno from #tmp_carcompotbl where tmp_compomodelno=@carcompomodelno)
								delete from nv_fixcatalog where fc_applyno+'_'+fc_carcompomodelno+'_'+fc_oriapplyno = (select tmp_applyno+'_'+tmp_compomodelno+'_'+tmp_oriapplyno from #tmp_carcompotbl where tmp_compomodelno=@carcompomodelno)
								delete from nv_applydatafiles where adf_applyno+'_'+adf_carcompomodelno = (select tmp_applyno+'_'+tmp_compomodelno from #tmp_carcompotbl where tmp_compomodelno=@carcompomodelno)
								--1. copy 車型 nv_carcompomodel
								insert into dbo.nv_carcompomodel
								select @applyno
								,cpm_carcompomodelno
								,@Applytype
								,cpm_useeurocert
								,cpm_baseenginename
								,cpm_transmissiontypetype
								,cpm_gasolinetype
								,cpm_powerexportway
								,cpm_provideoilmethod
								,cpm_exhuast
								,cpm_cylindernums
								,cpm_cylindertype
								,cpm_enginesetupposnmethod
								,cpm_fituptype
								,cpm_coolsystem
								,cpm_testfactory
								,cpm_importarea
								,@applyaccount
								,GETDATE()
								,@applyaccount
								,GETDATE()
								,cpm_applyno
								,cpm_adaptstandarddate
								,cpm_vechilekind
								,cpm_silencersystem
								,cpm_engineompartment
								from nv_carcompomodel where cpm_applyno+'_'+cpm_carcompomodelno =( select tmp_oriapplyno+'_'+tmp_compomodelno from #tmp_carcompotbl where tmp_compomodelno=@carcompomodelno) --=  (select MAX(abd_applyno) from nv_applybasedata where abd_enginefamily=@enginefamily  and abd_applystatus in ('50','55') and abd_databelongcompidno=@compidno)+'_'+@carcompomodelno            --( select tmp_oriapplyno+'_'+tmp_compomodelno from #tmp_carcompotbl where tmp_compomodelno=@carcompomodelno)
								--2. copy 車型 nv_carmodeldata車型組清單中，依據 車型組暫存資料表裡的(applyno,carcompomodelno) ，取得車型資料
								insert into dbo.nv_carmodeldata
								SELECT @applyno
								,cmdg_carcompomodelno
								,cmdg_carmodelno
								,cmdg_carbodyshape
								,cmdg_carbodyshapedesc
								,cmdg_berepresentativevehicle
								,cmdg_cartype
								,cmdg_baseenginename
								,cmdg_carbrand
								,cmdg_carchnmodelname
								,cmdg_carengmodelname
								,cmdg_carmodelcode
								,cmdg_transmissionnum
								,cmdg_cardoorcount
								,cmdg_carmodeladd
								,cmdg_carbodylenth
								,cmdg_carbodywidth
								,cmdg_carbodyhieght
								,cmdg_carwheelbase
								,cmdg_carwheelbase_f
								,cmdg_carwheelbase_b
								,cmdg_caremptyweight
								,cmdg_cartotalweight
								,cmdg_passengers
								,cmdg_loadingweight
								,cmdg_cylindermeters
								,cmdg_cylinderstroke
								,cmdg_compressionratio
								,cmdg_maxhorsepower
								,cmdg_maxhorsepowerspeed
								,cmdg_torque
								,cmdg_torquespeed
								,cmdg_turborchargers
								,cmdg_strokeposdirection
								,cmdg_oilcapacity
								,cmdg_929598
								,cmdg_firemethod
								,cmdg_exhaustsystem
								,cmdg_PCV
								,cmdg_EEC
								,cmdg_PM
								,cmdg_CO
								,cmdg_HC
								,cmdg_drivetraintype
								,cmdg_differentialmodel
								,cmdg_differentialgearratio
								,cmdg_1stReductionratio
								,cmdg_2ndReductionratio
								,cmdg_transmissionmodel
								,cmdg_transmissionbackgearratio
								,cmdg_highestspeed
								,cmdg_suspensionsystem_f
								,cmdg_suspensionsystem_b
								,cmdg_tirespec_std_f
								,cmdg_tirespec_std_b
								,cmdg_tirespec_cho_f
								,cmdg_tirespec_cho_b
								,cmdg_testweight
								,cmdg_testgearreductionratio
								,cmdg_coolingdrivermethod
								,cmdg_tiresnum
								,cmdg_exhaustpipenum
								,cmdg_tirewidth
								,cmdg_inletmode
								,cmdg_beextendmodel
								,cmdg_beenmod
								,cmdg_carusedstatus
								,@applyaccount
								,GETDATE()
								,@applyaccount
								,GETDATE()
								,cmdg_drivetype
								,cmdg_remark
								,cmdg_carbodyweight
								,cmdg_numofaxes_f
								,cmdg_numofaxes_b
								,cmdg_increasepowerstyle
								,cmdg_increasepowerratio
								,cmdg_backgearnums
								,cmdg_highlowgear
								,cmdg_climbratio
								,cmdg_suspensionsystem_supplement
								,cmdg_maxhorsepower_hb
								,cmdg_maxhorsepowerspeed_hb
								,cmdg_torque_hb
								,cmdg_torquespeed_hb
								,cmdg_producercountry
								,cmdg_forwardgearnums
								,cmdg_drivetypeotherdesc
								,cmdg_salename_m
								,cmdg_carspeetuptype
								,cmdg_transmissiontype
								,cmdg_lockgeerratiomode
								,cmdg_lockgeerratiomodedesc
								,cmdg_lockgeerratiomodefileid
								,cmdg_silencersystemfileid
								,cmdg_engineompartmentfileid
								FROM nv_carmodeldata where cmdg_applyno+'_'+cmdg_carcompomodelno =( select tmp_oriapplyno+'_'+tmp_compomodelno from #tmp_carcompotbl where tmp_compomodelno=@carcompomodelno) --= (select MAX(abd_applyno) from nv_applybasedata where abd_enginefamily=@enginefamily  and abd_applystatus in ('50','55') and abd_databelongcompidno=@compidno)+'_'+@carcompomodelno
								--2.2排檔齒比資料
								insert into nv_gearratio
								select @applyno,gr_carcompomodelno,gr_carmodelno,gr_gearnum,gr_gearratio
								from dbo.nv_gearratio where gr_applyno+'_'+gr_carcompomodelno =( select tmp_oriapplyno+'_'+tmp_compomodelno from #tmp_carcompotbl where tmp_compomodelno=@carcompomodelno) --= (select MAX(abd_applyno) from nv_applybasedata where abd_enginefamily=@enginefamily  and abd_applystatus in ('50','55') and abd_databelongcompidno=@compidno)+'_'+@carcompomodelno 
								--3. copy 防治對策 nv_noisedefenseplan copy 上一次的申請資料即可 select max(applyno) from nv_applybasedata where abd_applystatus='50' and abd_databelongcompidno=@compidno and abd_enginefamily=@enginefamily
								insert into nv_noisedefenseplan(ndp_applyno,ndp_carcompomodelno,ndp_position,ndp_defenseeqpname,ndp_eqpmaterial,ndp_eqpheight,ndp_equremark,ndp_createaccount,ndp_createdate,ndp_modaccount,ndp_moddate)
								select @applyno,ndp_carcompomodelno
								,ndp_position
								,ndp_defenseeqpname
								,ndp_eqpmaterial
								,ndp_eqpheight
								,ndp_equremark
								,@applyaccount
								,GETDATE()
								,@applyaccount
								,GETDATE()
								from nv_noisedefenseplan where ndp_applyno+'_'+ndp_carcompomodelno =( select tmp_oriapplyno+'_'+tmp_compomodelno from #tmp_carcompotbl where tmp_compomodelno=@carcompomodelno) --= (select MAX(abd_applyno) from nv_applybasedata join nv_carcompomodel on abd_applyno=cpm_applyno and cpm_carcompomodelno=@carcompomodelno where abd_enginefamily like @enginefamily  and abd_applystatus in ('50','55') and abd_databelongcompidno=@compidno)+'_'+@carcompomodelno 
								
								--3.2  copy 防治對策的上傳檔案	
								--/*找出copy的資料id 和copy後產生的新id */
								--select @applyno applyno
								--	  ,ndp_carcompomodelno
								--	  ,ndp_position
								--	  ,ndp_defenseeqpname
								--	  ,ndp_eqpmaterial
								--	  ,ndp_eqpheight
								--	  ,ndp_equremark
								--	  ,@applyaccount createaccount
								--	  ,GETDATE() createdate
								--	  ,@applyaccount modaccount
								--	  ,GETDATE() moddate
								--	  ,ndp_defenseplanid
								--into #tmp_defenseplan		  
								--from nv_noisedefenseplan where ndp_applyno+'_'+ndp_carcompomodelno in (select distinct tmp_applyno+'_'+tmp_compomodelno from #tmp_carcompotbl where tmp_applyno =(select MAX(tmp_applyno) from #tmp_carcompotbl))	

								----取出新舊defeneplanid對照ID
								--select N.ndp_defenseplanid Old_id,O.ndp_defenseplanid as New_id
								--into #tmp_A
								--from nv_noisedefenseplan O,#tmp_defenseplan N  
								--where O.ndp_carcompomodelno = N.ndp_carcompomodelno and
								--O.ndp_position = N.ndp_position and
								--O.ndp_defenseeqpname = N.ndp_defenseeqpname and
								--O.ndp_eqpmaterial = N.ndp_eqpmaterial and
								--O.ndp_eqpheight = N.ndp_eqpheight and
								--O.ndp_equremark = N.ndp_equremark AND
								--O.ndp_applyno = @applyno  
								
								----取得舊資料的防治對策檔案資訊
								--select * 
								--into #tmp_defenseplanfiles 
								--from nv_noisedefenseplanfiles 
								--where ndpf_applyno+'_'+ndpf_carcompomodelno in (select distinct tmp_applyno+'_'+tmp_compomodelno from #tmp_carcompotbl where tmp_applyno =(select MAX(tmp_applyno) from #tmp_carcompotbl))	
								
								----透過新舊defenseplanid對照檔，更新defenseplanid
								--update #tmp_defenseplanfiles set ndpf_defenseplanid=New_id,ndpf_applyno=@applyno from #tmp_A where ndpf_defenseplanid=Old_id
								----將防治對策檔案資訊 新增至nv_noisedefenseplanfiles
								--insert into nv_noisedefenseplanfiles select * from #tmp_defenseplanfiles
								
								
								--4. copy 修正目錄 nv_fixcatalog copy 上一次的申請資料即可
								
								
								insert into nv_fixcatalog(fc_applyno,fc_carcompomodelno,fc_fixitem,fc_beforefix,fc_afterfix,fc_createaccount,fc_createdate,fc_modaccount,fc_moddate,fc_oriapplyno)
								select @applyno,fc_carcompomodelno,fc_fixitem,fc_beforefix,fc_afterfix
								,@applyaccount
								,fc_createdate
								,@applyaccount
								,fc_moddate
								,fc_oriapplyno
								from nv_fixcatalog where fc_applyno+'_'+fc_carcompomodelno =( select tmp_oriapplyno+'_'+tmp_compomodelno from #tmp_carcompotbl where tmp_compomodelno=@carcompomodelno) --= (select MAX(abd_applyno) from nv_applybasedata where abd_enginefamily=@enginefamily  and abd_applystatus in ('50','55') and abd_databelongcompidno=@compidno)+'_'+@carcompomodelno 
								--5. copy 檔案上傳
								insert into nv_applydatafiles(adf_applyno,adf_carcompomodelno,adf_filetype,adf_fileid)
								select @applyno
								,adf_carcompomodelno
								,adf_filetype
								,adf_fileid
								from nv_applydatafiles where adf_applyno+'_'+adf_carcompomodelno =( select tmp_oriapplyno+'_'+tmp_compomodelno from #tmp_carcompotbl where tmp_compomodelno=@carcompomodelno) --= (select MAX(abd_applyno) from nv_applybasedata where abd_enginefamily=@enginefamily  and abd_applystatus in ('50','55') and abd_databelongcompidno=@compidno)+'_'+@carcompomodelno 										
							end	
						end	
							
					fetch next from @cur_carcompomodel into @carcompomodelno					
				end
				--3.2  copy 防治對策的上傳檔案	
				--3.2  copy 防治對策的上傳檔案	
				/*找出copy的資料id 和copy後產生的新id */
				--先刪除不異動的車型組資料
				select @applyno applyno
					  ,ndp_carcompomodelno
					  ,ndp_position
					  ,ndp_defenseeqpname
					  ,ndp_eqpmaterial
					  ,ndp_eqpheight
					  ,ndp_equremark
					  ,@applyaccount createaccount
					  ,GETDATE() createdate
					  ,@applyaccount modaccount
					  ,GETDATE() moddate
					  ,ndp_defenseplanid
				into #tmp_defenseplan		  
				from nv_noisedefenseplan where ndp_applyno+'_'+ndp_carcompomodelno in (select distinct tmp_applyno+'_'+tmp_compomodelno from #tmp_carcompotbl )--where tmp_applyno =(select MAX(tmp_applyno) from #tmp_carcompotbl))	

				--取出新舊defeneplanid對照ID
				select N.ndp_defenseplanid New_id,O.ndp_defenseplanid as Old_id
				into #tmp_A
				from nv_noisedefenseplan O,#tmp_defenseplan N  
				where O.ndp_carcompomodelno = N.ndp_carcompomodelno and
				O.ndp_position = N.ndp_position and
				O.ndp_defenseeqpname = N.ndp_defenseeqpname and
				O.ndp_eqpmaterial = N.ndp_eqpmaterial and
				O.ndp_eqpheight = N.ndp_eqpheight and
				O.ndp_equremark = N.ndp_equremark AND
				O.ndp_applyno <> @applyno  
				
				--取得舊資料的防治對策檔案資訊
				select * 
				into #tmp_defenseplanfiles 
				from nv_noisedefenseplanfiles 
				where ndpf_applyno+'_'+ndpf_carcompomodelno in 
				(select distinct tmp_oriapplyno+'_'+tmp_compomodelno from #tmp_carcompotbl where tmp_applyno =(select MAX(tmp_applyno) from #tmp_carcompotbl))	
							
				--透過新舊defenseplanid對照檔，更新defenseplanid
				update #tmp_defenseplanfiles set ndpf_defenseplanid=New_id,ndpf_applyno=@applyno from #tmp_A where ndpf_defenseplanid=Old_id
				--將防治對策檔案資訊 新增至nv_noisedefenseplanfiles
				insert into nv_noisedefenseplanfiles select * from #tmp_defenseplanfiles where ndpf_applyno=@applyno

				--******** 新增單純沿用 更新
				if exists(select cpm_applyno from dbo.nv_carcompomodel where cpm_applyno=@applyno and cpm_applytype='2')
				begin
				declare @powerfrom varchar(1)
				declare @num int
				select @powerfrom = abd_powerfrom from nv_applybasedata where abd_applyno=@applyno

				if (@powerfrom ='3') --電動車
				begin
					set @num = 11
				end
				else
				begin
					set @num = 10
				end


				UPDATE nv_carcompomodel SET cpm_carcompomodelno=tmp_compomodelno from #tmp_carcompotbl WHERE cpm_applyno=tmp_applyno and left(cpm_carcompomodelno,@num)=left(tmp_compomodelno,@num)
				UPDATE nv_carmodeldata SET cmdg_carcompomodelno=tmp_compomodelno from #tmp_carcompotbl WHERE cmdg_applyno=tmp_applyno and left(cmdg_carcompomodelno,@num)=left(tmp_compomodelno,@num)
				UPDATE nv_fixcatalog SET fc_carcompomodelno=tmp_compomodelno from #tmp_carcompotbl WHERE fc_applyno=tmp_applyno and left(fc_carcompomodelno,@num)=left(tmp_compomodelno,@num)
				UPDATE nv_gearratio  SET gr_carcompomodelno=tmp_compomodelno from #tmp_carcompotbl WHERE gr_applyno=tmp_applyno and left(gr_carcompomodelno,@num)=left(tmp_compomodelno,@num)
				UPDATE nv_noisedefenseplan  SET ndp_carcompomodelno=tmp_compomodelno from #tmp_carcompotbl WHERE ndp_applyno=tmp_applyno and left( ndp_carcompomodelno,@num)=left(tmp_compomodelno,@num)
				UPDATE nv_noisedefenseplanfiles SET ndpf_carcompomodelno=tmp_compomodelno from #tmp_carcompotbl WHERE ndpf_applyno=tmp_applyno and left(ndpf_carcompomodelno,@num)=left(tmp_compomodelno,@num)
				UPDATE nv_applydatafiles SET adf_carcompomodelno=tmp_compomodelno from #tmp_carcompotbl WHERE adf_applyno=tmp_applyno and left(adf_carcompomodelno,@num)=left(tmp_compomodelno,@num)

				--【如果車型號碼 有已核可過的資料；不可變更測試報告的車型名稱】 pr_check_IsAlterable4Testrpt]
				declare @sub10_carcompomodelno varchar(12) --取前10(電動車11)碼比對 (包括 % 符號)
				declare @databelongcompidno varchar(100)
				declare @applytype varchar(2)
				declare @OneExist int
				select @applytype= cpm_applytype,@vechiletype=abd_vechiletype,@enginefamily = abd_enginefamily ,@powerfrom = abd_powerfrom 
				from nv_carcompomodel
				join nv_applybasedata on cpm_applyno=abd_applyno and cpm_carcompomodelno = cpm_carcompomodelno 
				where abd_databelongcompidno=@databelongcompidno and cpm_carcompomodelno=@carcompomodelno 
				and abd_enginefamily =@enginefamily and abd_applyno=@applyno

				if (@powerfrom ='3') --電動車
				begin
					set @sub10_carcompomodelno = left(@carcompomodelno,11) + '%'
				end
				else
				begin
					set @sub10_carcompomodelno = left(@carcompomodelno,10) + '%'
				end

				if (@vechiletype ='D') --柴油車(enginefamily比對-前字串)
				begin                                       
  					set	@enginefamily= left(@enginefamily,(len(@enginefamily)-ISNULL(NULLIF(CHARINDEX('-',REVERSE(@enginefamily)),0),0))) + '%'      
				end

				if (@vechiletype ='D') --柴油車
				begin                                         	
					select @OneExist=count(abd_applyno)  from nv_applybasedata 
					join nv_carmodeldata on abd_applyno=cmdg_applyno 
					join nv_carcompomodel on nv_carmodeldata.cmdg_applyno=cpm_applyno and nv_carmodeldata.cmdg_carcompomodelno = cpm_carcompomodelno
					where abd_applystatus in ('50','55')--not in ('10','60','70','91','40','90')--填單中、消件、退件、複審核閱退回、補件中、拒件		
					and abd_databelongcompidno=@databelongcompidno and cpm_carcompomodelno like @sub10_carcompomodelno and abd_enginefamily like @enginefamily
					and abd_applyno<>@applyno
					end
				else
					begin                                         	
					select @OneExist= count(abd_applyno) from nv_applybasedata 
					join nv_carmodeldata on abd_applyno=cmdg_applyno 
					join nv_carcompomodel on nv_carmodeldata.cmdg_applyno=cpm_applyno and nv_carmodeldata.cmdg_carcompomodelno = cpm_carcompomodelno
					where abd_applystatus in ('50','55')--not in ('10','60','70','91','40','90')--填單中、消件、退件、複審核閱退回、補件中、拒件			
					and abd_databelongcompidno=@databelongcompidno and cpm_carcompomodelno like @sub10_carcompomodelno and abd_enginefamily = @enginefamily
					and abd_applyno<>@applyno
				end

				if(@OneExist=0)
				begin    
				UPDATE nv_noisetestrpt          SET ntr_carcompomodelno=tmp_compomodelno from #tmp_carcompotbl WHERE ntr_carcompomodelno=@carcompomodelno
				UPDATE nv_noisetestrpt_remark   SET ntrr_carcompomodelno=tmp_compomodelno from #tmp_carcompotbl WHERE ntrr_carcompomodelno=@carcompomodelno
				UPDATE nv_testrptspeedupinfo    SET trsui_carcompomodelno=tmp_compomodelno from #tmp_carcompotbl WHERE trsui_carcompomodelno=@carcompomodelno
				end
                     
				UPDATE nv_validationchecknote   SET vcn_carcompomodelno=tmp_compomodelno from #tmp_carcompotbl WHERE vcn_applyno=tmp_applyno and left(vcn_carcompomodelno,@num)=left(tmp_compomodelno,@num)
				UPDATE nv_validationchecknotefornotpassdetail SET vcnnpd_carcompomodelno=tmp_compomodelno from #tmp_carcompotbl WHERE vcnnpd_applyno=tmp_applyno and left(vcnnpd_carcompomodelno,@num)=left(tmp_compomodelno,@num)

				end
			end
			

		COMMIT TRANSACTION
		SET XACT_ABORT OFF
		if (@@ERROR > 0)
		begin
			select '0' 'update_result'
		end
		else
		begin
			select '1' 'update_result'
		end	
	end
	else
	begin
	select '案件狀態不為填單中或補件中無法送件' 'update_result'
	end
	
END
GO
PRINT N'正在更改 [dbo].[pr_check_carname_exist]...';


GO


-- 判斷此車型名稱是否已存在在過去申請過的紀錄裡 如果是本次新增的延伸車型可以編輯自己的測試報告
--20180306 新增@beextendmodel 多加參數@carmodelno
ALTER procedure [dbo].[pr_check_carname_exist] 
@applyno varchar(9)='',
@carcompomodelno varchar(15)='',
@carstylename nvarchar(150)='',
@carmodelno varchar(36)=''

as 
declare @sub10_carcompomodelno varchar(12) --取前10(電動車11)碼比對 (包括 % 符號)
declare @powerfrom varchar(1)
declare @vechiletype char(1)
declare @applytype varchar(2)
declare @enginefamily varchar(32)
declare @compidno nvarchar(100)
declare @beextendmodel char(1)

select @beextendmodel=cmdg_beextendmodel,@applyno=abd_applyno,@applytype= cpm_applytype,@vechiletype=abd_vechiletype,@enginefamily = abd_enginefamily ,@powerfrom = abd_powerfrom ,@compidno=abd_databelongcompidno
from nv_carcompomodel
join nv_applybasedata on cpm_applyno=abd_applyno and cpm_carcompomodelno = cpm_carcompomodelno 
join nv_carmodeldata on cmdg_applyno=cpm_applyno and cmdg_carcompomodelno = cpm_carcompomodelno
where abd_applyno=@applyno and cpm_carcompomodelno=@carcompomodelno and cmdg_beextendmodel=@carmodelno

if (@powerfrom ='3') --電動車
	begin
	  set @sub10_carcompomodelno = left(@carcompomodelno,11) + '%'
	end
else
	begin
	   set @sub10_carcompomodelno = left(@carcompomodelno,10) + '%'
	end

if (@vechiletype ='D') --柴油車(enginefamily比對-前字串)
	begin                                       
		set	@enginefamily= left(@enginefamily,(len(@enginefamily)-ISNULL(NULLIF(CHARINDEX('-',REVERSE(@enginefamily)),0),0))) + '%'      
	end
--是延伸才做判斷	
--20180306新增7,8
--if(exists(select cpm_applytype from nv_carcompomodel where cpm_applyno=@applyno and cpm_carcompomodelno=@carcompomodelno and cpm_applytype='3'))
if (@applytype ='3' or (@applytype ='7' and @beextendmodel = '1') or (@applytype ='8' and @beextendmodel = '1'))
	begin 		
		if (@vechiletype ='D') --柴油車
			begin 
				if exists(SELECT dbo.fn_FullCarstyleNameStr(abd_applyno,cmdg_carcompomodelno,cmdg_carmodelno) as 'fullcarstylename'
						  from nv_applybasedata 
						  join nv_carcompomodel on abd_applyno = cpm_applyno 
						  join nv_carmodeldata on abd_applyno = cmdg_applyno and cpm_carcompomodelno = cmdg_carcompomodelno
						  where abd_applyno <> @applyno and cpm_carcompomodelno like @sub10_carcompomodelno and abd_enginefamily like @enginefamily and abd_applystatus in ('50','55') and abd_carstyleyear>=(Year(GETDATE())-1) and abd_carstyleyear<=(Year(GETDATE())+ 1) and abd_databelongcompidno=@compidno and cpm_oriapplyno=''
						  and dbo.fn_FullCarstyleNameStr(abd_applyno,cmdg_carcompomodelno,cmdg_carmodelno) in
						  (@carstylename))
						begin 
								 select 1 as OneExist
						end
				else
						begin 
								select 0  as OneExist
						end
			end
		else
			begin 						
				if exists(SELECT dbo.fn_FullCarstyleNameStr(abd_applyno,cmdg_carcompomodelno,cmdg_carmodelno) as 'fullcarstylename'
						  from nv_applybasedata 
						  join nv_carcompomodel on abd_applyno = cpm_applyno 
						  join nv_carmodeldata on abd_applyno = cmdg_applyno and cpm_carcompomodelno = cmdg_carcompomodelno
						  where abd_applyno <> @applyno and cpm_carcompomodelno like @sub10_carcompomodelno and abd_enginefamily = @enginefamily and abd_applystatus in ('50','55') and abd_carstyleyear>=(Year(GETDATE())-1) and abd_carstyleyear<=(Year(GETDATE())+ 1) and abd_databelongcompidno=@compidno and cpm_oriapplyno=''
						  and dbo.fn_FullCarstyleNameStr(abd_applyno,cmdg_carcompomodelno,cmdg_carmodelno) in
						  (@carstylename))
						begin 
								 select 1 as OneExist
						end
				else
						begin 
								select 0  as OneExist --表示延伸新申請
						end
			end
	end
else
	begin 
	 select 1 as OneExist
	end


--exec pr_check_carname_exist '101110005','101080004_51','ALFA ROMEO asd asd asd 1200c.c. CVT asd 車身式樣-其他'

--exec pr_check_data_one_exist 'GChImx7VoBF9JEpJUXyKMg==','101100006_2','w765432c'
GO
PRINT N'正在更改 [dbo].[pr_check_data_one_exist]...';


GO
--exec pr_check_data_one_exist '6B3DU+Zqs/QCcC0yRN4Heg==','B6M000-G02-B7','G123456','B42E968F-D006-4846-8B49-E46BEF99EF26'
-- 【沿用】 如果為系統第一筆需調整為可編輯 (區隔測試報告)
--******** 新增@beextendmodel 多加參數@carmodelno
ALTER procedure [dbo].[pr_check_data_one_exist] 
@databelongcompidno varchar(100)='',
@carcompomodelno varchar(15)='',
@enginefamily varchar(32)='',
@carmodelno varchar(36)=''
as 

declare @sub10_carcompomodelno varchar(12) --取前10(電動車11)碼比對 (包括 % 符號)
declare @powerfrom varchar(1)
declare @vechiletype char(1)
declare @applytype varchar(2)
declare @applyno varchar(9)
declare @beextendmodel char(1)=''




if(@carmodelno='')
begin
	select distinct @applyno=abd_applyno,@applytype= cpm_applytype,@vechiletype=abd_vechiletype,@enginefamily = abd_enginefamily ,@powerfrom = abd_powerfrom 
	from nv_carcompomodel
	join nv_applybasedata on cpm_applyno=abd_applyno and cpm_carcompomodelno = @carcompomodelno 
	where abd_databelongcompidno=@databelongcompidno and cpm_carcompomodelno=@carcompomodelno and abd_enginefamily =@enginefamily 
end
else
begin

	select distinct @beextendmodel=cmdg_beextendmodel,@applyno=abd_applyno,@applytype= cpm_applytype,@vechiletype=abd_vechiletype,@enginefamily = abd_enginefamily ,@powerfrom = abd_powerfrom 
	from nv_carcompomodel
	join nv_applybasedata on cpm_applyno=abd_applyno and cpm_carcompomodelno = @carcompomodelno 
	join nv_carmodeldata on cmdg_applyno=cpm_applyno and cmdg_carcompomodelno = cpm_carcompomodelno
	where abd_databelongcompidno=@databelongcompidno and cpm_carcompomodelno=@carcompomodelno and abd_enginefamily =@enginefamily and cmdg_carmodelno=@carmodelno
end




if (@applytype ='2' or @applytype ='7' ) --沿用、(沿用及延伸)
begin
		if (@powerfrom ='3') --電動車
		begin
		  set @sub10_carcompomodelno = left(@carcompomodelno,11) + '%'
		end
		else
		begin
		   set @sub10_carcompomodelno = left(@carcompomodelno,10) + '%'
		end

		if (@vechiletype ='D') --柴油車(enginefamily比對-前字串)
		begin                                       
  			set	@enginefamily= left(@enginefamily,(len(@enginefamily)-ISNULL(NULLIF(CHARINDEX('-',REVERSE(@enginefamily)),0),0))) + '%'      
		end

		if (@vechiletype ='D') --柴油車
		begin  
		     
		    if (@applytype ='7' and @beextendmodel='1') --(沿用及延伸)的延伸 可以修改
			begin			
			  select 0 'OneExist'
			end						
			else	
			begin                                   	
				select count(abd_applyno) as OneExist from nv_applybasedata 
				join nv_carmodeldata on abd_applyno=cmdg_applyno 
				join nv_carcompomodel on nv_carmodeldata.cmdg_applyno=cpm_applyno and nv_carmodeldata.cmdg_carcompomodelno = cpm_carcompomodelno
				where abd_applystatus in ('50','55')--not in ('10','60','70','91','40','90')--填單中、消件、退件、複審核閱退回、補件中、拒件
				--and cpm_applytype in('2','3','4') --沿用、延伸、修改
				and abd_databelongcompidno=@databelongcompidno and cpm_carcompomodelno like @sub10_carcompomodelno and abd_enginefamily like @enginefamily
				and abd_applyno<>@applyno						
			end
		end
		else
		begin
			                   
			if (@applytype ='7' and @beextendmodel='1') --(沿用及延伸)的延伸 可以修改
			begin			
			  select 0 'OneExist'
			end		
			else	
			begin                                         	
				select count(abd_applyno) as OneExist from nv_applybasedata 
				join nv_carmodeldata on abd_applyno=cmdg_applyno 
				join nv_carcompomodel on nv_carmodeldata.cmdg_applyno=cpm_applyno and nv_carmodeldata.cmdg_carcompomodelno = cpm_carcompomodelno
				where abd_applystatus in ('50','55')--not in ('10','60','70','91','40','90')--填單中、消件、退件、複審核閱退回、補件中、拒件
				--and cpm_applytype in('2','3','4') --沿用、延伸、修改
				and abd_databelongcompidno=@databelongcompidno and cpm_carcompomodelno like @sub10_carcompomodelno and abd_enginefamily = @enginefamily
				and abd_applyno<>@applyno						
			end
		end				
end
ELSE if(@applytype ='1' OR @applytype ='3' OR @applytype ='4' OR @applytype ='5' OR @applytype ='8') --新車型、延伸、修改、沿用及修改、(沿用、延伸及修改)
begin
	select 0 'OneExist'
end
ELSE if(@applytype ='6' OR @applytype ='9') --不異動、撤銷
begin
	select 1 'OneExist'
end



--exec pr_check_data_one_exist '6B3DU+Zqs/QCcC0yRN4Heg==','B8G1234567','TEST10.1QWER',''
GO
PRINT N'正在更改 [dbo].[pr_check_data_one_exist_For_TRP]...';


GO
ALTER procedure [dbo].[pr_check_data_one_exist_For_TRP] 
@databelongcompidno varchar(100)='',
@applyno varchar(9)='',
@carcompomodelno varchar(15)='',
@enginefamily varchar(32)='',
@carmodelno varchar(36)=''

as 

declare @sub10_carcompomodelno varchar(12) --取前10(電動車11)碼比對 (包括 % 符號)
declare @powerfrom varchar(1)
declare @vechiletype char(1)
declare @applytype varchar(2)
declare @beextendmodel char(1)=''

if(@carmodelno='')
begin
	select distinct @applytype= cpm_applytype,@vechiletype=abd_vechiletype,@enginefamily = abd_enginefamily ,@powerfrom = abd_powerfrom 
	from nv_carcompomodel
	join nv_applybasedata on cpm_applyno=abd_applyno and cpm_carcompomodelno = cpm_carcompomodelno 	
	where abd_databelongcompidno=@databelongcompidno and cpm_carcompomodelno=@carcompomodelno 
	and abd_enginefamily =@enginefamily and abd_applyno=@applyno
end
else
begin
	select distinct @beextendmodel=cmdg_beextendmodel,@applytype= cpm_applytype,@vechiletype=abd_vechiletype,@enginefamily = abd_enginefamily ,@powerfrom = abd_powerfrom 
	from nv_carcompomodel
	join nv_applybasedata on cpm_applyno=abd_applyno and cpm_carcompomodelno = cpm_carcompomodelno 
	join nv_carmodeldata on cmdg_applyno=cpm_applyno and cmdg_carcompomodelno = cpm_carcompomodelno
	where abd_databelongcompidno=@databelongcompidno and cpm_carcompomodelno=@carcompomodelno 
	and abd_enginefamily =@enginefamily and abd_applyno=@applyno and cmdg_carmodelno =@carmodelno
end

if (@applytype ='2' OR @applytype ='4' OR @applytype ='5' OR @applytype ='7') --沿用、修改、沿用及修改、(沿用及延伸)
begin
		if (@powerfrom ='3') --電動車
		begin
		  set @sub10_carcompomodelno = left(@carcompomodelno,11) + '%'
		end
		else
		begin
		   set @sub10_carcompomodelno = left(@carcompomodelno,10) + '%'
		end

		if (@vechiletype ='D') --柴油車(enginefamily比對-前字串)
		begin                                       
  			set	@enginefamily= left(@enginefamily,(len(@enginefamily)-ISNULL(NULLIF(CHARINDEX('-',REVERSE(@enginefamily)),0),0))) + '%'      
		end

		if (@vechiletype ='D') --柴油車
		begin                     
			    if(@applytype ='7' and @beextendmodel='1') --(沿用及延伸)的延伸 可以修改
				begin			
				    select 0 'OneExist'
				end						
				else	
				begin                    	
					select count(abd_applyno) as OneExist from nv_applybasedata 
					join nv_carmodeldata on abd_applyno=cmdg_applyno 
					join nv_carcompomodel on nv_carmodeldata.cmdg_applyno=cpm_applyno and nv_carmodeldata.cmdg_carcompomodelno = cpm_carcompomodelno
					where abd_applystatus  in ('50','55')--not in ('10','60','70','91','40','90')--填單中、消件、退件、複審核閱退回、補件中、拒件
					--and cpm_applytype in('2','3','4') --沿用、延伸、修改
					and abd_databelongcompidno=@databelongcompidno and cpm_carcompomodelno like @sub10_carcompomodelno and abd_enginefamily like @enginefamily
					and abd_applyno<>@applyno
			    end
		end
		else
		begin  
				if(@applytype ='7' and @beextendmodel='1') --(沿用及延伸)的延伸 可以修改
				begin			
				    select 0 'OneExist'
				end						
				else	
				begin                                          	
					select count(abd_applyno) as OneExist from nv_applybasedata 
					join nv_carmodeldata on abd_applyno=cmdg_applyno 
					join nv_carcompomodel on nv_carmodeldata.cmdg_applyno=cpm_applyno and nv_carmodeldata.cmdg_carcompomodelno = cpm_carcompomodelno
					where abd_applystatus in ('50','55')--not in ('10','60','70','91','40','90')--填單中、消件、退件、複審核閱退回、補件中、拒件
					--and cpm_applytype in('2','3','4') --沿用、延伸、修改
					and abd_databelongcompidno=@databelongcompidno and cpm_carcompomodelno like @sub10_carcompomodelno and abd_enginefamily = @enginefamily
					and abd_applyno<>@applyno
				end
		end
end
ELSE if(@applytype ='1' OR @applytype ='3' OR @applytype ='8' ) --新車型、延伸、(沿用、延伸及修改)
begin
	select 0 'OneExist'
end
ELSE if(@applytype ='6' OR @applytype ='9') --不異動、撤銷
begin
	select 1 'OneExist'
end
GO
PRINT N'正在更改 [dbo].[pr_check_submit_applydata]...';


GO

/*
1.	需檢查所有必填欄位是否皆以正確填寫完成。
2.	檢查必上傳資料是否皆已上傳，如仍有資料未上傳不可送件。
3.	檢察同一車型組只能存在一筆資料為”送件”&”審查中”的狀態。
4.	如果全部的申請型式皆為”不異動”，需跳出訊息告知使用者，所有型式皆為不異動無法送件。
5.	同一車型組編號不可申請兩次以上的”新車型”
6.	(x)檢查每一車型組下的每一車型最大馬力必須落在代表車的最大馬力80%-120%之間，如果超出則不可送件。
7.	如果車型組的申請型式為新車型，則必須檢查與污染的共同欄位資料是否一致，不一致需彈出警告訊息。
8.	將資料狀態更新為”送件”
9.	EMAIL通知審驗人員
10.	產生barcode(申請表、規格表、代表車選定審查表、修正目錄)
11. 修改、沿用及修改 引擎族不可修改(不可和上次申請的不一樣)
*/
ALTER procedure [dbo].[pr_check_submit_applydata] @applyno varchar(9) 
as
declare @check_result nvarchar(2000)
--exec [pr_check_submit_applydata] '105100111'
set @check_result = ''
if exists(select * from nv_applybasedata where abd_applyno=@applyno)
begin
	--如果全部的申請型式皆為”不異動”，需跳出訊息告知使用者，所有型式皆為不異動無法送件。
	if not exists(select cpm_carcompomodelno from nv_carcompomodel where cpm_applyno=@applyno and cpm_applytype<>'6')
	begin
		set @check_result=@check_result+'全部的申請型式皆為＜不異動＞無法送件、'
	end
	
	-- 未勾選合併或單項不可送件
    if not exists(select abd_twocertmethod from nv_applybasedata where abd_applyno=@applyno ) 
    begin		
		set @check_result=@check_result+'尚未勾選合併或單項申請不可、'		
    end
	-- 一定要有繳費單的上傳，才可以送件。  
    if exists(SELECT pd_applyno FROM nv_paydata where pd_applyno=@applyno)
    begin
		if not exists(SELECT pd_applyno FROM nv_paydata join nv_attachfileinfo on afi_fileid=pd_attachfileid where pd_applyno=@applyno)
		begin
			set @check_result=@check_result+'繳費單據未上傳、'
		end
    end
    else
    begin
		set @check_result=@check_result+'繳費資料未填寫、'
    end
    -- 送件要檢查費用=2300(任一車型組申請型式為新車型) or 1500(沒有新車型的車型組) 
    --20140505 暫停檢查柴油車
    if exists(select abd_enginefamily from nv_applybasedata where abd_applyno=@applyno and abd_vechiletype <>'D')
    begin
		if exists(select cpm_applyno from nv_carcompomodel where cpm_applyno=@applyno and cpm_applytype='1') 
		begin
			if ((select sum(isnull(pd_paymoney,0)) FROM nv_paydata where pd_applyno=@applyno) <> 2300 )
			begin
				set @check_result=@check_result+'新車型申請之繳費金額不正確、'
			end
		end
		--20140505 暫停檢查柴油車
		if  not exists(select cpm_applyno from nv_carcompomodel where cpm_applyno=@applyno and cpm_applytype='1') 
		begin
			if ((select sum(isnull(pd_paymoney,0)) FROM nv_paydata where pd_applyno=@applyno)<>1500 )
			begin
				set @check_result=@check_result+'無新車型申請之繳費金額不正確、'
			end
		end
    end
	if exists(select * from nv_carcompomodel where cpm_applyno=@applyno)
	begin
	    	    
		declare @carcompomodelno varchar(15)	
		declare @carstyleyear varchar(4)			
		declare @max_upper_power decimal(9,2)
		declare @min_upper_power decimal(9,2)
		declare @sub10_carcompomodelno varchar(12) --取前10(電動車11)碼比對 (包括 % 符號)
		declare @powerfrom varchar(1)
		declare @vechiletype char(1)
		declare @applystatus varchar(2)	
		declare @enginefamily varchar(32)	
		declare @applytype_new  varchar(2)	
		select @vechiletype=abd_vechiletype,@enginefamily = abd_enginefamily ,@powerfrom = abd_powerfrom from nv_applybasedata where abd_applyno=@applyno
		select @applystatus=cpm_applytype from nv_carcompomodel where cpm_applyno=@applyno 
		
		if (@vechiletype ='D') --柴油車(enginefamily比對-前字串)
		begin
			set @enginefamily= left(@enginefamily,(len(@enginefamily)-ISNULL(NULLIF(CHARINDEX('-',REVERSE(@enginefamily)),0),0))) + '%'                                                            
		end
		else
		begin
			set @enginefamily= @enginefamily + '%'                                                            
		end
		
		declare @cur_carcompomodel cursor		
		set @cur_carcompomodel = cursor for
		
		--select cpm_carcompomodelno from nv_carcompomodel where cpm_applyno=@applyno
		select cpm_carcompomodelno,abd_carstyleyear,cpm_applytype from nv_carcompomodel join nv_applybasedata on cpm_applyno=abd_applyno where cpm_applyno=@applyno
		
		open @cur_carcompomodel
		fetch next from @cur_carcompomodel into @carcompomodelno,@carstyleyear,@applytype_new
		while (@@fetch_status = 0)
		  begin
				if (@powerfrom ='3') --電動車
				begin
					set @sub10_carcompomodelno = left(@carcompomodelno,11) + '%'
				end
				else
				begin
					set @sub10_carcompomodelno = left(@carcompomodelno,10) + '%'
				end
				--比對車型編號的末2碼或前2碼 是否與車型年相符 ex 2012->B2
				declare @csy varchar(2)			
				SELECT @csy= ct_csy_id FROM nv_codetbl_carstyleyear where ct_csy_desc=@carstyleyear
				if (@csy <> LEFT(@carcompomodelno,2) AND @csy <> RIGHT(@carcompomodelno,2))
				begin
				set @check_result=@check_result+@carcompomodelno+'車型組與填寫之車型年不符、'
				end
				
				--同一車型年下不可申請沿用
				--******** 新增578
				if (@applytype_new ='2' or @applytype_new ='5' or @applytype_new ='7' or @applytype_new ='8') --沿用
				begin
                       if exists(select abd_carstyleyear from nv_applybasedata 
							join nv_carcompomodel on abd_applyno = cpm_applyno 
							where cpm_carcompomodelno like @sub10_carcompomodelno
							and abd_applyno= (select MAX(abd_applyno) from nv_applybasedata where abd_enginefamily like @enginefamily and abd_applystatus in ('50','55'))
							and abd_carstyleyear =@carstyleyear)
							begin
								set @check_result=@check_result+@carcompomodelno+'同一車型年下不可申請沿用、'
							end
				end
				
				--申請沿用，中間車型年份不得中斷 末2碼
				--******** 新增578
				if (@applytype_new ='2' or @applytype_new ='5' or @applytype_new ='7' or @applytype_new ='8') --沿用
				begin
                      --當後前2碼=後2碼減1 或 有存在後2減1 (符合表示沒有中斷，所以相反表示中斷)
                      if (LEFT(@carcompomodelno,2) <> (select ct_csy_id from nv_codetbl_carstyleyear where ct_csy_desc=(select ct_csy_desc from nv_codetbl_carstyleyear where ct_csy_id=RIGHT(@carcompomodelno,2))-1 ) 
					  AND not exists (select cpm_carcompomodelno from nv_applybasedata 
									join nv_carcompomodel on abd_applyno = cpm_applyno 
									where cpm_carcompomodelno like @sub10_carcompomodelno +'-'+ (select ct_csy_id from nv_codetbl_carstyleyear where ct_csy_desc=(select ct_csy_desc from nv_codetbl_carstyleyear where ct_csy_id=RIGHT(@carcompomodelno,2))-1 ))
						)
						begin
						set @check_result=@check_result+@carcompomodelno+'申請沿用，中間車型年份不得中斷、'
						end                                                                  
				end

				--修改、沿用及修改 引擎族不可修改(不可和上次申請的不一樣)
				
				--用當次申請的 cpm_oriapplyno，抓到上次申請的abd_enginefamily 比對一不一樣
				--select @enginefamily
				--select top 1 abd_enginefamily from nv_applybasedata  
				--							 where abd_applyno in (select cpm_oriapplyno from nv_carcompomodel where cpm_applyno=@applyno and cpm_applytype in (4,5))
				--******** 新增8
				if (@applytype_new ='4' or @applytype_new ='5' or @applytype_new ='8') --修改、沿用及修改
				begin      							      
                      if ((select case abd_vechiletype when 'D' then left(abd_enginefamily,11) else left(abd_enginefamily,10) end from nv_applybasedata l where abd_applyno=@applyno) <> ( select top 1 case abd_vechiletype when 'D' then left(abd_enginefamily,11) else left(abd_enginefamily,10) end from nv_applybasedata  
											 where abd_applyno in (select cpm_oriapplyno from nv_carcompomodel where cpm_applyno=@applyno and cpm_applytype in (4,5)))
						 )
						
						begin
						set @check_result=@check_result+@carcompomodelno+'修改、沿用及修改 引擎族不可修改、'
						end                                                                  
				end
				
				--檢察同一車型組只能存在一筆資料為”送件”&”審查中”的狀態
				if exists(select abd_applyno from nv_applybasedata join nv_carcompomodel on abd_applyno = cpm_applyno where cpm_carcompomodelno like @sub10_carcompomodelno and abd_applyno<>@applyno and abd_applystatus in ('20','21','30','31','32','33','34','40','80','91','92','93'))
				begin
					set @check_result=@check_result+@carcompomodelno+'車型組仍有申請中之資料無法在申請、'
				end
				--同一車型組編號不可申請兩次以上的”新車型”
				if ((select count(cpm_applyno) from nv_carcompomodel where cpm_carcompomodelno like @sub10_carcompomodelno and cpm_applytype='1' and cpm_applyno in (select abd_applyno from nv_applybasedata where abd_applystatus in ('20','21','30','31','32','33','34','40','50','55','80','91','92','93'))) >1 )
				begin
					set @check_result=@check_result+@carcompomodelno+'車型組不可申請兩次以上的＜新車型＞、'
				end
				-- 送件排除檢查 不異動和撤銷資料
				if (@applytype_new not in ('6','9'))
				begin
					if exists(select * from nv_carmodeldata where cmdg_applyno=@applyno and cmdg_carcompomodelno=@carcompomodelno)
					begin
						if ((select COUNT(distinct gr_carmodelno) from nv_gearratio where gr_applyno=@applyno and gr_carcompomodelno=@carcompomodelno) <> (select COUNT(distinct cmdg_carmodelno) from nv_carmodeldata where cmdg_applyno=@applyno and cmdg_carcompomodelno=@carcompomodelno))
						begin
							set @check_result=@check_result+@carcompomodelno+'車型規格表資料之齒比未輸入完整、'						
						end
					end
					else
					begin
						set @check_result=@check_result+@carcompomodelno+'車型組車型資料不存在、'
					end
					
					-- cpm_applytype not in 新車型/沿用
					--if (not exists(select * from nv_fixcatalog join nv_carcompomodel on cpm_applyno=fc_applyno and cpm_carcompomodelno=fc_carcompomodelno where fc_applyno=@applyno and fc_carcompomodelno=@carcompomodelno and isnull(fc_oriapplyno,'')=@applyno) and exists(select cpm_carcompomodelno from nv_carcompomodel where cpm_applyno=@applyno and cpm_carcompomodelno=@carcompomodelno and cpm_applytype not in ('1','2','6','9')))
					--begin
					--	set @check_result=@check_result+@carcompomodelno+'車型組之修正紀錄未填寫完整、'						
					--end
					/**修正紀錄**/					
						--if (@applytype_new not in ('1','2'))  20140612 改為只有修改需要檢查
						if (@applytype_new ='4') 
							begin
								if (not exists(select * from nv_fixcatalog join nv_carcompomodel on cpm_applyno=fc_applyno and cpm_carcompomodelno=fc_carcompomodelno where fc_applyno=@applyno and fc_carcompomodelno=@carcompomodelno and isnull(fc_oriapplyno,'')=@applyno))
								begin
									set @check_result=@check_result+@carcompomodelno+'車型組之修正紀錄未填寫完整、'						
								end
							end
						--else
						--	begin
						--	if (not exists(select * from nv_fixcatalog join nv_carcompomodel on cpm_applyno=fc_applyno and cpm_carcompomodelno=fc_carcompomodelno where fc_applyno=@applyno and fc_carcompomodelno=@carcompomodelno ))
						--	begin
						--		set @check_result=@check_result+@carcompomodelno+'車型組之修正紀錄未填寫完整、'						
						--	end
						--end
						
					if not exists(select * from nv_noisedefenseplan where ndp_applyno=@applyno and ndp_carcompomodelno=@carcompomodelno)
						begin
							--select * from nv_noisedefenseplanfiles
							set @check_result=@check_result+@carcompomodelno+'車型組之噪音防治對策未填寫完整、'				
						end					
					--cmdg_producercountry
					if exists(select * from nv_carmodeldata where cmdg_applyno=@applyno and cmdg_carcompomodelno=@carcompomodelno and ISNULL(cmdg_producercountry,'')='')
						begin
							set @check_result=@check_result+@carcompomodelno+'車型組之車輛規格表有未填寫製造國之資料、'	
						end
					
					if exists(select * from nv_carcompomodel where cpm_applyno=@applyno and cpm_carcompomodelno=@carcompomodelno and cpm_applytype not in ('4','6','9'))
						begin
							if not exists(select * from nv_applydatafiles join nv_carcompomodel on cpm_applyno=adf_applyno and cpm_carcompomodelno=adf_carcompomodelno where adf_applyno=@applyno and adf_carcompomodelno=@carcompomodelno and cpm_applytype not in ('4','6','9'))
								begin
									--select * from nv_attachfileinfo
									set @check_result=@check_result+@carcompomodelno+'車型組之其他檔案上傳未填寫完整、'	
								end
						end
					
					--每一車型組下的車型期規格表中馬力必須介於代表車+-20%內    20140514 mark check by 880346
					--select @max_upper_power = CONVERT(float , isnull(cmdg_maxhorsepower,0))*120 from nv_carmodeldata where cmdg_berepresentativevehicle='Y' and cmdg_applyno=@applyno and cmdg_carcompomodelno=@carcompomodelno
					--select @min_upper_power = CONVERT(float , isnull(cmdg_maxhorsepower,0))*80 from nv_carmodeldata where cmdg_berepresentativevehicle='Y' and cmdg_applyno=@applyno and cmdg_carcompomodelno=@carcompomodelno

					if not exists(select cmdg_carmodelno from nv_carmodeldata where cmdg_applyno=@applyno and cmdg_carcompomodelno=@carcompomodelno and cmdg_berepresentativevehicle='Y')
					begin
						set @check_result=@check_result+@carcompomodelno+'未挑選代表車、'
					end
					--else
					--begin
					--	if ((select max(CONVERT(float , isnull(cmdg_maxhorsepower,99999)))*100 from nv_carmodeldata where cmdg_berepresentativevehicle='N' and cmdg_applyno=@applyno and cmdg_carcompomodelno=@carcompomodelno) > @max_upper_power or 
					--		(select min(CONVERT(float , isnull(cmdg_maxhorsepower,0)))*100 from nv_carmodeldata where cmdg_berepresentativevehicle='N' and cmdg_applyno=@applyno and cmdg_carcompomodelno=@carcompomodelno) < @min_upper_power)
					--	begin
					--		set @check_result=@check_result+@carcompomodelno+'車型規格表資料之最大馬力超出代表車的正負20％、'	
					--	end
					--end
					
					/**測試報告**/
					--每個車型組至少要有一分測試報告，比對測試報告與此次申請的車型組資料之車型組數是否相同
					--if exists(select cpm_carcompomodelno from nv_carcompomodel where cpm_applyno=@applyno and cpm_carcompomodelno=@carcompomodelno and cpm_applytype = '1')
					if (@applytype_new ='1')
					begin
						if not exists(select ntr_carcompomodelno from nv_noisetestrpt join nv_carmodeldata on cmdg_carmodelno=ntr_carmodelno where ntr_carcompomodelno=@carcompomodelno and cmdg_applyno=@applyno)
						begin
							set @check_result=@check_result+@carcompomodelno+'車型規格表資料之測試報告未填寫完整、'
						end
						--速進入檔位--
						if not exists(select * from nv_testrptspeedupinfo where trsui_carcompomodelno=@carcompomodelno and trsui_carmodelno=(select cmdg_carmodelno from nv_carmodeldata where cmdg_berepresentativevehicle='Y' and cmdg_applyno=@applyno and cmdg_carcompomodelno=@carcompomodelno))
						begin
							set @check_result=@check_result+@carcompomodelno+'車型組代表車測試報告之加速進入檔位未填寫完整、'
						end						
					end
					--不為新車型(排除不異動和撤銷資料)且也沒有測試報告資料
					--if not exists(
					--	select cpm_carcompomodelno from nv_carcompomodel where cpm_applyno=@applyno and cpm_carcompomodelno like @sub10_carcompomodelno and cpm_applytype not in ('1','6','9') 
					--	) and 
					--	not exists(select cpm_carcompomodelno from nv_carcompomodel join nv_applybasedata on cpm_applyno=abd_applyno where cpm_carcompomodelno like @sub10_carcompomodelno and abd_applystatus in ('50','55'))				
					if (@applytype_new not in ('1','6','9')) 
					begin
						if exists(select cpm_carcompomodelno from nv_carcompomodel join nv_applybasedata on cpm_applyno=abd_applyno where cpm_carcompomodelno like @sub10_carcompomodelno and abd_applystatus in ('50','55'))
							begin													
								--不為新車型且也沒有測試報告資料 且 測試報告未填
								if not exists(select ntr_carcompomodelno from nv_noisetestrpt join nv_carmodeldata on cmdg_carmodelno=ntr_carmodelno where ntr_carcompomodelno like @sub10_carcompomodelno)
								begin
									set @check_result=@check_result+@carcompomodelno+'車型規格表資料之測試報告未填寫完整、'
								end
								--速進入檔位--
								if not exists(select * from nv_testrptspeedupinfo where trsui_carcompomodelno like @sub10_carcompomodelno )
								begin
									set @check_result=@check_result+@carcompomodelno+'車型組代表車測試報告之加速進入檔位未填寫完整、'
								end									
							end
					end
														
				end
				fetch next from @cur_carcompomodel into @carcompomodelno,@carstyleyear,@applytype_new
		  end
		
	end
	else
	begin
		set @check_result=@check_result+'車型組資料不存在、'
	end
	
	
end
else
begin
	set @check_result='申請編號不存在'
end	

select @check_result

--exec pr_check_submit_applydata '*********'
--exec pr_check_submit_applydata '*********'B2G001-A11-B4車型組之修正紀錄未填寫完整、B2G001-A11-B4車型組之噪音防治對策未填寫完整、
GO
PRINT N'正在更改 [dbo].[pr_copy_existapplydata]...';


GO

ALTER procedure [dbo].[pr_copy_existapplydata] @applyno varchar(9),@enginefamily varchar(32),@account varchar(20),@compidno varchar(100)as
--206	396	*********	U1234567_1222_1
/*
 1. 申請表中利用引擎族複製資料時，需帶出該引擎族當中目前”車型年”&上一”車型年”仍有效的車型組資料(帶出來讓他刪)。
 2 .在帶出的車型組所有資料中其申請型式需預設為"不異動"
 Copy
 1. copy 車型組  nv_carcompomodeldata
 2. copy 車型 nv_carmodeldata、nv_gearratio
 3. copy 防治對策 nv_noisedefenseplan
 4. copy 修正目錄 nv_fixcatalog
 5. 上傳檔案？
 6.******** 新增 因應新增申請型式(7,8)，在複製資料時，將”延伸車型”在複製到本申請件時，設為”非延伸車”
*/
--取得要複製的車型組清單-->迴圈：從最新的資料開始抓，每次取得一個申請表的車型組資料存入暫存的車型組資料表(applyno,carcompomodelno)，存在此暫存表的車型組編號就不用再抓進來了
--1. copy nv_carmodeldata ：cpm_applyno=@applyno、cpm_applytype=6、cpm_createaccount&cpm_modaccount=@account、cpm_createdate&cpm_moddate = getdate()
--取得所有申請編號
--exec pr_copy_existapplydata '*********','G1-981-13','tedwei','nsll8nsvnvRVaL0yaSUgZA=='

			
declare @vechiletype char(1)	
declare @IsExist varchar(2)	


select @vechiletype=abd_vechiletype from nv_applybasedata where abd_applyno=@applyno
set	@IsExist='0'

if (@vechiletype ='D') --柴油車(比對-前字串)
begin                                       
	set	@enginefamily= left(@enginefamily,(len(@enginefamily)-ISNULL(NULLIF(CHARINDEX('-',REVERSE(@enginefamily)),0),0))) + '%'                           		     		
	--******** fix 複製資料不卡車型年
	--if exists(select * from nv_applybasedata where abd_enginefamily like @enginefamily and abd_carstyleyear>=(Year(GETDATE())-1) and abd_carstyleyear<=(Year(GETDATE())+ 1) and abd_applystatus in ('50','55') and abd_databelongcompidno=@compidno)
	if exists(select * from nv_applybasedata where abd_enginefamily like @enginefamily  and abd_applystatus in ('50','55') and abd_databelongcompidno=@compidno)
	begin                                       
		set	@IsExist='1'
	end
end
else
begin                                       
	set @enginefamily = @enginefamily
	if exists(select * from nv_applybasedata where abd_enginefamily = @enginefamily and abd_applystatus in ('50','55') and abd_databelongcompidno=@compidno)
	begin                                       
		set	@IsExist='1'
	end
	
	
end

if (@IsExist='1')
begin
	create table #tmp_carcompotbl
	(
	tmp_applyno varchar(9),
	tmp_compomodelno varchar(15)
	)
	
	create table #tmp_applydata
	(
		abd_applyno varchar(9)	,
		abd_carstyleyear varchar(4)
	)
	declare @tmp_applyno varchar(9)
	set @tmp_applyno=''
	declare @powerfrom varchar(1)
	SET @powerfrom=''
	SET XACT_ABORT ON
	begin TRANSACTION
	delete from nv_carcompomodel where cpm_applyno=@applyno
	delete from nv_carmodeldata where cmdg_applyno=@applyno
	delete from nv_gearratio where gr_applyno=@applyno
	delete from nv_noisedefenseplan where ndp_applyno=@applyno
	delete from nv_noisedefenseplanfiles where ndpf_applyno=@applyno
	delete from nv_fixcatalog where fc_applyno=@applyno
	declare @caryear varchar(4)
	select @powerfrom = abd_powerfrom,@caryear = abd_carstyleyear from nv_applybasedata where abd_applyno=@applyno
	
	if (@vechiletype ='D')
	begin		
		insert into #tmp_applydata 			
		select abd_applyno,abd_carstyleyear from nv_applybasedata 
		where abd_enginefamily like @enginefamily and abd_carstyleyear>=convert(varchar,convert(int,@caryear)-1) and abd_carstyleyear<=@caryear
		and abd_applystatus in ('50','55') and abd_databelongcompidno=@compidno
		--and abd_carstyleyear>=(Year(GETDATE())-1) and abd_carstyleyear<=(Year(GETDATE())+ 1)
	end
	else
	begin
		insert into #tmp_applydata 			
		select abd_applyno,abd_carstyleyear from nv_applybasedata where abd_enginefamily = @enginefamily 
		and abd_carstyleyear>=convert(varchar,convert(int,@caryear)-1) and abd_carstyleyear<=@caryear		 
		and abd_applystatus in ('50','55') and abd_databelongcompidno=@compidno
		--and abd_carstyleyear>=(Year(GETDATE())-1) and abd_carstyleyear<=(Year(GETDATE())+ 1)
	end	  
	
	select case abd_vechiletype when 'D' then left(cpm_carcompomodelno,11) else left(cpm_carcompomodelno,10) end as 'invalid_carcompomodelno' into #invalid_carcompo 
	from nv_carcompomodel 
	join nv_applybasedata on cpm_applyno=abd_applyno
	where cpm_applyno in (select abd_applyno from #tmp_applydata) and cpm_applytype = '9'

	--select abd_applyno into #tmp_applydata from nv_applybasedata where abd_enginefamily like @enginefamily and abd_carstyleyear>=(Year(GETDATE())-1) and abd_carstyleyear<=(Year(GETDATE())+ 1) and abd_applystatus in ('50','55') and abd_databelongcompidno=@compidno
	--車型組編號：新車型的車型年2碼+廠商編號4碼(電動車5碼E+四碼)+”-“車型種類+”流水號2碼”(同一廠商的車型種類流水號)+”-“+沿用車型年”。系統比對時僅能取前10(電動車11)碼比對
	while (select count(*) from #tmp_applydata) > 0
	begin
		set @tmp_applyno = (select top 1 abd_applyno from #tmp_applydata order by abd_carstyleyear desc,abd_applyno desc)
		if (@powerfrom ='3')
		begin
			insert into #tmp_carcompotbl 
			select cpm_applyno,cpm_carcompomodelno from nv_carcompomodel 
			where cpm_applyno = @tmp_applyno and left(cpm_carcompomodelno,11) not in (select left(tmp_compomodelno,11) from #tmp_carcompotbl) 
			and left(cpm_carcompomodelno,11) not in (select invalid_carcompomodelno from #invalid_carcompo)
			and cpm_applytype <> '9'
		end
		else
		begin
			insert into #tmp_carcompotbl 
			select cpm_applyno,cpm_carcompomodelno from nv_carcompomodel 
			where cpm_applyno = @tmp_applyno and left(cpm_carcompomodelno,10) not in (select left(tmp_compomodelno,10) from #tmp_carcompotbl) 
			and left(cpm_carcompomodelno,10) not in (select invalid_carcompomodelno from #invalid_carcompo)
			and cpm_applytype <> '9'
		end
		delete from #tmp_applydata where abd_applyno = @tmp_applyno
	end
	--select distinct nv_carcompomodel.* from nv_carcompomodel
	--join nv_applybasedata  on abd_applyno=cpm_applyno 
	--where abd_enginefamily=@enginefamily and abd_carstyleyear>=(Year(GETDATE())-1) and abd_carstyleyear<=(Year(GETDATE())+ 1) and cpm_applytype <> '9' and abd_applystatus='50' and abd_databelongcompidno=@compidno
	insert into dbo.nv_carcompomodel
	select @applyno
			,cpm_carcompomodelno
			,'6'
			,cpm_useeurocert
			,cpm_baseenginename
			,cpm_transmissiontypetype
			,cpm_gasolinetype
			,cpm_powerexportway
			,cpm_provideoilmethod
			,cpm_exhuast
			,cpm_cylindernums
			,cpm_cylindertype
			,cpm_enginesetupposnmethod
			,cpm_fituptype
			,cpm_coolsystem
			,cpm_testfactory
			,cpm_importarea
			,@account
			,dateadd(n,ROW_NUMBER() OVER(ORDER BY cpm_createdate ASC),getdate())
			,@account
			,dateadd(n,ROW_NUMBER() OVER(ORDER BY cpm_createdate ASC),getdate())
			,cpm_applyno
			,cpm_adaptstandarddate
			,cpm_vechilekind
			,cpm_silencersystem
			,cpm_engineompartment
			from nv_carcompomodel where cpm_applyno+'_'+cpm_carcompomodelno in (select tmp_applyno+'_'+tmp_compomodelno from #tmp_carcompotbl)
			
	--2. copy 車型 nv_carmodeldata車型組清單中，依據 車型組暫存資料表裡的(applyno,carcompomodelno) ，取得車型資料	
	insert into dbo.nv_carmodeldata
	SELECT @applyno
		  ,cmdg_carcompomodelno
		  ,cmdg_carmodelno
		  ,cmdg_carbodyshape
		  ,cmdg_carbodyshapedesc
		  ,cmdg_berepresentativevehicle
		  ,cmdg_cartype
		  ,cmdg_baseenginename
		  ,cmdg_carbrand
		  ,cmdg_carchnmodelname
		  ,cmdg_carengmodelname
		  ,cmdg_carmodelcode
		  ,cmdg_transmissionnum
		  ,cmdg_cardoorcount
		  ,cmdg_carmodeladd
		  ,cmdg_carbodylenth
		  ,cmdg_carbodywidth
		  ,cmdg_carbodyhieght
		  ,cmdg_carwheelbase
		  ,cmdg_carwheelbase_f
		  ,cmdg_carwheelbase_b
		  ,cmdg_caremptyweight
		  ,cmdg_cartotalweight
		  ,cmdg_passengers
		  ,cmdg_loadingweight
		  ,cmdg_cylindermeters
		  ,cmdg_cylinderstroke
		  ,cmdg_compressionratio
		  ,cmdg_maxhorsepower
		  ,cmdg_maxhorsepowerspeed
		  ,cmdg_torque
		  ,cmdg_torquespeed
		  ,cmdg_turborchargers
		  ,cmdg_strokeposdirection
		  ,cmdg_oilcapacity
		  ,cmdg_929598
		  ,cmdg_firemethod
		  ,cmdg_exhaustsystem
		  ,cmdg_PCV
		  ,cmdg_EEC
		  ,cmdg_PM
		  ,cmdg_CO
		  ,cmdg_HC
		  ,cmdg_drivetraintype
		  ,cmdg_differentialmodel
		  ,cmdg_differentialgearratio
		  ,cmdg_1stReductionratio
		  ,cmdg_2ndReductionratio
		  ,cmdg_transmissionmodel
		  ,cmdg_transmissionbackgearratio
		  ,cmdg_highestspeed
		  ,cmdg_suspensionsystem_f
		  ,cmdg_suspensionsystem_b
		  ,cmdg_tirespec_std_f
		  ,cmdg_tirespec_std_b
		  ,cmdg_tirespec_cho_f
		  ,cmdg_tirespec_cho_b
		  ,cmdg_testweight
		  ,cmdg_testgearreductionratio
		  ,cmdg_coolingdrivermethod
		  ,cmdg_tiresnum
		  ,cmdg_exhaustpipenum
		  ,cmdg_tirewidth
		  ,cmdg_inletmode
		  ,'0' 
		  ,cmdg_beenmod
		  ,cmdg_carusedstatus
		  ,@account
		  ,dateadd(n,ROW_NUMBER() OVER(ORDER BY cmdg_createdate ASC),getdate())
		  ,@account
		  ,dateadd(n,ROW_NUMBER() OVER(ORDER BY cmdg_createdate ASC),getdate())
		  ,cmdg_drivetype
		  ,cmdg_remark
		  ,cmdg_carbodyweight
		  ,cmdg_numofaxes_f
		  ,cmdg_numofaxes_b
		  ,cmdg_increasepowerstyle
		  ,cmdg_increasepowerratio
		  ,cmdg_backgearnums
		  ,cmdg_highlowgear
		  ,cmdg_climbratio
		  ,cmdg_suspensionsystem_supplement
		  ,cmdg_maxhorsepower_hb
		  ,cmdg_maxhorsepowerspeed_hb
		  ,cmdg_torque_hb
		  ,cmdg_torquespeed_hb
		  ,cmdg_producercountry
		  ,cmdg_forwardgearnums
		  ,cmdg_drivetypeotherdesc
		  ,cmdg_salename_m
		  ,cmdg_carspeetuptype
		  ,cmdg_transmissiontype	
		  ,cmdg_lockgeerratiomode
		  ,cmdg_lockgeerratiomodedesc
		  ,cmdg_lockgeerratiomodefileid
		  ,cmdg_silencersystemfileid
		  ,cmdg_engineompartmentfileid
	  FROM nv_carmodeldata  	  
	  where cmdg_applyno+'_'+cmdg_carcompomodelno in (select tmp_applyno+'_'+tmp_compomodelno from #tmp_carcompotbl)
	  
	  --排檔齒比資料
	  insert into nv_gearratio
	  select @applyno,gr_carcompomodelno,gr_carmodelno,gr_gearnum,gr_gearratio from dbo.nv_gearratio where gr_applyno+'_'+gr_carcompomodelno in (select tmp_applyno+'_'+tmp_compomodelno from #tmp_carcompotbl)
	--3. copy 防治對策 nv_noisedefenseplan copy 上一次的申請資料即可 select max(applyno) from nv_applybasedata where abd_applystatus='50' and abd_databelongcompidno=@compidno and abd_enginefamily=@enginefamily
		insert into nv_noisedefenseplan(ndp_applyno,ndp_carcompomodelno,ndp_position,ndp_defenseeqpname,ndp_eqpmaterial,ndp_eqpheight,ndp_equremark,ndp_createaccount,ndp_createdate,ndp_modaccount,ndp_moddate)
		select @applyno,ndp_carcompomodelno
		  ,ndp_position
		  ,ndp_defenseeqpname
		  ,ndp_eqpmaterial
		  ,ndp_eqpheight
		  ,ndp_equremark
		  ,@account
		  ,GETDATE()
		  ,@account
		  ,GETDATE() from nv_noisedefenseplan where ndp_applyno+'_'+ndp_carcompomodelno in (select tmp_applyno+'_'+tmp_compomodelno from #tmp_carcompotbl)--in (select distinct tmp_applyno+'_'+tmp_compomodelno from #tmp_carcompotbl where tmp_applyno =(select MAX(tmp_applyno) from #tmp_carcompotbl))	

	--3.2  copy 防治對策的上傳檔案	
	/*找出copy的資料id 和copy後產生的新id */
	select @applyno applyno
		  ,ndp_carcompomodelno
		  ,ndp_position
		  ,ndp_defenseeqpname
		  ,ndp_eqpmaterial
		  ,ndp_eqpheight
		  ,ndp_equremark
		  ,@account createaccount
		  ,GETDATE() createdate
		  ,@account modaccount
		  ,GETDATE() moddate
		  ,ndp_defenseplanid
	into #tmp_defenseplan		  
	from nv_noisedefenseplan where ndp_applyno+'_'+ndp_carcompomodelno in (select distinct tmp_applyno+'_'+tmp_compomodelno from #tmp_carcompotbl)-- where tmp_applyno =(select MAX(tmp_applyno) from #tmp_carcompotbl))	
--select distinct tmp_applyno+'_'+tmp_compomodelno from #tmp_carcompotbl where tmp_applyno =(select MAX(tmp_applyno) from #tmp_carcompotbl)
--select * from #tmp_defenseplan
	--取出新舊defeneplanid對照ID
	select N.ndp_defenseplanid Old_id,O.ndp_defenseplanid as New_id
	into #tmp_A
	from nv_noisedefenseplan O,#tmp_defenseplan N  
	where O.ndp_carcompomodelno = N.ndp_carcompomodelno and
	O.ndp_position = N.ndp_position and
	O.ndp_defenseeqpname = N.ndp_defenseeqpname and
	O.ndp_eqpmaterial = N.ndp_eqpmaterial and
	O.ndp_eqpheight = N.ndp_eqpheight and
	O.ndp_equremark = N.ndp_equremark AND
	O.ndp_applyno = @applyno  
	--select * from #tmp_A
	--取得舊資料的防治對策檔案資訊
	select * 
	into #tmp_defenseplanfiles 
	from nv_noisedefenseplanfiles 
	where ndpf_applyno+'_'+ndpf_carcompomodelno in (select distinct tmp_applyno+'_'+tmp_compomodelno from #tmp_carcompotbl)-- where tmp_applyno =(select MAX(tmp_applyno) from #tmp_carcompotbl))	
	--select * from #tmp_defenseplanfiles
	--透過新舊defenseplanid對照檔，更新defenseplanid
	update #tmp_defenseplanfiles set ndpf_defenseplanid=New_id,ndpf_applyno=@applyno from #tmp_A where ndpf_defenseplanid=Old_id
	
	--select * from #tmp_defenseplanfiles
	--將防治對策檔案資訊 新增至nv_noisedefenseplanfiles
	insert into nv_noisedefenseplanfiles select * from #tmp_defenseplanfiles where ndpf_applyno=@applyno
						
		

	--4. copy 修正目錄 nv_fixcatalog copy 上一次的申請資料即可
		insert into nv_fixcatalog(fc_applyno,fc_carcompomodelno,fc_fixitem,fc_beforefix,fc_afterfix,fc_createaccount,fc_createdate,fc_modaccount,fc_moddate,fc_oriapplyno)
		select @applyno,fc_carcompomodelno,fc_fixitem,fc_beforefix,fc_afterfix
			,@account
			,fc_createdate
			,@account
			,fc_moddate
			,fc_oriapplyno from nv_fixcatalog where fc_applyno+'_'+fc_carcompomodelno in (select tmp_applyno+'_'+tmp_compomodelno from #tmp_carcompotbl)-- (select distinct tmp_applyno+'_'+tmp_compomodelno from #tmp_carcompotbl where tmp_applyno =(select MAX(tmp_applyno) from #tmp_carcompotbl))
	COMMIT TRANSACTION
	SET XACT_ABORT OFF
	if (@@ERROR > 0)
	begin
		select '1'
	end
	else
	begin
		select '0'
	end		
end
else
begin
	select '0'
end
GO
PRINT N'正在更改 [dbo].[pr_copy_existapplydataDFcarmodelno]...';


GO

--select * from nv_applybasedata where abd_applyno='*********'
ALTER procedure [dbo].[pr_copy_existapplydataDFcarmodelno] @o_applyno varchar(9),@carcompomodelno varchar(15),@n_applyno varchar(9)as
-- =============================================
-- Description:	誤刪資料的修復
-- =============================================

--select * from nv_carmodeldata where cmdg_applyno='*********' and cmdg_carcompomodelno='*********_q'

--select @o_applyno=cpm_oriapplyno from nv_carcompomodel where cpm_applyno=@n_applyno and cpm_carcompomodelno=@carcompomodelno

if exists (select * from nv_applybasedata where abd_applyno=@o_applyno) 
begin
	if exists(select * from nv_carcompomodel where cpm_applyno=@o_applyno and cpm_carcompomodelno=@carcompomodelno ) --and abd_applystatus in ('50','55') )
	begin
		create table #tmp_carcompotbl
		(
		tmp_applyno varchar(9),
		tmp_compomodelno varchar(15)
		)
			
		SET XACT_ABORT ON
		begin TRANSACTION			
		insert into #tmp_carcompotbl 
		select cpm_applyno,cpm_carcompomodelno from nv_carcompomodel where cpm_applyno = @o_applyno and cpm_carcompomodelno=@carcompomodelno --and cpm_applytype <> '9'
		--select * from #tmp_carcompotbl	
		--select distinct nv_carcompomodel.* from nv_carcompomodel
		--join nv_applybasedata  on abd_applyno=cpm_applyno 
		--where abd_enginefamily=@enginefamily and abd_carstyleyear>=(Year(GETDATE())-1) and abd_carstyleyear<=(Year(GETDATE())+ 1) and cpm_applytype <> '9' and abd_applystatus='50' and abd_databelongcompidno=@compidno
		insert into dbo.nv_carcompomodel
		select @n_applyno
				,cpm_carcompomodelno
				,'6'
				,cpm_useeurocert
				,cpm_baseenginename
				,cpm_transmissiontypetype
				,cpm_gasolinetype
				,cpm_powerexportway
				,cpm_provideoilmethod
				,cpm_exhuast
				,cpm_cylindernums
				,cpm_cylindertype
				,cpm_enginesetupposnmethod
				,cpm_fituptype
				,cpm_coolsystem
				,cpm_testfactory
				,cpm_importarea
				,cpm_createaccount
				,dateadd(n,ROW_NUMBER() OVER(ORDER BY cpm_createdate ASC),getdate())
				,cpm_modaccount
				,dateadd(n,ROW_NUMBER() OVER(ORDER BY cpm_createdate ASC),getdate())
				,cpm_applyno
				,cpm_adaptstandarddate
				,cpm_vechilekind
				,cpm_silencersystem
				,cpm_engineompartment
				from nv_carcompomodel where cpm_applyno=@o_applyno and cpm_carcompomodelno=@carcompomodelno-- in (select tmp_applyno+'_'+tmp_compomodelno from #tmp_carcompotbl)
				
		--2. copy 車型 nv_carmodeldata車型組清單中，依據 車型組暫存資料表裡的(applyno,carcompomodelno) ，取得車型資料
		insert into dbo.nv_carmodeldata
		SELECT @n_applyno
		  ,cmdg_carcompomodelno
		  ,cmdg_carmodelno
		  ,cmdg_carbodyshape
		  ,cmdg_carbodyshapedesc
		  ,cmdg_berepresentativevehicle
		  ,cmdg_cartype
		  ,cmdg_baseenginename
		  ,cmdg_carbrand
		  ,cmdg_carchnmodelname
		  ,cmdg_carengmodelname
		  ,cmdg_carmodelcode
		  ,cmdg_transmissionnum
		  ,cmdg_cardoorcount
		  ,cmdg_carmodeladd
		  ,cmdg_carbodylenth
		  ,cmdg_carbodywidth
		  ,cmdg_carbodyhieght
		  ,cmdg_carwheelbase
		  ,cmdg_carwheelbase_f
		  ,cmdg_carwheelbase_b
		  ,cmdg_caremptyweight
		  ,cmdg_cartotalweight
		  ,cmdg_passengers
		  ,cmdg_loadingweight
		  ,cmdg_cylindermeters
		  ,cmdg_cylinderstroke
		  ,cmdg_compressionratio
		  ,cmdg_maxhorsepower
		  ,cmdg_maxhorsepowerspeed
		  ,cmdg_torque
		  ,cmdg_torquespeed
		  ,cmdg_turborchargers
		  ,cmdg_strokeposdirection
		  ,cmdg_oilcapacity
		  ,cmdg_929598
		  ,cmdg_firemethod
		  ,cmdg_exhaustsystem
		  ,cmdg_PCV
		  ,cmdg_EEC
		  ,cmdg_PM
		  ,cmdg_CO
		  ,cmdg_HC
		  ,cmdg_drivetraintype
		  ,cmdg_differentialmodel
		  ,cmdg_differentialgearratio
		  ,cmdg_1stReductionratio
		  ,cmdg_2ndReductionratio
		  ,cmdg_transmissionmodel
		  ,cmdg_transmissionbackgearratio
		  ,cmdg_highestspeed
		  ,cmdg_suspensionsystem_f
		  ,cmdg_suspensionsystem_b
		  ,cmdg_tirespec_std_f
		  ,cmdg_tirespec_std_b
		  ,cmdg_tirespec_cho_f
		  ,cmdg_tirespec_cho_b
		  ,cmdg_testweight
		  ,cmdg_testgearreductionratio
		  ,cmdg_coolingdrivermethod
		  ,cmdg_tiresnum
		  ,cmdg_exhaustpipenum
		  ,cmdg_tirewidth
		  ,cmdg_inletmode
		  ,'0' 
		  ,cmdg_beenmod
		  ,cmdg_carusedstatus
		  ,cmdg_modaccount
		  ,dateadd(n,ROW_NUMBER() OVER(ORDER BY cmdg_createdate ASC),getdate())
		  ,cmdg_modaccount
		  ,dateadd(n,ROW_NUMBER() OVER(ORDER BY cmdg_createdate ASC),getdate())
		  ,cmdg_drivetype
		  ,cmdg_remark
		  ,cmdg_carbodyweight
		  ,cmdg_numofaxes_f
		  ,cmdg_numofaxes_b
		  ,cmdg_increasepowerstyle
		  ,cmdg_increasepowerratio
		  ,cmdg_backgearnums
		  ,cmdg_highlowgear
		  ,cmdg_climbratio
		  ,cmdg_suspensionsystem_supplement
		  ,cmdg_maxhorsepower_hb
		  ,cmdg_maxhorsepowerspeed_hb
		  ,cmdg_torque_hb
		  ,cmdg_torquespeed_hb
		  ,cmdg_producercountry
		  ,cmdg_forwardgearnums
		  ,cmdg_drivetypeotherdesc
		  ,cmdg_salename_m
		  ,cmdg_carspeetuptype
		  ,cmdg_transmissiontype
		  ,cmdg_lockgeerratiomode
		  ,cmdg_lockgeerratiomodedesc
		  ,cmdg_lockgeerratiomodefileid
		  ,cmdg_silencersystemfileid
		  ,cmdg_engineompartmentfileid
		  FROM nv_carmodeldata  		  
		  where cmdg_applyno=@o_applyno and cmdg_carcompomodelno=@carcompomodelno-- in (select tmp_applyno+'_'+tmp_compomodelno from #tmp_carcompotbl)
		  --排檔齒比資料
		  insert into nv_gearratio
		  select @n_applyno,gr_carcompomodelno,gr_carmodelno,gr_gearnum,gr_gearratio from dbo.nv_gearratio 
		  where gr_applyno=@o_applyno and gr_carcompomodelno=@carcompomodelno --in (select tmp_applyno+'_'+tmp_compomodelno from #tmp_carcompotbl)
		--3. copy 防治對策 nv_noisedefenseplan copy 上一次的申請資料即可 select max(applyno) from nv_applybasedata where abd_applystatus='50' and abd_databelongcompidno=@compidno and abd_enginefamily=@enginefamily
			insert into nv_noisedefenseplan(ndp_applyno,ndp_carcompomodelno,ndp_position,ndp_defenseeqpname,ndp_eqpmaterial,ndp_eqpheight,ndp_equremark,ndp_createaccount,ndp_createdate,ndp_modaccount,ndp_moddate)
			select @n_applyno,ndp_carcompomodelno
			  ,ndp_position
			  ,ndp_defenseeqpname
			  ,ndp_eqpmaterial
			  ,ndp_eqpheight
			  ,ndp_equremark
			  ,ndp_createaccount
			  ,GETDATE()
			  ,ndp_modaccount
			  ,GETDATE() from nv_noisedefenseplan where ndp_applyno=@o_applyno and ndp_carcompomodelno =@carcompomodelno--in (select distinct tmp_applyno+'_'+tmp_compomodelno from #tmp_carcompotbl where tmp_applyno =(select MAX(tmp_applyno) from #tmp_carcompotbl))
				--,GETDATE() from nv_noisedefenseplan where ndp_applyno+'_'+ndp_carcompomodelno in (select tmp_applyno+'_'+tmp_compomodelno from #tmp_carcompotbl)			  

--3.2  copy 防治對策的上傳檔案	
	/*找出copy的資料id 和copy後產生的新id */
	select @n_applyno applyno
		  ,ndp_carcompomodelno
		  ,ndp_position
		  ,ndp_defenseeqpname
		  ,ndp_eqpmaterial
		  ,ndp_eqpheight
		  ,ndp_equremark
		  ,ndp_createaccount
		  ,GETDATE() ndp_createdate
		  ,ndp_modaccount
		  ,GETDATE() moddate
		  ,ndp_defenseplanid
	into #tmp_defenseplan		  
	from nv_noisedefenseplan where ndp_applyno=@o_applyno and ndp_carcompomodelno =@carcompomodelno-- in (select distinct tmp_applyno+'_'+tmp_compomodelno from #tmp_carcompotbl)-- where tmp_applyno =(select MAX(tmp_applyno) from #tmp_carcompotbl))	
--select distinct tmp_applyno+'_'+tmp_compomodelno from #tmp_carcompotbl where tmp_applyno =(select MAX(tmp_applyno) from #tmp_carcompotbl)
--select * from #tmp_defenseplan
	--取出新舊defeneplanid對照ID
	select N.ndp_defenseplanid Old_id,O.ndp_defenseplanid as New_id
	into #tmp_A
	from nv_noisedefenseplan O,#tmp_defenseplan N  
	where O.ndp_carcompomodelno = N.ndp_carcompomodelno and
	O.ndp_position = N.ndp_position and
	O.ndp_defenseeqpname = N.ndp_defenseeqpname and
	O.ndp_eqpmaterial = N.ndp_eqpmaterial and
	O.ndp_eqpheight = N.ndp_eqpheight and
	O.ndp_equremark = N.ndp_equremark AND
	O.ndp_applyno = @n_applyno  
	--select * from #tmp_A
	--取得舊資料的防治對策檔案資訊
	select * 
	into #tmp_defenseplanfiles 
	from nv_noisedefenseplanfiles 
	where ndpf_applyno=@o_applyno and ndpf_carcompomodelno =@carcompomodelno --in (select distinct tmp_applyno+'_'+tmp_compomodelno from #tmp_carcompotbl)-- where tmp_applyno =(select MAX(tmp_applyno) from #tmp_carcompotbl))	
	--select * from #tmp_defenseplanfiles
	--透過新舊defenseplanid對照檔，更新defenseplanid
	update #tmp_defenseplanfiles set ndpf_defenseplanid=New_id,ndpf_applyno=@n_applyno from #tmp_A where ndpf_defenseplanid=Old_id
	
	--select * from #tmp_defenseplanfiles
	--將防治對策檔案資訊 新增至nv_noisedefenseplanfiles
	insert into nv_noisedefenseplanfiles select * from #tmp_defenseplanfiles where ndpf_applyno=@n_applyno			  
			  
			  
			  
		--4. copy 修正目錄 nv_fixcatalog copy 上一次的申請資料即可
			insert into nv_fixcatalog(fc_applyno,fc_carcompomodelno,fc_fixitem,fc_beforefix,fc_afterfix,fc_createaccount,fc_createdate,fc_modaccount,fc_moddate,fc_oriapplyno)
			select @n_applyno,fc_carcompomodelno,fc_fixitem,fc_beforefix,fc_afterfix
				,fc_createaccount
				,fc_createdate
				,fc_modaccount
				,fc_moddate
				,fc_oriapplyno from nv_fixcatalog where fc_applyno=@o_applyno and fc_carcompomodelno=@carcompomodelno-- in (select distinct tmp_applyno+'_'+tmp_compomodelno from #tmp_carcompotbl where tmp_applyno =(select MAX(tmp_applyno) from #tmp_carcompotbl))
		COMMIT TRANSACTION
		SET XACT_ABORT OFF
		if (@@ERROR > 0)
		begin
			select '1'
		end
		else
		begin
			select '0'
		end		
	end
	else
	begin
		select '0'
	end
end
else
begin
	select '0'
end
GO
PRINT N'正在更改 [dbo].[pr_get_carcompomodelBasicData]...';


GO
--車型組基本資料
ALTER procedure [dbo].[pr_get_carcompomodelBasicData]

@applyno varchar(9)='',
@carcompomodelno varchar(15)=''

as 

if(exists(select abd_applyno from nv_applybasedata where abd_applyno = @applyno and abd_vechiletype in ('G','D') ))
begin 
SELECT cpm_applyno,cpm_carcompomodelno,cpm_applytype,ct_eu_desc,cpm_baseenginename,cpm_exhuast,abd_enginefamily,abd_carstyleyear,abd_carstyleyear,cpm_importarea,
ct_aps_desc,ct_at_desc,ct_twct_desc,ct_ft_desc,ct_le_desc,cpm_cylindernums,ct_ep_desc_g,ct_cs_desc,ct_tf_desc
FROM nv_carcompomodel
left join nv_applybasedata on abd_applyno=cpm_applyno
left join nv_codetbl_applystatus on ct_aps_id = abd_applystatus --申請狀態 ct_aps_desc
left join nv_codetbl_applytype on ct_at_id = cpm_applytype --申請型式ct_at_desc
left join nv_codetbl_twocerttype  on ct_twct_id = abd_twocertmethod --二證合一申請方式ct_twct_desc (二證合一申請方式：單項申請/合併申請)
left join nv_codetbl_useeurocert on ct_eu_id = cpm_useeurocert --以歐盟合格申請
left join nv_codetbl_fueltype on ct_ft_id = cpm_gasolinetype --使用燃料ct_ft_desc
--left join nv_codetbl_gearmethod on ct_gm_id = cpm_transmissiontypetype --排檔型式ct_gm_desc
left join nv_codetbl_lubricationtype on ct_le_id=cpm_provideoilmethod --供油方式ct_le_desc
--left join nv_codetbl_drivetype on ct_dt_id = cmdg_drivetype --驅動方式ct_dt_desc
left join nv_codetbl_enginepos on ct_ep_id_g=cpm_fituptype --引擎安裝位置ct_ep_desc_g
left join nv_codetbl_coolsystem on ct_cs_id=cpm_coolsystem --冷卻系統ct_cs_desc
left join nv_codetbl_testfactory on ct_tf_id = cpm_testfactory -- 檢測廠ct_tf_desc
WHERE cpm_applyno=@applyno and cpm_carcompomodelno=@carcompomodelno
end
if(exists(select abd_applyno from nv_applybasedata where abd_applyno = @applyno and abd_vechiletype = 'M' ))
begin 
SELECT cpm_applyno,cpm_carcompomodelno,cpm_applytype,ct_eu_desc,cpm_baseenginename,cpm_exhuast,abd_enginefamily,abd_carstyleyear,abd_carstyleyear,cpm_importarea,
ct_aps_desc,ct_at_desc,ct_twct_desc,ct_ft_desc,ct_ebm_desc,ct_le_desc,cpm_cylindernums,ct_cp_desc_m,cpm_enginesetupposnmethod,ct_cs_desc,ct_tf_desc,
ct_vk_desc
FROM nv_carcompomodel
left join nv_applybasedata on abd_applyno=cpm_applyno
left join nv_codetbl_applystatus on ct_aps_id = abd_applystatus --申請狀態 ct_aps_desc
left join nv_codetbl_applytype on ct_at_id = cpm_applytype --申請型式ct_at_desc
--left join nv_codetbl_gearmethod on ct_gm_id = cpm_transmissiontypetype --排檔型式ct_gm_desc
left join nv_codetbl_twocerttype  on ct_twct_id = abd_twocertmethod --兩證合一 ct_twct_desc  cpm_useeurocert (以歐盟合格證申請 是/否)
left join nv_codetbl_useeurocert on ct_eu_id = cpm_useeurocert --以歐盟合格申請
left join nv_codetbl_fueltype on ct_ft_id = cpm_gasolinetype --使用燃料ct_ft_desc
left join nv_codetbl_engineburnmethod on ct_ebm_id = cpm_powerexportway --燃燒循環ct_ebm_desc
left join nv_codetbl_lubricationtype on ct_le_id=cpm_provideoilmethod --供油方式ct_le_desc
left join nv_codetbl_cylinderpos_m on ct_cp_id_m = cpm_cylindertype --汽缸排列ct_cp_desc_m
left join nv_codetbl_coolsystem on ct_cs_id=cpm_coolsystem --冷卻系統ct_cs_desc
left join nv_codetbl_testfactory on ct_tf_id = cpm_testfactory -- 檢測廠ct_tf_desc
left join nv_codetbl_vehiclekind on ct_vk_id = cpm_vechilekind -- 車輛分類
WHERE cpm_applyno=@applyno and cpm_carcompomodelno=@carcompomodelno
end
GO
PRINT N'正在更改 [dbo].[pr_get_cardetails]...';


GO

--測定報告 車型年、廠商名稱(唯獨畫面需要)
--20180208新增顯示規格表總重與最大馬力
ALTER procedure [dbo].[pr_get_cardetails] 
@applyno varchar(9)='',
@carcompomodelno varchar(15)='',
@carmodelno varchar(36)=''

as 
select abd_isimport,ct_sd_desc as standarddate,cpm_adaptstandarddate,abd_powerfrom,cpm_applytype,cpm_applyno,cpm_carcompomodelno,cmdg_carmodelno,cmdg_carengmodelname,cmdg_producercountry,abd_databelongcompname,cmdg_berepresentativevehicle,cpm_testfactory,abd_carstyleyear,ct_tf_desc,
cmdg_carspeetuptype,cmdg_cartype,cmdg_cartotalweight,cmdg_maxhorsepower,ct_vk_desc
from (nv_carcompomodel left join nv_applybasedata on cpm_applyno=abd_applyno)
left join nv_carmodeldata on cpm_applyno=cmdg_applyno and cpm_carcompomodelno = cmdg_carcompomodelno
left join nv_codetbl_testfactory on ct_tf_id=cpm_testfactory
left join nv_codetbl_standarddate on ct_sd_id=cpm_adaptstandarddate
left join nv_codetbl_vehiclekind on ct_vk_id=cpm_vechilekind
where cpm_applyno=@applyno
and cpm_carcompomodelno=@carcompomodelno
and cmdg_carmodelno=@carmodelno
GO
PRINT N'正在更改 [dbo].[pr_get_combinecarlist]...';


GO

--審查表車型組資料(申請廠商資料)
ALTER procedure [dbo].[pr_get_combinecarlist] 
@applyno varchar(9)='',
@carcompomodelno varchar(15)=''
as 
--declare @brandname nvarchar(max)
declare @isimport nvarchar(15)
set @isimport = (select abd_isimport from nv_applybasedata where abd_applyno=@applyno)

if(exists(select abd_isimport from nv_applybasedata where abd_applyno=@applyno and abd_isimport = '1' ))
begin
set @isimport = '中華民國'
end
else
begin
set @isimport = (select cpm_importarea from nv_carcompomodel where cpm_applyno=@applyno and cpm_carcompomodelno=@carcompomodelno)
end


	declare @str nvarchar(max) set @str=''	
	declare @bd_desc nvarchar(250)
	declare curData cursor for
	select distinct ct_bd_desc 
	from nv_carmodeldata left join nv_codetbl_brand on cmdg_carbrand=ct_bd_id where cmdg_applyno=@applyno and cmdg_carcompomodelno=@carcompomodelno
		
	open curData

	fetch next from curData into @bd_desc
	while(@@fetch_status=0)
	begin
		if len(@str)>0 set @str=@str+','
		set @str = @str + @bd_desc
		fetch next from curData into @bd_desc
	end
	close curData
	deallocate curData

--set @brandname = (select top 1 ct_bd_desc from nv_carmodeldata left join nv_codetbl_brand on cmdg_carbrand=ct_bd_id where cmdg_applyno=@applyno and cmdg_carcompomodelno=@carcompomodelno)


select cpm_applytype,abd_applyno,ct_aps_desc,abd_applystatus,abd_carstyleyear,abd_databelongcompname,abd_enginefamily,cpm_importarea,abd_factoryname,abd_isimport,@str 'cmdg_carbrand',@isimport 'ct_cy_desc'
from (nv_carcompomodel left join nv_applybasedata on cpm_applyno=abd_applyno)
left join nv_codetbl_applystatus on ct_aps_id=abd_applystatus 
where cpm_applyno=@applyno
and cpm_carcompomodelno=@carcompomodelno

--exec pr_get_combinecarlist '101080004','101080004_3'
GO
PRINT N'正在更改 [dbo].[pr_get_cpm_applytype]...';


GO
--申請型式
ALTER procedure [dbo].[pr_get_cpm_applytype] 
@applyno varchar(9)='',
@carcompomodelno varchar(15)=''

as 
set @applyno=@applyno
set @carcompomodelno=@carcompomodelno

select distinct cmdg_beextendmodel,cpm_applytype,abd_applyno,cpm_carcompomodelno,ct_at_desc
from (nv_applybasedata join nv_carcompomodel on cpm_applyno = abd_applyno )
left join nv_codetbl_applytype on ct_at_id =cpm_applytype
join nv_carmodeldata on cmdg_applyno collate Chinese_Taiwan_Stroke_BIN=cpm_applyno collate Chinese_Taiwan_Stroke_BIN and cmdg_carcompomodelno collate Chinese_Taiwan_Stroke_BIN = cpm_carcompomodelno collate Chinese_Taiwan_Stroke_BIN
where abd_applyno=@applyno and cpm_carcompomodelno =@carcompomodelno
GO
PRINT N'正在更改 [dbo].[pr_get_latestcarbodyno]...';


GO


-- 取得最後一次申請的 車身號碼
--20180306 新增@beextendmodel 多加參數@carmodelno
ALTER procedure [dbo].[pr_get_latestcarbodyno] 
@applyno varchar(9)='',
@carcompomodelno varchar(15)='',
@carmodelno varchar(36)=''

as 

declare @sub10_carcompomodelno varchar(12) --取前10(電動車11)碼比對 (包括 % 符號)
declare @powerfrom varchar(1)
declare @applytype varchar(2)
declare @beextendmodel char(1)

select @beextendmodel=cmdg_beextendmodel,@applytype=cpm_applytype,@powerfrom = abd_powerfrom 
from nv_carcompomodel
join nv_applybasedata on cpm_applyno=abd_applyno and cpm_carcompomodelno = @carcompomodelno 
join nv_carmodeldata on cmdg_applyno=cpm_applyno and cmdg_carcompomodelno = cpm_carcompomodelno
where abd_applyno=@applyno and cpm_carcompomodelno=@carcompomodelno and cmdg_carmodelno=@carmodelno

		if (@powerfrom ='3') --電動車
		begin
		  set @sub10_carcompomodelno = left(@carcompomodelno,11) + '%'
		end
		else
		begin
		   set @sub10_carcompomodelno = left(@carcompomodelno,10) + '%'
		end
--沿用;取自己本身 20140515 增加 當自己本身具有額外車身號碼時取本身
--20180305新增7,8
if ((@applytype ='2' or @applytype ='5' or (@applytype ='7' and @beextendmodel = '0')or (@applytype ='8' and @beextendmodel = '0')) or exists(select * from nv_additionalbodyno where abn_applyno = @applyno and abn_carcompomodelno like @sub10_carcompomodelno)) 
begin
    if exists(select * from nv_additionalbodyno where abn_applyno = @applyno and abn_carcompomodelno like @sub10_carcompomodelno)
		begin
			select ISNULL(abn_bodyno,'-') 'carbodyno',ISNULL(abn_engineno,'-') 'engineno'
			from nv_carmodeldata
			left join nv_additionalbodyno on cmdg_carcompomodelno=abn_carcompomodelno and abn_carmodelno=cmdg_carmodelno
			join nv_carcompomodel on cmdg_applyno=cpm_applyno and cmdg_carcompomodelno = cpm_carcompomodelno 	
			where cmdg_applyno=@applyno and cmdg_carcompomodelno=@carcompomodelno and cmdg_berepresentativevehicle='Y' 
		end
		else
		begin
			select ISNULL(ntr_carbodyno,'-') 'carbodyno',ISNULL(ntr_engineno,'-') 'engineno'	
			from nv_carmodeldata
			left join nv_noisetestrpt on cmdg_carcompomodelno=ntr_carcompomodelno and ntr_carmodelno=cmdg_carmodelno
			join nv_carcompomodel on cmdg_applyno=cpm_applyno and cmdg_carcompomodelno = cpm_carcompomodelno 	
			where cmdg_applyno=@applyno and cmdg_carcompomodelno=@carcompomodelno and cmdg_berepresentativevehicle='Y'  
		end

end
else
begin
		--如果在nv_additionalbodyno沒有資料取nv_noisetestrpt;如果有取得applyno小於自己的最新申請資料
		if exists(select * from nv_additionalbodyno join nv_applybasedata on abd_applyno=abn_applyno and abd_applystatus not in ('10','70','90','99') where abn_applyno <=@applyno and abn_carcompomodelno like @sub10_carcompomodelno) 		
		and exists(select * from nv_carcompomodel where cpm_applyno=@applyno and cpm_carcompomodelno=@carcompomodelno and cpm_applytype<>'1' )
		begin
				 --20160303修改
				 --先判斷 abn_carcompomodelno = @carcompomodelno 有沒有資料
				 --沒有再 abn_carcompomodelno like @sub10_carcompomodelno
				 if exists(select * from nv_additionalbodyno join nv_applybasedata on abd_applyno=abn_applyno and abd_applystatus not in ('10','70','90','99') where abn_applyno <=@applyno and abn_carcompomodelno = @carcompomodelno) 
					 begin
					 	SELECT TOP 1 ISNULL(abn_bodyno,'-') 'carbodyno',ISNULL(abn_engineno,'-') 'engineno' FROM nv_additionalbodyno 
						 join nv_applybasedata on abd_applyno=abn_applyno and abd_applystatus not in ('10','70','90','99')
						 WHERE abn_applyno <> @applyno and abn_applyno < @applyno		                 
						 AND abn_carcompomodelno = @carcompomodelno                 
						 ORDER BY abn_moddate DESC
					 end
				 else
					 begin
						 SELECT TOP 1 ISNULL(abn_bodyno,'-') 'carbodyno',ISNULL(abn_engineno,'-') 'engineno' FROM nv_additionalbodyno 
						 join nv_applybasedata on abd_applyno=abn_applyno and abd_applystatus not in ('10','70','90','99')
						 WHERE abn_applyno <> @applyno and abn_applyno < @applyno		                 
						 AND abn_carcompomodelno like @sub10_carcompomodelno                 
						 ORDER BY abn_moddate DESC
					 end
		end
		else
		begin
          select ISNULL(ntr_carbodyno,'-') 'carbodyno',ISNULL(ntr_engineno,'-') 'engineno'	
			from nv_carmodeldata
			left join nv_noisetestrpt on cmdg_carcompomodelno=ntr_carcompomodelno and ntr_carmodelno=cmdg_carmodelno
			join nv_carcompomodel on cmdg_applyno=cpm_applyno and cmdg_carcompomodelno = cpm_carcompomodelno 	
			where cmdg_applyno=@applyno and cmdg_carcompomodelno=@carcompomodelno and cmdg_berepresentativevehicle='Y' 
		end

end
		

--EXEC pr_get_latestcarbodyno '103010025','B2M596-G01-B3'
--exec pr_get_latestcarbodyno '101080003','101080003_A'

--EXEC pr_get_carmodeldatarpt '101080003','101080003_A' 
GO
PRINT N'正在更改 [dbo].[pr_get_unNewApplyData]...';


GO

--當申請型式 不為新車型 且有50,55取得上次申請代表車資料
--比對相同引擎族、車型編號前10碼、上次核可代表車
/*pr_applysubmit_datahandle,pr_copy_existapplydata*/
ALTER procedure [dbo].[pr_get_unNewApplyData] 
@applyno varchar(9)='',
@carcompomodelno varchar(15)=''

as 
if exists(select * from nv_carcompomodel where cpm_applyno=@applyno)
        begin
                                
                declare @enginefamily varchar(32)               
                declare @sub10_carcompomodelno varchar(12) --取前10(電動車11)碼比對 (包括 % 符號)
                declare @powerfrom varchar(1)
                declare @vechiletype char(1)
                select @vechiletype=abd_vechiletype,@enginefamily = abd_enginefamily ,@powerfrom = abd_powerfrom from nv_applybasedata where abd_applyno=@applyno
                
                if (@powerfrom ='3') --電動車
                begin
                        set @sub10_carcompomodelno = left(@carcompomodelno,11) + '%'
                end
                else
                begin
                        set @sub10_carcompomodelno = left(@carcompomodelno,10) + '%'
                end

                if (@vechiletype ='D') --柴油車(enginefamily比對-前字串)
                begin  
                /*反轉字串，然後找出第一個『-』出現位置的 index 值*/
                        set @enginefamily= left(@enginefamily,(len(@enginefamily)-ISNULL(NULLIF(CHARINDEX('-',REVERSE(@enginefamily)),0),0))) + '%'                                                            
                        select top 1 cmdg_applyno,cmdg_carcompomodelno,cmdg_carmodelno from nv_carmodeldata 
                        where cmdg_carcompomodelno like @sub10_carcompomodelno
                        and cmdg_applyno = (select MiN(abd_applyno) from nv_applybasedata join nv_carcompomodel on cpm_applyno = abd_applyno where abd_enginefamily like @enginefamily 
                        and abd_applystatus in ('50','55') 
                        --AND abd_applyno<>@applyno 
                        and cpm_carcompomodelno like @sub10_carcompomodelno)
                        and cmdg_berepresentativevehicle='Y'
                end             
                else
                begin       
                        --if (@applyno <> (select min(abd_applyno) from nv_applybasedata where  abd_enginefamily like @enginefamily))
                        --begin
							select top 1 cmdg_applyno,cmdg_carcompomodelno,cmdg_carmodelno from nv_carmodeldata 
							where cmdg_carcompomodelno like @sub10_carcompomodelno
							and cmdg_applyno = (select MiN(abd_applyno) from nv_applybasedata join nv_carcompomodel on cpm_applyno = abd_applyno where abd_enginefamily like @enginefamily and abd_applystatus in ('50','55') 
							--AND abd_applyno<>@applyno 
							and cpm_carcompomodelno like @sub10_carcompomodelno)
							and cmdg_berepresentativevehicle='Y'
       --                 end
       --                 else
       --                 begin
							--select top 1 cmdg_applyno,cmdg_carcompomodelno,cmdg_carmodelno from nv_carmodeldata 
							--where cmdg_carcompomodelno like @sub10_carcompomodelno
							--and cmdg_applyno = @applyno
							--and cmdg_berepresentativevehicle='Y'
       --                 end
                end             
    end

--exec pr_get_unNewApplyData '102070003','101120001_1'
--exec pr_get_unNewApplyData '101080003','101100008_3'
GO
PRINT N'正在更改 [dbo].[pr_statistical_table]...';


GO


ALTER PROCEDURE [dbo].[pr_statistical_table] 
@year nvarchar(4)=''
AS
BEGIN
--exec pr_statistical_table '2013'
/*合格證核發統計	
2018新增 nv_codetbl_statisticalcode 代碼檔
資料狀態為：歸檔，不異動的車型組不列入
用合格證的核發日去歸到每個月份去

select * from nv_codetbl_noisevechilecategory --車種分類 cmdg_cartype
1轎車、旅行車(A)
2小客、貨車(B)
3大客車( C )
4大貨車(D)
5 50cc以下(E)
6 50~100cc(F)
7,8 101cc以上(G)

1	(A類) 轎車、旅行車
2	(B類) 小客車、貨車及經公告之特殊車輛≦3.5公噸
3	(C類) 貨車、大客車及經公告之特殊車輛＞3.5公噸＜150 kW
4	(C類) 貨車、大客車及經公告之特殊車輛＞3.5公噸≧150 kW
5	(D類) 貨車、大客車及經公告之特殊車輛＞3.5公噸＜150 kW
6	(D類) 貨車、大客車及經公告之特殊車輛＞3.5公噸≧150 kW
7	(E類) 機器腳踏車≦50c.c
8	(F類) 機器腳踏車＞50c.c. ≦100c.c.
9	(G類) 機器腳踏車＞100c.c.≦175c.c
10	(G類) 機器腳踏車＞175c.c.
abd_powerfrom --動力來源ct_pf_desc nv_codetbl_powerfrom

abd_applystatus
50	已上傳平台
55	歸檔

abd_validateddate 核可日期
abd_isimport為1(國產)

--日期區間
DECLARE @i int    
declare @DateS datetime
declare @DateE datetime
    SET @i = 1
    WHILE( @i <=12)
BEGIN     
    SET @DateS=DATEADD(mm, DATEDIFF(mm,0,datename(year,getdate())+'-'+convert(varchar(10),@i)+'-01'), 0) 
	SET @DateE=dateadd(ms,-3,DATEADD(mm, DATEDIFF(m,0,datename(year,getdate())+'-'+convert(varchar(10),@i)+'-01')+1, 0))
    SELECT @DateS,@DateE
SET @i = @i + 1
    
END
SET @YearDateS=DATEADD(mm, DATEDIFF(mm,0,datename(year,getdate())+'-'+convert(varchar(10),1)+'-01'), 0) 
SET @YearDateE=dateadd(ms,-3,DATEADD(mm, DATEDIFF(m,0,datename(year,getdate())+'-'+convert(varchar(10),12)+'-01')+1, 0))
    
*/
declare @AddString varchar(500)

DECLARE @m int   --months
DECLARE @i int   --isimport
DECLARE @ct int  --cartype
DECLARE @pf int  --powerfrom

declare @DateS datetime
declare @DateE datetime
declare @YearDateS datetime
declare @YearDateE datetime

create table #tmp_vechiletype
(
vechile nvarchar(20)
)

create table #tmp_isimport
(
import int
)

create table #tmp_cartype
(
cardesc int
)
create table #tmp_month
(
months int
)

create table #tmp_power
(
powerfrom int
)

insert into #tmp_vechiletype values('G')
insert into #tmp_vechiletype values('D')
insert into #tmp_vechiletype values('M')
insert into #tmp_vechiletype values('E')

/*月份*/
SET @m = 1   
    WHILE( @m <=12)
    BEGIN   
		insert into #tmp_month values(CONVERT(nvarchar(10) ,@m))  
	SET @m = @m + 1
	END
/*進口 國產*/	
SET @i = 1   	
WHILE( @i <=2)
    BEGIN   
		insert into #tmp_isimport values(@i)  
	SET @i = @i + 1
END	

/*車型種類*/	
SET @ct = 1   	
WHILE( @ct <=7)
    BEGIN   
		insert into #tmp_cartype values(@ct)  
	SET @ct = @ct + 1
END	

/*新車型種類 #tmp_EnCartype*/	
/*2018改從nv_codetbl_statisticalcode代碼檔裡取資料*/

/*動力來源*/	
SET @pf = 1   	
WHILE( @pf <=3)
    BEGIN   
		insert into #tmp_power values(@pf)  
	SET @pf = @pf + 1
END	

if (@year <> '' and @year <> 'n' )
begin 
	SET @YearDateS=@year+'-01-01 00:00:00.000'
	SET @YearDateE=@year+'-12-31 23:59:59.997'
end
else
begin 
	SET @YearDateS=DATEADD(mm, DATEDIFF(mm,0,datename(year,getdate())+'-'+convert(varchar(10),1)+'-01'), 0) 
SET @YearDateE=dateadd(ms,-3,DATEADD(mm, DATEDIFF(m,0,datename(year,getdate())+'-'+convert(varchar(10),12)+'-01')+1, 0))
end

		
		--舊邏輯start
		
		--select * 
		--INTO #tmp_StandardForm
		--from #tmp_cartype,#tmp_vechiletype,#tmp_power,#tmp_month,#tmp_isimport
		
		
		--DELETE FROM #tmp_StandardForm
		--WHERE (vechile = 'M' AND cardesc in (1,2,3,4)) OR (vechile <> 'M' and cardesc in (5,6,7)) OR (vechile = 'E' and powerfrom in (1,2)) OR (vechile <> 'E' and powerfrom =3 ) 
		
		/*cmdg_cartype =8 紀錄在101cc以上(G)
		abd_powerfrom=3 就都視為電動車*/
				
			--select case cmdg_cartype 
			--when '11' then '1'
			--when '12' then '1'
			--when '4' then '3'
			--when '5' then '4'
			--when '6' then '4'
			--when '7' then '5'		
			--when '8' then '6' 
			--when '9' then '7'
			--when '10' then '7'
			--when '13' then '7'
			--when '14' then '7'
			--else cmdg_cartype end as cmdg_cartype,case abd_powerfrom when '3' then 'E' else abd_vechiletype end as abd_vechiletype,abd_powerfrom,abd_validateddate,abd_isimport,abd_applyno ,cmdg_carcompomodelno 
			--INTO #tmp_statistical
			--from nv_applybasedata				
			--join nv_carmodeldata on abd_applyno=cmdg_applyno 
			--join nv_carcompomodel on cpm_applyno=abd_applyno and cmdg_applyno=cpm_applyno
			--where abd_applystatus in ('50','55') and cpm_applytype <>'6' and abd_validateddate BETWEEN @YearDateS AND @YearDateE
		
			--select distinct 
			--case vechile 
			--when 'E' then '4'					
			--else ct_ct_order end as ct_ct_order
			--,cardesc,
			--case abd_powerfrom 
			--when '3' then 'E'					
			--else vechile end as vechile					 
			--, powerfrom, months, import, cmdg_cartype, abd_vechiletype,abd_powerfrom, abd_validateddate, abd_isimport, abd_applyno, cmdg_carcompomodelno 	
			--from #tmp_StandardForm 		 
			--left join #tmp_statistical on vechile=abd_vechiletype and powerfrom=abd_powerfrom and import=abd_isimport and months=DatePart(month, abd_validateddate) and cardesc=cmdg_cartype
			--left join nv_codetbl_cartype on ct_ct_id = vechile
		
		--舊邏輯end
		
		
		----以車型組代碼第八碼(電動車第九碼)取得車輛種類，ABCDEFG的定義由代碼檔重新定義
		select * 
		INTO #tmp_StandardForm2
		from #tmp_vechiletype,#tmp_power,#tmp_month,#tmp_isimport,nv_codetbl_statisticalcode


		DELETE FROM #tmp_StandardForm2
		WHERE (vechile = 'M' AND ct_sc_id in (1,2,3,4)) OR (vechile <> 'M' and ct_sc_id in (5,6,7)) OR (vechile = 'E' and powerfrom in (1,2)) OR (vechile <> 'E' and powerfrom =3 ) 
		
		select 
		case abd_powerfrom when '3' then SUBSTRING(cpm_carcompomodelno,9,1) else SUBSTRING(cpm_carcompomodelno,8,1) end as cardesc,
		case abd_powerfrom when '3' then 'E' else abd_vechiletype end as abd_vechiletype,
		abd_powerfrom,abd_validateddate,abd_isimport,abd_applyno ,cpm_carcompomodelno  
		INTO #tmp_statistical2
		from nv_applybasedata
		join nv_carcompomodel on cpm_applyno=abd_applyno			
		where abd_applystatus in ('50','55') and cpm_applytype <> '6' and abd_validateddate BETWEEN @YearDateS AND @YearDateE

		select distinct 
		ct_sc_cartype,
		ct_sc_desc,
		ct_sc_id,
		vechile,powerfrom, months, import, abd_vechiletype,abd_powerfrom, abd_validateddate, abd_isimport, abd_applyno, cpm_carcompomodelno ,
		case vechile when 'E' then '4' else ct_ct_order end as ct_ct_order	
		from #tmp_StandardForm2 
		left join #tmp_statistical2 
		on vechile collate Chinese_Taiwan_Stroke_BIN =abd_vechiletype collate Chinese_Taiwan_Stroke_BIN 
		and powerfrom  =abd_powerfrom  
		and import =abd_isimport  
		and months  =DatePart(month, abd_validateddate)  
		and cardesc =ct_sc_cartype
		left join nv_codetbl_cartype on ct_ct_id = vechile

		----新邏輯 end		
drop table #tmp_cartype,#tmp_vechiletype,#tmp_power,#tmp_month,#tmp_isimport,#tmp_statistical2,#tmp_StandardForm2 
	
END
GO
PRINT N'正在更改 [dbo].[pr_get_noisestandarddata]...';


GO

--噪音標準值
ALTER procedure [dbo].[pr_get_noisestandarddata] 
@applyno varchar(9)='',
@carcompomodelno varchar(15)='',
@carmodelno varchar(36)='',
@standarddate varchar(5)=''

as 

declare @speedupstandardvalue DECIMAL(9,0) --加速 標準
declare @stationarystandardvalue DECIMAL(9,0) --原地 標準

declare @transporttype varchar(2) --原地噪音
declare @carspeetuptype varchar(3) --加速噪音
declare @carusedstatus char(1)
select top 1 @carspeetuptype=cmdg_carspeetuptype,@transporttype=cmdg_cartype,@carusedstatus=cmdg_carusedstatus 
from nv_carmodeldata 
where cmdg_applyno=@applyno 
and cmdg_carcompomodelno =@carcompomodelno 
and cmdg_carmodelno =@carmodelno


select @stationarystandardvalue=nsd_speedupstandardvalue
from nv_noisestandarddata
where nsd_transporttype = @transporttype 
and nsd_standarddate = @standarddate
and nsd_neworinuse=@carusedstatus
and nsd_type='O'


if (Convert(DECIMAL,@standarddate)>5.0)
begin
select @speedupstandardvalue=nsd_speedupstandardvalue
from nv_noisestandarddata
where nsd_transporttype = @carspeetuptype --加速
and nsd_standarddate = @standarddate
and nsd_neworinuse=@carusedstatus
and nsd_type='S'
end
else
begin
select @speedupstandardvalue=nsd_speedupstandardvalue
from nv_noisestandarddata
where nsd_transporttype = @transporttype --5期前不分
and nsd_standarddate = @standarddate
and nsd_neworinuse=@carusedstatus
and nsd_type='S'
end

SELECT @speedupstandardvalue AS nsd_speedupstandardvalue, @stationarystandardvalue AS nsd_stationarystandardvalue
GO
PRINT N'正在更改 [dbo].[pr_get_applylistqry]...';


GO


--EXEC pr_get_applylistqry @applyno='101080004'
ALTER procedure [dbo].[pr_get_applylistqry] 
@databelongcompname nvarchar(200)='',
@applyno nvarchar(9)='',
@applystatus varchar(2)='',
@enginefamily varchar(150)='',
@carbrand nvarchar(50)='',
@carcompomodelno varchar(15)='',
@vechiletype varchar(1)='',
@adaptstandarddate varchar(5)='',
@cartype varchar(2)='',
@applysdate varchar(8)='',
@applyedate varchar(8)='',
@validatedsdate varchar(8)='',
@validatededate varchar(8)='',
@Vcnnp_receivedate1 varchar(8)='',
@Vcnnp_receivedate2 varchar(8)='',
@powerfrom varchar(2)='',
@fullcarname nvarchar(max)=''

--set @carbrand='%'+@carbrand+'%' 
--set @carcompomodelno='%'+@carcompomodelno+'%'
--set @enginefamily='%'+@enginefamily+'%'
--set @databelongcompname ='%'+@databelongcompname+'%'
as
declare @where_condi nvarchar(2000)
declare @exec_sql nvarchar(4000)
set @where_condi=' where 1=1 '

if @databelongcompname <> ''
begin 
	set @where_condi = @where_condi + ' and abd_databelongcompidno in( select ci_compidno from nv_companyinfo where ci_compname like ''%'+@databelongcompname+'%'')'
end 

if @enginefamily <> ''
begin 
	set @where_condi = @where_condi + ' and abd_enginefamily like ''%'+@enginefamily+'%'''
end

if @carcompomodelno <> ''
begin 
	set @where_condi = @where_condi + ' and cmdg_carcompomodelno like ''%'+@carcompomodelno+'%'''
end  

if @applyno <> ''
begin 
	set @where_condi = @where_condi + ' and abd_applyno like ''%'+@applyno+'%'''
end 

if (@applystatus <> '' and @applystatus <> 'n' )
begin 
	set @where_condi = @where_condi + ' and abd_applystatus='''+@applystatus+''''
end

if (@vechiletype <> '' and @vechiletype <> 'n' and @vechiletype <> 'E')
begin 
	set @where_condi = @where_condi + ' and abd_vechiletype='''+@vechiletype+''''
end 
else if (@vechiletype ='E')
begin 
	set @where_condi = @where_condi + ' and abd_powerfrom=''3'''
end 

if @carbrand <> ''
begin 
	set @where_condi = @where_condi + ' and cmdg_carbrand in( select ct_bd_id from nv_codetbl_brand where ct_bd_desc like ''%'+@carbrand+'%'')'
end 

if (@cartype <> '' and @cartype <> 'n')
begin 
	set @where_condi = @where_condi + ' and cmdg_cartype='''+@cartype+''''
end 

if (@adaptstandarddate <> '' and @adaptstandarddate <> 'n')
begin 
	set @where_condi = @where_condi + ' and cpm_adaptstandarddate='''+@adaptstandarddate+''''
end 

if(@applysdate <> '' and @applyedate <> '')
begin
	set @where_condi = @where_condi + '  and convert(nvarchar,abd_applydate,112) >= '''+@applysdate+''''
					+ '  and convert(nvarchar,abd_applydate,112) <= '''+@applyedate+''''
end
else begin
	if @applysdate <> ''
	begin 
		set @where_condi = @where_condi + '  and convert(nvarchar,abd_applydate,112) >= convert(datetime,'''+@applysdate+''')'
	end 

	if @applyedate <> ''
	begin 
		set @where_condi = @where_condi + '  and convert(nvarchar,abd_applydate,112) <= convert(datetime,'''+@applyedate+''')'
	end
end

if(@validatedsdate <> '' and @validatededate <>'')
begin
	set @where_condi = @where_condi + '  and convert(nvarchar,abd_validateddate,112) >= '''+@validatedsdate+''''
					+ '  and convert(nvarchar,abd_validateddate,112) <= '''+@validatededate+''''
end
else begin
	if @validatedsdate <> ''
	begin 
		set @where_condi = @where_condi + '  and convert(nvarchar,abd_validateddate,112) >= convert(datetime,'''+@validatedsdate+''')'
	end 

	if @validatededate <> ''
	begin 
		set @where_condi = @where_condi + '  and convert(nvarchar,abd_validateddate,112) <= convert(datetime,'''+@validatededate+''')'
	end
end

if(@Vcnnp_receivedate1 <> '' and @Vcnnp_receivedate2 <>'')
begin
	set @where_condi = @where_condi + '  and convert(nvarchar,vp_execdate,112) >= '''+@Vcnnp_receivedate1+''''
					+ '  and convert(nvarchar,vp_execdate,112) <= '''+@Vcnnp_receivedate2+''''
end
else begin
	if @Vcnnp_receivedate1 <> ''
	begin 
		set @where_condi = @where_condi + '  and convert(nvarchar,vp_execdate,112) >= convert(datetime,'''+@Vcnnp_receivedate1+''')'
	end 

	if @Vcnnp_receivedate2 <> ''
	begin 
		set @where_condi = @where_condi + '  and convert(nvarchar,vp_execdate,112) <= convert(datetime,'''+@Vcnnp_receivedate2+''')'
	end
end

if (@powerfrom <> '' and @powerfrom <> 'n' )
begin 
	set @where_condi = @where_condi + ' and abd_powerfrom ='''+@powerfrom+''''
end 

if (@fullcarname <> '')
begin 
	--set @where_condi = @where_condi + ' and dbo.fn_FullCarstyleNameStr(cmdg_applyno,cmdg_carcompomodelno,cmdg_carmodelno) like ''%'+@fullcarname+'%'''
	set @where_condi = @where_condi +  'and nv_carmodeldata.cmdg_carchnmodelname+ nv_carmodeldata.cmdg_carengmodelname+ nv_carmodeldata.cmdg_carmodelcode+cmdg_carmodeladd like ''%'+@fullcarname+'%'''
end 

print @where_condi

declare @sql_1 nvarchar(max)
set @sql_1='select distinct abd_id,abd_applyno,abd_databelongcompidno,abd_vechiletype,abd_enginefamily,abd_enginesn,abd_applystatus,ct_aps_desc,ct_aps_id,vp_execdate,abd_steps,
ct_cs_desc as abd_certstatus,ct_cs_id as certstatus,abd_certdocfileid,convert(nvarchar,abd_certdocissuedate,112) certdocissuedate,abd_certdocno,dbo.fn_GetStandarddateStr(abd_applyno) as standarddate ,
abd_statusdate
from nv_applybasedata 
left join nv_carmodeldata on cmdg_applyno = abd_applyno 
left join nv_carcompomodel on cpm_applyno=abd_applyno
left join nv_codetbl_certstatus on ct_cs_id=abd_certstatus 
left join nv_codetbl_applystatus on ct_aps_id =abd_applystatus
left join nv_codetbl_powerfrom on ct_pf_id =abd_powerfrom --ct_pf_desc
left join (SELECT  top 1 [vp_applyno],vp_execdate
  FROM [noisevalidation].[dbo].[nv_validationprocess]
  where vp_applystatus=80) #s on #s.vp_applyno = abd_applyno
'
set @sql_1=@sql_1+@where_condi
print @sql_1

exec (@sql_1)
GO
PRINT N'正在更改 [dbo].[pr_get_cw_applylistqry]...';


GO

ALTER procedure [dbo].[pr_get_cw_applylistqry] 
@databelongcompname nvarchar(200)='',
@applyno nvarchar(9)='',
@applystatus varchar(2)='',
@enginefamily varchar(150)='',
@carbrand nvarchar(50)='',
@carcompomodelno varchar(15)='',
@vechiletype varchar(1)='',
@adaptstandarddate varchar(5)='',
@cartype varchar(2)='',
@applysdate varchar(8)='',
@applyedate varchar(8)='',
@validatedsdate varchar(8)='',
@validatededate varchar(8)='',
@Vcnnp_receivedate1 varchar(8)='',
@Vcnnp_receivedate2 varchar(8)=''

--set @carbrand='%'+@carbrand+'%' 
--set @carcompomodelno='%'+@carcompomodelno+'%'
--set @enginefamily='%'+@enginefamily+'%'
--set @databelongcompname ='%'+@databelongcompname+'%'
as
declare @where_condi nvarchar(2000)
declare @exec_sql nvarchar(4000)
set @where_condi=' where 1=1 '

if @databelongcompname <> ''
begin 
	set @where_condi = @where_condi + ' and abd_databelongcompidno in( select ci_compidno from nv_companyinfo where ci_compname like ''%'+@databelongcompname+'%'')'
end 

if @enginefamily <> ''
begin 
	set @where_condi = @where_condi + ' and abd_enginefamily like ''%'+@enginefamily+'%'''
end

if @carcompomodelno <> ''
begin 
	set @where_condi = @where_condi + ' and cmdg_carcompomodelno like ''%'+@carcompomodelno+'%'''
end  

if @applyno <> ''
begin 
	set @where_condi = @where_condi + ' and abd_applyno like ''%'+@applyno+'%'''
end 

if (@applystatus <> '' and @applystatus <> 'n' )
begin
	set @where_condi = @where_condi + ' and abd_applystatus not in(''10'',''60'') and abd_applystatus='''+@applystatus+''''
end
else
begin
	set @where_condi = @where_condi + ' and abd_applystatus not in(''10'',''60'')'
end

if (@vechiletype <> '' and @vechiletype <> 'n' and @vechiletype <> 'E')
begin 
	set @where_condi = @where_condi + ' and abd_vechiletype='''+@vechiletype+''''
end 
else if (@vechiletype ='E')
begin 
	set @where_condi = @where_condi + ' and abd_powerfrom=''3'''
end 

if @carbrand <> ''
begin 
	set @where_condi = @where_condi + ' and abd_factoryname like ''%'+ @carbrand +'%'''
end 

if (@cartype <> '' and @cartype <> 'n')
begin 
	set @where_condi = @where_condi + ' and cmdg_cartype='''+@cartype+''''
end 

if (@adaptstandarddate <> '' and @adaptstandarddate <> 'n')
begin 
	set @where_condi = @where_condi + ' and abd_adaptstandarddate='''+@adaptstandarddate+''''
end 

if(@applysdate <> '' and @applyedate <> '')
begin
	set @where_condi = @where_condi + '  and convert(nvarchar,abd_applydate,112) >= '''+@applysdate+''''
					+ '  and convert(nvarchar,abd_applydate,112) <= '''+@applyedate+''''
end
else begin
	if @applysdate <> ''
	begin 
		set @where_condi = @where_condi + '  and convert(nvarchar,abd_applydate,112) >= convert(datetime,'''+@applysdate+''')'
	end 

	if @applyedate <> ''
	begin 
		set @where_condi = @where_condi + '  and convert(nvarchar,abd_applydate,112) <= convert(datetime,'''+@applyedate+''')'
	end
end

if(@validatedsdate <> '' and @validatededate <>'')
begin
	set @where_condi = @where_condi + '  and convert(nvarchar,abd_validateddate,112) >= '''+@validatedsdate+''''
					+ '  and convert(nvarchar,abd_validateddate,112) <= '''+@validatededate+''''
end
else begin
	if @validatedsdate <> ''
	begin 
		set @where_condi = @where_condi + '  and convert(nvarchar,abd_validateddate,112) >= convert(datetime,'''+@validatedsdate+''')'
	end 

	if @validatededate <> ''
	begin 
		set @where_condi = @where_condi + '  and convert(nvarchar,abd_validateddate,112) <= convert(datetime,'''+@validatededate+''')'
	end
end





if(@Vcnnp_receivedate1 <> '' and @Vcnnp_receivedate2 <>'')
begin
	set @where_condi = @where_condi + '  and convert(nvarchar,vp_execdate,112) >= '''+@Vcnnp_receivedate1+''''
					+ '  and convert(nvarchar,vp_execdate,112) <= '''+@Vcnnp_receivedate2+''''
end
else begin
	if @Vcnnp_receivedate1 <> ''
	begin 
		set @where_condi = @where_condi + '  and convert(nvarchar,vp_execdate,112) >= convert(datetime,'''+@Vcnnp_receivedate1+''')'
	end 

	if @Vcnnp_receivedate2 <> ''
	begin 
		set @where_condi = @where_condi + '  and convert(nvarchar,vp_execdate,112) <= convert(datetime,'''+@Vcnnp_receivedate2+''')'
	end
end

--print @where_condi

declare @sql_1 nvarchar(max)
set @sql_1='select distinct abd_id,abd_applyno,abd_databelongcompidno,abd_vechiletype,abd_enginefamily,abd_adaptstandarddate,abd_enginesn,abd_applystatus,ct_aps_desc,ct_aps_id,vp_execdate from nv_applybasedata 
left join nv_carmodeldata on cmdg_applyno = abd_applyno 
left join nv_codetbl_applystatus on ct_aps_id =abd_applystatus
left join (SELECT  top 1 [vp_applyno],vp_execdate
  FROM [noisevalidation].[dbo].[nv_validationprocess]
  where vp_applystatus=80 ) #s on #s.vp_applyno = abd_applyno
'
set @sql_1=@sql_1+@where_condi
print @sql_1

exec (@sql_1)
GO
PRINT N'正在更改 [dbo].[pr_get_export_cname]...';


GO

--匯出欄位
ALTER procedure [dbo].[pr_get_export_cname] 
@type varchar(2)=''
as 
create table #tmp_export( 

tmp_ep_id nvarchar(50),
tmp_ep_desc nvarchar(50),
)

insert into #tmp_export values('a1_applyno','申請編號')
insert into #tmp_export values('a1_compname','公司名稱')
insert into #tmp_export values('a1_databelongcompidno','公司統編')
insert into #tmp_export values('a1_vechiletype','交通工具種類')
insert into #tmp_export values('a1_enginefamily','引擎族')

insert into #tmp_export values('a1_twocertmethod','兩證合一申請方式')
insert into #tmp_export values('a1_factoryname','製造廠')
insert into #tmp_export values('a1_carstyleyear','車型年')
insert into #tmp_export values('a1_powerfrom','動力來源')
insert into #tmp_export values('a1_enginesn','引擎族流水碼')
insert into #tmp_export values('a1_officedocno','申請函發文字號')
insert into #tmp_export values('a1_officedocdate','申請函發文日期')
insert into #tmp_export values('a1_mailcertidocaddr','合格證郵寄地址')
insert into #tmp_export values('a1_contactname','聯絡人')
insert into #tmp_export values('a1_contacttel','聯絡人電話')
insert into #tmp_export values('a1_contactemail','聯絡人Email')
insert into #tmp_export values('a1_applydate','申請日期')
insert into #tmp_export values('a1_applystatus','資料狀態')
insert into #tmp_export values('a1_validateddate','合格證核准日期')
insert into #tmp_export values('a1_denyreason','退件原因')
--abd_ (a1_)
insert into #tmp_export values('a2_adaptstandarddate','適用期別')
insert into #tmp_export values('a2_carcompomodelno','車型組代號')
insert into #tmp_export values('a2_applytype','申請型式')
insert into #tmp_export values('a2_useeurocert','以歐盟合格證申請')
--insert into #tmp_export values('a2_baseenginename','基本引擎')
--insert into #tmp_export values('a2_transmissiontypetype','排檔型式')
insert into #tmp_export values('a2_gasolinetype','使用燃料')
insert into #tmp_export values('a2_powerexportway','燃燒循環(M)')
insert into #tmp_export values('a2_provideoilmethod','供油方式')
insert into #tmp_export values('a2_exhuast','總排氣量')
insert into #tmp_export values('a2_cylindernums','汽缸數')
insert into #tmp_export values('a2_cylindertype','汽缸排列')
insert into #tmp_export values('a2_enginesetupposnmethod','引擎安裝方式(M)')
insert into #tmp_export values('a2_fituptype','引擎安裝位置(G)')
insert into #tmp_export values('a2_coolsystem','冷卻系統型式')
insert into #tmp_export values('a2_testfactory','檢測廠')
insert into #tmp_export values('a2_importarea','進口國')

insert into #tmp_export values('a2ntr_testrptno','報告編號')
insert into #tmp_export values('a2ntr_carbodyno','車身碼')
insert into #tmp_export values('a2ntr_engineno','引擎碼')
insert into #tmp_export values('a2ntr_staynoise','代表車原地噪音值')
insert into #tmp_export values('a2ntr_speedupnoise','代表車加速噪音值')
insert into #tmp_export values('a2ntr_Estaynoise','延伸車原地噪音值(有延伸測試者)')
insert into #tmp_export values('a2ntr_Espeedupnoise','延伸車加速噪音值(有延伸測試者)')
insert into #tmp_export values('a2ntr_remarkdesc','測試報告備註')

--cpm_  (a2_)
--insert into #tmp_export values('a3_carmodelno','車型編號')
insert into #tmp_export values('a3_producercountry','製造地區')
insert into #tmp_export values('a3_carbodyshape','車身式樣') --分汽機車
--insert into #tmp_export values('a3_carbodyshapedesc','車身式樣')
insert into #tmp_export values('a3_fullcarstylename','車型名稱(10個欄位組合)')
insert into #tmp_export values('a3_berepresentativevehicle','代表車')
insert into #tmp_export values('a3_cartype','車種分類')
insert into #tmp_export values('a3_baseenginename','基本引擎')
insert into #tmp_export values('a3_carbrand','車型廠牌')
insert into #tmp_export values('a3_carchnmodelname','中文車型名稱')
insert into #tmp_export values('a3_carengmodelname','英文車型名稱')
insert into #tmp_export values('a3_carmodelcode','車型代碼')
insert into #tmp_export values('a3_transmissiontype','排檔型式')
insert into #tmp_export values('a3_transmissionnum','車型排檔數')
insert into #tmp_export values('a3_cardoorcount','車型門數')
insert into #tmp_export values('a3_carmodeladd','車型名稱補充')
insert into #tmp_export values('a3_carbodylenth','全長')
insert into #tmp_export values('a3_carbodywidth','全寬')
insert into #tmp_export values('a3_carbodyhieght','全高')
insert into #tmp_export values('a3_carwheelbase','軸距')
insert into #tmp_export values('a3_carwheelbase_f','輪距-前')
insert into #tmp_export values('a3_carwheelbase_b','輪距-後')
insert into #tmp_export values('a3_caremptyweight','空重')
insert into #tmp_export values('a3_cartotalweight','總重')
insert into #tmp_export values('a3_passengers','乘坐人數')
insert into #tmp_export values('a3_loadingweight','載重(M)')
insert into #tmp_export values('a3_cylindermeters','缸徑')
insert into #tmp_export values('a3_cylinderstroke','衝程')
insert into #tmp_export values('a3_compressionratio','壓縮比')
insert into #tmp_export values('a3_maxhorsepower','最大馬力')
insert into #tmp_export values('a3_maxhorsepowerspeed','最大馬力轉速')
insert into #tmp_export values('a3_torque','最大扭力')
insert into #tmp_export values('a3_torquespeed','最大扭力轉速')
insert into #tmp_export values('a3_maxhorsepower_hb','馬達最大馬力(D)')
insert into #tmp_export values('a3_maxhorsepowerspeed_hb','馬達最大馬力轉速(D)')
insert into #tmp_export values('a3_torque_hb','馬達最大扭力(D)')
insert into #tmp_export values('a3_torquespeed_hb','馬達最大扭力轉速(D)')
insert into #tmp_export values('a3_turborchargers','增壓器(G)') --ct_htm_desc
insert into #tmp_export values('a3_strokeposdirection','排氣位置及方向')
insert into #tmp_export values('a3_oilcapacity','油箱容量')
insert into #tmp_export values('a3_929598','油料')
insert into #tmp_export values('a3_firemethod','啟動方式(M)')
insert into #tmp_export values('a3_exhaustsystem','排氣系統')
insert into #tmp_export values('a3_PCV','PCV(G)')
insert into #tmp_export values('a3_EEC','EEC(G)')
insert into #tmp_export values('a3_PM','粒狀汙染物(M)')
insert into #tmp_export values('a3_CO','一氧化碳(M)')
insert into #tmp_export values('a3_HC','碳氫化合物(M)')
--insert into #tmp_export values('a3_drivetraintype','傳動方式') --ct_dt_desc
insert into #tmp_export values('a3_differentialmodel','差速器-型式')
insert into #tmp_export values('a3_differentialgearratio','差速器-齒比')
insert into #tmp_export values('a3_1stReductionratio','一次減速比(M)')
insert into #tmp_export values('a3_2ndReductionratio','二次減速比(M)')
insert into #tmp_export values('a3_transmissionmodel','變速系統-型式')
insert into #tmp_export values('a3_transmissionbackgearratio','變速系統-倒檔齒比')
insert into #tmp_export values('a3_highestspeed','最高車速')
insert into #tmp_export values('a3_suspensionsystem_f','懸吊系統-前')
insert into #tmp_export values('a3_suspensionsystem_b','懸吊系統-後')
insert into #tmp_export values('a3_tirespec_std_f','輪胎規格-前')
insert into #tmp_export values('a3_tirespec_std_b','輪胎規格-後')
insert into #tmp_export values('a3_tirespec_cho_f','輪胎選配-前')
insert into #tmp_export values('a3_tirespec_cho_b','輪胎選配-後')
insert into #tmp_export values('a3_testweight','受驗車重')
insert into #tmp_export values('a3_testgearreductionratio','測試檔位總減速比')
insert into #tmp_export values('a3_coolingdrivermethod','冷卻風扇驅動方式')
insert into #tmp_export values('a3_tiresnum','輪胎數量(不含備胎)')
insert into #tmp_export values('a3_exhaustpipenum','排氣管開口數量')
insert into #tmp_export values('a3_tirewidth','輪胎寬度')
insert into #tmp_export values('a3_inletmode','進氣方式')
insert into #tmp_export values('a3_drivetype','驅動方式(G)')
insert into #tmp_export values('a3_remark','規格表備註')
--cmdg_(a3_)
	
 
if (@type <> ''  )
begin 
	select * from #tmp_export where left(tmp_ep_id,2)= 'a'+@type
end
else
begin 
	select * from #tmp_export 
end


--exec pr_get_export_cname '2'
GO
PRINT N'正在更改 [dbo].[pr_get_ExportDataContent]...';


GO

ALTER procedure [dbo].[pr_get_ExportDataContent] 
@s_sqltitle nvarchar(max),
@databelongcompname nvarchar(200)='',
@applyno nvarchar(9)='',
@applystatus varchar(2)='',
@enginefamily varchar(150)='',
@carbrand nvarchar(50)='',
@carcompomodelno varchar(15)='',
@vechiletype varchar(1)='',
@adaptstandarddate varchar(5)='',
@cartype varchar(2)='',
@applysdate varchar(8)='',
@applyedate varchar(8)='',
@validatedsdate varchar(8)='',
@validatededate varchar(8)='',
@Vcnnp_receivedate1 varchar(8)='',
@Vcnnp_receivedate2 varchar(8)=''

--set @carbrand='%'+@carbrand+'%' 
--set @carcompomodelno='%'+@carcompomodelno+'%'
--set @enginefamily='%'+@enginefamily+'%'
--set @databelongcompname ='%'+@databelongcompname+'%'
as
declare @where_condi nvarchar(2000)
declare @exec_sql nvarchar(4000)
set @where_condi='where 1=1 and cpm_applytype not in (''6'',''9'')'

if @s_sqltitle <> ''
begin 
	set @s_sqltitle = @s_sqltitle 
end 

if @databelongcompname <> ''
begin 
	set @where_condi = @where_condi + ' and abd_databelongcompidno in( select ci_compidno from nv_companyinfo where ci_compname like ''%'+@databelongcompname+'%'')'
end 

if @enginefamily <> ''
begin 
	set @where_condi = @where_condi + ' and abd_enginefamily like ''%'+@enginefamily+'%'''
end

if @carcompomodelno <> ''
begin 
	set @where_condi = @where_condi + ' and cmdg_carcompomodelno like ''%'+@carcompomodelno+'%'''
end  

if @applyno <> ''
begin 
	set @where_condi = @where_condi + ' and abd_applyno like ''%'+@applyno+'%'''
end 

if (@applystatus <> '' and @applystatus <> 'n' )
begin 
	set @where_condi = @where_condi + ' and abd_applystatus='''+@applystatus+''''
end

if (@vechiletype <> '' and @vechiletype <> 'n' and @vechiletype <> 'E')
begin 
	set @where_condi = @where_condi + ' and abd_vechiletype='''+@vechiletype+''''
end 
else if (@vechiletype ='E')
begin 
	set @where_condi = @where_condi + ' and abd_powerfrom=''3'''
end 

if @carbrand <> ''
begin 
	set @where_condi = @where_condi + ' and cmdg_carbrand in( select ct_bd_id from nv_codetbl_brand where ct_bd_desc like ''%'+@carbrand+'%'')'
end 

if (@cartype <> '' and @cartype <> 'n')
begin 
	set @where_condi = @where_condi + ' and cmdg_cartype='''+@cartype+''''
end 

if (@adaptstandarddate <> '' and @adaptstandarddate <> 'n')
begin 
	set @where_condi = @where_condi + ' and cpm_adaptstandarddate='''+@adaptstandarddate+''''
end 

if(@applysdate <> '' and @applyedate <> '')
begin
	set @where_condi = @where_condi + '  and convert(nvarchar,abd_applydate,112) >= '''+@applysdate+''''
					+ '  and convert(nvarchar,abd_applydate,112) <= '''+@applyedate+''''
end
else begin
	if @applysdate <> ''
	begin 
		set @where_condi = @where_condi + '  and convert(nvarchar,abd_applydate,112) >= convert(datetime,'''+@applysdate+''')'
	end 

	if @applyedate <> ''
	begin 
		set @where_condi = @where_condi + '  and convert(nvarchar,abd_applydate,112) <= convert(datetime,'''+@applyedate+''')'
	end
end

if(@validatedsdate <> '' and @validatededate <>'')
begin
	set @where_condi = @where_condi + '  and convert(nvarchar,abd_validateddate,112) >= '''+@validatedsdate+''''
					+ '  and convert(nvarchar,abd_validateddate,112) <= '''+@validatededate+''''
end
else begin
	if @validatedsdate <> ''
	begin 
		set @where_condi = @where_condi + '  and convert(nvarchar,abd_validateddate,112) >= convert(datetime,'''+@validatedsdate+''')'
	end 

	if @validatededate <> ''
	begin 
		set @where_condi = @where_condi + '  and convert(nvarchar,abd_validateddate,112) <= convert(datetime,'''+@validatededate+''')'
	end
end





if(@Vcnnp_receivedate1 <> '' and @Vcnnp_receivedate2 <>'')
begin
	set @where_condi = @where_condi + '  and convert(nvarchar,vp_execdate,112) >= '''+@Vcnnp_receivedate1+''''
					+ '  and convert(nvarchar,vp_execdate,112) <= '''+@Vcnnp_receivedate2+''''
end
else begin
	if @Vcnnp_receivedate1 <> ''
	begin 
		set @where_condi = @where_condi + '  and convert(nvarchar,vp_execdate,112) >= convert(datetime,'''+@Vcnnp_receivedate1+''')'
	end 

	if @Vcnnp_receivedate2 <> ''
	begin 
		set @where_condi = @where_condi + '  and convert(nvarchar,vp_execdate,112) <= convert(datetime,'''+@Vcnnp_receivedate2+''')'
	end
end

--print @where_condi

declare @sql_1 nvarchar(max)
set @sql_1='select distinct '+@s_sqltitle+' 
from nv_applybasedata
join nv_companyinfo on ci_compidno=abd_databelongcompidno
join nv_carcompomodel on cpm_applyno=abd_applyno
join nv_carmodeldata on cmdg_applyno=cpm_applyno and cmdg_carcompomodelno=cpm_carcompomodelno
left join nv_noisetestrpt on ntr_carmodelno = cmdg_carmodelno and ntr_carcompomodelno=cpm_carcompomodelno
--left join nv_noisetestrpt_remark on ntr_carcompomodelno = ntrr_carcompomodelno and ntr_carmodelno=ntrr_carmodelno and ntr_testrptno=ntrr_testrptno
join nv_codetbl_standarddatelawinfo on cpm_adaptstandarddate=ct_sdli_id and abd_vechiletype=ct_sdli_vechiletype

left join nv_codetbl_applytype on ct_at_id = cpm_applytype --申請型式 ct_at_desc
left join nv_codetbl_twocerttype on ct_twct_id=abd_twocertmethod --兩證合一申請方式ct_twct_desc
left join nv_codetbl_useeurocert on ct_eu_id=cpm_useeurocert --以歐盟合格證申請ct_eu_desc
left join nv_codetbl_standarddate ON ct_sd_id = cpm_adaptstandarddate --適用期別 ct_sd_desc
left join nv_codetbl_carbodyshape_g on ct_cbsg_id = cmdg_carbodyshape --車身樣式 ct_cbsg_desc (汽車)
left join nv_codetbl_carbodyshape_m on ct_cbsm_id = cmdg_carbodyshape --車身樣式 ct_cbsm_desc (機車)
left join nv_codetbl_gearmethod on ct_gm_id = cmdg_transmissiontype --排檔型式 ct_gm_desc
left join nv_codetbl_cartype on ct_ct_id =abd_vechiletype --車輛種類 ct_ct_desc 
left join nv_codetbl_applystatus on ct_aps_id =abd_applystatus --資料狀態ct_aps_desc
left join nv_codetbl_powerfrom on ct_pf_id =abd_powerfrom --動力來源ct_pf_desc
left join nv_codetbl_cylinderpos_m on ct_cp_id_m = cpm_cylindertype --汽缸排列ct_cp_desc_m
left join nv_codetbl_fueltype on ct_ft_id =cpm_gasolinetype --使用燃料ct_ft_desc
left join nv_codetbl_engineburnmethod on ct_ebm_id =cpm_powerexportway --燃燒循環ct_ebm_desc
left join nv_codetbl_lubricationtype on ct_le_id =cpm_provideoilmethod --供油方式ct_le_desc
left join nv_codetbl_coolsystem on ct_cs_id=cpm_coolsystem--冷卻系統型式 ct_cs_desc
left join nv_codetbl_testfactory on ct_tf_id = cpm_testfactory--檢測廠 ct_tf_desc
left join nv_codetbl_country on ct_cy_id = cpm_importarea -- 製造地區 ct_cy_desc
left join nv_codetbl_noisevechilecategory on ct_nvc_id = cmdg_cartype -- 車種分類 ct_nvc_desc
left join nv_codetbl_brand on ct_bd_id =cmdg_carbrand --廠牌ct_bd_desc
left join nv_codetbl_vehicledoortype on ct_vr_id=cmdg_cardoorcount --車門 ct_vr_desc
left join nv_codetbl_haveturbomode on ct_htm_id = cmdg_turborchargers --渦輪增壓器 ct_htm_desc
left join nv_codetbl_inletmode on ct_im_id=cmdg_inletmode --進氣方式 ct_im_desc
left join nv_codetbl_drivetype on ct_dt_id=cmdg_drivetype  --驅動方式ct_dt_desc

left join (SELECT  top 1 [vp_applyno],vp_execdate
  FROM [noisevalidation].[dbo].[nv_validationprocess]
  where vp_applystatus=80) #s on #s.vp_applyno = abd_applyno
'
set @sql_1=@sql_1+@where_condi
print @sql_1

exec (@sql_1)
GO
PRINT N'正在建立 [dbo].[pr_CheckCarClass]...';


GO

-- =============================================
-- Author:		<Olvia>
-- Create date: <20180205>
-- Description:	審查表-調整汽機柴，依據期別及車種顯示顯示
-- =============================================
--exec dbo.pr_CheckCarClass '27' 
 
CREATE procedure [dbo].[pr_CheckCarClass] @id varchar(3)  
AS

BEGIN



--6-1:加速噪音車輛種類：M1、N1與總重3.5公噸以下M2類機動車輛
--6-2:加速噪音車輛種類：總重逾3.5公噸M2、M3、N2與N3
if exists(select * from nv_codetbl_noisevechilecategory where ct_nvc_id=@id and
 (
 CHARINDEX('M1', ct_nvc_desc) >0 
 or CHARINDEX('N1', ct_nvc_desc) >0
 or ct_nvc_id='27')
  )
begin   
		SELECT '6-1' ReCheck    
end

else if exists(select * from nv_codetbl_noisevechilecategory where ct_nvc_id=@id and
 (
 CHARINDEX('M3', ct_nvc_desc) >0 
 or CHARINDEX('N2', ct_nvc_desc) >0
 or CHARINDEX('N3', ct_nvc_desc) >0
 or ct_nvc_id in('21','22'))
  )
begin   
			SELECT '6-2' ReCheck    
end
else
begin                                       				                           
			SELECT 'NO' ReCheck                           		     		
end

END
GO
PRINT N'正在建立 [dbo].[pr_CheckWeight]...';


GO


-- =============================================
-- Author:		<Olvia>
-- Create date: <20180205>
-- Description:	規格表-檢查總重與馬力的輸入是否符合所挑選的車種分類
-- =============================================
--exec dbo.pr_CheckWeight '1',4.5,123
  
  CREATE procedure [dbo].[pr_CheckWeight] @id varchar(3),@Weight money,@Power money 
AS

BEGIN


declare @strCheck varchar(3)='0'

if exists(select * from nv_codetbl_noisevechilecategory where ct_nvc_id=@id and (CHARINDEX('總重', ct_nvc_desc) >0 or CHARINDEX('額定引擎', ct_nvc_desc) >0) )
	begin                                       
		if(@id='21')               
		begin  	
		 if(@Weight>3.5 and @Power <= 135) 
			set @strCheck='1'
		end	 
		if(@id='22')               
		begin  	
		 if(@Weight>3.5 and @Power > 135) 
			set @strCheck='1'
		end	 
		if(@id='23')               
		begin  	
		 if( @Power <= 135) 
			set @strCheck='1'
		end	 
		if(@id='24')               
		begin  	
		 if( 150 <@Power and @Power <= 250) 
			set @strCheck='1'
		end	 
		if(@id='25')               
		begin  	
		 if( @Power > 250) 
			set @strCheck='1'
		end	 
		
		if(@id='26')               
		begin  	
		 if(@Weight<=2.5) 
			set @strCheck='1'
		end	 
		if(@id='27')               
		begin  	
		 if(2.5<@Weight and @Weight <= 3.5) 
			set @strCheck='1'
		end	 
		if(@id='28')               
		begin  	
		 if( @Power <= 135) 
			set @strCheck='1'
		end	 
		if(@id='29')               
		begin  	
		 if( @Power > 135) 
			set @strCheck='1'
		end	
		if(@id='30')               
		begin  	
		 if( @Power <= 150) 
			set @strCheck='1'
		end	
		if(@id='31')               
		begin  	
		 if( @Power < 150 and @Power<=250) 
			set @strCheck='1'
		end	
		if(@id='32')               
		begin  	
		 if( @Power > 250 ) 
			set @strCheck='1'
		end	
		if(@id='36')               
		begin  	
		 if(@Weight>2.5) 
			set @strCheck='1'
		end	 	
		
		if(@strCheck='1')
		SELECT 'OK' ReCheck
		else
		SELECT 'NG' ReCheck
	end			
	else
		begin                                       
			SELECT 'OK' ReCheck                          		     		
		end


END
GO
PRINT N'正在建立 [dbo].[pr_get_CarClass]...';


GO

-- =============================================
-- Author:		<Olvia>
-- Create date: <20180205>
-- Description:	測試報告-顯示車種分類
-- =============================================
--exec dbo.pr_get_CarClass 'G','6','40','40'
--exec dbo.pr_get_CarClass 'M','6','35','14'  
CREATE procedure [dbo].[pr_get_CarClass] @gdm varchar(1), @sdid varchar(5),@S_categoryid varchar(3), @O_categoryid varchar(3) 
AS

BEGIN

--加速\客車\M1。原地\轎車、旅行車<4000c.c.
declare @S_CarClassAll nvarchar(250)=''
declare @O_CarClassAll nvarchar(250)=''



select @S_CarClassAll='加速 \ '+
case ISNULL(ct_vc_carclass1,'') when '' then ct_vc_carclass2 else ct_vc_carclass1+' \ '+ct_vc_carclass2 end 
from nv_codetbl_gdm_vechilecategory
where ct_vc_gdm=@gdm and ct_vc_categoryid=@S_categoryid and ct_vc_sdid=@sdid and ct_vc_type='S'


select @O_CarClassAll='原地 \ '+
case ISNULL(ct_vc_carclass1,'') when '' then ct_vc_carclass2 else ct_vc_carclass1+' \ '+ct_vc_carclass2 end 
from nv_codetbl_gdm_vechilecategory
where ct_vc_gdm=@gdm and ct_vc_categoryid=@O_categoryid and ct_vc_sdid=@sdid and ct_vc_type='O'

--select @O_carclass1=ct_vc_carclass1,@O_carclass2=ct_vc_carclass2 from nv_codetbl_gdm_vechilecategory
--where ct_vc_gdm=@gdm and ct_vc_categoryid=@O_categoryid and ct_vc_sdid=@sdid and ct_vc_type='O'

if(len(@S_CarClassAll)>0 and len(@O_CarClassAll)>0 )
begin
select @S_CarClassAll+'。 </br>'+@O_CarClassAll+'。' as CarClassAll
end
else if(len(@S_CarClassAll)>0)
begin
select @S_CarClassAll+'。' as CarClassAll
end
else 
begin
select @O_CarClassAll+'。' as CarClassAll
end


END
GO
PRINT N'正在建立 [dbo].[pr_get_LastCertdocID]...';


GO

-- =============================================
-- Author:		<Olvia>
-- Create date: <20180223>
-- Description:	其他檔案上傳-取得上年度合格證明影本
-- =============================================
--exec dbo.pr_get_LastCertdocID '104120070'
--exec dbo.pr_get_LastCertdocID '107020001'
CREATE procedure [dbo].[pr_get_LastCertdocID] @applyno varchar(9)
AS

BEGIN

declare @vechiletype char(1)	
declare @IsExist varchar(2)	
declare @compidno nvarchar(100)	
declare @enginefamily varchar(30)

select @enginefamily=abd_enginefamily,@compidno=abd_databelongcompidno,@vechiletype=abd_vechiletype from nv_applybasedata where abd_applyno=@applyno
set	@IsExist='0'

if (@vechiletype ='D') --柴油車(比對-前字串)
begin                                       
	set	@enginefamily= left(@enginefamily,(len(@enginefamily)-ISNULL(NULLIF(CHARINDEX('-',REVERSE(@enginefamily)),0),0))) + '%'                           		     			

	if exists(select * from nv_applybasedata 
				join nv_attachfileinfo on abd_certdocfileid=afi_fileid and afi_filetype='25'
				where abd_enginefamily like @enginefamily  
				and abd_carstyleyear>=(Year(GETDATE())-1) and abd_carstyleyear<=(Year(GETDATE())+ 1) 
				and abd_applystatus in ('50','55') 
				and abd_databelongcompidno=@compidno)
	begin                                       
		set	@IsExist='1'
		select abd_applyno,abd_certdocno,abd_certdocfileid,abd_enginefamily,afi_filepath,afi_sysfilename from nv_applybasedata 
				join nv_attachfileinfo on abd_certdocfileid=afi_fileid and afi_filetype='25'
				where abd_enginefamily like @enginefamily  
				and abd_carstyleyear>=(Year(GETDATE())-1) and abd_carstyleyear<=(Year(GETDATE())+ 1) 
				and abd_applystatus in ('50','55') 
				and abd_databelongcompidno=@compidno
	end
end
else
begin                                       
	set @enginefamily = @enginefamily
	if exists(select * from nv_applybasedata 
				join nv_attachfileinfo on abd_certdocfileid=afi_fileid and afi_filetype='25'
				where abd_enginefamily = @enginefamily 
				and abd_carstyleyear>=(Year(GETDATE())-1) and abd_carstyleyear<=(Year(GETDATE())+ 1) 
				and abd_applystatus in ('50','55') 
				and abd_databelongcompidno=@compidno)
	begin                                       
		set	@IsExist='1'
		
		select abd_applyno,abd_certdocno,abd_certdocfileid,abd_enginefamily,afi_filepath,afi_sysfilename from nv_applybasedata 
				join nv_attachfileinfo on abd_certdocfileid=afi_fileid and afi_filetype='25'
				where abd_enginefamily = @enginefamily 
				and abd_carstyleyear>=(Year(GETDATE())-1) and abd_carstyleyear<=(Year(GETDATE())+ 1) 
				and abd_applystatus in ('50','55') 
				and abd_databelongcompidno=@compidno
	end
	
	
end

	if (@IsExist='0')
	begin

	select '' as abd_applyno,'' as abd_certdocno,'' as abd_certdocfileid,'' as abd_enginefamily ,''as afi_filepath,''as afi_sysfilename

	end

END
GO
PRINT N'正在建立 [dbo].[nv_codetbl_gdm_vechilecategory].[ct_vc_categoryid].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'車種分類ID', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'nv_codetbl_gdm_vechilecategory', @level2type = N'COLUMN', @level2name = N'ct_vc_categoryid';


GO
PRINT N'正在建立 [dbo].[nv_codetbl_gdm_vechilecategory].[ct_vc_sdid].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'期別', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'nv_codetbl_gdm_vechilecategory', @level2type = N'COLUMN', @level2name = N'ct_vc_sdid';


GO
PRINT N'正在建立 [dbo].[nv_codetbl_gdm_vechilecategory].[ct_vc_type].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'加速S/原地O', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'nv_codetbl_gdm_vechilecategory', @level2type = N'COLUMN', @level2name = N'ct_vc_type';


GO
PRINT N'正在建立 [dbo].[nv_codetbl_gdm_vechilecategory].[ct_vc_carclass1].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'車種分類1', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'nv_codetbl_gdm_vechilecategory', @level2type = N'COLUMN', @level2name = N'ct_vc_carclass1';


GO
PRINT N'正在建立 [dbo].[nv_codetbl_gdm_vechilecategory].[ct_vc_carclass2].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'車種分類2', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'nv_codetbl_gdm_vechilecategory', @level2type = N'COLUMN', @level2name = N'ct_vc_carclass2';


GO
PRINT N'正在建立 [dbo].[nv_codetbl_statisticalcode].[ct_sc_cartype].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'對應車型組代碼第八碼(電動車第九碼)取得車輛種類', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'nv_codetbl_statisticalcode', @level2type = N'COLUMN', @level2name = N'ct_sc_cartype';


GO
PRINT N'正在建立 [dbo].[nv_noisestandarddata].[nsd_speedupstandardvalue].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'加速標準值', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'nv_noisestandarddata', @level2type = N'COLUMN', @level2name = N'nsd_speedupstandardvalue';


GO
PRINT N'正在建立 [dbo].[nv_noisestandarddata].[nsd_standarddate].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'期別', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'nv_noisestandarddata', @level2type = N'COLUMN', @level2name = N'nsd_standarddate';


GO
PRINT N'正在建立 [dbo].[nv_noisestandarddata].[nsd_stationarystandardvalue].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'原地標準值', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'nv_noisestandarddata', @level2type = N'COLUMN', @level2name = N'nsd_stationarystandardvalue';


GO
PRINT N'正在建立 [dbo].[nv_noisestandarddata].[nsd_transporttype].[MS_Description]...';


GO
EXECUTE sp_addextendedproperty @name = N'MS_Description', @value = N'車種分類', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'nv_noisestandarddata', @level2type = N'COLUMN', @level2name = N'nsd_transporttype';


GO
PRINT N'正在重新整理 [dbo].[pr_self_data_for_population_D]...';


GO
EXECUTE sp_refreshsqlmodule N'[dbo].[pr_self_data_for_population_D]';


GO
PRINT N'更新完成。';


GO
