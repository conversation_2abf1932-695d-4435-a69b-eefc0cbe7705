﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data;
using System.Data.SqlClient;

/// <summary>
/// Summary description for MailParaControl
/// </summary>
public class MailParaControl
{
	public MailParaControl()
	{
	}

	public DataView GetApplyBaseData(string ApplyNo)
	{
		SqlCommand sqlCmd = new SqlCommand(@"
select 
abd_applyno ApplyNo
,abd_databelongcompidno CompNo
,abd_databelongcompname CompName
,abd_enginefamily EngineFamily
,dbo.fn_CastAccountCn(abd_validatechargeaccount) AccountName
,dbo.fn_GetAccountMail(abd_validatechargeaccount) AccontMail
,abd_vechiletype CarType
,abd_contactname ContactName
,abd_contactemail ContactMail
,abd_carstyleyear CarstyleYear
,abd_isimport IsImport
,abd_certdocno CertdocNo
,abd_receiptno ReceiptNo
,abd_certdocissuedate CertdocDate
from dbo.nv_applybasedata
where abd_applyno=@ApplyNo
");
		sqlCmd.Parameters.AddWithValue("@ApplyNo", ApplyNo);
		DataView dv = Common.Data_noisevalidation.runParaCmd(sqlCmd);
		return dv;
	}

	/// <summary>
	/// 取該群組所有帳號
	/// </summary>
	/// <param name="GroupId">
	/// 00	系統管理人
	/// 01	收件人員
	/// 02	初審人員
	/// 03	複審人員
	/// 04	二級主管
	/// 05	一級主管
	/// 50	公司帳號管理者
	/// 51	公司一般帳號
	/// 90	EPA主管
	/// 99	程式設計 
	/// </param>
	/// <returns>DataView</returns>
	public DataView GetGroupAccount(string GroupId)
	{
		SqlCommand sqlCmd = new SqlCommand(@"
select AccountName,AccountMail from dbo.tb_GetGroupAccount(@GroupId)");
		sqlCmd.Parameters.AddWithValue("@GroupId",GroupId);
		DataView dv = Common.Data_noisevalidation.runParaCmd(sqlCmd);
		return dv;
	}
}