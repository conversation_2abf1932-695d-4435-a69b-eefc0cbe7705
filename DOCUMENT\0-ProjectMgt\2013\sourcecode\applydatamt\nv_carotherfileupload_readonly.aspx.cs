﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.IO;
using System.Text;
using System.Drawing;

using System.Xml;
using Common;

public partial class applydatamt_nv_carotherfileupload_readonly : BaseAdminPage
{
    public DataTable dtList
    {
        get { return (DataTable)ViewState["dtList"]; }
        set { ViewState["dtList"] = value; }
    }

    protected void Page_Load(object sender, EventArgs e)
    {

        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = '../accountmt/index.aspx';</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" &&
                (
                (
                GetAgentAccountGrpList().Contains("epa_adm") || GetAgentAccountGrpList().Contains("ac_adm") || GetAgentAccountGrpList().Contains("tc_adm") || GetAgentAccountGrpList().Contains("rv_adm") || GetAgentAccountGrpList().Contains("rs_adm") || GetAgentAccountGrpList().Contains("fis_adm") || GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("pg_adm")
                )
                ||
                (
                  (GetAgentAccountGrpList().Contains("cga_adm") || GetAgentAccountGrpList().Contains("cam_adm")) && CheckSingleApplyDataAccessRight(DeCode(Request["applyno"].ToString()))
                )
                )
                )
            {
                if (!IsPostBack)
                {
                    if (Request["applyno"].ToString() != "")
                    {

                        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
                        Cryptography crypy = new Cryptography();
                        int keynum = crypy.getRadNum();
                        string[] arr = crypy.wsApplybasedataDetailValid(keynum);

                        DataTable dt_applydatadetail = wsApplyData.get_applybasedate_detail(crypy.DeCode(Request["applyno"].ToString()), arr[0].ToString(), arr[1].ToString());
                        if (dt_applydatadetail.Rows.Count > 0)
                        {
                            //if (dt_applydatadetail.Rows[0]["abd_databelongcompidno"].ToString() == Session["compidno"].ToString())
                            //{
                                BindData();
                                dt_applydatadetail.Dispose();
                            //}
                            //else
                            //{
                            //    dt_applydatadetail.Dispose();
                            //    Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                            //    Response.End();
                            //}
                        }
                        else
                        {
                            dt_applydatadetail.Dispose();
                            Response.Write("<script languge='javascript'>alert('發生取不到申請編號錯誤，系統將導回首頁！！');window.location.href='../index.aspx'</script>");
                            Response.End();
                        }
                    }
                    else
                    {
                        Response.Write("<script languge='javascript'>alert('發生取不到申請編號錯誤，系統將導回首頁！！');window.location.href='../index.aspx'</script>");
                        Response.End();
                    }
                }
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }
    }

    public void BindData()
    {
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        Cryptography crypy = new Cryptography();
        wsOtherFileupload oOFD = new wsOtherFileupload();
        int keynum = crypy.getRadNum();
        string[] arr = crypy.wsOtherFileuploadValid(keynum);
        string[] Warr = crypy.wsApplybasedataDetailValid(keynum);
        string[] radom_C = wsCompanyValidate(getRadNum());
        string SelectedTextList = string.Empty;
        string applyno = DeCode(Request["applyno"].ToString());
        string carcompomodelno = DeCode(Request["carcompomodelno"].ToString());
        string filetype = DeCode(Request["filetype"].ToString());

        lab_applyno.Text = applyno;
        lab_carcompomodelno.Text = carcompomodelno;

        dtList = oOFD.get_uploadfiled_list(applyno, carcompomodelno, filetype, arr[0].ToString(), arr[1].ToString());
        gv_data.DataSource = dtList;
        gv_data.DataBind();
        //繳費資料        
        string[] arr_apd = crypy.wsApplyBaseDataValidate(getRadNum());
        DataTable dt_paydata = new DataTable();
        wsApplyBaseDataInfo wsApplyBaseData = new wsApplyBaseDataInfo();
        dt_paydata = wsApplyBaseData.getApplyBasePaydata(applyno, arr_apd[0].ToString(), arr_apd[1].ToString());
        gv_paydata.DataSource = dt_paydata;
        gv_paydata.DataBind();
    }

    protected void gv_data_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {         
            string select = DataBinder.Eval(e.Row.DataItem, "afi_fileid").ToString();          
            LinkButton lbnt_userfilename = (LinkButton)e.Row.FindControl("lbnt_userfilename");
            string userfilename = DataBinder.Eval(e.Row.DataItem, "afi_userfilename").ToString();
            lbnt_userfilename.Attributes["afi_fileid"] = DataBinder.Eval(e.Row.DataItem, "afi_fileid").ToString().Trim();
            lbnt_userfilename.Text = userfilename;
            lbnt_userfilename.Attributes.Add("onclick", "javascript:window.open('../FileDownloadSeal.aspx?File=" + Server.UrlEncode(EnCode(DataBinder.Eval(e.Row.DataItem, "afi_fileid").ToString().Trim())) + "','_blank');");
        }
    }

    protected void gv_data_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        gv_data.PageIndex = e.NewPageIndex;
        BindData();

    }
    protected void gv_paydata_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {                       
            LinkButton lbnt_userfilename = (LinkButton)e.Row.FindControl("lbnt_userfilename");
            string userfilename = DataBinder.Eval(e.Row.DataItem, "afi_userfilename").ToString();
            lbnt_userfilename.Attributes["afi_fileid"] = DataBinder.Eval(e.Row.DataItem, "afi_fileid").ToString().Trim();
            lbnt_userfilename.Text = userfilename;
            lbnt_userfilename.Attributes.Add("onclick", "javascript:window.open('../FileDownloadSeal.aspx?File=" + Server.UrlEncode(EnCode(DataBinder.Eval(e.Row.DataItem, "afi_fileid").ToString().Trim())) + "','_blank');");

        }
    }

    protected void gv_paydata_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        gv_paydata.PageIndex = e.NewPageIndex;
        BindData();

    }
}