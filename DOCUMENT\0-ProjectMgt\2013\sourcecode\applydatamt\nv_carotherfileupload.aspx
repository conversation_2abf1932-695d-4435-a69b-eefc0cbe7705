﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="nv_carotherfileupload.aspx.cs"
    Inherits="applydatamt_nv_carotherfileupload" MaintainScrollPositionOnPostback='true' %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head id="Head1" runat="server">
    <link href="../css/style.css" rel="stylesheet" type="text/css" />
    <base target="_self">
    </base>
    <title>檔案上傳</title>
</head>
<body>
    <form id="form1" runat="server">
    <div class="fullheader">
        <table cellpadding="0" cellspacing="0" class="fullheaderinfo" width="100%">
            <tr>
                <td valign="top">
                    <b>檔案上傳</b>
                </td>
                <td align="right" class="font_loginInfo" valign="top">
                </td>
            </tr>
        </table>
    </div>
    <div class="fullcontent">
        <div id="div_maintain" runat="server" class="formstyle">
            <table border="0" cellspacing="0" cellpadding="0" class="font_fullcontent">
                <tr>
                    <td align="right">
                        上傳文件：
                    </td>
                    <td>
                        <asp:FileUpload ID="fupload_companfile" runat="server" />
                    </td>
                </tr>
                <tr>
                    <td colspan="2" align="right">                      
                       
                        <asp:Button ID="btn_Add" runat="server" Text="上傳" OnClick="btn_Add_Click" CssClass="btn_mouseout"
                            onmouseover="this.className='btn_mouseover'" onmouseout="this.className='btn_mouseout'"
                            CausesValidation="False" />&nbsp;&nbsp;
                        <asp:Button ID="btn_Delet" runat="server" Text="刪除" OnClick="btn_Delet_Click" CssClass="btn_mouseout"
                            onmouseover="this.className='btn_mouseover'" onmouseout="this.className='btn_mouseout'"
                            CausesValidation="False" />&nbsp;&nbsp;
                        <asp:Button ID="btn_Close" runat="server" Text="離開"
                            CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'" onmouseout="this.className='btn_mouseout'"
                            CausesValidation="False" onclick="btn_Close_Click"/>
                    </td>
                </tr>
            </table>
        </div>
        <div class="tableoutcome">
            <asp:GridView ID="gv_data" runat="server" AutoGenerateColumns="False" 
                CellSpacing="0" CellPadding="0" BorderWidth="0" GridLines="None"
                Width="100%" OnRowDataBound="gv_data_RowDataBound" 
                CssClass="font_fullcontent" AllowPaging="True" 
                OnPageIndexChanging="gv_data_PageIndexChanging">
                <Columns>
                    <asp:TemplateField HeaderText="選擇" HeaderStyle-Width="40px" ItemStyle-CssClass="lineleft" HeaderStyle-CssClass="header" ItemStyle-HorizontalAlign="Center" HeaderStyle-HorizontalAlign="Center">
                        <ItemTemplate>
                            <asp:CheckBox ID="cbl_select" runat="server"></asp:CheckBox>
                        </ItemTemplate>
                        <HeaderStyle CssClass="header"></HeaderStyle>
                        <ItemStyle CssClass="lineleft"></ItemStyle>
                    </asp:TemplateField>
                    <asp:TemplateField HeaderText="文件名稱" ItemStyle-CssClass="lineleft" HeaderStyle-CssClass="header">
                        <ItemTemplate>
                            <asp:LinkButton ID="lbnt_userfilename" runat="server"></asp:LinkButton>
                        </ItemTemplate>
                        <HeaderStyle CssClass="header"></HeaderStyle>
                        <ItemStyle CssClass="lineleft"></ItemStyle>
                    </asp:TemplateField>
                    <asp:BoundField ItemStyle-CssClass="lineright" HeaderStyle-CssClass="lineright" DataField="afi_uploaddate"
                        HeaderText="上傳日期" DataFormatString="{0:d}" />
                    <asp:TemplateField Visible="false">
                        <ItemTemplate>
                            <asp:Label ID="lbl_afino" runat="server" Text='<%# Eval("afi_fileid").ToString() %>'></asp:Label></ItemTemplate>
                    </asp:TemplateField>
                </Columns>
                <EmptyDataRowStyle ForeColor="Red" />
                <EmptyDataTemplate>
                    尚未上傳任何檔案
                </EmptyDataTemplate>
                <PagerStyle CssClass="S_PageCss" HorizontalAlign="Center" />
            </asp:GridView>
        </div>
    </div>
    <!--{* fullcontent end *}-->
    <div class="fullfooter">
    </div>
    <!--{* content end
    *}-->
    </form>
</body>
</html>
