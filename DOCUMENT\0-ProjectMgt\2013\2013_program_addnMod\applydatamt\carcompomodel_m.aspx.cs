﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;

public partial class applydatamt_carcompomodel_m : BaseAdminPage
{
    Cryptography crypy = new Cryptography();
    wsApplyBaseDataInfo wsApplyData = new wsApplyBaseDataInfo();
    wsCarCompoModel wsCarCmpModel = new wsCarCompoModel();
    wsCodetbl wsCodeTbl = new wsCodetbl();
    private string strApplyno = "";
    string IsCopy = "";
    protected void Page_Load(object sender, EventArgs e)
    {
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = 'index.aspx';</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" && (GetAgentAccountGrpList().Contains("cam_adm") || GetAgentAccountGrpList().Contains("cga_adm") || GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("pg_adm")))
            {
                if (!IsPostBack)
                {
                    if (Request["applyno"] != null && Request["applyno"] != "")
                    {
                        //strApplyno = Request["applyno"].ToString();
                        strApplyno = crypy.DeCode(Request["applyno"].ToString());
                        int keynum = crypy.getRadNum();
                        string[] arr_apd = crypy.wsApplyBaseDataValidate(keynum);
                        DataSet ds_applybase = new DataSet();
                        ds_applybase = wsApplyData.getApplyBaseDataById(strApplyno, arr_apd[0].ToString(), arr_apd[1].ToString());
                        if (ds_applybase.Tables[0].Rows.Count > 0)
                        {
                            if (ds_applybase.Tables[0].Rows[0]["abd_applystatus"].ToString() == "10" || ds_applybase.Tables[0].Rows[0]["abd_applystatus"].ToString() == "40" || ds_applybase.Tables[0].Rows[0]["abd_applystatus"].ToString() == "90")
                            {//車型組編號 ->完成填單後 OR 已經有申請過的資料即不可修改
                                //if (wsApplyData.check_carcompomodelno_been_applied(crypy.DeCode(Request["carcompomodelno"].ToString()), GetAgentIDNo(), arr_apd[0].ToString(), arr_apd[1].ToString()) > 0)
                                //{
                                //    txt_carcompomodelno.ReadOnly = true;
                                //    btn_changecarcompomodelno.Visible = true;
                                //}

                                string[] arr_as = crypy.wsCodetblValidate(keynum);
                                lbl_applystatus.Text = wsCodeTbl.getApplyStatusById(ds_applybase.Tables[0].Rows[0]["abd_applystatus"].ToString(), arr_as[0].ToString(), arr_as[1].ToString());

                                if (Request["carcompomodelno"] != null && Request["carcompomodelno"] != "")
                                {
                                    hidden_flag.Value = "2"; //修改-會傳過來申請編號+車型組代碼
                                    txt_carcompomodelno.Text = crypy.DeCode(Request["carcompomodelno"].ToString());
                                }
                                else
                                    hidden_flag.Value = "1"; //新增-只會傳過來申請編號                    

                                BindBaseData();

                                if (hidden_flag.Value == "2")  //修改-所以要取出該筆資料並顯示在畫面
                                {
                                    //車型組編號存檔後即不可改
                                    txt_carcompomodelno.ReadOnly = true;
                                    btn_changecarcompomodelno.Visible = true;
                                    //複製車型組同樣只提供新增時使用
                                    ddl_carcompolist.Visible = false;
                                    btn_copycarcompomodel_data.Visible = false;
                                    lbl_copy_carcompomodel.Visible = true;
                                    string[] arr = crypy.wsCarCompoModelValidate(crypy.getRadNum());
                                    DataSet dsCarCmpModel = wsCarCmpModel.getDataByApplyNo_CarCompoModelNo_m(strApplyno, txt_carcompomodelno.Text, arr[0].ToString(), arr[1].ToString());
                                    if (dsCarCmpModel.Tables[0].Rows.Count > 0)
                                    {
                                        chkItem_All(dsCarCmpModel);
                                        if (dsCarCmpModel.Tables[0].Rows[0]["cpm_oriapplyno"].ToString() != "")
                                        {
                                            //新填寫，有可能是新的OR 舊資料不再DB，不控制
                                            //COPY來的資料，不能改為新車型-->新車型選項disabled
                                            rdBtnLst_applytype.Items[0].Enabled = false;
                                            IsCopy = "Y";
                                        }
                                        dsCarCmpModel.Dispose();
                                        #region　資料是否可編輯：rdBtnLst_applytype
                                        //畫面上資料是否可編輯：如果申請型式不為新車型(1)/修改(4) & 新系統無原合格證的資料,所以沿用(2)等申請型式須開放讓廠商填寫 & 原申請編號不為空白時-->資料從系統而來，須鎖住                                    
                                        wsApplybasedataDetail wsApplyBaseData = new wsApplybasedataDetail();
                                        string[] arr_abd = crypy.wsApplybasedataDetailValid(keynum);
                                        //如果有舊資料 沿用(2)及延伸(3) 不能修改
                                        DataTable dt_unNewApplyData = wsApplyBaseData.get_unNewApplyData(crypy.DeCode(Request["applyno"].ToString()), txt_carcompomodelno.Text.Trim(), arr_abd[0].ToString(), arr_abd[1].ToString());
                                        if (dt_unNewApplyData.Rows.Count > 0)
                                        {
                                            if (rdBtnLst_applytype.SelectedValue == "2" || rdBtnLst_applytype.SelectedValue == "3")
                                                set_input_be_edit(false);
                                            else
                                                set_input_be_edit(true);
                                        }
                                        else
                                        {
                                            set_input_be_edit(true);
                                        }
                                        if (dsCarCmpModel.Tables[0].Rows[0]["cpm_oriapplyno"].ToString() != "")
                                        {
                                            set_input_be_edit(false);
                                        }
                                        #endregion
                                    }
                                    else
                                    {
                                        //取不到車型組資料畫面轉走
                                        /*MessageBox.Show("無法取得車型組資料，畫面將轉回首頁！");
                                        Response.Redirect("../index.aspx");   此寫法無法轉網址*/
                                        Response.Write(string.Format("<script languge='javascript'>alert('無此車型組資料！');window.location.href='nv_applylistqry.aspx'</script>", false));
                                        Response.End();
                                    }
                                }
                            }
                            else
                            {
                                Response.Redirect("carcompomodel_readnoly.aspx?applyno=" + Server.UrlEncode(Request["applyno"].ToString()) + "&carcompomodelno=" + Server.UrlEncode(Request["carcompomodelno"].ToString()));
                            }


                        }
                        else
                        {
                            //取不到申請編號畫面轉走
                            Response.Write(string.Format("<script languge='javascript'>alert('申請編號不存在！');window.location.href='nv_applylistqry.aspx'</script>", false));
                            Response.End();
                            //MessageBox.Show("無法取得申請編號資料，畫面將轉回首頁！");
                            //Response.Redirect("../index.aspx");
                        }
                    }
                    else
                    {
                        //取不到申請編號畫面轉走
                        Response.Write(string.Format("<script languge='javascript'>alert('無法取得申請編號！');window.location.href='nv_applylistqry.aspx'</script>", false));
                        Response.End();
                        //MessageBox.Show("無法取得申請編號，畫面將轉回首頁！");
                        //Response.Redirect("../index.aspx");
                    }
                    applybase.NavigateUrl = "applybasedata.aspx?applyno=" + Server.UrlEncode(Request["applyno"]);
                    carcompomodel.NavigateUrl = "carcompomodelList.aspx?applyno=" + Server.UrlEncode(Request["applyno"]);
                    docreview.NavigateUrl = "";
                }
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }
    }

    #region 更改的麵包屑傳入的參數
    protected void Page_LoadComplete(object sender, EventArgs e)
    {
        ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text = ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text.Replace("applydatamt/carcompomodelList.aspx", "applydatamt/carcompomodelList.aspx?applyno=" + Server.UrlEncode(Request["applyno"].ToString()));
    }
    #endregion

    //取出該筆資料並顯示在畫面
    private void chkItem_All(DataSet dsCarCmpModel)
    {
        //取得RadioButtonList資料
        getrdBtnLstData(dsCarCmpModel, rdBtnLst_applytype, "cpm_applytype"); //申請型式
        getrdBtnLstData(dsCarCmpModel, rdBtnLst_useeurocert, "cpm_useeurocert"); //以歐盟合格證申請
        getrdBtnLstData(dsCarCmpModel, rdBtnLst_transmissiontypetype, "cpm_transmissiontypetype"); //排檔型式
        getrdBtnLstData(dsCarCmpModel, rdBtnLs_GasolineType, "cpm_gasolinetype"); //使用燃料
        getrdBtnLstData(dsCarCmpModel, rdBtnLst_powerexportway, "cpm_powerexportway"); //燃燒循環        
        getrdBtnLstData(dsCarCmpModel, rdBtnLst_provideoilmethod, "cpm_provideoilmethod"); //供油方式               
        getrdBtnLstData(dsCarCmpModel, rdBtnLst_coolsystem, "cpm_coolsystem"); //冷卻系統型式
        getrdBtnLstData(dsCarCmpModel, rdBtnLst_testfactory, "cpm_testfactory"); //檢測廠
        txt_exhuast.Text = dsCarCmpModel.Tables[0].Rows[0]["cpm_exhuast"].ToString().Replace(".00", "");  //總排氣量
        txt_cylindernums.Text = dsCarCmpModel.Tables[0].Rows[0]["cpm_cylindernums"].ToString();  //缸數
        getrdBtnLstData(dsCarCmpModel, DrpDnList_cylindertype, "cpm_cylindertype");//汽缸排列       
        txt_enginesetupposnmethod.Text = dsCarCmpModel.Tables[0].Rows[0]["cpm_enginesetupposnmethod"].ToString(); //引擎安裝位置及方式        
        DrpDnList_importarea.SelectedValue = dsCarCmpModel.Tables[0].Rows[0]["cpm_importarea"].ToString();  //進口國
        //判斷歐盟合格證換證的僅能申請”新車型”&”沿用”
        set_ApplytypeAbled(rdBtnLst_useeurocert.SelectedValue);
        btn_changecarcompomodelno.Attributes.Add("onClick", string.Format(@"javascript:var Mleft = (screen.width - 550) / 2;var Mtop = (screen.height - 450) / 2;window.open('AlterCarcompomodelno.aspx?applyno={0}&carcompomodelno={1}',10,'height=200,width=450,top='+Mtop+',left='+Mleft+'');", Server.UrlEncode(Request["applyno"].ToString()), Server.UrlEncode(EnCode(txt_carcompomodelno.Text.Trim()))));
    }

    private void BindBaseData()
    {
        int keynum = crypy.getRadNum();
        lbl_applyno.Text = strApplyno;
        lbl_modifyuser.Text = GetAgentAccount();
        lbl_modifydate.Text = DateTime.Now.ToString("yyyy/MM/dd");

        #region 車型組清單
        string[] arr_carcompomodel = crypy.wsCarCompoModelValidate(keynum);
        ddl_carcompolist.DataSource = wsCarCmpModel.get_carcompomodelno_byapplyno(crypy.DeCode(Request["applyno"].ToString()), arr_carcompomodel[0].ToString(), arr_carcompomodel[1].ToString());
        ddl_carcompolist.DataTextField = "cpm_carcompomodelno";
        ddl_carcompolist.DataValueField = "cpm_carcompomodelno";
        ddl_carcompolist.DataBind();
        #endregion

        #region 申請型式
        string[] arr_at = crypy.wsCodetblValidate(keynum);
        DataSet dsApplyType = wsCodeTbl.getCarCompoModelApplyType(arr_at[0].ToString(), arr_at[1].ToString());

        rdBtnLst_applytype.DataSource = dsApplyType;
        rdBtnLst_applytype.DataTextField = "ct_at_desc";
        rdBtnLst_applytype.DataValueField = "ct_at_id";
        rdBtnLst_applytype.DataBind();
        #endregion

        #region 使用燃料
        string[] arr_flt = crypy.wsCodetblValidate(keynum);
        DataSet dsFuelType = wsCodeTbl.getFuelType(arr_flt[0].ToString(), arr_flt[1].ToString());

        rdBtnLs_GasolineType.DataSource = dsFuelType;
        rdBtnLs_GasolineType.DataTextField = "ct_ft_desc";
        rdBtnLs_GasolineType.DataValueField = "ct_ft_id";
        rdBtnLs_GasolineType.DataBind();
        #endregion

        #region 供油方式
        string[] arr_prvdoil = crypy.wsCodetblValidate(keynum);
        DataSet dsProvideOilMethod = wsCodeTbl.getProvideOilMethod(arr_prvdoil[0].ToString(), arr_prvdoil[1].ToString());

        rdBtnLst_provideoilmethod.DataSource = dsProvideOilMethod;
        rdBtnLst_provideoilmethod.DataTextField = "ct_le_desc";
        rdBtnLst_provideoilmethod.DataValueField = "ct_le_id";
        rdBtnLst_provideoilmethod.DataBind();
        #endregion

        #region 汽缸排列
        string[] arr_cp = crypy.wsCodetblValidate(keynum);
        DataSet dsCylinderPos_M = wsCodeTbl.getCylinderPos_M(arr_cp[0].ToString(), arr_cp[1].ToString());

        DrpDnList_cylindertype.DataSource = dsCylinderPos_M;
        DrpDnList_cylindertype.DataTextField = "ct_cp_desc_m";
        DrpDnList_cylindertype.DataValueField = "ct_cp_id_m";
        DrpDnList_cylindertype.DataBind();
        #endregion

        #region 冷卻系統型式
        string[] arr_cs = crypy.wsCodetblValidate(keynum);
        DataSet dsCoolSystem = wsCodeTbl.getCoolSystem(arr_cs[0].ToString(), arr_cs[1].ToString());

        rdBtnLst_coolsystem.DataSource = dsCoolSystem;
        rdBtnLst_coolsystem.DataTextField = "ct_cs_desc";
        rdBtnLst_coolsystem.DataValueField = "ct_cs_id";
        rdBtnLst_coolsystem.DataBind();
        #endregion

        #region 以歐盟合格證申請
        string[] arr_eu = crypy.wsCodetblValidate(keynum);
        DataSet dsUseEurocert = wsCodeTbl.getUseEurocert(arr_eu[0].ToString(), arr_eu[1].ToString());

        rdBtnLst_useeurocert.DataSource = dsUseEurocert;
        rdBtnLst_useeurocert.DataTextField = "ct_eu_desc";
        rdBtnLst_useeurocert.DataValueField = "ct_eu_id";
        rdBtnLst_useeurocert.DataBind();
        #endregion

        #region 排檔型式
        string[] arr_gm = crypy.wsCodetblValidate(keynum);
        DataSet dsGearMethod = wsCodeTbl.getGearMethod(arr_gm[0].ToString(), arr_gm[1].ToString());

        rdBtnLst_transmissiontypetype.DataSource = dsGearMethod;
        rdBtnLst_transmissiontypetype.DataTextField = "ct_gm_desc";
        rdBtnLst_transmissiontypetype.DataValueField = "ct_gm_id";
        rdBtnLst_transmissiontypetype.DataBind();
        #endregion

        #region 燃燒循環
        string[] arr_ebm = crypy.wsCodetblValidate(keynum);
        DataSet dsEngineBurnMethod = wsCodeTbl.getEngineBurnMethod(arr_ebm[0].ToString(), arr_ebm[1].ToString());

        rdBtnLst_powerexportway.DataSource = dsEngineBurnMethod;
        rdBtnLst_powerexportway.DataTextField = "ct_ebm_desc";
        rdBtnLst_powerexportway.DataValueField = "ct_ebm_id";
        rdBtnLst_powerexportway.DataBind();
        #endregion

        #region 檢測廠
        string[] arr_tf = crypy.wsCodetblValidate(keynum);
        DataSet dsTestFactory = wsCodeTbl.getTestFactory(arr_tf[0].ToString(), arr_tf[1].ToString());

        rdBtnLst_testfactory.DataSource = dsTestFactory;
        rdBtnLst_testfactory.DataTextField = "ct_tf_desc";
        rdBtnLst_testfactory.DataValueField = "ct_tf_id";
        rdBtnLst_testfactory.DataBind();
        #endregion

        #region 進口國
        string[] arr_cy = crypy.wsCodetblValidate(keynum);
        DataSet dsCountry = wsCodeTbl.getCountry(arr_cy[0].ToString(), arr_cy[1].ToString());

        DrpDnList_importarea.DataSource = dsCountry;
        DrpDnList_importarea.DataTextField = "ct_cy_desc";
        DrpDnList_importarea.DataValueField = "ct_cy_id";
        DrpDnList_importarea.DataBind();
        #endregion

        //

    }

    protected void btn_apply_Click(object sender, EventArgs e)
    {

        #region 特殊字元判斷-沒有MasterPage
        foreach (Control ctl in Page.Form.Controls)
        {
            Control content = (Control)ctl;
            foreach (Control c in content.Controls)
            {
                if (c is TextBox)
                {
                    TextBox objTextBox = default(TextBox);
                    objTextBox = (TextBox)c;
                    if ((tbWord(objTextBox.Text)))
                    {
                        MessageBox.Show(objTextBox.Text + ":含有系統不允許的特殊字元");
                        return;
                    }
                }
            }
        }
        #endregion
        if (hidden_flag.Value == "1") // 1新增-只會傳過來申請編號;2修改-會傳過來申請編號+車型組代碼
            insCarCompoModel("1"); //0 為車型變更前的存檔;1一般修改程序
        else
            updCarCompoModel("1");

        //Response.Write("<script languge='javascript'>alert('" + reMsg + "');window.location.href='carcompomodelList.aspx?applyno=" + Server.UrlEncode(Request["applyno"].ToString()) + "'</script>");
        //Response.End();      


    }

    #region 檢查車型組代碼
    //檢查車型組代碼
    private Boolean chkCarCompoModelNo(string strApplyNo, string strCarCompoModelNo)
    {
        Boolean blnRtn = true;
        /* 目前只檢查有無重複
        string strChk_point1 = "";
        string strChk_point2 = "";
        string strChk_point3 = "";
        string strCompIdNo = Session["compidno"].ToString();
        int keynum = crypy.getRadNum();
        string[] arr_apd = crypy.wsApplyBaseDataValidate(keynum);
        DataSet ds_apd = wsApplyData.getApplyBaseDataById(strApplyno, arr_apd[0].ToString(), arr_apd[1].ToString());
        
        strChk_point1 = ds_apd.Tables[0].Rows[0]["abd_carstyleyear"].ToString().Substring(0,2);  //車型年前2碼

        if (ds_apd.Tables[0].Rows[0]["abd_powerfrom"].ToString()=="3")
            strChk_point2 = "E"+strCompIdNo.Substring(0, 4);  //"E"+廠商編號4碼 (電動車)
        else
            strChk_point2 = strCompIdNo.Substring(0,4);  //廠商編號4碼

        strChk_point3 = ds_apd.Tables[0].Rows[0]["abd_vechiletype"].ToString(); //車型種類
        */

        int keynum = crypy.getRadNum();
        string[] arr = crypy.wsCarCompoModelValidate(keynum);
        int cnt = wsCarCmpModel.getCountByCarCompoModelNo(strCarCompoModelNo, arr[0].ToString(), arr[1].ToString());

        if (hidden_flag.Value == "1")  //新增
        {
            if (cnt > 0)
                blnRtn = false; //表示該車型組代碼已存在,不能使用
            else
                blnRtn = true; //表示該車型組代碼不存在,能使用
        }
        else if (hidden_flag.Value == "2")  //更新
        {
            if (cnt == 0)  //表示該車型組代碼不存在,能使用
                blnRtn = true;
            else if (cnt == 1)  //表示該車型組代碼存在,要再檢查是否為該申請編號所有
            {
                int cnt_1 = wsCarCmpModel.getCountByApplyNo_CarCompoModelNo(strApplyNo, strCarCompoModelNo, arr[0].ToString(), arr[1].ToString());
                if (cnt_1 == 1)  //表示該車型組是該申請編號所有
                    blnRtn = true;
                else
                    blnRtn = false; //表示該車型組是別的申請編號所有,所以不能當作新車型的車型組代碼
            }
        }

        return blnRtn;
    }
    #endregion


    #region 新增資料
    //新增資料(nv_carcompomodel)
    private void insCarCompoModel(string Ctype) //Ctype:0為車型變更前的存檔,不跳出"新增車型組資料完成"文字;1一般修改程序
    {
        int keynum = crypy.getRadNum();
        string[] arr = crypy.wsCarCompoModelValidate(keynum);
        string[] arrData = new string[19];
        arrData[0] = lbl_applyno.Text;  //申請編號
        if (txt_carcompomodelno.Text.Trim() != "")  //車型組編號  
            arrData[1] = txt_carcompomodelno.Text;
        else
        {
            MessageBox.Show("車型組編號不可為空值！");
            return;
        }

        if (rdBtnLst_applytype.SelectedValue.ToString().Trim() != "")  //申請方式
            arrData[2] = rdBtnLst_applytype.SelectedValue.ToString().Trim();

        if (rdBtnLst_useeurocert.SelectedValue.ToString().Trim() != "")  //以歐盟合格證申請
            arrData[3] = rdBtnLst_useeurocert.SelectedValue.ToString().Trim();

        if (rdBtnLst_transmissiontypetype.SelectedValue.ToString().Trim() != "")  //排檔型式
            arrData[4] = rdBtnLst_transmissiontypetype.SelectedValue.ToString().Trim();

        if (rdBtnLs_GasolineType.SelectedValue.ToString().Trim() != "")  //使用燃料
            arrData[5] = rdBtnLs_GasolineType.SelectedValue.ToString().Trim();

        if (rdBtnLst_powerexportway.SelectedValue.ToString().Trim() != "")  //燃燒循環
            arrData[6] = rdBtnLst_powerexportway.SelectedValue.ToString().Trim();

        if (rdBtnLst_provideoilmethod.SelectedValue.ToString().Trim() != "")  //供油方式
            arrData[7] = rdBtnLst_provideoilmethod.SelectedValue.ToString().Trim();

        if (txt_exhuast.Text.Trim() != "")  //總排氣量
            arrData[8] = txt_exhuast.Text;

        if (txt_cylindernums.Text.Trim() != "")  //汽缸數
            arrData[9] = txt_cylindernums.Text;

        if (DrpDnList_cylindertype.SelectedValue.ToString().Trim() != "")  //汽缸排列
            arrData[10] = DrpDnList_cylindertype.SelectedValue.ToString().Trim();

        if (txt_enginesetupposnmethod.Text.Trim() != "")  //引擎安裝位置及方式
            arrData[11] = txt_enginesetupposnmethod.Text.Trim();

        if (rdBtnLst_coolsystem.SelectedValue.ToString().Trim() != "")
            arrData[12] = rdBtnLst_coolsystem.SelectedValue.ToString().Trim();  //冷卻系統型式

        if (rdBtnLst_testfactory.SelectedValue.ToString().Trim() != "")  //檢測廠
            arrData[13] = rdBtnLst_testfactory.SelectedValue.ToString().Trim();

        if (DrpDnList_importarea.SelectedValue.ToString().Trim() != "")  //進口國
            arrData[14] = DrpDnList_importarea.SelectedValue.ToString().Trim();

        arrData[15] = lbl_modifyuser.Text;
        arrData[16] = lbl_modifyuser.Text;
        arrData[17] = lbl_modifydate.Text;
        arrData[18] = lbl_modifydate.Text;

        //if (arrData[2] == "1")  //當申請方式為新車型,則要檢查車型組代碼
        //{
        if (Ctype.Equals("1")) //Ctype:0為車型變更前的存檔;1一般修改程序
        {
            if (chkCarCompoModelNo(arrData[0], arrData[1]))  //檢查該車型組代碼
            {
                wsCarCmpModel.InsertCarCompoModel_M(arrData, arr[0].ToString(), arr[1].ToString());
                //MessageBox.Show("新增車型組資料完成2");
                Response.Write(string.Format("<script languge='javascript'>alert('新增車型組資料完成！');window.location.href='carcompomodel_m.aspx?applyno={0}&carcompomodelno={1}'</script>", Server.UrlEncode(EnCode(arrData[0])), Server.UrlEncode(EnCode(arrData[1])), false));
            }
            else
            {
                MessageBox.Show("當申請方式為新車型時,車型組代碼不可重複!!");
            }
        }
        else
        {
            if (chkCarCompoModelNo(arrData[0], arrData[1]))  //檢查該車型組代碼
            {
                wsCarCmpModel.InsertCarCompoModel_M(arrData, arr[0].ToString(), arr[1].ToString());
            }
            else
            {
                MessageBox.Show("當申請方式為新車型時,車型組代碼不可重複!!");
            }
        }
        //}
        //else
        //{
        //    wsCarCmpModel.InsertCarCompoModel_M(arrData, arr[0].ToString(), arr[1].ToString());
        //    MessageBox.Show("新增車型組資料完成");
        //}
    }
    #endregion

    #region 更新資料
    //更新資料(nv_carcompomodel)
    private void updCarCompoModel(string Ctype) //Ctype:0為車型變更前的存檔,不跳出"新增車型組資料完成"文字;1一般修改程序
    {
        int keynum = crypy.getRadNum();
        string[] arr = crypy.wsCarCompoModelValidate(keynum);
        string[] arrData = new string[17];
        arrData[0] = lbl_applyno.Text;  //申請編號
        for (int i = 1; i <= 16; i++)
        {
            arrData[i] = "";
        }
        if (txt_carcompomodelno.Text.Trim() != "")  //車型組編號  
            arrData[1] = txt_carcompomodelno.Text;

        if (rdBtnLst_applytype.SelectedValue.ToString().Trim() != "")  //申請方式
            arrData[2] = rdBtnLst_applytype.SelectedValue.ToString().Trim();

        if (rdBtnLst_useeurocert.SelectedValue.ToString().Trim() != "")  //以歐盟合格證申請
            arrData[3] = rdBtnLst_useeurocert.SelectedValue.ToString().Trim();

        if (rdBtnLst_transmissiontypetype.SelectedValue.ToString().Trim() != "")  //排檔型式
            arrData[4] = rdBtnLst_transmissiontypetype.SelectedValue.ToString().Trim();

        if (rdBtnLs_GasolineType.SelectedValue.ToString().Trim() != "")  //使用燃料
            arrData[5] = rdBtnLs_GasolineType.SelectedValue.ToString().Trim();

        if (rdBtnLst_powerexportway.SelectedValue.ToString().Trim() != "")  //燃燒循環
            arrData[6] = rdBtnLst_powerexportway.SelectedValue.ToString().Trim();

        if (rdBtnLst_provideoilmethod.SelectedValue.ToString().Trim() != "")  //供油方式
            arrData[7] = rdBtnLst_provideoilmethod.SelectedValue.ToString().Trim();

        if (txt_exhuast.Text.Trim() != "")  //總排氣量
            arrData[8] = txt_exhuast.Text;

        if (txt_cylindernums.Text.Trim() != "")  //汽缸數
            arrData[9] = txt_cylindernums.Text;

        if (DrpDnList_cylindertype.SelectedValue.ToString().Trim() != "")  //汽缸排列
            arrData[10] = DrpDnList_cylindertype.SelectedValue.ToString().Trim();

        if (txt_enginesetupposnmethod.Text.Trim() != "")  //引擎安裝位置及方式
            arrData[11] = txt_enginesetupposnmethod.Text.Trim();

        if (rdBtnLst_coolsystem.SelectedValue.ToString().Trim() != "")
            arrData[12] = rdBtnLst_coolsystem.SelectedValue.ToString().Trim();  //冷卻系統型式

        if (rdBtnLst_testfactory.SelectedValue.ToString().Trim() != "")  //檢測廠
            arrData[13] = rdBtnLst_testfactory.SelectedValue.ToString().Trim();

        if (DrpDnList_importarea.SelectedValue.ToString().Trim() != "")  //進口國
            arrData[14] = DrpDnList_importarea.SelectedValue.ToString().Trim();

        arrData[15] = lbl_modifyuser.Text;
        arrData[16] = lbl_modifydate.Text;
        if (Ctype.Equals("1")) //Ctype:0為車型變更前的存檔,不跳出"新增車型組資料完成"文字;1一般修改程序
        {
            if (arrData[2] == "1")  //當申請方式為新車型,則要檢查車型組代碼
            {
                if (chkCarCompoModelNo(arrData[0], arrData[1]))  //檢查該車型組代碼
                {
                    wsCarCmpModel.UpdateCarCompoModel_M(arrData, arr[0].ToString(), arr[1].ToString());
                    Response.Write(string.Format("<script languge='javascript'>alert('更新車型組資料完成！');window.location.href='carcompomodel_m.aspx?applyno={0}&carcompomodelno={1}'</script>", Server.UrlEncode(EnCode(arrData[0])), Server.UrlEncode(EnCode(arrData[1])), false));
                    Response.End();
                }
                else
                    MessageBox.Show("當申請方式為新車型時,車型組代碼不可重複!!");
            }
            else
            {
                wsCarCmpModel.UpdateCarCompoModel_M(arrData, arr[0].ToString(), arr[1].ToString());
                Response.Write(string.Format("<script languge='javascript'>alert('更新車型組資料完成！');window.location.href='carcompomodel_m.aspx?applyno={0}&carcompomodelno={1}'</script>", Server.UrlEncode(EnCode(arrData[0])), Server.UrlEncode(EnCode(arrData[1])), false));
                Response.End();


            }
        }
        else
        {
            if (arrData[2] == "1")  //當申請方式為新車型,則要檢查車型組代碼
            {
                if (chkCarCompoModelNo(arrData[0], arrData[1]))  //檢查該車型組代碼
                {
                    wsCarCmpModel.UpdateCarCompoModel_M(arrData, arr[0].ToString(), arr[1].ToString());

                }
                else
                    MessageBox.Show("當申請方式為新車型時,車型組代碼不可重複!!");
            }
            else
            {
                wsCarCmpModel.UpdateCarCompoModel_M(arrData, arr[0].ToString(), arr[1].ToString());

            }
        }
    }
    #endregion

    #region 複製車型組
    protected void btn_copycarcompomodel_data_Click(object sender, EventArgs e)
    {
        string[] arr = crypy.wsCarCompoModelValidate(crypy.getRadNum());
        DataSet dsCarCmpModel = wsCarCmpModel.getDataByApplyNo_CarCompoModelNo_m(lbl_applyno.Text, ddl_carcompolist.SelectedValue, arr[0].ToString(), arr[1].ToString());
        if (dsCarCmpModel.Tables[0].Rows.Count > 0)
        {
            chkItem_All(dsCarCmpModel);
            dsCarCmpModel.Dispose();
        }
        lbl_modifyuser.Text = GetAgentAccount();
        lbl_modifydate.Text = DateTime.Now.ToString();
        //MessageBox.Show("a");
    }
    #endregion

    protected void btn_tmpsave_Click(object sender, EventArgs e)
    {
        if (txt_carcompomodelno.Text.Trim() == "")
        {
            MessageBox.Show("車型組編號不可空白");
            txt_carcompomodelno.Focus();
            return;
        }

        if (rdBtnLst_applytype.SelectedValue.ToString().Trim() == "")
        {
            MessageBox.Show("申請型式必須挑選");
            rdBtnLst_applytype.Focus();
            return;
        }

        if (rdBtnLst_useeurocert.SelectedValue.ToString().Trim() == "")
        {
            MessageBox.Show("以歐盟合格證申請必須挑選");
            rdBtnLst_useeurocert.Focus();
            return;
        }

        #region 特殊字元判斷-沒有MasterPage
        foreach (Control ctl in Page.Form.Controls)
        {
            Control content = (Control)ctl;
            foreach (Control c in content.Controls)
            {
                if (c is TextBox)
                {
                    TextBox objTextBox = default(TextBox);
                    objTextBox = (TextBox)c;
                    if ((tbWord(objTextBox.Text)))
                    {
                        MessageBox.Show(objTextBox.Text + ":含有系統不允許的特殊字元");
                        return;
                    }
                }
            }
        }
        #endregion

        if (hidden_flag.Value == "1") // 1新增-只會傳過來申請編號;2修改-會傳過來申請編號+車型組代碼
            insCarCompoModel("1"); //0 為車型變更前的存檔;1一般修改程序
        else
            updCarCompoModel("1");
    }

    private void set_input_be_edit(bool be_editable)
    {
        if (be_editable)
        {
            rdBtnLst_useeurocert.Enabled = true;
            rdBtnLst_transmissiontypetype.Enabled = true;
            //rbl_drivetype.Enabled = true;
            rdBtnLs_GasolineType.Enabled = true;
            rdBtnLst_provideoilmethod.Enabled = true;
            txt_exhuast.ReadOnly = false;
            txt_cylindernums.ReadOnly = false;
            //rdBtnLst_FitUpType.Enabled = true;
            rdBtnLst_coolsystem.Enabled = true;
            rdBtnLst_testfactory.Enabled = true;
            DrpDnList_importarea.Enabled = true;
        }
        else
        {
            rdBtnLst_useeurocert.Enabled = false;
            rdBtnLst_transmissiontypetype.Enabled = false;
            //rbl_drivetype.Enabled = false;
            rdBtnLs_GasolineType.Enabled = false;
            rdBtnLst_provideoilmethod.Enabled = false;
            txt_exhuast.ReadOnly = true;
            txt_cylindernums.ReadOnly = true;
            //rdBtnLst_FitUpType.Enabled = false;
            rdBtnLst_coolsystem.Enabled = false;
            rdBtnLst_testfactory.Enabled = false;
            DrpDnList_importarea.Enabled = false;
        }
    }

    protected void btn_changecarcompomodelno_Click(object sender, EventArgs e)
    {
        if (hidden_flag.Value == "1")
            insCarCompoModel("0");
        else
            updCarCompoModel("0");
    }

    protected void rdBtnLst_useeurocert_SelectedIndexChanged(object sender, EventArgs e)
    {
        set_ApplytypeAbled(rdBtnLst_useeurocert.SelectedValue);
    }

    #region 判斷歐盟合格證換證的僅能申請 新車型、沿用、修改、沿用及修改
    private void set_ApplytypeAbled(string useeurocert)
    {
        //COPY來的資料，不能改為新車型-->新車型選項disabled
        if (IsCopy.Equals("Y"))
            rdBtnLst_applytype.Items[0].Enabled = false;

        if (useeurocert == "1") //拿歐盟合格證換證
        {
            //只能選新車型或沿用、修改、沿用及修改               
            rdBtnLst_applytype.Items[2].Enabled = false;
            rdBtnLst_applytype.Items[5].Enabled = false;
        }
        else
        {
            for (int i = 1; i < rdBtnLst_applytype.Items.Count; i++)
            {
                rdBtnLst_applytype.Items[i].Enabled = true;
            }
        }

    }
    #endregion

    protected void lbtn_update_Click(object sender, EventArgs e)
    {
        // Response.Redirect("nv_applylistqry.aspx");
        Response.Redirect("carcompomodelList.aspx?applyno=" + Server.UrlEncode(EnCode(lbl_applyno.Text.Trim())));
    }

    #region 按下複製取得資料
    private void getrdBtnLstData(DataSet dsCCM, RadioButtonList rdBtnLstname, string strcolname)
    {
        string scol_name = strcolname;
        RadioButtonList sRdBtnLst_name = rdBtnLstname;
        if (!dsCCM.Tables[0].Rows[0][strcolname].ToString().Equals(""))
        {
            sRdBtnLst_name.SelectedValue = dsCCM.Tables[0].Rows[0][scol_name].ToString();
        }
        else
        {
            sRdBtnLst_name.ClearSelection();
        }
    }
    private void getrdBtnLstData(DataSet dsCCM, DropDownList ddLstname, string strcolname)
    {
        string scol_name = strcolname;
        DropDownList sDDLst_name = ddLstname;
        if (!dsCCM.Tables[0].Rows[0][strcolname].ToString().Equals(""))
        {
            sDDLst_name.SelectedValue = dsCCM.Tables[0].Rows[0][strcolname].ToString();
        }
        else
        {
            sDDLst_name.ClearSelection();
        }
    }
    #endregion

    protected void rdBtnLst_applytype_SelectedIndexChanged(object sender, EventArgs e)
    {
        Cryptography crypy = new Cryptography();
        wsApplybasedataDetail wsApplyBaseData = new wsApplybasedataDetail();
        int keynum = crypy.getRadNum();
        string[] arr_abd = crypy.wsApplybasedataDetailValid(keynum);
        //新系統無原合格證的資料,所以沿用(2)等申請型式須開放讓廠商填寫
        if (Request["carcompomodelno"] == null || Request["carcompomodelno"] == "")
        {
            //如果沒有舊資料 任何申請型式都可以修改
            set_input_be_edit(true);

        }
        else
        {
            //如果有舊資料 沿用(2)及延伸(3) 不能修改
            DataTable dt_unNewApplyData = wsApplyBaseData.get_unNewApplyData(crypy.DeCode(Request["applyno"].ToString()), txt_carcompomodelno.Text.Trim(), arr_abd[0].ToString(), arr_abd[1].ToString());
            if (dt_unNewApplyData.Rows.Count > 0)
            {
                if (rdBtnLst_applytype.SelectedValue == "2" || rdBtnLst_applytype.SelectedValue == "3")
                    set_input_be_edit(false);
                else
                    set_input_be_edit(true);
            }
            else
            {
                //如果沒有舊資料 任何申請型式都可以修改
                set_input_be_edit(true);
            }
        }
    }
}