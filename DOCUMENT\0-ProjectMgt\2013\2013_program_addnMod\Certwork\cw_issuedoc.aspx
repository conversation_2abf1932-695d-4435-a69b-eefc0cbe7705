﻿<%@ Page Title="" Language="C#" MasterPageFile="~/masterpage/MasterPage.master" AutoEventWireup="true"
    CodeFile="cw_issuedoc.aspx.cs" Inherits="Certwork_cw_issuedoc" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="fullheader">
        <table width="100%" cellspacing="0" cellpadding="0" class="fullheaderinfo">
            <tr>
                <td valign="top">
                    <b>核發</b>
                </td>
                <td align="right" valign="top" class="font_loginInfo">
                </td>
            </tr>
        </table>
    </div>
    <!--{* fullheader end *}-->
    <div class="fullcontent">
        <table border="0" cellspacing="0" cellpadding="0" width="100%" class="font_fullcontent">
            <tr>
                <td align="right">
                    申請廠商：
                </td>
                <td>
                    <asp:Label ID="lbl_CompanyName" runat="server"></asp:Label>
                </td>
                <td align="right">
                    申請編號：
                </td>
                <td>
                    <asp:Label ID="lbl_ApplyNo" runat="server"></asp:Label>
                </td>
            </tr>
            <tr>
                <td align="right">
                    核發人員：
                </td>
                <td>
                    <asp:Label ID="lbl_Account" runat="server"></asp:Label>
                </td>
                <td align="right">
                    核發時間：
                </td>
                <td>
                    <asp:Label ID="lbl_IssueDate" runat="server"></asp:Label>
                </td>
            </tr>
            <tr>
                <td align="right">
                    引擎流水碼：
                </td>
                <td>
                    <asp:TextBox ID="txt_EngineNo" runat="server" ></asp:TextBox>
                </td>
                <td align="right">
                    上案引擎流水碼：
                </td>
                <td>
                    <asp:Label ID="lab_exEngineNo" runat="server"></asp:Label>
                </td>
            </tr>
            <tr>
                <td align="right">
                    兩證合一電子檔：
                </td>
                <td colspan="3">
                    <asp:LinkButton ID="lb_Certificated" runat="server" OnClick="lb_Certificated_Click">合格證下載</asp:LinkButton>
                </td>
            </tr>
            <tr>
                <td colspan="4" align="center">
                    <asp:Button ID="btn_Issue" runat="server" Text="核發" OnClick="btn_Issue_Click" CssClass="btn_mouseout"
                        Visible="false" onmouseover="this.className='btn_mouseover'" onmouseout="this.className='btn_mouseout'" />
                </td>
            </tr>
        </table>
    </div>
    <!--{* fullcontent end *}-->
    <div class="fullfooter">
    </div>
    <!--{* content end *}-->
</asp:Content>
