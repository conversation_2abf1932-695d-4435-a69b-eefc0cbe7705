﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Services;
using System.Data;
using System.Data.SqlClient;

/// <summary>
/// Summary description for wsGroupInfo
/// </summary>
[WebService(Namespace = "http://tempuri.org/")]
[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
// To allow this Web Service to be called from script, using ASP.NET AJAX, uncomment the following line. 
// [System.Web.Script.Services.ScriptService]
public class wsGroupInfo : System.Web.Services.WebService
{

    public wsGroupInfo()
    {

        //Uncomment the following line if using designed components 
        //InitializeComponent(); 
    }

    /// <summary>
    /// 修改公司之廠商公司帳號管理者
    /// </summary>
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    /// <param name="compidnoE"></param>
    /// <param name="account"></param>
    [WebMethod]
    public void updateCompManage(string ID, string PD, string compidnoE, string account)
    {
        if (wsCheckGroupValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"
                            set XACT_ABORT on; begin tran;
                            delete from nv_accountbelonggroup
                            where abg_grpid='50' 
                            and abg_account in(select ai_account from nv_accountinfo 
                            where ai_accountbelongcomp=@compidno and ai_accountstatus in ('1','2','3'))

                            insert into nv_accountbelonggroup
                            select @account,'50'
                            commit tran;");
            cmd.Parameters.AddWithValue("@compidno", compidnoE);
            cmd.Parameters.AddWithValue("@account", account);
            Common.Data_noisevalidation.runParaCmd1(cmd);
        }
    }
    /// <summary>
    /// 修改公司之廠商公司帳號一般使用者
    /// </summary>
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    /// <param name="compidnoE"></param>
    /// <param name="account"></param>
    [WebMethod]
    public void updateCompUser(string ID, string PD, string compidnoE, string account)
    {
        if (wsCheckGroupValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"
                            if not exists(select * from nv_accountbelonggroup where abg_account=@account and abg_grpid='51')                           
                            begin
                                insert into nv_accountbelonggroup
                                select @account,'51'    
                            end
                           ");
            cmd.Parameters.AddWithValue("@compidno", compidnoE);
            cmd.Parameters.AddWithValue("@account", account);
            Common.Data_noisevalidation.runParaCmd1(cmd);
        }
    }
    /// <summary>
    /// 取得該公司是否已有廠商公司帳號管理者之筆數
    /// </summary>
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    /// <param name="compidnoE"></param>
    /// <returns></returns>
    [WebMethod]
    public string CompIsHaveManage(string ID, string PD, string compidnoE)
    {
        if (wsCheckGroupValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select * from nv_accountbelonggroup
                            where abg_grpid='50' 
                            and abg_account in(select ai_account from nv_accountinfo 
                            where ai_accountbelongcomp=@compidno and ai_accountstatus in ('1','2'))");
            cmd.Parameters.AddWithValue("@compidno", compidnoE);
            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0].Rows.Count.ToString();
        } return "";
    }

    [WebMethod]
    public bool isAccountInCompManage(string ID, string PD, string account)
    {
        if (wsCheckGroupValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select * from nv_accountbelonggroup where abg_account=@account and abg_grpid='50'");
            cmd.Parameters.AddWithValue("@account", account);
            DataTable dt = Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
            if (dt.Rows.Count > 0)
                return true;
            else return false;
        } return false;
    }
    [WebMethod]
    public bool isAccountInCompUser(string ID, string PD, string account)
    {
        if (wsCheckGroupValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select * from nv_accountbelonggroup where abg_account=@account and abg_grpid='51'");
            cmd.Parameters.AddWithValue("@account", account);
            DataTable dt = Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
            if (dt.Rows.Count > 0)
                return true;
            else return false;
        } return false;
    }

    /// <summary>
    /// 維護帳號之系統權限
    /// </summary>
    /// <param name="account"></param>
    /// <param name="grplist"></param>
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    [WebMethod]
    public void DelOldGrpInsNewGrp(string account, string grplist, string ID, string PD)
    {
        if (wsCheckGroupValid(ID, PD))
        {
            SqlCommand cmd_upd = new SqlCommand(@"set XACT_ABORT on; begin tran;
                                declare @SQL varchar(2000)
                                set @grouplist=SUBSTRING(@grouplist,1,len(@grouplist)-1)
                                set @SQL='
                                delete from nv_accountbelonggroup where abg_account='''+@account +''' '
                                +' insert into nv_accountbelonggroup
                                select '''+@account+''',ct_gd_grpid from nv_codetbl_groupdesc 
                                where ct_gd_grpid in ('''+REPLACE(@grouplist,',',''',''')+''')'
                                exec (@SQL)
                                commit tran;");
            cmd_upd.Parameters.AddWithValue("@account", account);
            cmd_upd.Parameters.AddWithValue("@grouplist", grplist);
            Common.Data_noisevalidation.runParaCmd1(cmd_upd);
        }
    }

    /// <summary>
    /// 取得使用者帳號管理明細之系統權限清單
    /// </summary>
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    /// <returns></returns>
    [WebMethod]
    public DataTable getGroupMainList(string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsCheckGroupValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select *
                            from nv_codetbl_groupdesc
                            where ct_gd_grpid in ('00','02','03','04','05')");
            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        } return dt;
    }

    [WebMethod]
    public DataTable getAccountBelogGrp(string account, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsCheckGroupValid(ID, PD))
        {
            SqlCommand cmd_get = new SqlCommand(@"select * from nv_accountbelonggroup where abg_account=@account");
            cmd_get.Parameters.AddWithValue("@account", account);
            return Common.Data_noisevalidation.runParaCmdDS(cmd_get).Tables[0];
        }
        else return dt;
    }

    /// <summary>
    /// 帳號所屬權限清單
    /// </summary>
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    /// <returns></returns>
    [WebMethod]
    public DataTable getAccountGroup(string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsCheckGroupValid(ID, PD))
        {
            SqlCommand cmd_get = new SqlCommand(@"select abg_account,abg_grpid,ct_gd_grpdesc
                                from nv_accountbelonggroup,nv_codetbl_groupdesc
                                where abg_grpid=ct_gd_grpid and ct_gd_grpid<>'99'");
            return Common.Data_noisevalidation.runParaCmdDS(cmd_get).Tables[0];
        } return dt;
    }

    /// <summary>
    /// 取得公司帳管理清單
    /// </summary>
    /// <param name="compidnoE"></param>
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    /// <returns></returns>
    [WebMethod]
    public DataTable getCompAccGroupList(string compidnoE, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsCheckGroupValid(ID, PD))
        {
            SqlCommand cmd_get = new SqlCommand(@"select * from nv_accountbelonggroup
                            where abg_account in 
                            (select ai_account 
                            from nv_accountinfo 
                            where ai_accountbelongcomp=@compidnoE)");
            cmd_get.Parameters.AddWithValue("@compidnoE", compidnoE);
            return Common.Data_noisevalidation.runParaCmdDS(cmd_get).Tables[0];
        }
        else return dt;
    }

    /// <summary>
    /// 取得所有初審人員清單
    /// </summary>
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    /// <returns></returns>
    [WebMethod]
    public DataTable getFirstAdmList(string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsCheckGroupValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select nv_accountinfo.* from nv_accountinfo,nv_accountbelonggroup
                            where abg_account=ai_account and abg_grpid='02'");
            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        } return dt;
    }

    [WebMethod]
    public DataTable getAccountBelongGrpRoleID(string account, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsCheckGroupValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select abg_account,abg_grpid,ct_gd_grpdesc,ct_gd_roleID
                            from nv_accountbelonggroup,nv_codetbl_groupdesc
                            where abg_grpid=ct_gd_grpid
                            and abg_account=@account");
            cmd.Parameters.AddWithValue("@account", account);
            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;
    }

    /// <summary>
    /// 修改公司之廠商公司帳號使用狀況 使用中/停止使用
    /// </summary>
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    /// <param name="compidnoE"></param>
    /// <param name="account"></param>
    [WebMethod]
    public void updateAccountStatus(string ID, string PD, string compidnoE, string account, string accountstatus)
    {
        if (wsCheckGroupValid(ID, PD))
        {
            SqlCommand cmd_upd = new SqlCommand(@"UPDATE nv_accountinfo
                       SET ai_accountstatus=@accountstatus                          
                     WHERE ai_account = @account and ai_accountbelongcomp=@compidnoE");
            cmd_upd.Parameters.AddWithValue("@compidnoE", compidnoE);
            cmd_upd.Parameters.AddWithValue("@account", account);
            cmd_upd.Parameters.AddWithValue("@accountstatus", accountstatus);
            
            Common.Data_noisevalidation.runParaCmd1(cmd_upd);
        }
    }
    ///
    private bool wsCheckGroupValid(string ID, string PD)
    {
        string[][] x = new string[7][];
        x[0] = new string[2] { "GRPWS1", "idjeow8" };
        x[1] = new string[2] { "GRPWS2", "NIOHJId7s" };
        x[2] = new string[2] { "GRPWS3", "323OJUds" };
        x[3] = new string[2] { "GRPWS4", "98HYGaud" };
        x[4] = new string[2] { "GRPWS5", "doi9scJd" };
        x[5] = new string[2] { "GRPWS6", "we2UHYd" };
        x[6] = new string[2] { "GRPWS7", "89HYgdu" };

        for (int i = 0; i < 7; i++)
        {
            if (x[i][0].ToString() == ID && x[i][1].ToString() == PD)
            {
                return true;
            }
        }
        return false;
    }
}
