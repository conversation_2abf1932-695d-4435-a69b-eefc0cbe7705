﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data.SqlClient;
using System.Data;
using Microsoft.Reporting.WebForms;
using System.Configuration;
using System.Xml;
using Common;
using System.IO;
using System.Drawing;
using System.Drawing.Imaging;
using System.Threading;

public partial class applydatamt_carmodeldatamt_d_rpt : BaseAdminPage
{
    protected void Page_Load(object sender, EventArgs e)
    {
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = '../accountmt/index.aspx';</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" &&
                (
                (
                GetAgentAccountGrpList().Contains("epa_adm") || GetAgentAccountGrpList().Contains("ac_adm") || GetAgentAccountGrpList().Contains("tc_adm") || GetAgentAccountGrpList().Contains("rv_adm") || GetAgentAccountGrpList().Contains("rs_adm") || GetAgentAccountGrpList().Contains("fis_adm") || GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("pg_adm")
                )
                ||
                (
                  (GetAgentAccountGrpList().Contains("cga_adm") || GetAgentAccountGrpList().Contains("cam_adm")) && CheckSingleApplyDataAccessRight(DeCode(Request["applyno"].ToString()))
                )
                )
                )
            {
                if (!IsPostBack)
                {
                    if (Request["applyno"].ToString() != "")
                    {
                        Cryptography crypy = new Cryptography();
                        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
                        int keynum = crypy.getRadNum();
                        string[] arr = crypy.wsApplybasedataDetailValid(keynum);
                        DataTable dt_applydatadetail = wsApplyData.get_applybasedate_detail(crypy.DeCode(Request["applyno"].ToString()), arr[0].ToString(), arr[1].ToString());
                        if (dt_applydatadetail.Rows.Count > 0)
                        {
                            //if (dt_applydatadetail.Rows[0]["abd_databelongcompidno"].ToString() == Session["compidno"].ToString())
                            //{
                            BindData();
                            dt_applydatadetail.Dispose();
                            //}
                            //else
                            //{
                            //    dt_applydatadetail.Dispose();
                            //    Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                            //    Response.End();
                            //}
                        }
                        else
                        {
                            dt_applydatadetail.Dispose();
                            Response.Write("<script languge='javascript'>alert('發生取不到申請編號錯誤，系統將導回首頁！！');window.location.href='../index.aspx'</script>");
                            Response.End();
                        }
                    }
                    else
                    {

                        Response.Write("<script languge='javascript'>alert('發生取不到申請編號錯誤，系統將導回首頁！！');window.location.href='../index.aspx'</script>");
                        Response.End();
                    }
                }
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }
    }

    #region 更改的麵包屑傳入的參數
    protected void Page_LoadComplete(object sender, EventArgs e)
    {
        ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text = ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text.Replace("applydatamt/applybasedata_detail.aspx", "applydatamt/applybasedata_detail.aspx?applyno=" + Server.UrlEncode(Request["applyno"].ToString()));
    }
    #endregion

    public void BindData()
    {
        Cryptography crypy = new Cryptography();

        int keynum = crypy.getRadNum();
        wsCarCompoModel wsCarCmpModel = new wsCarCompoModel();

        string applyno = DeCode(Request["applyno"].ToString());
        string carcompomodelno = DeCode(Request["carcompomodelno"].ToString());
        string carmodelno = DeCode(Request["carmodelno"].ToString());
        string[] CMarr = crypy.wsCarCompoModelValidate(crypy.getRadNum());
        string sVechiletype = "";
        string s_producercountry = " ";//製造地區
        string s_fullcarstylename = " ";//銷售名稱
        string s_databelongcompname = " "; //廠商名稱
        string s_carbrand = " "; //廠牌
        string s_carbodyshape = " ";//型式
        string s_carbodylenth = " ";//全長
        string s_carbodywidth = " ";//全寬
        string s_carbodyhieght = " ";//全高
        string s_carwheelbase = " ";//軸距
        string s_carwheelbase_f = " ";//輪距前
        string s_carwheelbase_b = " ";//輪距後
        string s_caremptyweight = " ";//空重
        string s_cartotalweight = " ";      //總重 
        string s_loadingweight = " ";      //載重 
        //string s_passengers = " ";//乘坐人數  
        string s_baseenginename = " ";//引擎型式 
        string s_fituptype = " "; //引擎安裝
        string s_exhuast = " "; //總排氣量
        string s_cylindermeters = "0"; //缸徑
        string s_cylinderstroke = "0"; //衝程
        string s_cylindernums = " "; //汽缸數
        string s_compressionratio = " "; //壓縮比
        string s_coolsystem = " "; //冷卻系統
        string s_turborchargers = " "; //渦輪增壓器
        string s_suspensionsystem_b = " "; //懸吊系統-前
        string s_suspensionsystem_f = " "; //懸吊系統-後
        string s_tirespec_std_b = " "; //輪胎規格-前
        string s_tirespec_std_f = " "; //輪胎規格-後
        string s_carstyleyear = " "; //車型年
        string s_ct_dt_desc = " "; //傳動方式
        string s_differentialmodel = " "; //型式
        string s_differentialgearratio = " "; //齒式
        string s_transmissionmodel = " "; //變速系統-型式
        string s_transmissionbackgearratio = " "; //變速系統-倒檔齒比
        string s_highestspeed = " "; //最高車速
        string s_provideoilmethod = " "; //供油方式
        string s_929598 = " "; //油料
        string s_oilcapacity = " "; //油箱容量
        string s_maxhorsepower = " "; //最大馬力
        string s_maxhorsepowerspeed = " "; //最大馬力轉速
        string s_torque = " "; //最大扭力
        string s_torquespeed = " "; //最大扭力轉速
        //string s_exhaustsystem = " "; //排氣系統
        //string s_EEC = " "; //EEC
        //string s_PCV = " "; //PCV
        
        //柴油車
        string s_carbodyweight = " "; //車體總重
        string s_increasepowerstyle = " "; //加力箱型式
        string s_increasepowerratio = " "; //加力箱齒比
        string s_backgearnums = " "; //後退檔
        string s_forwardgearnums = " "; //前進檔
        string s_highlowgear = " "; //高低檔
        string s_climbratio = " "; //爬坡檔
        string s_suspensionsystem_sup = " "; //補軸
        string s_maxhorsepower_hb = " "; //馬達最大馬力
        string s_maxhorsepowerspeed_hb = " "; //馬達最大馬力轉速
        string s_Binmaxhorsepowerspeed = " "; //馬達最大馬力+馬達最大馬力轉速
        string s_torque_hb = " "; //馬達最大扭力
        string s_torquespeed_hb = " "; //馬達最大扭力轉速
        string s_Bintorque = " "; //馬達最大扭力+馬達最大扭力轉速
       
        string s_numofaxes_f = " "; //驅動軸數前
        string s_numofaxes_b = " "; //驅動軸數後
        string s_transmissionnum = " "; //前進檔數
        string s_tirespec_cho_f = " "; //選配前
        string s_tirespec_cho_b = " "; //選配後
        string s_tirespec_cho = " "; //輪胎選配
        string s_HMremark = " "; //自填備註
        string s_g = "檔";
        int i;
        string[] arr_gearnum = new string[18];
        string[] arrData = { "一" + s_g, "二" + s_g, "三" + s_g, "四" + s_g, "五" + s_g, "六" + s_g, "七" + s_g, "八" + s_g, "9" + s_g, "10" + s_g, "11" + s_g, "12" + s_g, "13" + s_g, "14" + s_g, "15" + s_g, "16" + s_g, "17" + s_g, "18" + s_g };
        string[] arr_gearratio = { " ", " ", " ", " ", " ", " ", " ", " ", " ", " ", " ", " ", " ", " ", " ", " ", " ", " " };
        string s_remark_1 = "1. 表列尺度各項容差為 ± 2％。";
        string s_remark_2 = "2. 重量容差為：A,B類 ± 50kg；（框式、底盤）B類 ± 100kg；C,D類 ± 200kg。";
        string s_remark_3 = "3. 規格表格式僅供參考，業者依實際需求及車輛特性若有不足可酌加修改。";

        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        string[] arr_apply = crypy.wsApplybasedataDetailValid(crypy.getRadNum());
        /*取得流水號，產生Barcode
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        string[] arr_apply = crypy.wsApplybasedataDetailValid(crypy.getRadNum());
        string barcodeno = wsApplyData.getNewBarCodeNo(arr_apply[0].ToString(), arr_apply[1].ToString());
        byte[] barcodeimg = getBarCode.BitmapDataFromBitmap(getBarCode.GetCode39(barcodeno), ImageFormat.Jpeg);
        wsApplyData.Update_Nv_applybasedataBarcode(applyno, barcodeno, barcodeimg, arr_apply[0].ToString(), arr_apply[1].ToString());
        */
        //Barcode
        //DataTable dt_applybasebarcode = wsApplyData.get_Nv_Applybasebarcode(applyno, arr_apply[0].ToString(), arr_apply[1].ToString());

        sVechiletype = wsCarCmpModel.Get_Vechiletype(applyno, CMarr[0].ToString(), CMarr[1].ToString());
        //檔數
        DataTable dt_gearratio = wsCarCmpModel.get_Gearratio_list(applyno, carcompomodelno, carmodelno, CMarr[0].ToString(), CMarr[1].ToString());
        if (dt_gearratio.Rows.Count > 0)
        {
            for (int j = 0; j < dt_gearratio.Rows.Count; j++)
                arr_gearratio[j] = dt_gearratio.Rows[j]["gr_gearratio"].ToString();
        }
        
        //汽車欄位資料
        DataTable dt_combinecarlist = wsCarCmpModel.get_CarspecTableData(applyno, carcompomodelno, carmodelno, sVechiletype, CMarr[0].ToString(), CMarr[1].ToString());
        if (dt_combinecarlist.Rows.Count > 0)
        {
            s_databelongcompname = dt_combinecarlist.Rows[0]["abd_databelongcompname"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["abd_databelongcompname"].ToString(); //廠商名稱
            s_carbrand = dt_combinecarlist.Rows[0]["ct_bd_desc"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["ct_bd_desc"].ToString(); //廠牌
            //車型名稱不要廠牌;電動車沒有c.c數CC改         
            s_fullcarstylename = dt_combinecarlist.Rows[0]["fullcarstylename"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["fullcarstylename"].ToString(); //型式/銷售名稱 
            s_fullcarstylename = s_fullcarstylename.Substring(s_carbrand.Length, (s_fullcarstylename.Length - s_carbrand.Length)).TrimStart();
            s_carbodyshape = dt_combinecarlist.Rows[0]["ct_cbs_desc"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["ct_cbs_desc"].ToString(); //車身樣式　有分g,m
            s_carbodylenth = dt_combinecarlist.Rows[0]["cmdg_carbodylenth"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_carbodylenth"].ToString(); //全長
            s_carbodywidth = dt_combinecarlist.Rows[0]["cmdg_carbodywidth"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_carbodywidth"].ToString(); //全寬
            s_carbodyhieght = dt_combinecarlist.Rows[0]["cmdg_carbodyhieght"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_carbodyhieght"].ToString(); //全高
            s_carwheelbase = dt_combinecarlist.Rows[0]["cmdg_carwheelbase"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_carwheelbase"].ToString(); //軸距
            s_carwheelbase_f = dt_combinecarlist.Rows[0]["cmdg_carwheelbase_f"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_carwheelbase_f"].ToString(); //輪距前
            s_carwheelbase_b = dt_combinecarlist.Rows[0]["cmdg_carwheelbase_b"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_carwheelbase_b"].ToString(); //輪距後
            s_caremptyweight = dt_combinecarlist.Rows[0]["cmdg_caremptyweight"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_caremptyweight"].ToString(); //空重
            s_cartotalweight = dt_combinecarlist.Rows[0]["cmdg_cartotalweight"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_cartotalweight"].ToString(); //總重
            s_loadingweight = dt_combinecarlist.Rows[0]["cmdg_passengers"].ToString().Trim() + "/" + dt_combinecarlist.Rows[0]["cmdg_loadingweight"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_passengers"].ToString() + "/" + dt_combinecarlist.Rows[0]["cmdg_loadingweight"].ToString().Trim().Replace(".00", "");//乘坐人數或載重 
            //s_passengers = dt_combinecarlist.Rows[0]["cmdg_passengers"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_passengers"].ToString(); //乘坐人數        
            s_fituptype = dt_combinecarlist.Rows[0]["ct_ep_desc"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["ct_ep_desc"].ToString(); //引擎安裝位置  有分g,m
            s_baseenginename = dt_combinecarlist.Rows[0]["cmdg_baseenginename"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_baseenginename"].ToString(); //引擎型式
            s_exhuast = dt_combinecarlist.Rows[0]["cpm_exhuast"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cpm_exhuast"].ToString().Replace(".00", ""); //總排氣量
            s_cylindermeters = dt_combinecarlist.Rows[0]["cmdg_cylindermeters"].ToString().Trim() == "" ? "0" : dt_combinecarlist.Rows[0]["cmdg_cylindermeters"].ToString().Replace(".00", ""); //缸徑
            s_cylinderstroke = dt_combinecarlist.Rows[0]["cmdg_cylinderstroke"].ToString().Trim() == "" ? "0" : dt_combinecarlist.Rows[0]["cmdg_cylinderstroke"].ToString().Replace(".00", ""); //衝程
            s_cylindernums = dt_combinecarlist.Rows[0]["cpm_cylindernums"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cpm_cylindernums"].ToString(); //汽缸數
            s_compressionratio = dt_combinecarlist.Rows[0]["cmdg_compressionratio"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_compressionratio"].ToString(); //壓縮比
            s_coolsystem = dt_combinecarlist.Rows[0]["ct_cs_desc"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["ct_cs_desc"].ToString(); //冷卻系統
            s_turborchargers = dt_combinecarlist.Rows[0]["ct_htm_desc"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["ct_htm_desc"].ToString(); //渦輪增壓器
            s_suspensionsystem_f = dt_combinecarlist.Rows[0]["cmdg_suspensionsystem_f"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_suspensionsystem_f"].ToString(); //懸吊系統-前
            s_suspensionsystem_b = dt_combinecarlist.Rows[0]["cmdg_suspensionsystem_b"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_suspensionsystem_b"].ToString(); //懸吊系統-後
            s_tirespec_std_f = dt_combinecarlist.Rows[0]["cmdg_tirespec_std_b"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_tirespec_std_b"].ToString(); //輪胎規格-前
            s_tirespec_std_b = dt_combinecarlist.Rows[0]["cmdg_tirespec_std_f"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_tirespec_std_f"].ToString(); //輪胎規格-後        
            s_carstyleyear = dt_combinecarlist.Rows[0]["abd_carstyleyear"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["abd_carstyleyear"].ToString(); //車型年
            s_ct_dt_desc = dt_combinecarlist.Rows[0]["ct_dt_desc"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["ct_dt_desc"].ToString(); //傳動方式
            s_differentialmodel = dt_combinecarlist.Rows[0]["cmdg_differentialmodel"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_differentialmodel"].ToString(); //型式
            s_differentialgearratio = dt_combinecarlist.Rows[0]["cmdg_differentialgearratio"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_differentialgearratio"].ToString(); //齒式
            s_transmissionmodel = dt_combinecarlist.Rows[0]["cmdg_transmissionmodel"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_transmissionmodel"].ToString(); //變速系統-型式
            s_transmissionbackgearratio = dt_combinecarlist.Rows[0]["cmdg_transmissionbackgearratio"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_transmissionbackgearratio"].ToString(); //變速系統-倒檔齒比
            s_highestspeed = dt_combinecarlist.Rows[0]["cmdg_highestspeed"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_highestspeed"].ToString().Replace(".00", ""); //最高車速
            s_provideoilmethod = dt_combinecarlist.Rows[0]["ct_le_desc"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["ct_le_desc"].ToString(); //供油方式
            s_929598 = dt_combinecarlist.Rows[0]["cmdg_929598"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_929598"].ToString(); //油料
            s_oilcapacity = dt_combinecarlist.Rows[0]["cmdg_oilcapacity"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_oilcapacity"].ToString(); //油箱容量
            s_maxhorsepower = dt_combinecarlist.Rows[0]["cmdg_maxhorsepower"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_maxhorsepower"].ToString(); //最大馬力
            s_maxhorsepowerspeed = dt_combinecarlist.Rows[0]["cmdg_maxhorsepowerspeed"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_maxhorsepowerspeed"].ToString(); //最大馬力轉速
            s_torque = dt_combinecarlist.Rows[0]["cmdg_torque"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_torque"].ToString(); //最大扭力
            s_torquespeed = dt_combinecarlist.Rows[0]["cmdg_torquespeed"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_torquespeed"].ToString(); //最大扭力轉速
            //s_exhaustsystem = dt_combinecarlist.Rows[0]["cmdg_exhaustsystem"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_exhaustsystem"].ToString(); //排氣系統
            //s_EEC = dt_combinecarlist.Rows[0]["cmdg_EEC"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_EEC"].ToString(); //EEC
            //s_PCV = dt_combinecarlist.Rows[0]["cmdg_PCV"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_PCV"].ToString(); //PCV
            s_tirespec_cho_f = dt_combinecarlist.Rows[0]["cmdg_tirespec_cho_f"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_tirespec_cho_f"].ToString(); //輪胎選配前
            s_tirespec_cho_b = dt_combinecarlist.Rows[0]["cmdg_tirespec_cho_b"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_tirespec_cho_b"].ToString(); //輪胎選配後
            s_HMremark = dt_combinecarlist.Rows[0]["cmdg_remark"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_remark"].ToString(); //自填備註

            s_carbodyweight = dt_combinecarlist.Rows[0]["cmdg_carbodyweight"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_carbodyweight"].ToString();  //車體總重
            s_numofaxes_f = dt_combinecarlist.Rows[0]["cmdg_numofaxes_f"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_numofaxes_f"].ToString().Trim() == "O" ? "單" : "雙"; //驅動軸數前
            s_numofaxes_b = dt_combinecarlist.Rows[0]["cmdg_numofaxes_b"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_numofaxes_b"].ToString().Trim() == "O" ? "單" : "雙"; //驅動軸數後
            s_increasepowerstyle = dt_combinecarlist.Rows[0]["cmdg_increasepowerstyle"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_increasepowerstyle"].ToString();  //加力箱型式
            s_increasepowerratio = dt_combinecarlist.Rows[0]["cmdg_increasepowerratio"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_increasepowerratio"].ToString(); //加力箱齒比
            s_backgearnums = dt_combinecarlist.Rows[0]["cmdg_backgearnums"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_backgearnums"].ToString(); //後退檔
            s_forwardgearnums = dt_combinecarlist.Rows[0]["cmdg_forwardgearnums"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_forwardgearnums"].ToString(); //前進檔
            s_highlowgear = dt_combinecarlist.Rows[0]["highlowgear"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["highlowgear"].ToString(); //高低檔
            s_climbratio = dt_combinecarlist.Rows[0]["cmdg_climbratio"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_climbratio"].ToString(); //爬坡檔
            s_suspensionsystem_sup = dt_combinecarlist.Rows[0]["cmdg_suspensionsystem_supplement"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_suspensionsystem_supplement"].ToString(); //補軸
            s_maxhorsepower_hb = dt_combinecarlist.Rows[0]["cmdg_maxhorsepower_hb"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_maxhorsepower_hb"].ToString(); //馬達最大馬力
            s_maxhorsepowerspeed_hb = dt_combinecarlist.Rows[0]["cmdg_maxhorsepowerspeed_hb"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_maxhorsepowerspeed_hb"].ToString(); //馬達最大馬力
            s_torque_hb = dt_combinecarlist.Rows[0]["cmdg_torque_hb"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_torque_hb"].ToString(); //馬達最大扭力
            s_torquespeed_hb = dt_combinecarlist.Rows[0]["cmdg_torquespeed_hb"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_torquespeed_hb"].ToString(); //馬達最大馬力
            s_transmissionnum = dt_combinecarlist.Rows[0]["cmdg_transmissionnum"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_transmissionnum"].ToString(); //前進檔數
            s_producercountry = dt_combinecarlist.Rows[0]["cmdg_producercountry"].ToString().Trim() == "" ? " " : dt_combinecarlist.Rows[0]["cmdg_producercountry"].ToString(); //製造地區
            s_Binmaxhorsepowerspeed = s_maxhorsepower_hb + " kW / " + s_maxhorsepowerspeed_hb + " rpm";
            s_Bintorque=s_torque_hb + " kg-m / " + s_torquespeed_hb + " rpm";
            if (s_tirespec_cho_f.Length > 0 || s_tirespec_cho_b.Length > 0)
            {
                s_tirespec_cho = "＊輪胎選配： 前： " + s_tirespec_cho_f + " 、 後： " + s_tirespec_cho_b;
            }
            if (s_HMremark.Length > 0)
            {
                s_HMremark = "＊備註： " + s_HMremark;
            }

            if (dt_combinecarlist.Rows[0]["cpm_transmissiontypetype"].Equals("3"))
            {
                arr_gearnum[0] = "UD";
                arr_gearnum[1] = "OD";
                for (i = 2; i < arrData.Length; i++)
                {
                    arr_gearnum[i] = " ";

                }
            }
            else
            {
                for (i = 0; i < arrData.Length; i++)
                {
                    arr_gearnum[i] = arrData[i];

                }
            }
        }
        else
        {

            for (i = 0; i < arrData.Length; i++)
            {
                arr_gearnum[i] = arrData[i];

            }
        }

        #region 資料狀態-當申請型式 不為新申請 且有 50,55資料 取得上次申請代表車資料 否 擇讀取自己本身填寫資料
        DataTable dt_applystatus = wsApplyData.get_applystatus(applyno, carcompomodelno, arr_apply[0].ToString(), arr_apply[1].ToString());
        DataTable dt_unNewApplyData = new DataTable();
        bool b_IsApplied = wsApplyData.IsApplied(applyno, carcompomodelno, arr_apply[0].ToString(), arr_apply[1].ToString());
        if (dt_applystatus.Rows.Count > 0 && !(dt_applystatus.Rows[0]["ct_aps_desc"].Equals("新申請")) && b_IsApplied)
        {

            dt_unNewApplyData = wsApplyData.get_unNewApplyData(applyno, carcompomodelno, arr_apply[0].ToString(), arr_apply[1].ToString());
            if (dt_unNewApplyData.Rows.Count > 0)
            {
                applyno = dt_unNewApplyData.Rows[0]["cmdg_applyno"].ToString();
                carcompomodelno = dt_unNewApplyData.Rows[0]["cmdg_carcompomodelno"].ToString();
                carmodelno = dt_unNewApplyData.Rows[0]["cmdg_carmodelno"].ToString();
            }
        }
        #endregion
        //備註
        DataTable dt_remark = wsCarCmpModel.get_RemarkForRpt(applyno, carcompomodelno, carmodelno, CMarr[0].ToString(), CMarr[1].ToString());


        //開啟Viewer顯示   
        rpv_MyData.Visible = true;
        //指定Viewer的rdlc檔   
        rpv_MyData.LocalReport.ReportPath = Server.MapPath("rpt_Dcarcompomodel.rdlc");
        //清掉之前的狀態，重新把資料丟進去，並做Viewer的Refresh   
        rpv_MyData.LocalReport.DataSources.Clear();


        //rpv_MyData.LocalReport.DataSources.Add(new ReportDataSource("DataSet_barcode", dt_applybasebarcode));
        rpv_MyData.LocalReport.DataSources.Add(new ReportDataSource("DataSet_Remark", dt_remark));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_databelongcompname", s_databelongcompname));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_carbrand", s_carbrand));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_fullcarstylename", s_fullcarstylename));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_carbodyshape", s_carbodyshape));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_carbodylenth", s_carbodylenth));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_carbodywidth", s_carbodywidth));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_carbodyhieght", s_carbodyhieght));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_carwheelbase", s_carwheelbase));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_carwheelbase_f", s_carwheelbase_f));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_carwheelbase_b", s_carwheelbase_b));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_caremptyweight", s_caremptyweight));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_loadingweight", s_loadingweight));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_carbodyweight", s_carbodyweight));

        //rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_passengers", s_passengers));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_baseenginename", s_baseenginename));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_fituptype", s_fituptype));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_exhuast", s_exhuast));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_cylindermeters", s_cylindermeters));

        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_cylinderstroke", s_cylinderstroke));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_cylindernums", s_cylindernums));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_compressionratio", s_compressionratio));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_coolsystem", s_coolsystem));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_turborchargers", s_turborchargers));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_provideoilmethod", s_provideoilmethod));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_oil", s_929598));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_oilcapacity", s_oilcapacity));

        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_tirespec_std_f", s_tirespec_std_f));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_tirespec_std_b", s_tirespec_std_b));

        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_transmissionnum", s_transmissionnum));


        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_carstyleyear", s_carstyleyear));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_drivetraintype", s_ct_dt_desc));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_numofaxes_f", s_numofaxes_f));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_numofaxes_b", s_numofaxes_b));

        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_differentialmodel", s_differentialmodel));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_differentialgearratio", s_differentialgearratio));

        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_transmissionmodel", s_transmissionmodel));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_transmissionbackgearratio", s_transmissionbackgearratio));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_increasepowerstyle", s_increasepowerstyle));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_increasepowerratio", s_increasepowerratio));

        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_backgearnums", s_backgearnums));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_forwardgearnums", s_forwardgearnums));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_highlowgear", s_highlowgear));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_climbratio", s_climbratio));


        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_maxhorsepower", s_maxhorsepower));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_maxhorsepowerspeed", s_maxhorsepowerspeed));

        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_suspensionsystem_f", s_suspensionsystem_f));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_suspensionsystem_b", s_suspensionsystem_b));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_suspensionsystem_sup", s_suspensionsystem_sup));

        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_torque", s_torque));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_torquespeed", s_torquespeed));

        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_maxhorsepower_hb", s_Binmaxhorsepowerspeed));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_maxhorsepowerspeed_hb", s_maxhorsepowerspeed_hb));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_torque_hb", s_Bintorque));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_torquespeed_hb", s_torquespeed_hb));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_highestspeed", s_highestspeed));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_producercountry", s_producercountry));
      
        //rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_exhaustsystem", s_exhaustsystem));
        //rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_EEC", s_EEC));
        //rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_PCV", s_PCV));

        //rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_Gear1", arr_gearnum[0]));
        //rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_Gear2", arr_gearnum[1]));
        //rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_Gear3", arr_gearnum[2]));
        //rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_Gear4", arr_gearnum[3]));
        //rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_Gear5", arr_gearnum[4]));
        //rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_Gear6", arr_gearnum[5]));
        //rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_Gear7", arr_gearnum[6]));
        //rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_Gear8", arr_gearnum[7]));
        //for (i = 0; i < arr_gearratio.Length; i++)
        //{
        //   // arr_gearnum[i] = arrData[i];
        //    rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_GearRatio" +i+1 , arr_gearratio[i]));

        //}
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_GearRatio1", arr_gearratio[0]));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_GearRatio2", arr_gearratio[1]));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_GearRatio3", arr_gearratio[2]));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_GearRatio4", arr_gearratio[3]));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_GearRatio5", arr_gearratio[4]));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_GearRatio6", arr_gearratio[5]));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_GearRatio7", arr_gearratio[6]));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_GearRatio8", arr_gearratio[7]));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_GearRatio9", arr_gearratio[8]));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_GearRatio10", arr_gearratio[9]));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_GearRatio11", arr_gearratio[10]));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_GearRatio12", arr_gearratio[11]));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_GearRatio13", arr_gearratio[12]));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_GearRatio14", arr_gearratio[13]));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_GearRatio15", arr_gearratio[14]));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_GearRatio16", arr_gearratio[15]));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_GearRatio17", arr_gearratio[16]));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_GearRatio18", arr_gearratio[17]));

        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_tirespec_cho", s_tirespec_cho));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_HMremark", s_HMremark));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_remark_1", s_remark_1));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_remark_2", s_remark_2));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_remark_3", s_remark_3));
        rpv_MyData.LocalReport.Refresh();
    }
}