﻿<%@ Page Title="" Language="C#" MasterPageFile="~/masterpage/MasterPage.master" AutoEventWireup="true"
    CodeFile="noisetestrpt_readnoly.aspx.cs" Inherits="applydatamt_noisetestrpt_readnoly" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="fullheader">
        <table width="100%" cellspacing="0" cellpadding="0" class="fullheaderinfo">
            <tr>
                <td valign="top">
                    <b>噪音合格證明申請-測試報告</b>
                </td>
                <td align="right" valign="top" class="font_loginInfo">
                </td>
            </tr>
        </table>
    </div>
    <div class="fullcontent">
        <div id="div_maintain" runat="server" class="formstyle">
            <table border="0" cellspacing="0" cellpadding="0" width="100%" class="font_fullcontent">
                <tr>
                    <td align="right" width="120px">
                        申請編號：
                    </td>
                    <td>
                        <asp:Label ID="lbl_applyno" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        車型組代碼：
                    </td>
                    <td>
                        <asp:Label ID="lbl_carcompomodelno" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        廠商名稱：
                    </td>
                    <td>
                        <asp:Label ID="lbl_databelongcompname" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        車型年：
                    </td>
                    <td>
                        <asp:Label ID="lbl_carstyleyear" runat="server"></asp:Label>
                    </td>
                </tr>
                 <tr>
                    <td align="right">
                        測試車製造地區：
                    </td>
                    <td colspan="3">
                        <asp:Label ID="lbl_producercountry" runat="server"></asp:Label>
                    </td>
                    
                </tr>
                <tr>
                    <td align="right">
                        車型名稱：
                    </td>
                    <td colspan="3">
                        <div>
                            <asp:Repeater ID="rpt_representvehicle" runat="server" OnItemDataBound="rpt_representvehicle_ItemDataBound">
                                <ItemTemplate>
                                    <asp:Label runat="server" ID="lbl_carengmodelname" />
                                </ItemTemplate>
                                <SeparatorTemplate>
                                    <asp:Label ID="lbl_dot" runat="server" Text=";"></asp:Label><br>
                                </SeparatorTemplate>
                            </asp:Repeater>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        報告號碼：
                    </td>
                    <td>
                        <asp:Label ID="lbl_testrptno" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        檢測廠：
                    </td>
                    <td>
                        <asp:Label ID="lbl_testfactory" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        引擎號碼：
                    </td>
                    <td>
                        <asp:Label ID="lbl_engineno" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        車身號碼：
                    </td>
                    <td>
                        <asp:Label ID="lbl_carbodyno" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        加速進入檔位：
                    </td>
                    <td>
                        <asp:Repeater ID="rpt_speedupgear" runat="server">
                            <ItemTemplate>
                                <asp:Label runat="server" ID="lbl_speedupgear" Text='<%#Eval("trsui_speedupgear","{0}")%>' />
                            </ItemTemplate>
                            <SeparatorTemplate>
                                <asp:Label ID="lbl_dot" runat="server" Text=";"></asp:Label>
                                <%# ((Container.ItemIndex+1) %3 == 0 )? "<br />" :""%>
                            </SeparatorTemplate>
                        </asp:Repeater>
                    </td>
                    <td align="right">
                        加速進場車速：
                    </td>
                    <td>
                        <div>
                            <asp:Repeater ID="rpt_speed" runat="server">
                                <ItemTemplate>
                                    <asp:Label runat="server" ID="lbl_speed" Text='<%#Eval("trsui_speed","{0}")%>' />
                                </ItemTemplate>
                                <SeparatorTemplate>
                                    <asp:Label ID="lbl_dot2" runat="server" Text=";"></asp:Label>
                                     <%# ((Container.ItemIndex+1) %3 == 0 )? "<br />" :""%>
                                </SeparatorTemplate>
                            </asp:Repeater>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        加速選定條件：
                    </td>
                    <td colspan="3">
                        <asp:Label ID="lbl_speedupcondi" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        原地引擎轉速設定：
                    </td>
                    <td>
                        <asp:Label ID="lbl_stayrpmparam" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        原地設定條件：
                    </td>
                    <td>
                        <asp:Label ID="lbl_stayrpmcondi" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        加速噪音值：
                    </td>
                    <td>
                        <asp:Label ID="lbl_speedupnoise" runat="server"></asp:Label>
                        <b>&nbsp;dB(A)</b>
                    </td>
                    <td align="right">
                        原地噪音值：
                    </td>
                    <td>
                        <asp:Label ID="lbl_staynoise" runat="server"></asp:Label>&nbsp;<b>dB(A)</b>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        加速噪音標準值：
                    </td>
                    <td>
                        <asp:Label ID="lbl_speedupnoisestandardvalue" runat="server"></asp:Label>&nbsp;<b>dB(A)</b>
                    </td>
                    <td align="right">
                        原地噪音標準值：
                    </td>
                    <td>
                        <asp:Label ID="lbl_staynoisestandardvalue" runat="server"></asp:Label>&nbsp;<b>dB(A)</b>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        測定報告：
                    </td>
                    <td colspan="3">
                     <asp:HyperLink ID="hlk_rptfileid" runat="server" Target="_blank"></asp:HyperLink>                        
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        備註：
                    </td>
                    <td colspan="3">
                        <asp:Repeater ID="rpt_remark" runat="server">
                            <HeaderTemplate>
                                <table>
                            </HeaderTemplate>
                            <ItemTemplate>
                                <tr>
                                    <td>
                                        <%#Eval("ntrr_no", "{0}.")%><asp:Label runat="server" ID="lbl_speedupgear" Text='<%#Eval("ntrr_remark_desc","{0}")%>' />
                                    </td>
                                </tr>
                            </ItemTemplate>
                            <FooterTemplate>
                                </table>
                            </FooterTemplate>
                        </asp:Repeater>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        資料最後修改人：
                    </td>
                    <td>
                        <asp:Label ID="lbl_modaccount" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        資料最後修改日期：
                    </td>
                    <td>
                        <asp:Label ID="lbl_moddate" runat="server"></asp:Label>
                    </td>
                </tr>
              <tr>
                    <td align="center" colspan="4">
                        <asp:Button ID="btn_Print" runat="server" Text="列印" CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'"
                            onmouseout="this.className='btn_mouseout'" CausesValidation="False" OnClick="btn_Print_Click" />
                    </td>
                </tr>
            </table>
        </div>
    </div>
    <!--{* fullcontent end *}-->
    <div class="fullfooter">
    </div>
    <!--{* content end *}-->
</asp:Content>
