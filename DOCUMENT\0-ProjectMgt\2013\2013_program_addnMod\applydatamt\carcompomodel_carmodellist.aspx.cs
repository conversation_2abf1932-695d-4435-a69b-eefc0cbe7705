﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;

public partial class applydatamt_carcompomodel_carmodellist : BaseAdminPage
{
    wsCarCompoModel wsCarCmpModel = new wsCarCompoModel();
    wsCodetbl wsCodeTbl = new wsCodetbl();
    Cryptography crypy = new Cryptography();
    JSCommon alert = new JSCommon();
    int Chassis_number_limt = 20;//車身號碼長度限制
    int Engine_number_limt = 20;//引擎號碼長度限制

    protected void Page_Load(object sender, EventArgs e)
    {
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = '../accountmt/index.aspx';</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" && (GetAgentAccountGrpList().Contains("cam_adm") || GetAgentAccountGrpList().Contains("cga_adm") || GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("pg_adm")))
            {

                if (Request["applyno"] != null && Request["applyno"] != "" && Request["carcompomodelno"] != null && Request["carcompomodelno"] != "")
                {
                    if (!IsPostBack)
                    {

                        DataTable Table = GetData();
                        //lbl_applyno.Text = applyno;
                        string applytype = "";

                        if (Table.Rows.Count > 0)
                        {
                            //BindData(applyno);
                            //BindDataStauts();
                            //BindCarrType();
                            //BindAdaptStandarddata();
                            //BindTransportType();
                            addnew_carcompomodel.CommandArgument = crypy.EnCode(Table.Rows[0]["abd_vechiletype"].ToString());
                            Edit_carcompomodel.CommandArgument = Table.Rows[0]["cpm_applytype"].ToString();
                            applytype = Table.Rows[0]["cpm_applytype"].ToString();
                            lbl_applytype.Text = applytype;
                            Vehicle_configuration(Table.Rows[0]["cpm_carcompomodelno"].ToString());
                            Model_number(Table.Rows[0]["cpm_applyno"].ToString());
                            Data_state(Table.Rows[0]["ct_aps_desc"].ToString());
                            ViewState["abd_applystatus"] = Table.Rows[0]["abd_applystatus"].ToString();

                            string[] arr = crypy.wsCarCompoModelValidate(getRadNum());

                            wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
                            string[] arrApply = crypy.wsApplybasedataDetailValid(getRadNum());

                            gv_carmodellist.DataSource = wsCarCmpModel.get_VehicleCarInformation(request_applyno(), request_carcompomodelno(), arr[0].ToString(), arr[1].ToString());
                            gv_carmodellist.DataBind();

                            lbl_vechiletype.Text = Table.Rows[0]["abd_vechiletype"].ToString();
                            //沿用、延伸、沿用及修改 才可編輯
                            if (Table.Rows[0]["cpm_applytype"].ToString() == "2" || Table.Rows[0]["cpm_applytype"].ToString() == "3" || Table.Rows[0]["cpm_applytype"].ToString() == "5")
                            {
                                Edit_carcompomodel.Visible = true;
                            }
                            else
                            {
                                Edit_carcompomodel.Visible = false;
                            }
                        }
                        string compidnoE = Session["compidno"].ToString(); //公司統編
                        DataView dv = new MailParaControl().GetApplyBaseData(lbl_applyno.Text);
                        string carcompomodelno = DeCode(Request["carcompomodelno"].ToString());//車型組編號
                        string EngineFamily = dv[0]["EngineFamily"].ToString(); //引擎族

                        //wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
                        //int keynum = getRadNum();
                        //string[] AD_arr = wsApplybasedataDetailValid(keynum);

                        //20130829 沿用 可新增就算已有舊資料也要可以新增不然無法顯示連結
                        if (applytype == "6" || applytype == "9")
                        {
                            addnew_carcompomodel.Visible = false;
                        }
                        else
                        {
                            addnew_carcompomodel.Visible = true;
                        }
                        ////延伸、修改 沿用 如果為系統第一筆需調整為可編輯
                        //if (!(wsApplyData.IsDateoneExist(compidnoE, carcompomodelno, EngineFamily, AD_arr[0].ToString(), AD_arr[1].ToString())))
                        //{
                        //    //新增車型按鈕
                        //    addnew_carcompomodel.Visible = true;
                        //}
                        //else
                        //{
                        //    //資料狀態 填單中、補件中、拒件
                        //    if (ViewState["abd_applystatus"].Equals("10") || ViewState["abd_applystatus"].Equals("40") || ViewState["abd_applystatus"].Equals("90"))
                        //    {
                        //        //申請型式 新車型、延伸
                        //        if (applytype == "1" || applytype == "3")
                        //        {
                        //            addnew_carcompomodel.Visible = true;
                        //        }
                        //        else
                        //        {
                        //            addnew_carcompomodel.Visible = false;
                        //        }
                        //    }
                        //    else
                        //    {
                        //        addnew_carcompomodel.Visible = false;
                        //    }

                        //}


                    }

                    //addnew_carcompomodel.Visible = false;
                    //gv_carcompomodel_RowDataBound();
                }
                else
                {
                    //取不到申請編號畫面轉走
                    wsCarCmpModel.Dispose();
                    wsCodeTbl.Dispose();
                    crypy.Dispose();
                    MessageBox.Show("無法取得申請編號資料，畫面將轉回首頁！");
                    Response.Redirect("../index.aspx");
                }
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }
    }

    #region 更改的麵包屑傳入的參數
    protected void Page_LoadComplete(object sender, EventArgs e)
    {
        ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text = ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text.Replace("applydatamt/carcompomodelList.aspx", "applydatamt/carcompomodelList.aspx?applyno=" + Server.UrlEncode(Request["applyno"].ToString()));
    }
    #endregion

    /// <summary>
    /// 是否可以編輯，如果申請行為是 2  3  6  9 僅唯獨，延伸出的新車型 可以修改
    /// </summary>
    /// <param name="abd_applystatus"></param>
    /// <returns></returns>
    private bool abd_applystatusNameSpace(string abd_applystatus)
    {
        bool success = false;
        string[] status = new string[] { "2", "3", "9", "6" }; //不能修改，僅能連到唯讀畫面

        foreach (string i in status)
        {

            if (abd_applystatus == i)
            {
                success = true;
            }

        }

        return success;
    }

    private string request_applyno()
    {
        return crypy.DeCode(Request["applyno"].ToString());
    }

    private string request_carcompomodelno()
    {
        return crypy.DeCode(Request["carcompomodelno"].ToString());
    }



    private DataTable GetData()
    {
        string strApplyno = request_applyno();
        string carcompomodelno = request_carcompomodelno();
        int keynum = crypy.getRadNum();
        string[] arr = crypy.wsCarCompoModelValidate(keynum);
        DataSet dsCarCmpModel = wsCarCmpModel.getDataByApplyNo_CarCompoModelNo(strApplyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
        DataTable Table = dsCarCmpModel.Tables[0];

        return Table;
    }

    /// <summary>
    /// 車型組代碼
    /// </summary>
    /// <param name="CodeS"></param>
    private void Vehicle_configuration(string CodeS)//車型組代碼
    {
        Vehicle_configuration_Label1.Text = "車型組(" + CodeS + ")-車型資料";
    }

    /// <summary>
    /// 申請編號
    /// </summary>
    /// <param name="CodeS"></param>
    private void Model_number(string CodeS)//申請編號
    {
        lbl_applyno.Text = CodeS;
    }

    /// <summary>
    /// 資料狀態
    /// </summary>
    /// <param name="CodeS"></param>
    private void Data_state(string CodeS)//資料狀態
    {
        lbl_applystatus.Text = CodeS;
    }



    protected void addnew_carcompomodel_Click(object sender, EventArgs e)
    {
        Response.Redirect("carmodeldatamt_g.aspx?applyno=" + Server.UrlEncode(Request["applyno"].ToString()) + "&carcompomodelno=" + Server.UrlEncode(Request["carcompomodelno"].ToString()) + "&carmodelno=" + "&mode=" + Server.UrlEncode(((Button)(sender)).CommandArgument.ToString()));
    }




    /// <summary>
    /// 控制Del 按鈕是否要顯示在畫面上 申請狀態:不等於 50 則顯示
    /// </summary>
    protected void gv_carmodellist_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            //申請型式=新申請、修改  連到編輯
            //申請型式=沿用、不異動、撤銷  連到唯讀
            //申請型式=延伸 原有車型連到唯讀   新車型(cmdg_beextendmodel=1)連到編輯
            wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
            int keynum = crypy.getRadNum();
            string[] arr = crypy.wsApplybasedataDetailValid(keynum);
            string[] CMarr = crypy.wsCarCompoModelValidate(getRadNum());
            string applyno = lbl_applyno.Text.Trim();
            string carcompomodelno = DataBinder.Eval(e.Row.DataItem, "cmdg_carcompomodelno").ToString().Trim();
            string carmodelno = DataBinder.Eval(e.Row.DataItem, "cmdg_carmodelno").ToString();


            Button Button1_del = (Button)e.Row.FindControl("Button1_del");
            Button1_del.Attributes.Add("0", DataBinder.Eval(e.Row.DataItem, "abd_applystatus").ToString());
            Button1_del.Attributes.Add("1", DataBinder.Eval(e.Row.DataItem, "cmdg_applyno").ToString());
            Button1_del.Attributes.Add("2", DataBinder.Eval(e.Row.DataItem, "cmdg_carcompomodelno").ToString());
            Button1_del.Attributes.Add("3", DataBinder.Eval(e.Row.DataItem, "cmdg_carmodelno").ToString());

            System.Web.UI.HtmlControls.HtmlContainerControl div_Measurement_Report = (System.Web.UI.HtmlControls.HtmlContainerControl)e.Row.FindControl("Measurement_Report");
            LinkButton LinkButton1_Measurement_Report = (LinkButton)e.Row.FindControl("LinkButton1_Measurement_Report");
            HyperLink hl_carstylename = (HyperLink)e.Row.FindControl("hl_carstylename");

            string s_ntr_testrptno = DataBinder.Eval(e.Row.DataItem, "ntr_testrptno").ToString() + "<br>";
            string s_noise = "加速噪音值:" + DataBinder.Eval(e.Row.DataItem, "ntr_speedupnoise").ToString() + "<br>原地噪音值:" + DataBinder.Eval(e.Row.DataItem, "ntr_staynoise").ToString();
            Label lbl_noise = (Label)e.Row.FindControl("lbl_noise");
            HyperLink hlk_testrptno = (HyperLink)e.Row.FindControl("hlk_testrptno");
            hlk_testrptno.NavigateUrl = string.Format("noisetestrpt_readnoly.aspx?applyno={0}&carcompomodelno={1}&carmodelno={2}", Server.UrlEncode(crypy.EnCode(applyno)), Server.UrlEncode(EnCode(carcompomodelno)), Server.UrlEncode(EnCode(carmodelno)));
            hlk_testrptno.Text = s_ntr_testrptno;
            lbl_noise.Text = s_noise;


            string compidnoE = Session["compidno"].ToString(); //公司統編
            DataView dv = new MailParaControl().GetApplyBaseData(applyno);
            string EngineFamily = dv[0]["EngineFamily"].ToString(); //引擎族


            if (wsApplyData.IsDateoneExist(compidnoE, carcompomodelno, EngineFamily, arr[0].ToString(), arr[1].ToString())) //沿用 如果為系統第一筆需調整為可編輯
            {
                hl_carstylename.NavigateUrl = "carmodeldata_readonly.aspx?applyno=" + Server.UrlEncode(EnCode(applyno)) + "&carcompomodelno=" + Server.UrlEncode(EnCode(carcompomodelno)) + "&carmodelno=" + Server.UrlEncode(EnCode(carmodelno)) + "&mode=" + Server.UrlEncode(EnCode(DataBinder.Eval(e.Row.DataItem, "abd_vechiletype").ToString().Trim()));
            }
            else
            {
                hl_carstylename.NavigateUrl = "carmodeldatamt_g.aspx?applyno=" + Server.UrlEncode(EnCode(applyno)) + "&carcompomodelno=" + Server.UrlEncode(EnCode(carcompomodelno)) + "&carmodelno=" + Server.UrlEncode(EnCode(carmodelno)) + "&mode=" + Server.UrlEncode(EnCode(DataBinder.Eval(e.Row.DataItem, "abd_vechiletype").ToString().Trim()));
            }

            if (LinkButton1_Measurement_Report != null && div_Measurement_Report != null)
            {
                string report_no = LinkButton1_Measurement_Report.CommandArgument.ToString().Split(',')[0];
                string cpm_applytype = LinkButton1_Measurement_Report.CommandArgument.ToString().Split(',')[1];
                if (!string.IsNullOrEmpty(report_no))
                {
                    DataTable dt_testreport = wsCarCmpModel.Set_Measurement_Report(report_no);
                    if (dt_testreport.Rows.Count > 0)
                    {
                        Label reportNa = new Label();
                        reportNa.Text = dt_testreport.Rows[0]["ntr_testrptno"].ToString();

                        Label speedupnoise = new Label();
                        reportNa.Text = "加速噪音值:" + dt_testreport.Rows[0]["ntr_speedupnoise"].ToString();

                        Label staynoise = new Label();
                        reportNa.Text = "原地噪音值:" + dt_testreport.Rows[0]["ntr_staynoise"].ToString();
                    }

                    System.Web.UI.HtmlControls.HtmlGenericControl br = new System.Web.UI.HtmlControls.HtmlGenericControl();
                    br.TagName = "br";
                    //div_Measurement_Report.Controls.Add(reportNa);
                    //div_Measurement_Report.Controls.Add(br);
                    //div_Measurement_Report.Controls.Add(speedupnoise);
                    //div_Measurement_Report.Controls.Add(br);
                    //div_Measurement_Report.Controls.Add(staynoise);
                }

            }

            //20130509刪除按鈕調整成ALWAYS出現
            //if (Button1_del != null)
            //{
            //    Button1_del.Visible = false;
            //    if (!string.IsNullOrEmpty(Button1_del.CommandArgument.ToString().Trim()))
            //    {
            //        //資料狀態 填單中、補件中、拒件
            //        if (Button1_del.CommandArgument.ToString().Trim() == "10" || Button1_del.CommandArgument.ToString().Trim() == "40" || Button1_del.CommandArgument.ToString().Trim() == "90")
            //        {
            //            //申請型式 新車型
            //            if (lbl_applytype.Text == "1")
            //            {
            //                //刪除按鈕
            //                Button1_del.Visible = true;
            //            }
            //            else
            //            {
            //                Button1_del.Visible = false;
            //            }
            //        }
            //        else
            //        {
            //            Button1_del.Visible = false;
            //        }
            //    }
            //}

            Label GridView1_berepresentativevehicle = (Label)e.Row.FindControl("GridView1_berepresentativevehicle");
            if (GridView1_berepresentativevehicle != null)
                if (GridView1_berepresentativevehicle.Text.Trim().ToLower() == "y")
                { GridView1_berepresentativevehicle.Text = "是"; }
                else { GridView1_berepresentativevehicle.Text = "否"; }


        }
    }
    protected void Button1_del_Click(object sender, EventArgs e)
    {
        bool success = false;
        //string[] arry = ((Button)(sender)).CommandArgument;
        //CommandArgument='<%# Eval("cmdg_applyno")+","+ Eval("cmdg_carcompomodelno")+","+Eval("cmdg_carmodelno") %>'
        string applystatus = ((Button)(sender)).Attributes["0"].ToString();
        string cmdg_applyno = ((Button)(sender)).Attributes["1"].ToString();
        string cmdg_carcompomodelno = ((Button)(sender)).Attributes["2"].ToString();
        string cmdg_carmodelno = ((Button)(sender)).Attributes["3"].ToString();

        try
        {

            //string applystatus = ((Button)(sender)).CommandArgument.ToString().Trim();

            if (!string.IsNullOrEmpty(applystatus))
            {

                if (applystatus != "50" || applystatus != "55")
                {
                    if (!string.IsNullOrEmpty(cmdg_applyno) && !string.IsNullOrEmpty(cmdg_carcompomodelno) && !string.IsNullOrEmpty(cmdg_carmodelno))
                    {
                        if (wsCarCmpModel.Del_Vehicle_Car_Information(cmdg_applyno, cmdg_carcompomodelno, cmdg_carmodelno))
                        {
                            success = true;
                        }
                    }
                }
                else
                {
                    alert.AlertMessage("審核完成-不能刪除");
                }

            }
        }
        catch (Exception ex) { alert.Alert(ex.Message); }

        if (success)
        {
            Response.Write("<script type=\"text/javascript\">alert('刪除成功');window.location.href=window.location.href;</script>");
        }
        else
        {
            alert.AlertMessage("刪除失敗");
        }
    }

    private void GetViewStats(string value)
    {
        ViewState["Pvaluect_at_id"] = value;
    }

    /// <summary>
    /// Cmdg_applyno , Cmdg_carcompomodelno , Cmdg_carmodelno
    /// </summary>
    /// <param name="n"></param>
    /// <returns></returns>
    private string setViewStats(int n)
    {
        string value = string.Empty;

        if (ViewState["Pvaluect_at_id"] != null)
        {
            if (!string.IsNullOrEmpty(ViewState["Pvaluect_at_id"].ToString().Trim()))
            {
                value = ViewState["Pvaluect_at_id"].ToString().Split(',')[n];
            }
        }

        return value;
    }

    protected void Edit_carcompomodel_Click(object sender, EventArgs e)
    {
        GridView2.Visible = true;
        gv_carmodellist.Visible = false;
        string cpm_applytype = ((Button)(sender)).CommandArgument.ToString();

        DataTable Table = wsCarCmpModel.Edit_chassis_number(request_applyno(), request_carcompomodelno(), cpm_applytype, lbl_vechiletype.Text);

        GridView2.DataSource = Table;
        GridView2.DataBind();
        Button1.Text = "上一步";
        Button1.CommandArgument = "up";

        if (GridView2.Rows.Count > 0)
        {
            #region 車身編號/引擎號碼
            Cryptography crypy = new Cryptography();
            wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
            int keynum = crypy.getRadNum();
            string[] arr_apply = crypy.wsApplybasedataDetailValid(keynum);
            DataTable dt_additionalbodyno = wsApplyData.get_Nv_additionalbodyno(request_applyno(), request_carcompomodelno(), Table.Rows[0]["cmdg_carmodelno"].ToString(), arr_apply[0].ToString(), arr_apply[1].ToString());
            #endregion
            
            
            GetViewStats(Table.Rows[0]["cmdg_applyno"].ToString() + "," + Table.Rows[0]["cmdg_carcompomodelno"].ToString() + "," + Table.Rows[0]["cmdg_carmodelno"].ToString());

            GridView2.HeaderRow.Cells[3].Visible = false;
            GridView2.HeaderRow.Cells[4].Visible = false;
            if (cpm_applytype.Trim() == "2" || cpm_applytype.Trim() == "3" || cpm_applytype.Trim() == "5")
            {
                GridView2.HeaderRow.Cells[3].Visible = true;
                GridView2.HeaderRow.Cells[4].Visible = true;
                Button1.Text = "完成填寫";
                Button1.CommandArgument = "complate";
            }


            int i = 0;
            int RowCount = GridView2.Rows.Count;

            for (i = 0; i < RowCount; i++)
            {
                TextBox txt_bodyno = (TextBox)GridView2.Rows[i].FindControl("txt_bodyno");
                TextBox txt_engineno = (TextBox)GridView2.Rows[i].FindControl("txt_engineno");

                txt_bodyno.Visible = GridView2.HeaderRow.Cells[3].Visible;
                txt_engineno.Visible = GridView2.HeaderRow.Cells[3].Visible;

                if (dt_additionalbodyno.Rows.Count > 0)
                {
                    txt_bodyno.Text = dt_additionalbodyno.Rows[0]["abn_bodyno"].ToString().Trim();
                    txt_engineno.Text = dt_additionalbodyno.Rows[0]["abn_engineno"].ToString().Trim();
                }
            }
        }

        Button1.Visible = GridView2.Visible;
    }
    protected void Button1_Click(object sender, EventArgs e)
    {
        #region 特殊字元判斷-沒有MasterPage
        foreach (Control ctl in Page.Form.Controls)
        {
            Control content = (Control)ctl;
            foreach (Control c in content.Controls)
            {
                if (c is TextBox)
                {
                    TextBox objTextBox = default(TextBox);
                    objTextBox = (TextBox)c;
                    if ((tbWord(objTextBox.Text)))
                    {
                        MessageBox.Show(objTextBox.Text + ":含有系統不允許的特殊字元");
                        return;
                    }
                }
            }
        }
        #endregion
        try
        {

            string DefalutString = ((Button)(sender)).CommandArgument.ToString();

            if (DefalutString.Trim().ToLower() == "complate")
            {


                bool success = true;
                int G2Count = GridView2.Rows.Count;
                int i;

                for (i = 0; i < G2Count; i++)
                {
                    TextBox txt_bodyno = (TextBox)GridView2.Rows[i].FindControl("txt_bodyno");
                    TextBox txt_engineno = (TextBox)GridView2.Rows[i].FindControl("txt_engineno");
                    if (!string.IsNullOrEmpty(txt_bodyno.Text.Trim()) && txt_bodyno.Text.Trim().Length <= Chassis_number_limt && txt_engineno.Text.Length <= Engine_number_limt)
                    {
                        string dd = setViewStats(2).Trim();
                        if (wsCarCmpModel.chassis_number_Exist(setViewStats(0).Trim(), setViewStats(1).Trim(), setViewStats(2).Trim(), txt_bodyno.Text))
                        {
                            if (!wsCarCmpModel.insert_chassis_number(setViewStats(0).Trim(), setViewStats(1).Trim(), setViewStats(2).Trim(), txt_bodyno.Text, txt_engineno.Text, DeCode(Session["account"].ToString())))
                            {
                                success = false;
                                alert.AlertMessage("新增第" + (i + 1) + "筆資料，發生錯誤");
                                break;
                            }
                        }
                        else
                        {
                            if (!wsCarCmpModel.update_chassis_number(setViewStats(0).Trim(), setViewStats(1).Trim(), setViewStats(2).Trim(), txt_bodyno.Text, txt_engineno.Text, DeCode(Session["account"].ToString())))
                            {
                                success = false;
                                alert.AlertMessage("新增第" + (i + 1) + "筆資料，發生錯誤");
                                break;
                            }
                            //success = false; alert.AlertMessage("新增第" + (i + 1) + "筆資料，資料已存在"); break; 
                        }
                    }
                    else
                    {
                        success = false;
                        if (string.IsNullOrEmpty(txt_bodyno.Text.Trim()))
                        {
                            alert.AlertMessage("車身號碼必填");
                        }
                        else
                        {

                            if (txt_bodyno.Text.Trim().Length > Chassis_number_limt) { alert.AlertMessage("車身號碼必填小於" + Chassis_number_limt); }
                            else
                            { alert.AlertMessage("引擎號碼必填小於" + Engine_number_limt); }
                        }

                        break;
                    }
                }
                if (success)
                {
                    alert.AlertMessage("新增完成"); gv_carmodellist.Visible = true; GridView2.Visible = false;

                }
                else { alert.AlertMessage("新增第" + (i + 1) + "筆資料，發生錯誤"); }
            }
            else { gv_carmodellist.Visible = true; GridView2.Visible = false; }
        }
        catch (Exception ex) { alert.AlertMessage(ex.Message); }

        Button1.Visible = GridView2.Visible;
    }
}