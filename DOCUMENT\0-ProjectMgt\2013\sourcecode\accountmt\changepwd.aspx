﻿<%@ Page Title="" Language="C#" MasterPageFile="~/masterpage/MasterPage.master" AutoEventWireup="true" CodeFile="changepwd.aspx.cs" Inherits="accountmt_changepwd" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" Runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" Runat="Server">
<div class="fullheader">
        <table width="100%" cellspacing="0" cellpadding="0" class="fullheaderinfo">
            <tr>
                <td valign="top">
                    <b>變更密碼</b>
                </td>
                <td align="right" valign="top" class="font_loginInfo">
                </td>
            </tr>
        </table>
    </div>
    <!--{* fullheader end *}-->
    <div class="fullcontent">
        <div class="formstyle">
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="font_fullcontent">
                <tr>
                    <td align="right">
                        舊密碼：
                    </td>
                    <td>
                        <asp:TextBox ID="txt_old_pwd" runat="server" TextMode="Password" MaxLength="20" Width="185px"
                            CssClass="inputTypeS"></asp:TextBox>
                        <asp:RequiredFieldValidator ID="rfOldPwd" runat="server" Display="None" ControlToValidate="txt_old_pwd"
                            ErrorMessage="舊密碼不可空白"></asp:RequiredFieldValidator>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        新密碼：
                    </td>
                    <td>
                        <asp:TextBox ID="txt_new_pwd" runat="server" TextMode="Password" MaxLength="20" Width="185px"
                            CssClass="inputTypeS"></asp:TextBox>
                        <asp:RequiredFieldValidator ID="rfNewPwd" runat="server" Display="None" ControlToValidate="txt_new_pwd"
                            ErrorMessage="新密碼不可空白"></asp:RequiredFieldValidator>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        確認密碼：
                    </td>
                    <td>
                        <asp:TextBox ID="txt_check_pwd" runat="server" TextMode="Password" MaxLength="20"
                            Width="185px" CssClass="inputTypeS"></asp:TextBox>
                        <asp:RequiredFieldValidator ID="rfCheckPwd" runat="server" Display="None" ControlToValidate="txt_check_pwd"
                            ErrorMessage="確定密碼不可空白"></asp:RequiredFieldValidator>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        請輸入驗證碼：
                    </td>
                    <td>
                        <asp:TextBox ID="txt_ValidateCode" runat="server" MaxLength="4" Width="72px" Height="22px"
                            CssClass="inputTypeS"></asp:TextBox>
                        <asp:RequiredFieldValidator ID="rfValidateCode" runat="server" Display="None" ControlToValidate="txt_ValidateCode"
                            ErrorMessage="驗證碼不可空白"></asp:RequiredFieldValidator>
                        <br />
                        ＊請將下方顯示的驗證碼四位數字輸入到此欄位
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        驗證碼：
                    </td>
                    <td>
                        <img src="../Sub_Sys/validatecode.aspx" alt="驗證碼" />
                        <asp:LinkButton ID="lbtn_refresh" runat="server" Text="重取驗證碼" 
                            CausesValidation="False" onclick="lbtn_refresh_Click"></asp:LinkButton>
                    </td>
                </tr>
                <tr>
                    <td colspan="2" align="center">
                        <asp:Button ID="btn_OK" runat="server" Text="變更" OnClientClick="if(confirm('是否將舊密碼變更為新密碼??')==false){return false;}"
                            OnClick="btn_OK_Click" CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'"
                            onmouseout="this.className='btn_mouseout'" />
                    </td>
                </tr>
            </table>
        </div>
    </div>
    <!--{* fullcontent end *}-->
    <div class="fullfooter">
    </div>
    <!--{* content end *}-->
    <asp:ValidationSummary ID="ValidationSummary1" runat="server" ShowMessageBox="True"
        ShowSummary="False" />
</asp:Content>

