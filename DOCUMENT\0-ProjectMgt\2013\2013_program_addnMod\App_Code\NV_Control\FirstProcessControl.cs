﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data.SqlClient;
using System.Data;
using System.Text;
using System.Web.UI.WebControls;

/// <summary>
/// Summary description for FirstProcessControl
/// </summary>
public class FirstProcessControl
{
    public FirstProcessControl()
    {
    }
    Cryptography crypy = new Cryptography();

    public string TagFirstProcess(string applyNo)
    {
        SqlCommand sqlCmd = new SqlCommand(@" exec pr_get_InfoOfWorkDays @ApplyNo");
        sqlCmd.Parameters.AddWithValue("@ApplyNo", applyNo);
        DataView dv = Common.Data_noisevalidation.runParaCmd(sqlCmd);
        string ApplyNo = dv[0]["ApplyNo"].ToString(); //申請編號
        string Compname = dv[0]["Compname"].ToString(); //申請者名稱

        string ApplyTypeStr = dv[0]["ApplyTypeStr"].ToString(); //申請內容
        string ContactName = dv[0]["ContactName"].ToString(); //聯絡人
        string ContactTel = dv[0]["ContactTel"].ToString(); //聯絡人電話

        string ChargeDate = dv[0]["ChargeDate"].ToString(); //收件日期
        string WorkDays = dv[0]["WorkDays"].ToString(); //辦理日期


        string RemnantValidDays = dv[0]["RemnantValidDays"].ToString(); //剩餘審驗天數
        string RePairDays = dv[0]["RePairDays"].ToString(); //已補件天數
        string RemnantRePairDays = dv[0]["RemnantRePairDays"].ToString();//剩餘補件天數

        //50已上傳平台55歸檔60消件99註銷
        if (new FirstProcessControl().IsValidationStatus(ApplyNo, "50") || new FirstProcessControl().IsValidationStatus(ApplyNo, "55") || new FirstProcessControl().IsValidationStatus(ApplyNo, "60") || new FirstProcessControl().IsValidationStatus(ApplyNo, "99"))
        {
            RemnantValidDays = "-"; //剩餘審驗天數
            RePairDays = "-"; //已補件天數
            RemnantRePairDays = "-";//剩餘補件天數
        }

        string ApplyStatusCn = dv[0]["ApplyStatusCn"].ToString(); //資料狀態

        string EngineFamily = dv[0]["EngineFamily"].ToString(); //引擎族
        string CarYear = dv[0]["CarYear"].ToString(); //車型年
        string FactoryName = dv[0]["FactoryName"].ToString(); //製造廠

        string CarType = dv[0]["CarType"].ToString(); //交通工具種類
        string ApplicationDate = dv[0]["ApplicationDate"].ToString(); //適用期別

        string TwoCertMethod = dv[0]["TwoCertMethod"].ToString(); //兩證合一申請方式
        string OfficeDocDate = dv[0]["OfficeDocDate"].ToString(); //申請函發文日期
        string OfficeDocNo = dv[0]["OfficeDocNo"].ToString(); //申請函發文字號
        string CertidocAddr = dv[0]["CertidocAddr"].ToString(); // 合格證郵寄地址
        string OfficeFileId = crypy.Server.UrlEncode(crypy.EnCode(dv[0]["OfficeFileId"].ToString())); //申請函發文字PDF

        string PayDataStr = dv[0]["PayDataStr"].ToString(); //繳費資料

        string FirstProcess = dv[0]["FirstProcess"].ToString(); //初審人/日期
        string ApplyTypeCount = dv[0]["ApplyTypeCount"].ToString();//車型組數
        string CarCompoCount = dv[0]["CarCompoCount"].ToString();//車型數

        string SecondProcess = dv[0]["SecondProcess"].ToString(); //複審人/日期

        string ClassBProcess = dv[0]["ClassBProcess"].ToString(); //二級主管/日期

        string ClassAProcess = dv[0]["ClassAProcess"].ToString(); //一級主管/日期

        StringBuilder sb = new StringBuilder();
        sb.Append("<table border='0' cellspacing='0' cellpadding='0' width='100%' class='font_fullcontent'>");
        sb.Append("<tr><td align='right'>申請編號：</td><td>" + ApplyNo + "</td><td align='right'>申請者名稱：</td><td colspan='3'>" + Compname + "</td></tr>");
        sb.Append("<tr><td align='right'>申請內容：</td><td>" + ApplyTypeStr + "</td><td align='right'>聯絡人：</td><td>" + ContactName + "</td><td align='right'>聯絡人電話：</td><td>" + ContactTel + "</td></tr>");
        sb.Append("<tr><td align='right'>收件日期：</td><td>" + ChargeDate + "</td><td align='right'>辦理期限：</td><td>" + WorkDays + "</td><td align='right'>剩餘審驗天數：</td><td>" + RemnantValidDays + "</td></tr>");
        sb.Append("<tr><td align='right'>已補件天數：</td><td>" + RePairDays + "</td><td align='right'>剩餘補件天數：</td><td>" + RemnantRePairDays + "</td><td align='right'>資料狀態：</td><td>" + ApplyStatusCn + "</td></tr>");
        sb.Append("<tr><td align='right'>引擎族：</td><td>" + EngineFamily + "</td><td align='right'>車型年：</td><td>" + CarYear + "</td><td align='right'>製造廠：</td><td>" + FactoryName + "</td></tr>");
        sb.Append("<tr><td align='right'>交通工具種類：</td><td>" + CarType + "</td><td align='right'>適用期別：</td><td>" + ApplicationDate + "</td><td align='right'>&nbsp;</td><td>&nbsp;</td></tr>");        
        sb.Append("<tr><td align='right'>兩證合一申請方式：</td><td>" + TwoCertMethod + "</td><td align='right'>申請函發文日期：</td><td>" + OfficeDocDate + "</td><td align='right'>申請函發文字號：</td><td>" + OfficeDocNo + "&nbsp;&nbsp;<a href='../FileDownloadSeal.aspx?File=" + OfficeFileId + "'><img src='../images/download-icon.png' width='24' height='24'></a> </td></tr>");
        sb.Append("<tr><td align='right'>合格證寄發地址：</td><td colspan='5'>" + CertidocAddr + "</td></tr>");
        sb.Append("<tr><td align='right'>繳費資料：</td><td colspan='5'>" + PayDataStr + "</td></tr>");
        sb.Append("<tr><td align='right'>初審人/日期：</td><td>" + FirstProcess + "</td><td align='right'>車型組數：</td><td>" + ApplyTypeCount + "</td><td align='right'>車型數：</td><td>" + CarCompoCount + "</td></tr>");
        sb.Append("<tr><td align='right'>複審人/日期：</td><td colspan='5'>" + SecondProcess + "</td></tr>");
        sb.Append("<tr><td align='right'>二級主管/日期：</td><td colspan='5'>" + ClassBProcess + "</td></tr>");
        sb.Append("<tr><td align='right'>一級主管/日期：</td><td colspan='5'>" + ClassAProcess + "</td></tr>");
        sb.Append("</table>");
        return sb.ToString();
    }

    public List<ListItem> LisCarCompoModel(string ApplyNo)
    {
        SqlCommand sqlCmd = new SqlCommand(@"
--declare @ApplyNo varchar(9) set @ApplyNo='*********'

select 
cpm_carcompomodelno+'('+dbo.fn_CastApplyTypeCn(cpm_applytype)+')' CarCompoModelCn
,cpm_carcompomodelno CarCompoModel
from dbo.nv_carcompomodel where cpm_applyno=@ApplyNo and cpm_applytype not in ('6','9') --不異動資料不顯示
");
        sqlCmd.Parameters.AddWithValue("@ApplyNo", ApplyNo);
        DataView dv = Common.Data_noisevalidation.runParaCmd(sqlCmd);
        return DataUtils.ConvertDataViewToList(dv);
    }

    public List<ListItem> LisCarCompoModelNG(string ApplyNo)
    {
        SqlCommand sqlCmd = new SqlCommand(@"
select 
cpm_carcompomodelno+'('+dbo.fn_CastApplyTypeCn(cpm_applytype)+')' CarCompoModelCn
,cpm_carcompomodelno CarCompoModel
from dbo.nv_carcompomodel where cpm_applyno=@ApplyNo and cpm_applytype not in ('6','9')
and cpm_carcompomodelno in (select DISTINCT vcn_carcompomodelno from nv_validationchecknote
where vcn_applyno=@ApplyNo and vcn_validationitemstatus='NG')
");
        sqlCmd.Parameters.AddWithValue("@ApplyNo", ApplyNo);
        DataView dv = Common.Data_noisevalidation.runParaCmd(sqlCmd);
        return DataUtils.ConvertDataViewToList(dv);
    }

    public string StrCarCompoNo(string ApplyNo)
    {
        List<ListItem> lis = LisCarCompoModel(ApplyNo);
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < lis.Count; i++)
        {
            if (!sb.Length.Equals(0)) { sb.Append("、"); }
            string carcompono = lis[i].Value;
            sb.Append(carcompono);
        }
        return sb.ToString();
    }
    public string StrCarCompoNoNG(string ApplyNo)
    {
        List<ListItem> lis = LisCarCompoModelNG(ApplyNo);
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < lis.Count; i++)
        {
            if (!sb.Length.Equals(0)) { sb.Append("、"); }
            string carcompono = lis[i].Value;
            sb.Append(carcompono);
        }
        return sb.ToString();
    }

    public List<ListItem> LisMostusedOpinion()
    {
        SqlCommand sqlCmd = new SqlCommand(@"
select ct_muo_desc,ct_muo_desc from nv_codetbl_mostusedopinion
");
        DataView dv = Common.Data_noisevalidation.runParaCmd(sqlCmd);
        return DataUtils.ConvertDataViewToList(dv);
    }

    public DataView ListCarCompoModelInfo(string ApplyNo, string CarCompoModelNo)
    {
        SqlCommand sqlCmd = new SqlCommand(@"
--declare @ApplyNo varchar(9) set @ApplyNo='*********'
--declare @CarCompoModelNo varchar(15) set @CarCompoModelNo='*********_5'
--declare @Account varchar(20) set @Account='U1234657'

exec dbo.pr_get_validationtable_data_by_applyno @ApplyNo,@CarCompoModelNo,@Account
--orderid, vcn_applyno, vcn_validationitemno, vil_desc, vcn_opinion, vilup_desc
");
        sqlCmd.Parameters.AddWithValue("@ApplyNo", ApplyNo);
        sqlCmd.Parameters.AddWithValue("@CarCompoModelNo", CarCompoModelNo);
        sqlCmd.Parameters.AddWithValue("@Account", new BaseAdminPage().GetAgentAccount());
        DataView dv = Common.Data_noisevalidation.runParaCmd(sqlCmd);
        return dv;
    }

    public void UpdateValidationCheckNote(string ApplyNo, string CarCompoModelNo, string ValidationItemNo, string NotNeeded, string Opinion, string ValidationItemStatus)
    {
        SqlCommand sqlCmd = new SqlCommand(@"
update dbo.nv_validationchecknote set
vcn_notneeded=@NotNeeded
,vcn_opinion=@Opinion
,vcn_validationitemstatus=@ValidationItemStatus
,vcn_modaccount=@Account
,vcn_moddate=getdate()
where vcn_applyno=@ApplyNo
and vcn_carcompomodelno=@CarCompoModelNo
and vcn_validationitemno=@ValidationItemNo
");
        sqlCmd.Parameters.AddWithValue("@ApplyNo", ApplyNo);
        sqlCmd.Parameters.AddWithValue("@CarCompoModelNo", CarCompoModelNo);
        sqlCmd.Parameters.AddWithValue("@ValidationItemNo", ValidationItemNo);
        sqlCmd.Parameters.AddWithValue("@NotNeeded", NotNeeded);
        sqlCmd.Parameters.AddWithValue("@Opinion", Opinion);
        sqlCmd.Parameters.AddWithValue("@ValidationItemStatus", ValidationItemStatus);
        sqlCmd.Parameters.AddWithValue("@Account", new BaseAdminPage().GetAgentAccount());
        Common.Data_noisevalidation.runParaCmd1(sqlCmd);
    }

    public bool ChackCarCompoModelIsOK(string ApplyNo)
    {
        SqlCommand sqlCmd = new SqlCommand(@"
--declare @ApplyNo varchar(9) set @ApplyNo='*********'
declare @NoCheckCount int
;
with C0 as(
	select cpm_carcompomodelno N from dbo.nv_carcompomodel where cpm_applyno=@ApplyNo and cpm_applytype not in ('6','9') -- 不異動,撤銷
	EXCEPT
	select distinct vcn_carcompomodelno N from nv_validationchecknote where vcn_applyno=@ApplyNo
)
select @NoCheckCount=Count(N) from C0
if(@NoCheckCount<>0)
begin
	select 0
	return
end
if exists(select * from nv_validationchecknote where vcn_applyno=@ApplyNo and vcn_validationitemstatus<>'OK')
begin
	select 0
	return
end
select 1
");
        sqlCmd.Parameters.AddWithValue("@ApplyNo", ApplyNo);
        object obj = Common.Data_noisevalidation.runScalar(sqlCmd);
        return obj.ToString() == "1" ? true : false;
    }

    public bool ChackCarCompoModelIsOpinion(string ApplyNo, string ValidationItemno)
    {
        SqlCommand sqlCmd = new SqlCommand(@"
--declare @ApplyNo varchar(9) set @ApplyNo='*********'
declare @NoCheckCount int
;
with C0 as(
	select cpm_carcompomodelno N from dbo.nv_carcompomodel where cpm_applyno=@ApplyNo and cpm_applytype not in ('6','9') -- 不異動,撤銷
	EXCEPT
	select distinct vcn_carcompomodelno N from nv_validationchecknote where vcn_applyno=@ApplyNo
)
select @NoCheckCount=Count(N) from C0
if(@NoCheckCount<>0)
begin
	select 0
	return
end
if exists(select * from nv_validationchecknote where vcn_applyno=@ApplyNo and vcn_validationitemno=@ValidationItemno and vcn_opinion ='')
begin
	select 0
	return
end
select 1
");
        sqlCmd.Parameters.AddWithValue("@ApplyNo", ApplyNo);
        sqlCmd.Parameters.AddWithValue("@ValidationItemno", ValidationItemno);
        object obj = Common.Data_noisevalidation.runScalar(sqlCmd);
        return obj.ToString() == "1" ? true : false;
    }

    public void ChangeApplyStatus(string ApplyNo, string ApplyStatus)
    {
        SqlCommand sqlCmd = new SqlCommand(@"
update dbo.nv_applybasedata set 
abd_applystatus=@ApplyStatus
where abd_applyno=@ApplyNo
");
        sqlCmd.Parameters.AddWithValue("@ApplyStatus", ApplyStatus);
        sqlCmd.Parameters.AddWithValue("@ApplyNo", ApplyNo);
        Common.Data_noisevalidation.runParaCmd1(sqlCmd);
    }
    public void UpdateCertReceipt(string ApplyNo, string Certdocno, string Certdocissuedate, string Receiptno)
    {
        SqlCommand sqlCmd = new SqlCommand(@"
                    if @Certdocissuedate <> ''
                    begin 
	                    update dbo.nv_applybasedata 
                        set 
                        abd_certdocno=@Certdocno --合格證號
                        ,abd_certdocissuedate=@Certdocissuedate --核發日期欄位
                        ,abd_receiptno=@Receiptno  --收據編號
                        where abd_applyno=@ApplyNo
                    end 
                    else
                        begin 
                        update dbo.nv_applybasedata 
                        set 
                        abd_certdocno=@Certdocno --合格證號
                        ,abd_receiptno=@Receiptno  --收據編號
                        where abd_applyno=@ApplyNo
                    end 
                ");
        sqlCmd.Parameters.AddWithValue("@Certdocno", Certdocno);
        sqlCmd.Parameters.AddWithValue("@Certdocissuedate", Certdocissuedate);
        sqlCmd.Parameters.AddWithValue("@Receiptno", Receiptno);
        sqlCmd.Parameters.AddWithValue("@ApplyNo", ApplyNo);
        Common.Data_noisevalidation.runParaCmd1(sqlCmd);
    }
    public void LogValidationProcess(string ApplyNo, object ApplyOpinion, string ApplyStatus)
    {
        SqlCommand sqlCmd = new SqlCommand(@"
insert into dbo.nv_validationprocess(vp_applyno, vp_applystatus, vp_applyopinion, vp_execdate, vp_execaccount)
values(@ApplyNo, @ApplyStatus, @ApplyOpinion, getdate(), @Account)
");
        sqlCmd.Parameters.AddWithValue("@ApplyNo", ApplyNo);
        sqlCmd.Parameters.AddWithValue("@ApplyOpinion", ApplyOpinion);
        sqlCmd.Parameters.AddWithValue("@ApplyStatus", ApplyStatus);
        sqlCmd.Parameters.AddWithValue("@Account", new BaseAdminPage().GetAgentAccount());
        Common.Data_noisevalidation.runParaCmd1(sqlCmd);
    }

    public string SetValidationCheckNote(string ApplyNo)
    {
        SqlCommand sqlCmd = new SqlCommand(@"
exec dbo.pr_set_validationchecknote @ApplyNo, @Account
");
        sqlCmd.Parameters.AddWithValue("@ApplyNo", ApplyNo);
        sqlCmd.Parameters.AddWithValue("@Account", new BaseAdminPage().GetAgentAccount());
        object obj = Common.Data_noisevalidation.runScalar(sqlCmd);
        return obj.ToString();
    }

    public DataView ListExport(string Type)
    {
        SqlCommand sqlCmd = new SqlCommand(@"exec pr_get_export_cname @Type");
        sqlCmd.Parameters.AddWithValue("@Type", Type);
        DataView dv = Common.Data_noisevalidation.runParaCmd(sqlCmd);
        return dv;
    }

    public DataView ListExportData(string s_sqltitle, string databelongcompname, string applyno, string applystatus, string enginefamily, string carbrand, string carcompomodelno, string vechiletype, string adaptstandarddate, string cartype, string applysdate, string applyedate, string validatedsdate, string validatededate)
    {
        SqlCommand cmd = new SqlCommand(@"exec pr_get_ExportDataContent 
                        @s_sqltitle
                       ,@databelongcompname 
                       ,@applyno 
                       ,@applystatus 
                       ,@enginefamily
                       ,@carbrand
                       ,@carcompomodelno
                       ,@vechiletype
                       ,@adaptstandarddate
                       ,@cartype
                       ,@applysdate
                       ,@applyedate
                       ,@validatedsdate
                       ,@validatededate");
        cmd.Parameters.AddWithValue("@s_sqltitle", s_sqltitle);
        cmd.Parameters.AddWithValue("@databelongcompname", databelongcompname);
        cmd.Parameters.AddWithValue("@applyno", applyno);
        cmd.Parameters.AddWithValue("@applystatus", applystatus);
        cmd.Parameters.AddWithValue("@enginefamily", enginefamily);
        cmd.Parameters.AddWithValue("@carbrand", carbrand);
        cmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
        cmd.Parameters.AddWithValue("@vechiletype", vechiletype);
        cmd.Parameters.AddWithValue("@adaptstandarddate", adaptstandarddate);
        cmd.Parameters.AddWithValue("@cartype", cartype);
        cmd.Parameters.AddWithValue("@applysdate", applysdate);
        cmd.Parameters.AddWithValue("@applyedate", applyedate);
        cmd.Parameters.AddWithValue("@validatedsdate", validatedsdate);
        cmd.Parameters.AddWithValue("@validatededate", validatededate);
        DataView dv = Common.Data_noisevalidation.runParaCmd(cmd);
        return dv;
    }

    public bool IsValidationExist(string ApplyNo, string ApplyStatus)
    {
        SqlCommand sqlCmd = new SqlCommand(@"
if exists(select * from dbo.nv_applybasedata where abd_applyno=@ApplyNo and abd_applystatus=@ApplyStatus)
begin
	select 1
	return
end
select 0
");
        sqlCmd.Parameters.AddWithValue("@ApplyNo", ApplyNo);
        sqlCmd.Parameters.AddWithValue("@ApplyStatus", ApplyStatus);
        object obj = Common.Data_noisevalidation.runScalar(sqlCmd);
        return obj.ToString() == "1" ? true : false;
    }
    public bool IsValidationStatus(string ApplyNo, string ApplyStatus)
    {
        SqlCommand sqlCmd = new SqlCommand(@"
if exists(select * from dbo.nv_applybasedata where abd_applyno=@ApplyNo and abd_applystatus=@ApplyStatus)
begin
	select 1
	return
end
select 0
");
        sqlCmd.Parameters.AddWithValue("@ApplyNo", ApplyNo);
        sqlCmd.Parameters.AddWithValue("@ApplyStatus", ApplyStatus);
        object obj = Common.Data_noisevalidation.runScalar(sqlCmd);
        return obj.ToString() == "1" ? true : false;
    }
    public bool IsValidationApplyType(string ApplyNo, string carcompomodelno, string ApplyType)
    {
        SqlCommand sqlCmd = new SqlCommand(@"
if exists(select * from dbo.nv_carcompomodel where cpm_applyno=@ApplyNo and cpm_carcompomodelno=@carcompomodelno and cpm_applytype=@ApplyType)
begin
	select 1
	return
end
select 0
");
        sqlCmd.Parameters.AddWithValue("@ApplyNo", ApplyNo);
        sqlCmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
        sqlCmd.Parameters.AddWithValue("@ApplyType", ApplyType);
        object obj = Common.Data_noisevalidation.runScalar(sqlCmd);
        return obj.ToString() == "1" ? true : false;
    }
}