﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Configuration;

public partial class masterpage_MasterPage : System.Web.UI.MasterPage
{
    protected void Page_Load(object sender, EventArgs e)
    {        
//        if (Session["account"] == null)
//        {
//            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
//                                                    window.location = '../accountmt/index.aspx';</script>");
//        }

        Breadcrumbs bread = new Breadcrumbs();
        lbl_Breadcrumbs.Text = bread.Breadcrumbs_string("~/BreadcrumbsFile.xml", "Y");
    }

    protected void imgbtn_Click(object sender, ImageClickEventArgs e)
    {
        try
        {
            Response.Redirect("../accountmt/index.aspx", false);
        }
        catch (Exception ex)
        {
            baseFunc func = new baseFunc();
            func.InsertLogMsg("", "點圖回首頁", ex.Message.ToString() + "*" + ex.Source, "");
        }
    }
}
