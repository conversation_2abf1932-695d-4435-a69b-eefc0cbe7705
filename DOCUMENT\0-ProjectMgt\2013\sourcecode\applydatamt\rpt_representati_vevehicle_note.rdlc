<?xml version="1.0" encoding="utf-8"?>
<Report xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner" xmlns="http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition">
  <DataSources>
    <DataSource Name="representativevehiclenoteDataSet">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>c58e1ae6-4148-4270-ac1e-d0b5a6a9c57f</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="DataSet_get_carmodeldatarpt">
      <Fields>
        <Field Name="cmdg_applyno">
          <DataField>cmdg_applyno</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="cmdg_carengmodelname">
          <DataField>cmdg_carengmodelname</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="cmdg_carmodelno">
          <DataField>cmdg_carmodelno</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="cmdg_maxhorsepower">
          <DataField>cmdg_maxhorsepower</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="cmdg_maxhorsepowerspeed">
          <DataField>cmdg_maxhorsepowerspeed</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="cmdg_testweight">
          <DataField>cmdg_testweight</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="cmdg_testgearreductionratio">
          <DataField>cmdg_testgearreductionratio</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="cmdg_coolingdrivermethod">
          <DataField>cmdg_coolingdrivermethod</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="cmdg_tiresnum">
          <DataField>cmdg_tiresnum</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="cmdg_tirewidth">
          <DataField>cmdg_tirewidth</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="cmdg_exhaustpipenum">
          <DataField>cmdg_exhaustpipenum</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="cmdg_inletmode">
          <DataField>cmdg_inletmode</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="cmdg_berepresentativevehicle">
          <DataField>cmdg_berepresentativevehicle</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ntr_carbodyno">
          <DataField>ntr_carbodyno</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ntr_engineno">
          <DataField>ntr_engineno</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="fullcarstylename">
          <DataField>fullcarstylename</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>representativevehiclenoteDataSet</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <rd:DataSetInfo>
        <rd:DataSetName>representativevehiclenoteDataSet</rd:DataSetName>
        <rd:SchemaPath>D:\Olivia\noisevalidation\PROGRAM\webRoot\App_Code\DataSet\representativevehiclenoteDataSet.xsd</rd:SchemaPath>
        <rd:TableName>pr_get_carmodeldatarpt</rd:TableName>
        <rd:TableAdapterFillMethod>Fill</rd:TableAdapterFillMethod>
        <rd:TableAdapterGetDataMethod>GetData</rd:TableAdapterGetDataMethod>
        <rd:TableAdapterName>pr_get_carmodeldatarptTableAdapter</rd:TableAdapterName>
      </rd:DataSetInfo>
    </DataSet>
    <DataSet Name="DataSet_get_representativevehiclerpt">
      <Fields>
        <Field Name="cpm_applytype">
          <DataField>cpm_applytype</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="cmdg_applyno">
          <DataField>cmdg_applyno</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="cmdg_carcompomodelno">
          <DataField>cmdg_carcompomodelno</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ntr_testrptno">
          <DataField>ntr_testrptno</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ntr_stayrpmparam">
          <DataField>ntr_stayrpmparam</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ntr_stayrpmcondi">
          <DataField>ntr_stayrpmcondi</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ntr_speedupcondi">
          <DataField>ntr_speedupcondi</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ntr_carmodelno">
          <DataField>ntr_carmodelno</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>representativevehiclenoteDataSet</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <rd:DataSetInfo>
        <rd:DataSetName>representativevehiclenoteDataSet</rd:DataSetName>
        <rd:SchemaPath>D:\Olivia\noisevalidation\PROGRAM\webRoot\App_Code\DataSet\representativevehiclenoteDataSet.xsd</rd:SchemaPath>
        <rd:TableName>pr_get_representativevehiclerpt</rd:TableName>
        <rd:TableAdapterFillMethod>Fill</rd:TableAdapterFillMethod>
        <rd:TableAdapterGetDataMethod>GetData</rd:TableAdapterGetDataMethod>
        <rd:TableAdapterName>pr_get_representativevehiclerptTableAdapter</rd:TableAdapterName>
      </rd:DataSetInfo>
    </DataSet>
    <DataSet Name="DataSet_get_combinecarlist">
      <Fields>
        <Field Name="cpm_applytype">
          <DataField>cpm_applytype</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="abd_applyno">
          <DataField>abd_applyno</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ct_aps_desc">
          <DataField>ct_aps_desc</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="abd_applystatus">
          <DataField>abd_applystatus</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="abd_carstyleyear">
          <DataField>abd_carstyleyear</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="abd_databelongcompname">
          <DataField>abd_databelongcompname</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="abd_enginefamily">
          <DataField>abd_enginefamily</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="cpm_importarea">
          <DataField>cpm_importarea</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="abd_factoryname">
          <DataField>abd_factoryname</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="abd_isimport">
          <DataField>abd_isimport</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="cmdg_carbrand">
          <DataField>cmdg_carbrand</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ct_cy_desc">
          <DataField>ct_cy_desc</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>representativevehiclenoteDataSet</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <rd:DataSetInfo>
        <rd:DataSetName>representativevehiclenoteDataSet</rd:DataSetName>
        <rd:SchemaPath>D:\Olivia\noisevalidation\PROGRAM\webRoot\App_Code\DataSet\representativevehiclenoteDataSet.xsd</rd:SchemaPath>
        <rd:TableName>pr_get_combinecarlist</rd:TableName>
        <rd:TableAdapterFillMethod>Fill</rd:TableAdapterFillMethod>
        <rd:TableAdapterGetDataMethod>GetData</rd:TableAdapterGetDataMethod>
        <rd:TableAdapterName>pr_get_combinecarlistTableAdapter</rd:TableAdapterName>
      </rd:DataSetInfo>
    </DataSet>
    <DataSet Name="DataSet_get_applybasebarcode">
      <Fields>
        <Field Name="abd_applyno">
          <DataField>abd_applyno</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="abd_databelongcompidno">
          <DataField>abd_databelongcompidno</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="abd_barcodeno">
          <DataField>abd_barcodeno</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="abd_barcodeimg">
          <DataField>abd_barcodeimg</DataField>
          <rd:TypeName>System.Byte[]</rd:TypeName>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>representativevehiclenoteDataSet</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <rd:DataSetInfo>
        <rd:DataSetName>representativevehiclenoteDataSet</rd:DataSetName>
        <rd:SchemaPath>D:\Olivia\noisevalidation\PROGRAM\webRoot\App_Code\DataSet\representativevehiclenoteDataSet.xsd</rd:SchemaPath>
        <rd:TableName>nv_applybasedata</rd:TableName>
        <rd:TableAdapterFillMethod>Fill</rd:TableAdapterFillMethod>
        <rd:TableAdapterGetDataMethod>GetData</rd:TableAdapterGetDataMethod>
        <rd:TableAdapterName>nv_applybasedataTableAdapter</rd:TableAdapterName>
      </rd:DataSetInfo>
    </DataSet>
  </DataSets>
  <Body>
    <ReportItems>
      <Tablix Name="Tablix1">
        <TablixBody>
          <TablixColumns>
            <TablixColumn>
              <Width>3.07087in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1.05568in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.70866in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.70866in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.70866in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.70866in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.70866in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1.05568in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.70866in</Width>
            </TablixColumn>
          </TablixColumns>
          <TablixRows>
            <TablixRow>
              <Height>0.23622in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox64">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>車型名稱</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox64</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>#e0eabf</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox67">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>引擎最大馬力(kw)</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox67</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>#e0eabf</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox95">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>受驗車重(kg)</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox95</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>#e0eabf</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox97">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>測試檔位總減速比</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox97</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>#e0eabf</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox99">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>冷卻風扇驅動方式</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox99</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>#e0eabf</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox101">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>輪胎數量(不含備胎)</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox101</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>#e0eabf</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox103">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>輪胎寬度</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox103</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>#e0eabf</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox105">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>排氣管開口數量</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox105</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>#e0eabf</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox71">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>進氣方式</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox71</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <BackgroundColor>#e0eabf</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.23622in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="fullcarstylename">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!fullcarstylename.Value</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>fullcarstylename</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="cmdg_maxhorsepower">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!cmdg_maxhorsepower.Value</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>cmdg_maxhorsepower</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="cmdg_testweight">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!cmdg_testweight.Value</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>cmdg_testweight</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="cmdg_testgearreductionratio">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!cmdg_testgearreductionratio.Value</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>cmdg_testgearreductionratio</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="cmdg_coolingdrivermethod">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!cmdg_coolingdrivermethod.Value</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>cmdg_coolingdrivermethod</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="cmdg_tiresnum">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!cmdg_tiresnum.Value</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>cmdg_tiresnum</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="cmdg_tirewidth">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!cmdg_tirewidth.Value</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>cmdg_tirewidth</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="cmdg_exhaustpipenum">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!cmdg_exhaustpipenum.Value</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>cmdg_exhaustpipenum</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="cmdg_inletmode">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!cmdg_inletmode.Value</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>cmdg_inletmode</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>LightGrey</Color>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
          </TablixRows>
        </TablixBody>
        <TablixColumnHierarchy>
          <TablixMembers>
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
          </TablixMembers>
        </TablixColumnHierarchy>
        <TablixRowHierarchy>
          <TablixMembers>
            <TablixMember>
              <KeepWithGroup>After</KeepWithGroup>
            </TablixMember>
            <TablixMember>
              <Group Name="Details" />
            </TablixMember>
          </TablixMembers>
        </TablixRowHierarchy>
        <RepeatColumnHeaders>true</RepeatColumnHeaders>
        <RepeatRowHeaders>true</RepeatRowHeaders>
        <FixedColumnHeaders>true</FixedColumnHeaders>
        <FixedRowHeaders>true</FixedRowHeaders>
        <DataSetName>DataSet_get_carmodeldatarpt</DataSetName>
        <Top>3.10361cm</Top>
        <Left>0.15091cm</Left>
        <Height>1.2cm</Height>
        <Width>23.96284cm</Width>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
        </Style>
      </Tablix>
      <Tablix Name="Tablix4">
        <TablixBody>
          <TablixColumns>
            <TablixColumn>
              <Width>1.43043in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1.97785in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1.58242in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1.36793in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1.53378in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1.60868in</Width>
            </TablixColumn>
          </TablixColumns>
          <TablixRows>
            <TablixRow>
              <Height>0.23622in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox54">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>申請編號</Value>
                              <Style>
                                <FontSize>12pt</FontSize>
                              </Style>
                            </TextRun>
                            <TextRun>
                              <Value>：</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontFamily>Verdana</FontFamily>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox54</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="abd_applyno1">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!abd_applyno.Value</Value>
                              <Style>
                                <FontSize>12pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>abd_applyno1</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox61">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>資料狀態</Value>
                              <Style>
                                <FontSize>12pt</FontSize>
                              </Style>
                            </TextRun>
                            <TextRun>
                              <Value>：</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontFamily>Verdana</FontFamily>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox61</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="ct_aps_desc">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!ct_aps_desc.Value</Value>
                              <Style>
                                <FontSize>12pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>ct_aps_desc</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox63">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>車型年</Value>
                              <Style>
                                <FontSize>12pt</FontSize>
                              </Style>
                            </TextRun>
                            <TextRun>
                              <Value>：</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontFamily>Verdana</FontFamily>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox63</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="abd_carstyleyear">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!abd_carstyleyear.Value</Value>
                              <Style>
                                <FontSize>12pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Left</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>abd_carstyleyear</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.23622in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox128">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>申請廠商</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                            <TextRun>
                              <Value>：</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontFamily>Verdana</FontFamily>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox128</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="abd_databelongcompname">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!abd_databelongcompname.Value</Value>
                              <Style>
                                <FontSize>12pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>abd_databelongcompname</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox68">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>引擎族</Value>
                              <Style>
                                <FontSize>12pt</FontSize>
                              </Style>
                            </TextRun>
                            <TextRun>
                              <Value>：</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontFamily>Verdana</FontFamily>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox68</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="abd_enginefamily">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!abd_enginefamily.Value</Value>
                              <Style>
                                <FontSize>12pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>abd_enginefamily</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox70">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Parameters!P_isimport.Value</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontFamily>Verdana</FontFamily>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                            <TextRun>
                              <Value>：</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontFamily>Verdana</FontFamily>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox70</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="ct_cy_desc">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!ct_cy_desc.Value</Value>
                              <Style>
                                <FontSize>12pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Left</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>ct_cy_desc</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.23622in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox72">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>廠牌名稱</Value>
                              <Style>
                                <FontSize>12pt</FontSize>
                              </Style>
                            </TextRun>
                            <TextRun>
                              <Value>：</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontFamily>Verdana</FontFamily>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox72</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="cmdg_carbrand">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!cmdg_carbrand.Value</Value>
                              <Style>
                                <FontSize>12pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>cmdg_carbrand</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox74">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>製造廠名稱</Value>
                              <Style>
                                <FontSize>12pt</FontSize>
                              </Style>
                            </TextRun>
                            <TextRun>
                              <Value>：</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontFamily>Verdana</FontFamily>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox74</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="abd_factoryname">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!abd_factoryname.Value</Value>
                              <Style>
                                <FontSize>12pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>abd_factoryname</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                    <ColSpan>3</ColSpan>
                  </CellContents>
                </TablixCell>
                <TablixCell />
                <TablixCell />
              </TablixCells>
            </TablixRow>
          </TablixRows>
        </TablixBody>
        <TablixColumnHierarchy>
          <TablixMembers>
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
          </TablixMembers>
        </TablixColumnHierarchy>
        <TablixRowHierarchy>
          <TablixMembers>
            <TablixMember />
            <TablixMember />
            <TablixMember />
          </TablixMembers>
        </TablixRowHierarchy>
        <DataSetName>DataSet_get_combinecarlist</DataSetName>
        <Top>0.79403cm</Top>
        <Left>0.15091cm</Left>
        <Height>1.8cm</Height>
        <Width>24.13277cm</Width>
        <ZIndex>1</ZIndex>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
        </Style>
      </Tablix>
      <Textbox Name="Textbox7">
        <CanGrow>true</CanGrow>
        <KeepTogether>true</KeepTogether>
        <Paragraphs>
          <Paragraph>
            <TextRuns>
              <TextRun>
                <Value>=Parameters!P_title.Value</Value>
                <Style>
                  <FontFamily>KaiTi</FontFamily>
                  <FontSize>16pt</FontSize>
                </Style>
              </TextRun>
            </TextRuns>
            <Style>
              <TextAlign>Center</TextAlign>
            </Style>
          </Paragraph>
        </Paragraphs>
        <rd:DefaultName>Textbox7</rd:DefaultName>
        <Left>5.59576cm</Left>
        <Height>0.75875cm</Height>
        <Width>14.60187cm</Width>
        <ZIndex>2</ZIndex>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
          <PaddingLeft>2pt</PaddingLeft>
          <PaddingRight>2pt</PaddingRight>
          <PaddingTop>2pt</PaddingTop>
          <PaddingBottom>2pt</PaddingBottom>
        </Style>
      </Textbox>
      <Tablix Name="Tablix15">
        <TablixBody>
          <TablixColumns>
            <TablixColumn>
              <Width>1.96283in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>2.51452in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>2.30649in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>2.71725in</Width>
            </TablixColumn>
          </TablixColumns>
          <TablixRows>
            <TablixRow>
              <Height>0.23622in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox290">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>代表車型名稱：</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontFamily>Verdana</FontFamily>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox290</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox291">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Parameters!P_rcarengmodelname.Value</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox291</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                    <ColSpan>3</ColSpan>
                  </CellContents>
                </TablixCell>
                <TablixCell />
                <TablixCell />
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.23622in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox154">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>車身號碼：</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontFamily>Verdana</FontFamily>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox134</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="cpm_applytype">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Parameters!P_rcarbodyno.Value</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>cpm_applytype</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox238">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>引擎號碼</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                            <TextRun>
                              <Value>：</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontFamily>Verdana</FontFamily>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox237</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox289">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Parameters!P_rengineno.Value</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox289</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.23622in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox294">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>加速進入檔位：</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontFamily>Verdana</FontFamily>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox294</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox295">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Parameters!P_speedupgear.Value</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox295</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox296">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>原地引擎轉速設定</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                            <TextRun>
                              <Value>：</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontFamily>Verdana</FontFamily>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox296</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox297">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Parameters!P_stayrpmparam.Value</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox297</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.23622in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox298">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>加速進場車速：</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontFamily>Verdana</FontFamily>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox298</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox299">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Parameters!P_speed.Value</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox299</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox300">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>原地設定條件</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                            <TextRun>
                              <Value>：</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontFamily>Verdana</FontFamily>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox300</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox301">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Parameters!P_stayrpmcondi.Value</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox301</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.23622in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox302">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>加速選定條件：</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontFamily>Verdana</FontFamily>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox302</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox303">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Parameters!P_speedupcondi.Value</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontSize>12pt</FontSize>
                                <FontWeight>Normal</FontWeight>
                                <TextDecoration>None</TextDecoration>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox303</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                    <ColSpan>3</ColSpan>
                  </CellContents>
                </TablixCell>
                <TablixCell />
                <TablixCell />
              </TablixCells>
            </TablixRow>
          </TablixRows>
        </TablixBody>
        <TablixColumnHierarchy>
          <TablixMembers>
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
          </TablixMembers>
        </TablixColumnHierarchy>
        <TablixRowHierarchy>
          <TablixMembers>
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
          </TablixMembers>
        </TablixRowHierarchy>
        <DataSetName>DataSet_get_representativevehiclerpt</DataSetName>
        <Top>4.78854cm</Top>
        <Left>0.15091cm</Left>
        <Height>2.99999cm</Height>
        <Width>24.13277cm</Width>
        <ZIndex>3</ZIndex>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
        </Style>
      </Tablix>
    </ReportItems>
    <Height>3.16716in</Height>
    <Style />
  </Body>
  <ReportParameters>
    <ReportParameter Name="P_speedupgear">
      <DataType>String</DataType>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="P_speed">
      <DataType>String</DataType>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="P_rcarengmodelname">
      <DataType>String</DataType>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="P_rcarbodyno">
      <DataType>String</DataType>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="P_rengineno">
      <DataType>String</DataType>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="P_stayrpmparam">
      <DataType>String</DataType>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="P_stayrpmcondi">
      <DataType>String</DataType>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="P_speedupcondi">
      <DataType>String</DataType>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="P_isimport">
      <DataType>String</DataType>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
    <ReportParameter Name="P_title">
      <DataType>String</DataType>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
  </ReportParameters>
  <Width>9.65207in</Width>
  <Page>
    <PageHeight>21cm</PageHeight>
    <PageWidth>29.7cm</PageWidth>
    <LeftMargin>2cm</LeftMargin>
    <RightMargin>2cm</RightMargin>
    <TopMargin>2cm</TopMargin>
    <BottomMargin>2cm</BottomMargin>
    <ColumnSpacing>0.13cm</ColumnSpacing>
    <Style />
  </Page>
  <rd:ReportID>97f52e14-dd70-48df-817b-1243dcb2f509</rd:ReportID>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
</Report>