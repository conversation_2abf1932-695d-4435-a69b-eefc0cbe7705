﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data;
using System.Data.SqlClient;

/// <summary>
/// Summary description for RejectProcessControl
/// </summary>
public class RejectControl
{
	public RejectControl()
	{
	}

	public string GetCompanyName(string ApplyNo)
	{
		SqlCommand sqlCmd = new SqlCommand(@"
select abd_databelongcompname from dbo.nv_applybasedata where abd_applyno=@ApplyNo
");
		sqlCmd.Parameters.AddWithValue("@ApplyNo", ApplyNo);
		object obj = Common.Data_noisevalidation.runScalar(sqlCmd);
		return obj == null ? string.Empty : obj.ToString();
	}

    public string GetCompanyNo(string ApplyNo)
    {
        SqlCommand sqlCmd = new SqlCommand(@"
select abd_databelongcompidno from dbo.nv_applybasedata where abd_applyno=@ApplyNo
");
        sqlCmd.Parameters.AddWithValue("@ApplyNo", ApplyNo);
        object obj = Common.Data_noisevalidation.runScalar(sqlCmd);
        return obj == null ? string.Empty : obj.ToString();
    }
    public string GetTwocertType(string ApplyNo)
    {
        SqlCommand sqlCmd = new SqlCommand(@"
select abd_twocertmethod from dbo.nv_applybasedata where abd_applyno=@ApplyNo
");
        sqlCmd.Parameters.AddWithValue("@ApplyNo", ApplyNo);
        object obj = Common.Data_noisevalidation.runScalar(sqlCmd);
        return obj == null ? string.Empty : obj.ToString();
    }


	public void UpdateDeny(string ApplyNo, string DenyNo, string DenyDate, string DenyReason)
	{
		SqlCommand sqlCmd = new SqlCommand(@"
update dbo.nv_applybasedata set 
abd_applydenydocno=@DenyNo
,abd_applydenydate=@DenyDate
,abd_denyreason=@DenyReason
where abd_applyno=@ApplyNo
");
		sqlCmd.Parameters.AddWithValue("@ApplyNo", ApplyNo);
		sqlCmd.Parameters.AddWithValue("@DenyNo", DenyNo);
		sqlCmd.Parameters.AddWithValue("@DenyDate", DenyDate);
		sqlCmd.Parameters.AddWithValue("@DenyReason", DenyReason);
		Common.Data_noisevalidation.runParaCmd1(sqlCmd);
	}

    public void UpdateDenyReason(string ApplyNo, string DenyReason)
    {
        SqlCommand sqlCmd = new SqlCommand(@"
update dbo.nv_applybasedata set 
abd_denyreason=@DenyReason
where abd_applyno=@ApplyNo
");
        sqlCmd.Parameters.AddWithValue("@ApplyNo", ApplyNo);        
        sqlCmd.Parameters.AddWithValue("@DenyReason", DenyReason);
        Common.Data_noisevalidation.runParaCmd1(sqlCmd);
    }

    public string getExEnginesn(string BelongCompidno, string EngineFamily)
    {
        SqlCommand sqlCmd = new SqlCommand(@"
                    if exists (select abd_enginesn from nv_applybasedata where abd_enginefamily = @enginefamily and abd_databelongcompidno=@databelongcompidno and abd_applystatus ='50')
                     begin
	                    select top 1 abd_enginesn from nv_applybasedata
	                    where abd_enginefamily = @enginefamily
	                    and abd_databelongcompidno=@databelongcompidno
	                    and abd_applystatus ='50' --核閱完成
	                    order by abd_validateddate desc
                
                    end
                    else if exists (select abd_enginesn from nv_applybasedata where abd_enginefamily = @enginefamily and abd_databelongcompidno=@databelongcompidno and abd_applystatus ='55')
                    begin
	                    select top 1 abd_enginesn from nv_applybasedata
	                    where abd_enginefamily = @enginefamily
	                    and abd_databelongcompidno=@databelongcompidno
	                    and abd_applystatus ='55' --歸檔
	                    order by abd_validateddate desc
                
                    end
                    else 
                    begin
	                    select '此為 '+@enginefamily+' 引擎族的首案'        
                    end                
                ");
        sqlCmd.Parameters.AddWithValue("@databelongcompidno", BelongCompidno);
        sqlCmd.Parameters.AddWithValue("@enginefamily", EngineFamily);
        object obj = Common.Data_noisevalidation.runScalar(sqlCmd);
        return obj == null ? string.Empty : obj.ToString();
    }
}