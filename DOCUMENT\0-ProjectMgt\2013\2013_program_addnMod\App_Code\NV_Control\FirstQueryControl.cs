﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;
using System.Data.SqlClient;
using System.Data;

/// <summary>
/// Summary description for FirstQueryControl
/// </summary>
public class FirstQueryControl
{
	public FirstQueryControl()
	{
	}

	public List<ListItem> LisFirstMan()
	{
		SqlCommand sqlCmd = new SqlCommand(@"
select ai_username, abg_account from dbo.nv_accountbelonggroup
join nv_accountinfo on ai_account=abg_account
where abg_grpid='02' --初審人員
");
		DataView dv = Common.Data_noisevalidation.runParaCmd(sqlCmd);
		return DataUtils.ConvertDataViewToList(dv);
	}

	public DataView ListFirstData(string ApplyNo, string CompanyName, string ChargeAccount, string inApplyStatus)
	{
		SqlCommand sqlCmd = new SqlCommand(@"
select 
abd_applyno ApplyNo
,abd_databelongcompname Compname
,CONVERT(VARCHAR(19),abd_applydate,120) ApplyDate
,dbo.fn_CastCarTypeCn(abd_vechiletype) VechileType
,abd_enginefamily EngineFamily
,dbo.fn_CastApplyStatusCn(abd_applystatus) ApplyStatusCn
,isnull(abd_validatechargeaccount,'') ChargeAccount
,abd_applystatus ApplyStatus
from dbo.nv_applybasedata
where abd_applyno like '%'+@ApplyNo+'%'
and isnull(abd_databelongcompname,'') like '%'+@CompanyName+'%'
and isnull(abd_validatechargeaccount,'') like '%'+@ChargeAccount+'%' order by abd_applydate
--and abd_applystatus in('31','40','91','92','93','33','50')
--初審中,補件中,複審核閱退回,一級主管退回,二級主管退回,一級主管核閱,已上傳平台
");
		sqlCmd.Parameters.AddWithValue("@ApplyNo", ApplyNo);
		sqlCmd.Parameters.AddWithValue("@CompanyName", CompanyName);
		sqlCmd.Parameters.AddWithValue("@ChargeAccount", ChargeAccount);
		DataView dv = Common.Data_noisevalidation.runParaCmd(sqlCmd);
		if (!inApplyStatus.Length.Equals(0)) { dv.RowFilter = inApplyStatus; }
		return dv;
	}

	public void SetChargeMan(string ApplyNo)
	{
		SqlCommand sqlCmd = new SqlCommand(@"
update dbo.nv_applybasedata set
abd_validatechargeaccount=@ChargeAccount
,abd_validatechargedate=GETDATE()
where abd_applyno=@ApplyNo
");
		sqlCmd.Parameters.AddWithValue("@ApplyNo",ApplyNo);
		sqlCmd.Parameters.AddWithValue("@ChargeAccount", new BaseAdminPage().GetAgentAccount());
		Common.Data_noisevalidation.runParaCmd1(sqlCmd);
	}
}