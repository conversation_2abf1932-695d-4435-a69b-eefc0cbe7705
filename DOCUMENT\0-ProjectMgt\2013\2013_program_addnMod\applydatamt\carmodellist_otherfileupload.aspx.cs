﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.IO;
using System.Text;
using System.Xml;
using Common;


public partial class applydatamt_carmodellist_otherfileupload : BaseAdminPage
{
    public DataTable dtList
    {
        get { return (DataTable)ViewState["dtList"]; }
        set { ViewState["dtList"] = value; }
    }
    protected void Page_Load(object sender, EventArgs e)
    {
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = '../accountmt/index.aspx';</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" &&
                (
                (
                 GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("pg_adm") || GetAgentAccountGrpList().Contains("rs_adm") || GetAgentAccountGrpList().Contains("fis_adm") || GetAgentAccountGrpList().Contains("rv_adm")
                )
                ||
                (
                  (GetAgentAccountGrpList().Contains("cga_adm") || GetAgentAccountGrpList().Contains("cam_adm")) && CheckSingleApplyDataAccessRight(DeCode(Request["applyno"].ToString()))
                )
                )
                )
            {
                if (!IsPostBack)
                {
                    if (Request["applyno"].ToString() != "")
                    {
                        Cryptography crypy = new Cryptography();
                        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
                        int keynum = crypy.getRadNum();
                        string[] Warr = crypy.wsApplybasedataDetailValid(keynum);
                        DataTable dt_applydatadetail = wsApplyData.get_applybasedate_detail(crypy.DeCode(Request["applyno"].ToString()), Warr[0].ToString(), Warr[1].ToString());
                        if (dt_applydatadetail.Rows.Count > 0)
                        {
                            //if (dt_applydatadetail.Rows[0]["abd_databelongcompidno"].ToString() == Session["compidno"].ToString())
                            //{
                                BindData();
                                dt_applydatadetail.Dispose();
                            //}
                            //else
                            //{
                            //    dt_applydatadetail.Dispose();
                            //    Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                            //    Response.End();
                            //}
                        }
                        else
                        {
                            dt_applydatadetail.Dispose();
                            Response.Write("<script languge='javascript'>alert('發生取不到申請編號錯誤，系統將導回首頁！！');window.location.href='../index.aspx'</script>");
                            Response.End();
                        }
                    }
                    else
                    {
                        Response.Write("<script languge='javascript'>alert('發生取不到申請編號錯誤，系統將導回首頁！！');window.location.href='../index.aspx'</script>");
                        Response.End();
                    }
                }
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }
    }

    #region 更改的麵包屑傳入的參數
    protected void Page_LoadComplete(object sender, EventArgs e)
    {
        ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text = ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text.Replace("applydatamt/carcompomodelList.aspx", "applydatamt/carcompomodelList.aspx?applyno=" + Server.UrlEncode(Request["applyno"].ToString()));
    }
    #endregion

    #region 申請型式為沿用、不異動、撤件，則不可填寫並導回查詢頁面
    public void CheckApplyType(string sApplyType)
    {
        if (sApplyType.Equals("沿用") || sApplyType.Equals("不異動") || sApplyType.Equals("撤銷"))
        {
            Response.Write("<script languge='javascript'>alert('申請型式為「" + sApplyType + "」，不可編輯，系統將導回查詢頁面！！');window.location.href='nv_applylistqry.aspx'</script>");
            Response.End();
        }
    }
    #endregion

    #region 判斷資料狀態不為填寫中、補件、拒件，則不可填寫並導回查詢頁面
    public void CheckApplyStatus(string sStatusCode, string sStatusDesc)
    {
        if (!(sStatusCode.Equals("10") || sStatusCode.Equals("40") || sStatusCode.Equals("90")))
        {
            Response.Write("<script languge='javascript'>alert('資料狀態為「" + sStatusDesc + "」，不可編輯，系統將導回查詢頁面！！');window.location.href='nv_applylistqry.aspx'</script>");
            Response.End();
        }
    }
    #endregion

    public void BindData()
    {
        Cryptography crypy = new Cryptography();
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        wsOtherFileupload oOFD = new wsOtherFileupload();
        int keynum = crypy.getRadNum();
        string[] arr = crypy.wsOtherFileuploadValid(keynum);
        string[] Warr = crypy.wsApplybasedataDetailValid(keynum);
        string applyno = DeCode(Request["applyno"].ToString());
        string carcompomodelno = DeCode(Request["carcompomodelno"].ToString());
        string strStatusCode = "";
        string strStatusDesc = "";
        string strApplyType = "";
        lab_applyno.Text = applyno;
        lab_carcompomodelno.Text = carcompomodelno;
        DataTable dtItem = oOFD.get_carcompomodel_Item(applyno.ToString().Trim(), carcompomodelno, arr[0].ToString(), arr[1].ToString());
        DataTable dt_carmodelnames = wsApplyData.get_Nv_carmodelnames(applyno, carcompomodelno, Warr[0].ToString(), Warr[1].ToString());
        //取得代表車名稱
        if (dt_carmodelnames.Rows.Count > 0)
        {
            foreach (DataRow dr in dt_carmodelnames.Rows)
            {
                if (dr["cmdg_berepresentativevehicle"].ToString().Equals("Y", StringComparison.CurrentCultureIgnoreCase))
                {
                    lab_carengmodelname.Text = dr["fullcarstylename"].ToString();
                }
            }
        }
        //申請型式
        DataTable dt_applyno = wsApplyData.get_applyno(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
        if (dt_applyno.Rows.Count > 0)
        {
            strApplyType = dt_applyno.Rows[0]["ct_at_desc"].ToString();
            CheckApplyType(strApplyType);
        }
        //資料狀態
        DataTable dt_applystatus = wsApplyData.get_applystatus(applyno, carcompomodelno, Warr[0].ToString(), Warr[1].ToString());
        if (dt_applystatus.Rows.Count > 0)
        {
            lab_applystatus.Text = dt_applystatus.Rows[0]["ct_aps_desc"].ToString();
            strStatusDesc = dt_applystatus.Rows[0]["ct_aps_desc"].ToString();
            strStatusCode = dt_applystatus.Rows[0]["abd_applystatus"].ToString();
            CheckApplyStatus(strStatusCode, strStatusDesc);
        }
       
        dtList = oOFD.get_otherfileupload_list(dtItem.Rows[0]["abd_isimport"].ToString(), dtItem.Rows[0]["abd_vechiletype"].ToString(), dtItem.Rows[0]["abd_powerfrom"].ToString(), dtItem.Rows[0]["cpm_applytype"].ToString(), arr[0].ToString(), arr[1].ToString());
        gv_data.DataSource = dtList;
        gv_data.DataBind();

    }
    protected void gv_data_RowDataBound(object sender, GridViewRowEventArgs e)
    {

        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            Cryptography crypy = new Cryptography();
            wsOtherFileupload oOFD = new wsOtherFileupload();
            int keynum = crypy.getRadNum();
            string[] Oarr = crypy.wsOtherFileuploadValid(keynum);
            string applyno = DeCode(Request["applyno"].ToString());
            string carcompomodelno = DeCode(Request["carcompomodelno"].ToString());
            string sfiletype = string.Empty;
            string sflag = "";//1為可以編輯0為不可編輯
            int isFileSum = oOFD.get_CountFiletypeSum(applyno, carcompomodelno, DataBinder.Eval(e.Row.DataItem, "vil_filetype").ToString(), Oarr[0].ToString(), Oarr[1].ToString());
            HyperLink hlk_file = (HyperLink)e.Row.FindControl("hlk_file");
            sfiletype = DataBinder.Eval(e.Row.DataItem, "vil_filetype").ToString().Trim();
            if (lab_applystatus.Text.Trim() == "填單中" || lab_applystatus.Text.Trim() == "補件中" || lab_applystatus.Text.Trim() == "拒件") 
            {
                sflag = "1";
            }
            else
            {
                sflag = "0";
            }
           hlk_file.NavigateUrl = "nv_carotherfileupload.aspx?sflag=" + Server.UrlEncode(EnCode(sflag)) + "&applyno=" + Server.UrlEncode(Request["applyno"].ToString()) + "&carcompomodelno=" + Server.UrlEncode(Request["carcompomodelno"].ToString()) + "&filetype=" + Server.UrlEncode(EnCode(sfiletype));            
          

            hlk_file.Target = "_blank";
            hlk_file.Text = "檔案上傳";
            if (isFileSum > 0)
            {
                hlk_file.ImageUrl = "../images/show.GIF";
            }
        }
    }
    protected void gv_data_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        gv_data.PageIndex = e.NewPageIndex;
        BindData();
    }

    protected void btnReflash_Click(object sender, EventArgs e)
    {
        BindData();
    }
}