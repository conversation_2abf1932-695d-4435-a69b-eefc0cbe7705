﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using System.Configuration;

public partial class accountmt_changepwd : Rac_Example.RAC_BasePage
{
    private bool isFirst = false;
    wsAccountInfo wsaccount = new wsAccountInfo();
    Cryptography crypt = new Cryptography();
    Library.ExamineProcedure ep = new Library.ExamineProcedure();
    protected void Page_Load(object sender, EventArgs e)
    {
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = 'index.aspx';</script>"); return;
        }
        else
        {
            BaseAdminPage egpadmin = new BaseAdminPage();
            if (egpadmin.GetAgentName() != "" && (egpadmin.GetAgentAccountstatus() == "1" || egpadmin.GetAgentAccountstatus() == "2"))
            {
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }
    }
    protected void btn_OK_Click(object sender, EventArgs e)
    {
        if (CheckData())
        {
            string account = crypt.DeCode(Session["account"].ToString());
            string[] radom_A = crypt.wsAccountValidate(crypt.getRadNum());

            #region Check CarCert 驗證
            connectCarCert.CarCert CarCert = new connectCarCert.CarCert();
            string[] radom_CarCert = crypt.wsCarCertValid(Convert.ToInt16(DateTime.Now.DayOfWeek));

            //判斷帳號是否存在CarCert;若存在更改CarCert.UpdatePassword
            if (doCarCertCheck())
            {
                CarCert.UpdatePassword(crypt.EnCode(account), crypt.GetPAWDHash(txt_new_pwd.Text.Trim()), radom_CarCert[0].ToString(), radom_CarCert[1].ToString());
            }
            #endregion

            wsaccount.UpdataPWD(account, crypt.GetPAWDHash(txt_new_pwd.Text.Trim()), radom_A[0].ToString(), radom_A[1].ToString());

            if (isFirst)
            {
                wsaccount.UpdateStatus(account, "2", radom_A[0].ToString(), radom_A[1].ToString());

                #region useGMsystem 為1表示勾選污染申請 同步新增CarCert公司、帳號資料
                wsCompany wscompany = new wsCompany();
                string[] radom_C = crypt.wsCompanyValidate(crypt.getRadNum());
                DataSet ds_company = wscompany.getCompanyBase(Session["compidno"].ToString(), radom_C[0].ToString(), radom_C[1].ToString());
                DataSet ds_Account = wsaccount.getAccountBase(account, radom_A[0].ToString(), radom_A[1].ToString());
                string Stampcompno_G = ds_company.Tables[0].Rows[0]["ci_stampcompno_g"].ToString().Length <= 3 ? ds_company.Tables[0].Rows[0]["ci_stampcompno_g"].ToString() : ds_company.Tables[0].Rows[0]["ci_stampcompno_g"].ToString().Substring(1, ds_company.Tables[0].Rows[0]["ci_stampcompno_g"].ToString().Length - 1);
                string Stampcompno_M = ds_company.Tables[0].Rows[0]["ci_stampcompno_m"].ToString().Length <= 3 ? ds_company.Tables[0].Rows[0]["ci_stampcompno_m"].ToString() : ds_company.Tables[0].Rows[0]["ci_stampcompno_m"].ToString().Substring(1, ds_company.Tables[0].Rows[0]["ci_stampcompno_m"].ToString().Length - 1);
                string submit_resultCompany = ""; //回傳新增公司資料字串
                string submit_resultAccount = ""; //回傳新增帳號資料字串
                if (ds_Account.Tables[0].Rows[0]["ai_use_gm_system"].ToString().Equals("1"))
                {
                    bool isCompanyCarCert = CarCert.CheckCustomer(crypt.EnCode(Session["compidno"].ToString()), radom_CarCert[0].ToString(), radom_CarCert[1].ToString());
                    //同步-新增CarCert公司資料
                    if (!isCompanyCarCert) //如果不存在再新增公司資料 如果已存在 就新增帳號資料
                    {
                        submit_resultCompany = CarCert.InsertCustomer(Session["compidno"].ToString(), crypt.EnCode(ds_company.Tables[0].Rows[0]["ci_compname"].ToString()), crypt.EnCode(ds_company.Tables[0].Rows[0]["ci_compename"].ToString()), crypt.EnCode(ds_company.Tables[0].Rows[0]["ci_chargeman"].ToString()), crypt.EnCode(ds_company.Tables[0].Rows[0]["ci_compshortname"].ToString()), crypt.EnCode(DateTime.Now.Date.ToString("yyyyMMdd")), crypt.EnCode(ds_company.Tables[0].Rows[0]["ci_comptel"].ToString()), crypt.EnCode(ds_company.Tables[0].Rows[0]["ci_compfax"].ToString()), crypt.EnCode(ds_company.Tables[0].Rows[0]["ci_compaddrcity"].ToString()), crypt.EnCode(ds_company.Tables[0].Rows[0]["ci_compaddrlocalarea"].ToString()),
                             crypt.EnCode(ds_company.Tables[0].Rows[0]["ci_compaddrpostno"].ToString()), crypt.EnCode(ds_company.Tables[0].Rows[0]["ci_compaddr"].ToString()), crypt.EnCode(ds_Account.Tables[0].Rows[0]["ai_username"].ToString()), crypt.EnCode(ds_Account.Tables[0].Rows[0]["ai_email"].ToString()), crypt.EnCode(ds_Account.Tables[0].Rows[0]["ai_usertel"].ToString()), crypt.EnCode(ds_Account.Tables[0].Rows[0]["ai_usercellphone"].ToString()),
                             crypt.EnCode(ds_Account.Tables[0].Rows[0]["ai_userfax"].ToString()), crypt.EnCode(Stampcompno_G), crypt.EnCode(Stampcompno_M), radom_CarCert[0].ToString(), radom_CarCert[1].ToString());
                    }

                    //帳號是否存在 CarCert
                    bool isReaccountCarCert = CarCert.CheckUserID(crypt.EnCode(account), radom_CarCert[0].ToString(), radom_CarCert[1].ToString());
                    //同步-新增CarCert帳號資料
                    if (!isReaccountCarCert) //如果不存在再新增帳號資料
                    {
                        submit_resultAccount = CarCert.InsertAccount(crypt.EnCode(account), crypt.EnCode(ds_Account.Tables[0].Rows[0]["ai_username"].ToString()), Session["compidno"].ToString(), crypt.EnCode(ds_Account.Tables[0].Rows[0]["ai_usertel"].ToString()), crypt.EnCode(ds_Account.Tables[0].Rows[0]["ai_email"].ToString()), crypt.GetPAWDHash(txt_new_pwd.Text.Trim()), radom_CarCert[0].ToString(), radom_CarCert[1].ToString());
                    }
                    #region 信件通知CarCert
                    if (submit_resultAccount.Equals("新增帳號成功"))
                    {
                        string mailCC = ConfigurationManager.AppSettings["MailCC"].ToString();

                        string[] _mailTo = new string[1];
                        _mailTo[0] = ConfigurationManager.AppSettings["population_contactmail"].ToString();//ds_Account.Tables[0].Rows[0]["ai_email"].ToString();//收件人                
                        string[] _mailCc = new string[1];//副本
                        _mailCc[0] = "";
                        string[] _mailBcc = new string[1];//密件
                        _mailBcc[0] = "";
                        if (Convert.ToBoolean(ConfigurationManager.AppSettings["DEBUGMode"].ToString()))
                        {
                            //測試
                            _mailTo[0] = ConfigurationManager.AppSettings["MailTestTo"].ToString();
                        }
                        //信件通知
                        SendMail sendmail = new SendMail();

                        string CompName = ds_company.Tables[0].Rows[0]["ci_compname"].ToString();
                        string Account = account;

                        sendmail.Mail_ApplyCarCertAccount(_mailTo, _mailCc, _mailBcc, CompName, Account, ds_Account.Tables[0].Rows[0]["ai_username"].ToString());
                    #endregion

                        MessageBox.Show(submit_resultCompany + ";" + submit_resultAccount);
                        return;
                    }
                }
                   
                #endregion
                  
            }
            txt_old_pwd.Text = "";
            txt_new_pwd.Text = "";
            txt_check_pwd.Text = "";
            txt_ValidateCode.Text = "";

            Session.RemoveAll();
            ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('密碼修改完成, 系統已重新登出, 請再重新登入！!');
                                                    window.location = 'index.aspx';</script>");
        }
    }
    private bool CheckData()
    {
        StringBuilder sb = new StringBuilder();
        string[] radom_A = crypt.wsAccountValidate(crypt.getRadNum());
        string account = crypt.DeCode(Session["account"].ToString());
        DataSet ds_account = wsaccount.getAccountBase(account, radom_A[0].ToString(), radom_A[1].ToString());
        isFirst = (ds_account.Tables[0].Rows[0]["ai_accountstatus"].ToString() == "1") ? true : false;
        string sys_pwd = ds_account.Tables[0].Rows[0]["ai_password"].ToString();

        #region Accountmt特殊字元判斷-有MasterPage
        ContentPlaceHolder mpContentPlaceHolder;
        mpContentPlaceHolder = (ContentPlaceHolder)Master.FindControl("ContentPlaceHolder1");
        BaseAdminPage egpadmin = new BaseAdminPage();
        foreach (object ctrl in mpContentPlaceHolder.Controls)
        {
            if (ctrl is TextBox)
            {
                TextBox objTextBox = default(TextBox);
                objTextBox = (TextBox)ctrl;
                if ((egpadmin.tbWord(objTextBox.Text)))
                {
                    sb.Append(objTextBox.Text + ":含有系統不允許的特殊字元");
                   
                }
            }
        }
        #endregion

        if (sys_pwd != crypt.GetPAWDHash(txt_old_pwd.Text.Trim()))
        {
            sb.Append("舊密碼輸入不正確\n");
        }
        else
        {

            if (txt_new_pwd.Text.Trim().Length < 5)
            {
                sb.Append("密碼長度至少六碼\n");
            }
            else
            {
                if (txt_new_pwd.Text.Trim() != txt_check_pwd.Text.Trim())
                {
                    sb.Append("新密碼與確認新密碼需一致\n");
                }
            }
            if (txt_old_pwd.Text.Trim() == txt_new_pwd.Text.Trim())
            {
                sb.Append("新舊密碼不可一樣\n");
            }

            if (txt_ValidateCode.Text.Trim() != Session["validatecode"].ToString())
            {
                sb.Append("驗證碼不正確");
            }
        }
        if (sb.Length > 0)
        {
            MessageBox.Show(sb.ToString());
            txt_old_pwd.Text = "";
            txt_new_pwd.Text = "";
            txt_check_pwd.Text = "";
            txt_ValidateCode.Text = "";
            return false;
        }
        else
        {
            return true;
        }
    }
    protected void lbtn_refresh_Click(object sender, EventArgs e)
    {
        txt_old_pwd.Attributes.Add("value", txt_old_pwd.Text.Trim());
        txt_new_pwd.Attributes.Add("value", txt_new_pwd.Text.Trim());
        txt_check_pwd.Attributes.Add("value", txt_check_pwd.Text.Trim());
        txt_ValidateCode.Attributes.Add("value", txt_ValidateCode.Text.Trim());
    }
    private bool doCarCertCheck()
    {
        BaseAdminPage egpadmin = new BaseAdminPage();
        #region Check CarCert 驗證
        connectCarCert.CarCert CarCert = new connectCarCert.CarCert();
        string[] radom_CarCert = crypt.wsCarCertValid(Convert.ToInt16(DateTime.Now.DayOfWeek));
        //公司是否存在 CarCert
        bool isCompanyCarCert = CarCert.CheckCustomer(crypt.EnCode(egpadmin.GetAgentIDNo()), radom_CarCert[0].ToString(), radom_CarCert[1].ToString());
        //帳號是否存在 CarCert
        bool isReaccountCarCert = CarCert.CheckUserID(crypt.EnCode(egpadmin.GetAgentAccount()), radom_CarCert[0].ToString(), radom_CarCert[1].ToString());
        #endregion

        if (isCompanyCarCert && isReaccountCarCert)
            return true;
        else
            return false;
    }

}