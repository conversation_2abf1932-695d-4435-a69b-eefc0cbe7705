<?xml version="1.0" encoding="utf-8"?>
<Report xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner" xmlns="http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition">
  <DataSources>
    <DataSource Name="certificatedDataSet">
      <ConnectionProperties>
        <DataProvider>System.Data.DataSet</DataProvider>
        <ConnectString>/* Local Connection */</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>d36bdcc5-d487-4cfc-8a3f-55f2f4215230</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="DT_progressSpot">
      <Fields>
        <Field Name="Itno">
          <DataField>Itno</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="vp_applyno">
          <DataField>vp_applyno</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ci_compshortname">
          <DataField>ci_compshortname</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="cpm_carcompomodelno">
          <DataField>cpm_carcompomodelno</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ApplyStatusCn">
          <DataField>ApplyStatusCn</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="abd_enginesn">
          <DataField>abd_enginesn</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="abd_certdocno">
          <DataField>abd_certdocno</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ProcessSuc">
          <DataField>ProcessSuc</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="abd_certdocissuedate">
          <DataField>abd_certdocissuedate</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>certificatedDataSet</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <rd:DataSetInfo>
        <rd:DataSetName>certificatedDataSet</rd:DataSetName>
        <rd:SchemaPath>D:\Olivia\noisevalidation\PROGRAM\webRoot\App_Code\DataSet\certificatedDataSet.xsd</rd:SchemaPath>
        <rd:TableName>progressSpot_DataTable</rd:TableName>
        <rd:TableAdapterFillMethod />
        <rd:TableAdapterGetDataMethod />
        <rd:TableAdapterName />
      </rd:DataSetInfo>
    </DataSet>
  </DataSets>
  <Body>
    <ReportItems>
      <Tablix Name="Tablix1">
        <TablixBody>
          <TablixColumns>
            <TablixColumn>
              <Width>0.98425in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.98425in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.98425in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.98425in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.98425in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.98425in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.98425in</Width>
            </TablixColumn>
          </TablixColumns>
          <TablixRows>
            <TablixRow>
              <Height>0.23622in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Itno">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Itno.Value</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Itno</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="ci_compshortname">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!ci_compshortname.Value</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>ci_compshortname</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="cpm_carcompomodelno">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!cpm_carcompomodelno.Value</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>cpm_carcompomodelno</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="ApplyStatusCn">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!ApplyStatusCn.Value</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>ApplyStatusCn</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="ProcessSuc">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!ProcessSuc.Value</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>ProcessSuc</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="abd_certdocno">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!abd_certdocno.Value</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>abd_certdocno</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="vp_applyno">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!vp_applyno.Value</Value>
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>vp_applyno</rd:DefaultName>
                      <Style>
                        <Border>
                          <Style>Solid</Style>
                        </Border>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
          </TablixRows>
        </TablixBody>
        <TablixColumnHierarchy>
          <TablixMembers>
            <TablixMember>
              <TablixHeader>
                <Size>0.47244in</Size>
                <CellContents>
                  <Textbox Name="Textbox46">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>序號</Value>
                            <Style />
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox40</rd:DefaultName>
                    <Style>
                      <Border>
                        <Style>Solid</Style>
                      </Border>
                      <BackgroundColor>#e0eabf</BackgroundColor>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </CellContents>
              </TablixHeader>
              <TablixMembers>
                <TablixMember />
              </TablixMembers>
            </TablixMember>
            <TablixMember>
              <TablixHeader>
                <Size>0.23622in</Size>
                <CellContents>
                  <Textbox Name="Textbox36">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>申請案件送達收件</Value>
                            <Style />
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox25</rd:DefaultName>
                    <Style>
                      <Border>
                        <Style>Solid</Style>
                      </Border>
                      <BackgroundColor>#e0eabf</BackgroundColor>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </CellContents>
              </TablixHeader>
              <TablixMembers>
                <TablixMember>
                  <TablixHeader>
                    <Size>0.23622in</Size>
                    <CellContents>
                      <Textbox Name="Textbox27">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>廠商(簡稱)</Value>
                                <Style />
                              </TextRun>
                            </TextRuns>
                            <Style>
                              <TextAlign>Center</TextAlign>
                            </Style>
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox20</rd:DefaultName>
                        <Style>
                          <Border>
                            <Style>Solid</Style>
                          </Border>
                          <BackgroundColor>#e0eabf</BackgroundColor>
                          <VerticalAlign>Middle</VerticalAlign>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                  <TablixMembers>
                    <TablixMember />
                  </TablixMembers>
                </TablixMember>
                <TablixMember>
                  <TablixHeader>
                    <Size>0.23622in</Size>
                    <CellContents>
                      <Textbox Name="Textbox37">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>車型組代號</Value>
                                <Style />
                              </TextRun>
                            </TextRuns>
                            <Style>
                              <TextAlign>Center</TextAlign>
                            </Style>
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox22</rd:DefaultName>
                        <Style>
                          <Border>
                            <Style>Solid</Style>
                          </Border>
                          <BackgroundColor>#e0eabf</BackgroundColor>
                          <VerticalAlign>Middle</VerticalAlign>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                  <TablixMembers>
                    <TablixMember />
                  </TablixMembers>
                </TablixMember>
                <TablixMember>
                  <TablixHeader>
                    <Size>0.23622in</Size>
                    <CellContents>
                      <Textbox Name="Textbox38">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>申請型式</Value>
                                <Style />
                              </TextRun>
                            </TextRuns>
                            <Style>
                              <TextAlign>Center</TextAlign>
                            </Style>
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox28</rd:DefaultName>
                        <Style>
                          <Border>
                            <Style>Solid</Style>
                          </Border>
                          <BackgroundColor>#e0eabf</BackgroundColor>
                          <VerticalAlign>Middle</VerticalAlign>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                  <TablixMembers>
                    <TablixMember />
                  </TablixMembers>
                </TablixMember>
              </TablixMembers>
            </TablixMember>
            <TablixMember>
              <TablixHeader>
                <Size>0.23622in</Size>
                <CellContents>
                  <Textbox Name="Textbox74">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>2證合一</Value>
                            <Style />
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox48</rd:DefaultName>
                    <Style>
                      <Border>
                        <Style>Solid</Style>
                      </Border>
                      <BackgroundColor>#e0eabf</BackgroundColor>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </CellContents>
              </TablixHeader>
              <TablixMembers>
                <TablixMember>
                  <TablixHeader>
                    <Size>0.23622in</Size>
                    <CellContents>
                      <Textbox Name="Textbox45">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>核發日期</Value>
                                <Style />
                              </TextRun>
                            </TextRuns>
                            <Style>
                              <TextAlign>Center</TextAlign>
                            </Style>
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox40</rd:DefaultName>
                        <Style>
                          <Border>
                            <Style>Solid</Style>
                          </Border>
                          <BackgroundColor>#e0eabf</BackgroundColor>
                          <VerticalAlign>Middle</VerticalAlign>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                  <TablixMembers>
                    <TablixMember />
                  </TablixMembers>
                </TablixMember>
                <TablixMember>
                  <TablixHeader>
                    <Size>0.23622in</Size>
                    <CellContents>
                      <Textbox Name="Textbox58">
                        <CanGrow>true</CanGrow>
                        <KeepTogether>true</KeepTogether>
                        <Paragraphs>
                          <Paragraph>
                            <TextRuns>
                              <TextRun>
                                <Value>合格證編號</Value>
                                <Style />
                              </TextRun>
                            </TextRuns>
                            <Style>
                              <TextAlign>Center</TextAlign>
                            </Style>
                          </Paragraph>
                        </Paragraphs>
                        <rd:DefaultName>Textbox52</rd:DefaultName>
                        <Style>
                          <Border>
                            <Style>Solid</Style>
                          </Border>
                          <BackgroundColor>#e0eabf</BackgroundColor>
                          <VerticalAlign>Middle</VerticalAlign>
                          <PaddingLeft>2pt</PaddingLeft>
                          <PaddingRight>2pt</PaddingRight>
                          <PaddingTop>2pt</PaddingTop>
                          <PaddingBottom>2pt</PaddingBottom>
                        </Style>
                      </Textbox>
                    </CellContents>
                  </TablixHeader>
                  <TablixMembers>
                    <TablixMember />
                  </TablixMembers>
                </TablixMember>
              </TablixMembers>
            </TablixMember>
            <TablixMember>
              <TablixHeader>
                <Size>0.47244in</Size>
                <CellContents>
                  <Textbox Name="Textbox39">
                    <CanGrow>true</CanGrow>
                    <KeepTogether>true</KeepTogether>
                    <Paragraphs>
                      <Paragraph>
                        <TextRuns>
                          <TextRun>
                            <Value>申請編號</Value>
                            <Style />
                          </TextRun>
                        </TextRuns>
                        <Style>
                          <TextAlign>Center</TextAlign>
                        </Style>
                      </Paragraph>
                    </Paragraphs>
                    <rd:DefaultName>Textbox28</rd:DefaultName>
                    <Style>
                      <Border>
                        <Style>Solid</Style>
                      </Border>
                      <BackgroundColor>#e0eabf</BackgroundColor>
                      <VerticalAlign>Middle</VerticalAlign>
                      <PaddingLeft>2pt</PaddingLeft>
                      <PaddingRight>2pt</PaddingRight>
                      <PaddingTop>2pt</PaddingTop>
                      <PaddingBottom>2pt</PaddingBottom>
                    </Style>
                  </Textbox>
                </CellContents>
              </TablixHeader>
              <TablixMembers>
                <TablixMember />
              </TablixMembers>
            </TablixMember>
          </TablixMembers>
        </TablixColumnHierarchy>
        <TablixRowHierarchy>
          <TablixMembers>
            <TablixMember>
              <TablixMembers>
                <TablixMember>
                  <Group Name="Details" />
                </TablixMember>
              </TablixMembers>
            </TablixMember>
          </TablixMembers>
        </TablixRowHierarchy>
        <KeepTogether>true</KeepTogether>
        <DataSetName>DT_progressSpot</DataSetName>
        <Top>0.29104cm</Top>
        <Left>1.29646cm</Left>
        <Height>1.8cm</Height>
        <Width>17.49996cm</Width>
        <Style>
          <Border>
            <Style>None</Style>
          </Border>
        </Style>
      </Tablix>
    </ReportItems>
    <Height>1.08333in</Height>
    <Style />
  </Body>
  <Width>7.81327in</Width>
  <Page>
    <PageHeight>29.7cm</PageHeight>
    <PageWidth>21cm</PageWidth>
    <LeftMargin>2cm</LeftMargin>
    <RightMargin>2cm</RightMargin>
    <TopMargin>2cm</TopMargin>
    <BottomMargin>2cm</BottomMargin>
    <ColumnSpacing>0.13cm</ColumnSpacing>
    <Style />
  </Page>
  <rd:ReportID>01aa1e02-e1cc-469f-97c1-3ccac6ad488a</rd:ReportID>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
</Report>