﻿<%@ Page Title="" Language="C#" MasterPageFile="~/masterpage/MasterPage.master" AutoEventWireup="true"
    CodeFile="cw_export_collection_rpt.aspx.cs" Inherits="Certwork_cw_export_collection_rpt" %>

<%@ Register Assembly="Microsoft.ReportViewer.WebForms, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
    Namespace="Microsoft.Reporting.WebForms" TagPrefix="rsweb" %>
<%@ Register Src="../usercontrol/GetCalender.ascx" TagName="GetCalender" TagPrefix="uc1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <asp:ScriptManager ID="ScriptManager1" runat="server">
    </asp:ScriptManager>
    <div class="fullheader">
        <table width="100%" cellspacing="0" cellpadding="0" class="fullheaderinfo">
            <tr>
                <td valign="top">
                    <b>進出口彙編</b>
                </td>
                <td align="right" valign="top" class="font_loginInfo">
                </td>
            </tr>
        </table>
    </div>
    <!--{* fullheader end *}-->
    <div class="fullcontent">
        <table border="0" cellspacing="0" cellpadding="0" width="100%" class="font_fullcontent">
            <tr>
                <td align="right">
                    廠商：
                </td>
                <td>
                    <asp:TextBox ID="txt_compname" runat="server" MaxLength="60"></asp:TextBox>
                </td>
                <td align="right">
                    國產/進口：
                </td>
                <td>
                    <asp:RadioButtonList ID="rdBtnLst_isimport" runat="server" AutoPostBack="True" RepeatDirection="Horizontal"
                        RepeatLayout="Flow">
                    </asp:RadioButtonList>
                </td>
            </tr>
            <tr>
                <td align="right">
                    交通工具種類：
                </td>
                <td colspan="3">
                    <asp:CheckBoxList ID="cbl_vechiletypelist" runat="server" RepeatDirection="Horizontal"
                        RepeatLayout="Flow">
                    </asp:CheckBoxList>
                </td>
            </tr>
            <tr>
                <td align="right">
                    動力來源：
                </td>
                <td colspan="5">
                    <asp:RadioButtonList ID="rdBtnLst_powerfrom" runat="server" RepeatDirection="Horizontal">
                    </asp:RadioButtonList>
                </td>
            </tr>
            <tr>
                <td align="right">
                    合格証核發日期：
                </td>
                <td colspan="5">
                    <uc1:GetCalender ID="cal_applydate_S" runat="server" DateFormat="yyyyMMdd" />
                    ~<uc1:GetCalender ID="cal_applydate_E" runat="server" DateFormat="yyyyMMdd" />
                </td>
            </tr>
            <tr>
                <td align="right" colspan="6">
                    <asp:Button ID="btn_Search" runat="server" Text="查詢" CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'"
                        onmouseout="this.className='btn_mouseout'" OnClick="btn_Search_Click" />
                </td>
            </tr>
        </table>
        <rsweb:ReportViewer ID="rpv_MyData" runat="server" Font-Names="Verdana" Font-Size="8pt"
            InteractiveDeviceInfos="(Collection)" WaitMessageFont-Names="Verdana" WaitMessageFont-Size="14pt"
            Height="597px" Width="925px">
        </rsweb:ReportViewer>
    </div>
    <!--{* fullcontent end *}-->
    <div class="fullfooter">
    </div>
    <!--{* content end *}-->
</asp:Content>
