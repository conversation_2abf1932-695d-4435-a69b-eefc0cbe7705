﻿<%@ Page Title="" Language="C#" MasterPageFile="~/masterpage/MasterPage.master" AutoEventWireup="true"
    CodeFile="carcompomodel_carmodellist.aspx.cs" Inherits="applydatamt_carcompomodel_carmodellist" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="fullheader">
        <table width="100%" cellspacing="0" cellpadding="0" class="fullheaderinfo">
            <tr>
                <td valign="top">
                    <b>
                        <asp:Label ID="Vehicle_configuration_Label1" runat="server"></asp:Label></b>
                </td>
                <td align="right" valign="top" class="font_loginInfo">
                </td>
            </tr>
        </table>
    </div>
    <!--{* fullheader end *}-->
    <div class="fullcontent">
        <div class="formstyle">
            <table border="0" cellspacing="0" cellpadding="0" width="100%" class="font_fullcontent">
                <tr>
                    <td align="right">
                        申請編號：
                    </td>
                    <td>
                        <asp:Label ID="lbl_applyno" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        資料狀態：
                    </td>
                    <td>
                        <asp:Label ID="lbl_applystatus" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="left" colspan="2">
                        <asp:Button ID="addnew_carcompomodel" Text="新增車型" runat="server" CssClass="btn_mouseout"
                            onmouseover="this.className='btn_mouseover'" onmouseout="this.className='btn_mouseout'"
                            OnClick="addnew_carcompomodel_Click" />
                    </td>
                    <td align="right" colspan="2">
                        <asp:Button ID="Edit_carcompomodel" runat="server" Style="float: right;" Text="編輯車身號碼"
                            CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'"
                            onmouseout="this.className='btn_mouseout'" OnClick="Edit_carcompomodel_Click" />
                    </td>
                </tr>
            </table>
        </div>
        <div class="tableoutcome">
            <asp:GridView ID="gv_carmodellist" runat="server" Width="100%" CellPadding="0" BorderWidth="0px"
                GridLines="None" CssClass="font_fullcontent" EmptyDataText="查無資料" AutoGenerateColumns="False"
                OnRowDataBound="gv_carmodellist_RowDataBound" EnableModelValidation="True">
                <Columns>
                    <asp:TemplateField HeaderText="車型名稱" ItemStyle-CssClass="lineleft" HeaderStyle-CssClass="header">
                        <ItemTemplate>
                           <asp:Button ID="Button1_del" CommandArgument='<%# Eval("abd_applystatus") %>' runat="server"
                                Text="刪除" OnClick="Button1_del_Click" OnClientClick="return confirm('確定要刪除嗎？');"/>
                            <asp:HyperLink ID="hl_carstylename" runat="server" Text='<%# Eval("fullcarstylename") %>'></asp:HyperLink>
                        </ItemTemplate>
                        <HeaderStyle CssClass="header"></HeaderStyle>
                        <ItemStyle CssClass="lineleft"></ItemStyle>
                    </asp:TemplateField>
                    <asp:TemplateField HeaderText="最大馬力" ItemStyle-HorizontalAlign="Center" ItemStyle-CssClass="lineleft" HeaderStyle-CssClass="header">
                        <ItemTemplate>
                            <asp:Label ID="GridView1_cmdg_maxhorsepower_Label2" runat="server" Text='<%# Eval("cmdg_maxhorsepower") %>'></asp:Label>
                        </ItemTemplate>
                    </asp:TemplateField>
                    <asp:TemplateField HeaderText="最大馬力轉速" ItemStyle-HorizontalAlign="Center" ItemStyle-CssClass="lineleft" HeaderStyle-CssClass="header">
                        <ItemTemplate>
                            <asp:Label ID="GridView1_cmdg_cmdg_maxhorsepowerspeed_Label2" runat="server" Text='<%# Eval("cmdg_maxhorsepowerspeed") %>'></asp:Label>
                        </ItemTemplate>
                    </asp:TemplateField>
                    <asp:TemplateField HeaderText="代表車" ItemStyle-HorizontalAlign="Center" ItemStyle-CssClass="lineleft" HeaderStyle-CssClass="header">
                        <ItemTemplate>
                            <asp:Label ID="GridView1_berepresentativevehicle" runat="server" Text='<%# Eval("cmdg_berepresentativevehicle") %>'></asp:Label>
                        </ItemTemplate>
                    </asp:TemplateField>
                    <asp:TemplateField HeaderText="測定報告" ItemStyle-CssClass="lineright" HeaderStyle-CssClass="header">
                        <ItemTemplate>
                            <asp:HyperLink ID="hlk_testrptno" runat="server"></asp:HyperLink>
                            <asp:Label ID="lbl_noise" runat="server"></asp:Label>
                        </ItemTemplate>
                        <HeaderStyle CssClass="lineright"></HeaderStyle>
                        <ItemStyle CssClass="lineright"></ItemStyle>
                    </asp:TemplateField>
                </Columns>
            </asp:GridView>
        </div>
        <div class="tableoutcome">
            <asp:GridView ID="GridView2" runat="server" Width="100%" CellPadding="0" BorderWidth="0px"
                GridLines="None" CssClass="font_fullcontent" EmptyDataText="查無資料" AutoGenerateColumns="False"
                EnableModelValidation="True">
                <Columns>
                    <asp:TemplateField HeaderText="車型名稱" ItemStyle-CssClass="lineleft" HeaderStyle-CssClass="header">
                        <ItemTemplate>
                            <asp:Label ID="GridView1_Vehicle_Information_Label2" runat="server" Text='<%# Eval("fullcarstylename") %>'></asp:Label>
                        </ItemTemplate>
                    </asp:TemplateField>
                    <asp:TemplateField HeaderText="最大馬力" ItemStyle-HorizontalAlign="Center" ItemStyle-CssClass="lineleft" HeaderStyle-CssClass="header">
                        <ItemTemplate>
                            <asp:Label ID="GridView1_cmdg_maxhorsepower_Label2" runat="server" Text='<%# Eval("cmdg_maxhorsepower") %>'></asp:Label>
                        </ItemTemplate>
                    </asp:TemplateField>
                    <asp:TemplateField HeaderText="最大馬力轉速" ItemStyle-HorizontalAlign="Center" ItemStyle-CssClass="lineleft" HeaderStyle-CssClass="header">
                        <ItemTemplate>
                            <asp:Label ID="GridView1_cmdg_cmdg_maxhorsepowerspeed_Label2" runat="server" Text='<%# Eval("cmdg_maxhorsepowerspeed") %>'></asp:Label>
                        </ItemTemplate>
                    </asp:TemplateField>
                    <asp:TemplateField HeaderText="車身號碼" ItemStyle-CssClass="lineleft" HeaderStyle-CssClass="header">
                        <ItemTemplate>
                            <asp:TextBox ID="txt_bodyno" runat="server"></asp:TextBox>
                        </ItemTemplate>
                    </asp:TemplateField>
                    <asp:TemplateField HeaderText="引擎號碼" ItemStyle-CssClass="lineright" HeaderStyle-CssClass="header">
                        <ItemTemplate>
                            <asp:TextBox ID="txt_engineno" runat="server"></asp:TextBox>
                        </ItemTemplate>
                        <HeaderStyle CssClass="lineright"></HeaderStyle>
                    </asp:TemplateField>
                </Columns>
            </asp:GridView>
            <div>
                <asp:Button ID="Button1" Visible="false" runat="server" CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'"
                            onmouseout="this.className='btn_mouseout'" OnClick="Button1_Click" />
            </div>
        </div>
    </div>
    <!--{* fullcontent end *}-->
    <div class="fullfooter">
    </div>
    <!--{* content end *}-->
    <div>
        <asp:Label ID="lbl_applytype" runat="server" Visible="false"></asp:Label>
        <asp:Label ID="lbl_vechiletype" runat="server" Visible="false"></asp:Label>
    </div>
</asp:Content>
