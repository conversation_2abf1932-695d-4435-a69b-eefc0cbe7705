﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Services;
using System.Data;
using System.Data.SqlClient;

/// <summary>
/// Summary description for wsAccountInfo
/// </summary>
[WebService(Namespace = "http://tempuri.org/")]
[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
// To allow this Web Service to be called from script, using ASP.NET AJAX, uncomment the following line. 
// [System.Web.Script.Services.ScriptService]
public class wsAccountInfo : System.Web.Services.WebService
{

    public wsAccountInfo()
    {

        //Uncomment the following line if using designed components 
        //InitializeComponent(); 
    }

    [WebMethod]
    public void UpdateCompAccountStatusInPollution(string ID, string PD, string account, string compidnoE)
    {
        if (wsCheckAccountValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"update nv_companyinfo 
	                                        set ci_applystatus='8'
                                        where ci_compidno=@compidno

                                        update nv_accountinfo
	                                        set ai_accountstatus='8'
                                        where ai_account=@account");
            cmd.Parameters.AddWithValue("@compidno", compidnoE);
            cmd.Parameters.AddWithValue("@account", account);
            Common.Data_noisevalidation.runParaCmd1(cmd);
        }
    }

    /// <summary>
    /// 取得使用者帳號管理查詢清單
    /// </summary>
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    /// <param name="compname"></param>
    /// <param name="username"></param>
    /// <param name="statustype"></param>
    /// <returns></returns>
    [WebMethod]
    public DataSet getAccountValidationList(string ID, string PD, string compname, string username, string statustype, string stampcompno)
    {
        DataSet ds = new DataSet();
        if (wsCheckAccountValid(ID, PD))
        {
            string SQL = @"declare @SQL varchar(2000)
                        set @SQL='
                        select ci_compidno compidno,ci_compname compname,ai_username username
                        ,'''' accountbelonggroup
                        ,case ai_accountstatus
                        when ''1'' then ''已核發未啟用'' 
                        when ''2'' then ''已核發已啟用''
                        when ''3'' then ''停止使用''
                        end accountstatus
                        ,ai_usertel usertel,ai_email email
                        ,ai_account account
                        ,abg_grpid  grpid
                        into #tmplist
                        from nv_accountinfo
                        inner join nv_companyinfo on ai_accountbelongcomp=ci_compidno
                        left join nv_accountbelonggroup on abg_account=ai_account
                        where ai_accountbelongcomp=ci_compidno

                        {query}

                        select distinct compidno,compname,username
                        ,accountbelonggroup,accountstatus,usertel,email,account
                        from #tmplist

                        drop table #tmplist ' 
                        exec (@SQL)";
            string Query = string.Empty;
            if (!string.IsNullOrEmpty(compname))
                Query += string.Format(" and ci_compname like ''{0}''", "%" + compname + "%");

            if (!string.IsNullOrEmpty(username))
                Query += string.Format(" and ai_username like ''{0}''", "%" + username + "%");

            if (!string.IsNullOrEmpty(statustype))
            {
                if (statustype == "2")
                    Query += string.Format(" and ai_accountstatus in (''1'',''2'')");
                else
                    Query += string.Format(" and ai_accountstatus=''3''");
            }
            if (!string.IsNullOrEmpty(stampcompno))
            {
                Query += string.Format(" and (ci_stampcompno_g like ''{0}'' or ci_stampcompno_m like ''{0}'' or ci_stampcompno_d like ''{0}'' )", "%" + stampcompno + "%");              

            }
            SQL = SQL.Replace("{query}", Query);

            SqlCommand cmd_get = new SqlCommand(SQL);
            return Common.Data_noisevalidation.runParaCmdDS(cmd_get);

        } return ds;
    }

    /// <summary>
    /// 修改帳號之汽機車污染值
    /// </summary>
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    /// <param name="account"></param>
    /// <param name="usegmsystem"></param>
    [WebMethod]
    public void updateAccountUseGMSystem(string ID, string PD, string account, string usegmsystem)
    {
        if (wsCheckAccountValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"update nv_accountinfo set ai_use_gm_system=@usegmsystem where ai_account=@account");
            cmd.Parameters.AddWithValue("@account", account);
            cmd.Parameters.AddWithValue("@usegmsystem", usegmsystem);
            Common.Data_noisevalidation.runParaCmd1(cmd);
        }
    }

    /// <summary>
    /// 公司管理帳號清單
    /// </summary>
    /// <param name="compidnoE"></param>
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    /// <returns></returns>
    [WebMethod]
    public DataSet getCompaccountlist(string compidnoE, string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsCheckAccountValid(ID, PD))
        {
            SqlCommand cmd_get = new SqlCommand(@"select row_number() OVER(ORDER BY ai_applydate ASC) RowNum
                                 ,ai_account,ai_username,ai_usertel,ai_email,ai_accountstatus
                                ,case ai_accountstatus
                                when '0' then '申請中'
                                when '1' then '已核發未啟用'
                                when '2' then '已核發已啟用'
                                when '3' then '暫停使用'
                                when '4' then '已確認' end ai_accountstatusdesc,ai_use_gm_system
                                from nv_accountinfo
                                where ai_accountstatus in ('0','1','2','3','4')
                                and ai_accountbelongcomp=@compidnoE");
            cmd_get.Parameters.AddWithValue("@compidnoE", compidnoE);
            return Common.Data_noisevalidation.runParaCmdDS(cmd_get);
        }
        else return ds;
    }

    /// <summary>
    /// 公司基本資料維護-取得申請人基本資料
    /// </summary>
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    /// <param name="compidnoE"></param>
    /// <param name="ApplyName"></param>
    /// <returns></returns>
    [WebMethod]
    public DataTable getAccountbyCompanyAndAccountname(string ID, string PD, string compidnoE, string ApplyName)
    {
        DataTable dt = new DataTable();
        if (wsCheckAccountValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select * from nv_accountinfo where ai_accountbelongcomp=@accountbelongcomp and ai_username=@username");
            cmd.Parameters.AddWithValue("@username", ApplyName);
            cmd.Parameters.AddWithValue("@accountbelongcomp", compidnoE);
            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        } return dt;
    }

    /// <summary>
    /// 帳號申請審核查詢
    /// </summary>
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    /// <param name="status"></param>
    /// <param name="applydateS"></param>
    /// <param name="applydateE"></param>
    /// <param name="compname"></param>
    /// <returns></returns>
    [WebMethod]
    public DataTable getApplyAccountValidationList(string ID, string PD, string status
        , string applydateS, string applydateE, string compname)
    {
        DataSet ds = new DataSet();
        if (wsCheckAccountValid(ID, PD))
        {
            string QuerySQL = @"
                                --一般
                                select * from (
                                select ai_account account ,ai_accountbelongcomp compidno
                                ,ai_applydate applydate
                                ,case 
                                when ai_thefirstaccount='1' OR ci_applystatus='8'
                                then '第一次申請' else '非第一次申請' end kindtype
                                ,ci_compname compname
                                ,ai_username namedata,ai_usertel teldata
                                ,case ai_accountstatus
                                when '0' then '申請中'
                                when '1' then '已核發未啟用'
                                when '2' then '已核發已啟用'
                                when '4' then '已確認'
                                when '3' then '暫停使用'
                                when '7' then '油污染啟用'
                                when '8' then '同步中' end statusdesc
                                ,ai_enableddate enableddate
                                ,ai_accountstatus statusid
                                ,ai_account accountEdit
                                ,ai_accountbelongcomp compidnoEdit
                                from nv_accountinfo Account
                                left join nv_companyinfo on ci_compidno=ai_accountbelongcomp
                                where ai_accountstatus not in ('9')

                                union 
                                select cci_lastchangename account,cci_compidno compidno
                                ,cci_lastchangedate applydate
                                ,'公司資料異動' kindtype
                                ,ci_compname compname
                                ,cci_lastchangename namedata,ai_usertel teldata
                                ,case isnull(cci_enabledaccount,'')
                                when '' then '申請中' else '核可' end statusdesc
                                ,cci_enableddate  enableddate
                                ,case isnull(cci_enabledaccount,'')
                                when '' then '0' else '2' end statusid
                                ,convert(nvarchar,cci_changeid) accountEdit,cci_compidno compidnoEdit
                                from nv_companychangeinfo cust
                                left join nv_companyinfo on ci_compidno=cci_compidno
                                left join nv_accountinfo on ai_username=cci_lastchangename

                                union 
                                --退件
                                select aiad_account account,aiad_accountbelongcomp compidno
                                ,aiad_applydate applydate,'第一次申請' kindtype
                                ,ciad_compname compname
                                ,aiad_username namedata,aiad_usertel teldata
                                ,'退件' statusdesc,aiad_enableddate enableddate
                                ,'9' statusid
                                ,convert(nvarchar,aiad_id) accountEdit,convert(nvarchar,ciad_id) compidnoEdit
                                from nv_accountinfo_applydeny,nv_companyinfo_applydeny
                                where ciad_id=aiad_id
                                and ciad_compidno=aiad_accountbelongcomp 
                                and aiad_thefirstaccount='1'

                                union 
                                select aiad_account account,aiad_accountbelongcomp compidno
                                ,aiad_applydate applydate,'非第一次申請' kindtype
                                ,ci_compname compname
                                ,aiad_username namedata,aiad_usertel teldata
                                ,'退件' statusdesc,aiad_enableddate enableddate
                                ,'9' statusid
                                ,convert(nvarchar,aiad_id) accountEdit,aiad_accountbelongcomp compidnoEdit
                                from nv_accountinfo_applydeny,nv_companyinfo
                                where aiad_accountbelongcomp=ci_compidno and aiad_thefirstaccount<>'1'

                                union 
                                select cci_ad_lastchangename account,cci_ad_compidno compidno
                                ,cci_ad_lastchangedate applydate
                                ,'公司資料異動' kindtype
                                ,ci_compname compname
                                ,cci_ad_lastchangename namedata,ai_usertel teldata
                                ,'退件' statusdesc,cci_ad_enableddate enableddate
                                ,'9' statusid
                                ,convert(nvarchar,cci_ad_changeid) accountEdit,cci_ad_compidno compidnoEdit
                                from nv_companychangeinfo_applydeny,nv_companyinfo,nv_accountinfo
                                where ai_username=cci_ad_lastchangename
                                and cci_ad_compidno=ci_compidno
                                ) QueryA where 1=1  ";

            if (!string.IsNullOrEmpty(status) && status != "A")
                QuerySQL += string.Format(@" and statusid='{0}'", status);

            if (!string.IsNullOrEmpty(applydateS) && !string.IsNullOrEmpty(applydateE))
                QuerySQL += string.Format(@" and (convert(nvarchar,applydate,112)>='{0}' and convert(nvarchar,applydate,112)<='{1}')", applydateS, applydateE);
            else
            {
                if (!string.IsNullOrEmpty(applydateS))
                    QuerySQL += string.Format(@" and convert(nvarchar,applydate,112)>='{0}'", applydateS);
                if (!string.IsNullOrEmpty(applydateE))
                    QuerySQL += string.Format(@" and convert(nvarchar,applydate,112)<='{0}'", applydateE);
            }
            if (!string.IsNullOrEmpty(compname))
                QuerySQL += string.Format(@" and compname like '%{0}%'", compname);

            QuerySQL += " order by applydate desc";
            SqlCommand cmd_query = new SqlCommand(QuerySQL);
            return Common.Data_noisevalidation.runParaCmdDS(cmd_query).Tables[0];
        }
        return ds.Tables[0];
    }

    /// <summary>
    /// 是否有無帳號之帳號資料
    /// </summary>
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    /// <param name="compidnoE"></param>
    /// <returns></returns>
    [WebMethod]
    public bool ishavenoaccountdata(string ID, string PD, string compidnoE)
    {
        if (wsCheckAccountValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select * from nv_accountinfo ,nv_companyinfo
                            where ai_isnoaccount='1'
                            and ai_accountbelongcomp=ci_compidno
                            and ai_accountbelongcomp=@compidno");
            cmd.Parameters.AddWithValue("@compidno", compidnoE);
            DataTable dt = Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
            if (dt.Rows.Count > 0)
                return true;
            else return false;
        } return false;
    }

    /// <summary>
    /// 判斷該申請的統一編號是否有無帳號[申請中]的資料
    /// </summary>
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    /// <param name="compidnoE"></param>
    /// <returns>true:有;false:無</returns>
    [WebMethod]
    public bool NoAccountCompidnoApplyHaveChecking(string ID, string PD, string compidnoE)
    {
        if (wsCheckAccountValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select distinct ai_account from nv_accountinfo,nv_applybasedata
                            where abd_applystatus in ('20','21','31','32','33','34','40')
                            and ai_account=abd_createaccount
                            and ai_accountbelongcomp = abd_databelongcompidno
                            and ai_accountbelongcomp=@compidno");
            cmd.Parameters.AddWithValue("@compidno", compidnoE);
            DataTable dt = Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
            if (dt.Rows.Count > 0)
                return true;
            else return false;
        } return true;
    }

    /// <summary>
    /// 判斷統一編號，是否已申請過正常流程之帳號
    /// </summary>
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    /// <param name="compidno"></param>
    /// <returns>true:存在,false:不存在</returns>
    [WebMethod]
    public bool isExistsCompidno(string ID, string PD, string compidno)
    {
        if (wsCheckAccountValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select * from nv_accountinfo where ai_accountbelongcomp=@compidno");
            cmd.Parameters.AddWithValue("@compidno", compidno);
            DataTable dt = Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
            if (dt.Rows.Count > 0)
                return true;
            else return false;
        } return true;
    }

    [WebMethod]
    public void InsertCompManageGrp(string account, string compidnoE, string compmangtype, string ID, string PD)
    {
        if (wsCheckAccountValid(ID, PD))
        {
            SqlCommand cmd_ins = new SqlCommand(@"declare @count int,@type nvarchar(2)
                                select @count=COUNT(*) from nv_accountinfo
                                where ai_accountbelongcomp=@compidno
                                if(@count=1)
                                begin
	                                if(not exists(select * from nv_accountbelonggroup where abg_grpid=@compmangtype and abg_account=@account))
                                    begin
		                                insert into nv_accountbelonggroup
		                                select @account,@compmangtype
	                                end
                                end");
            cmd_ins.Parameters.AddWithValue("@account", account);
            cmd_ins.Parameters.AddWithValue("@compidno", compidnoE);
            cmd_ins.Parameters.AddWithValue("@compmangtype", compmangtype);
            Common.Data_noisevalidation.runParaCmd1(cmd_ins);

        }
    }
    [WebMethod]
    public void NonFristInsertCompManageGrp(string account, string compidnoE, string compmangtype, string ID, string PD)
    {
        if (wsCheckAccountValid(ID, PD))
        {
            SqlCommand cmd_ins = new SqlCommand(@"declare @count int
                                select @count=COUNT(*) from nv_accountbelonggroup
                                where abg_account=@account
                                if(@count=0) --帳號不存在nv_accountbelonggroup 再新增
                                begin
	                                if(not exists(select * from nv_accountbelonggroup where abg_grpid=@compmangtype and abg_account=@account))
                                    begin
		                                insert into nv_accountbelonggroup
		                                select @account,@compmangtype
	                                end
                                end");
            cmd_ins.Parameters.AddWithValue("@account", account);
            cmd_ins.Parameters.AddWithValue("@compidno", compidnoE);
            cmd_ins.Parameters.AddWithValue("@compmangtype", compmangtype);
            Common.Data_noisevalidation.runParaCmd1(cmd_ins);

        }
    }
    [WebMethod]
    public void TmpAccountInsertAccount(string account, string PWD_E, string username, string usertel
        , string email, string compidnoE, string tmpaccount, string IP
        , string enabledaccount
        , string ID, string PD)
    {
        if (wsCheckAccountValid(ID, PD))
        {
            SqlCommand cmd_insertmp = new SqlCommand(@"INSERT INTO nv_accountinfo
                   (ai_account                      ,ai_password
                   ,ai_username                     ,ai_usertel
                   ,ai_email                        ,ai_accountstatus
                   ,ai_accountbelongcomp            
                   ,ai_tempaccount                  ,ai_applydate
                   ,ai_enableddate                  ,ai_enabledip
                   ,ai_enabledaccount               )
             VALUES
                   (@account                        ,@password
                   ,@username                       ,@usertel
                   ,@email                          ,'2'
                   ,@accountbelongcomp              
                   ,@tempaccount                    ,GETDATE()    
                   ,GETDATE()                       ,@enabledip
                   ,@enabledaccount                 )");

            cmd_insertmp.Parameters.AddWithValue("@account", account);
            cmd_insertmp.Parameters.AddWithValue("@password", PWD_E);
            cmd_insertmp.Parameters.AddWithValue("@username", username);
            cmd_insertmp.Parameters.AddWithValue("@usertel", usertel);
            cmd_insertmp.Parameters.AddWithValue("@email", email);
            cmd_insertmp.Parameters.AddWithValue("@accountbelongcomp", compidnoE);
            cmd_insertmp.Parameters.AddWithValue("@tempaccount", tmpaccount);
            cmd_insertmp.Parameters.AddWithValue("@enabledip", IP);
            cmd_insertmp.Parameters.AddWithValue("@enabledaccount", enabledaccount);
            Common.Data_noisevalidation.runParaCmd1(cmd_insertmp);
        }
    }

    [WebMethod]
    public void ApproveAccount(string account, string approveaccount, string approvedate, string IP, string status, string ID, string PD)
    {
        if (wsCheckAccountValid(ID, PD))
        {
            SqlCommand cmd_upd = new SqlCommand(@"update nv_accountinfo
                                        set ai_enableddate=case isnull(@approvedate,'') when '' then '' else getdate() end
                                        ,ai_enabledip=@IP
                                        ,ai_enabledaccount=@approveaccount
                                        ,ai_accountstatus=@status
                                        where  ai_account=@account");
            cmd_upd.Parameters.AddWithValue("@approvedate", approvedate);
            cmd_upd.Parameters.AddWithValue("@IP", IP);
            cmd_upd.Parameters.AddWithValue("@approveaccount", approveaccount);
            cmd_upd.Parameters.AddWithValue("@status", status);
            cmd_upd.Parameters.AddWithValue("@account", account);
            Common.Data_noisevalidation.runParaCmd1(cmd_upd);
        }
    }

    [WebMethod]
    public void DelInsAccount(string account, string ID, string PD)
    {
        if (wsCheckAccountValid(ID, PD))
        {
            SqlCommand cmd_del = new SqlCommand("delete from nv_accountinfo where ai_account=@account");
            cmd_del.Parameters.AddWithValue("@account", account);
            Common.Data_noisevalidation.runParaCmd1(cmd_del);
        }
    }

    [WebMethod]
    /// <summary>
    /// 取得帳號資料
    /// </summary>
    /// <param name="accountid"></param>
    /// <returns></returns>
    public DataSet getAccountBase(string accountid, string ID, string PD)
    {
        DataSet ds_Account = new DataSet();
        if (wsCheckAccountValid(ID, PD))
        {
            SqlCommand cmd_Account = new SqlCommand(@"
                                    select nv_accountinfo.*
                                    ,ct_city_desc,ct_zipcode_desc 
                                    ,enabledaccount.ai_username ai_enabledaccountname
                                    from nv_accountinfo 
                                    left join nv_codetbl_city on ai_useraddrcity=ct_city_id
                                    left join nv_codetbl_zipcode on ai_useraddrlocalarea=ct_zipcode_id
                                    left join nv_accountinfo enabledaccount 
                                    on enabledaccount.ai_enabledaccount=nv_accountinfo.ai_account
                                    where nv_accountinfo.ai_account=@accountid");
            cmd_Account.Parameters.AddWithValue("@accountid", accountid);
            ds_Account = Common.Data_noisevalidation.runParaCmdDS(cmd_Account);
            return ds_Account;
        }
        else
        {
            return ds_Account;
        }
    }

    [WebMethod]
    public bool IsExistsAccount(string accountid, string ID, string PD)
    {
        //是否為核發過的帳號
        if (wsCheckAccountValid(ID, PD))
        {
            SqlCommand cmd_account = new SqlCommand(@"select * from nv_accountinfo where ai_account=@accountid and ai_accountstatus in ('1','2')");
            cmd_account.Parameters.AddWithValue("@accountid", accountid);
            DataView dv = Common.Data_noisevalidation.runParaCmd(cmd_account);
            if (dv.Count > 0) return true;
            else return false;
        }
        else return false;
    }

    [WebMethod]
    public DataSet getAccountList(string ID, string PD)
    {
        DataSet ds_Accountlist = new DataSet();
        if (wsCheckAccountValid(ID, PD))
        {
            SqlCommand cmd_Accountlist = new SqlCommand(@"select * from nv_accountinfo");
            ds_Accountlist = Common.Data_noisevalidation.runParaCmdDS(cmd_Accountlist);
            return ds_Accountlist;
        }
        else return ds_Accountlist;
    }
    /// <summary>
    /// 取得同公司所有申請帳號資料
    /// </summary>
    /// <param name="accountid"></param>
    /// <returns></returns>
    [WebMethod]
    public DataTable getSameComAccountList(string accountbelongcomp, string ID, string PD)
    {
        DataTable dt_Accountlist = new DataTable();
        if (wsCheckAccountValid(ID, PD))
        {
            SqlCommand cmd_Accountlist = new SqlCommand(@"select * from nv_accountinfo where ai_accountbelongcomp=@accountbelongcomp and ai_accountstatus in ('2') and ai_use_gm_system ='1'");
            cmd_Accountlist.Parameters.AddWithValue("@accountbelongcomp", accountbelongcomp);
            dt_Accountlist = Common.Data_noisevalidation.runParaCmdDS(cmd_Accountlist).Tables[0];
            return dt_Accountlist;
        }
        else return dt_Accountlist;
    }

    [WebMethod]
    public void InsertAccount(string account, string password, string username, string usertel, string userfax, string usercellphone
        , string email, string useraddrcity, string useraddrlocalarea, string useraddrpostno, string useraddr, string createdbysystem
        , string accountbelongcompE, string use_gm_system, string isFirst, string ID, string PD)
    {
        if (wsCheckAccountValid(ID, PD))
        {
            SqlCommand cmd_insaccount = new SqlCommand(@"INSERT INTO nv_accountinfo
                                    (ai_account,ai_password
                                    ,ai_username,ai_usertel,ai_userfax
                                    ,ai_usercellphone,ai_email
                                    ,ai_useraddrcity,ai_useraddrlocalarea
                                    ,ai_useraddrpostno,ai_useraddr
                                    ,ai_accountstatus,ai_createdbysystem
                                    ,ai_accountbelongcomp,ai_applydate
                                    ,ai_use_gm_system
                                    ,ai_thefirstaccount)
                                    VALUES
                                    (@account,@password
                                    ,@username,@usertel,@userfax
                                    ,@usercellphone,@email
                                    ,@useraddrcity,@useraddrlocalarea
                                    ,@useraddrpostno,@useraddr
                                    ,'0',@createdbysystem
                                    ,@accountbelongcomp,GETDATE()
                                    ,@use_gm_system
                                    ,@isFirst)");

            cmd_insaccount.Parameters.AddWithValue("@account", account);
            cmd_insaccount.Parameters.AddWithValue("@password", password);
            cmd_insaccount.Parameters.AddWithValue("@username", username);
            cmd_insaccount.Parameters.AddWithValue("@usertel", usertel);
            cmd_insaccount.Parameters.AddWithValue("@userfax", userfax);
            cmd_insaccount.Parameters.AddWithValue("@usercellphone", usercellphone);
            cmd_insaccount.Parameters.AddWithValue("@email", email);
            cmd_insaccount.Parameters.AddWithValue("@useraddrcity", useraddrcity);
            cmd_insaccount.Parameters.AddWithValue("@useraddrlocalarea", useraddrlocalarea);
            cmd_insaccount.Parameters.AddWithValue("@useraddrpostno", useraddrpostno);
            cmd_insaccount.Parameters.AddWithValue("@useraddr", useraddr);
            cmd_insaccount.Parameters.AddWithValue("@createdbysystem", createdbysystem);
            cmd_insaccount.Parameters.AddWithValue("@accountbelongcomp", accountbelongcompE);
            cmd_insaccount.Parameters.AddWithValue("@use_gm_system", use_gm_system);
            cmd_insaccount.Parameters.AddWithValue("@isFirst", isFirst);
            Common.Data_noisevalidation.runParaCmd1(cmd_insaccount);
        }
    }

    [WebMethod]
    public bool CheckhReAccount(string account, string ID, string PD)
    {
        //判斷是否已有重覆帳號
        if (wsCheckAccountValid(ID, PD))
        {
            SqlCommand cmd_check = new SqlCommand(@"select * from nv_accountinfo where ai_account=@account");
            cmd_check.Parameters.AddWithValue("@account", account);
            DataView dv_check = Common.Data_noisevalidation.runParaCmd(cmd_check);
            if (dv_check.Count > 0)
                return true;
            else return false;
        }
        else return false;
    }

    /// <summary>
    /// 取得該公司統一編號已有核發之帳號的筆數
    /// </summary>
    /// <param name="compidnoE"></param>
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    /// <returns></returns>
    [WebMethod]
    public int hIssuedAccountbyComp(string compidnoE, string ID, string PD)
    {
        if (wsCheckAccountValid(ID, PD))
        {
            SqlCommand cmd_issue = new SqlCommand(@"select * from nv_accountinfo 
                                    where ai_accountbelongcomp=@compidno 
                                    and ai_accountstatus in ('1','2') ");
            cmd_issue.Parameters.AddWithValue("@compidno", compidnoE);
            DataSet ds = Common.Data_noisevalidation.runParaCmdDS(cmd_issue);
            if (ds.Tables[0].Rows.Count > 0)
                return ds.Tables[0].Rows.Count;
            else return 0;
        } return 0;
    }

    /// <summary>
    /// 若公司帳號只有一組，但尚未已核發已啟用，不可再申請另一組帳號
    /// </summary>
    /// <param name="compidnoE"></param>
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    /// <returns></returns>
    [WebMethod]
    public bool OnlyAndNotApprovebyComp(string compidnoE, string ID, string PD)
    {
        //******** Gemma ai_isnoaccount='0'
        if (wsCheckAccountValid(ID, PD))
        {
            SqlCommand cmd_get = new SqlCommand("select * from nv_accountinfo where ai_accountbelongcomp=@compidno");
            cmd_get.Parameters.AddWithValue("@compidno", compidnoE);
            DataTable dt_get = Common.Data_noisevalidation.runParaCmdDS(cmd_get).Tables[0];
            if (dt_get.Rows.Count == 1 && dt_get.Rows[0]["ai_accountstatus"].ToString() != "2")
            {
                return false;
            }
            else return true;
        } return false;
    }

    [WebMethod]
    public void UpdataPWD(string account, string pwd_H, string ID, string PD)
    {
        if (wsCheckAccountValid(ID, PD))
        {
            SqlCommand cmd_upd = new SqlCommand(@"update nv_accountinfo set ai_password=@password where ai_account=@account");
            cmd_upd.Parameters.AddWithValue("@password", pwd_H);
            cmd_upd.Parameters.AddWithValue("@account", account);
            Common.Data_noisevalidation.runParaCmd1(cmd_upd);
        }
    }

    /// <summary>
    /// 廠商帳號審核狀態維護
    /// </summary>
    /// <param name="account"></param>
    /// <param name="checkaccount"></param>
    /// <param name="status"></param>
    /// <param name="checkdesc"></param>
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    [WebMethod]
    public void UpdateStatusAndCheckData(string account, string checkaccount, string status, string checkdesc, string ID, string PD)
    {
        if (wsCheckAccountValid(ID, PD))
        {
            SqlCommand cmd_upd = new SqlCommand(@"update nv_accountinfo 
                                set ai_accountstatus=@status
                                ,ai_compmgrcheckdate=getdate()
                                ,ai_compmgrcheckaccount=@checkaccount
                                ,ai_compmgrcheckdesc= @checkdesc
                                where ai_account=@account");
            cmd_upd.Parameters.AddWithValue("@status", status);
            cmd_upd.Parameters.AddWithValue("@account", account);
            cmd_upd.Parameters.AddWithValue("@checkaccount", checkaccount);
            cmd_upd.Parameters.AddWithValue("@checkdesc", checkdesc);
            Common.Data_noisevalidation.runParaCmd1(cmd_upd);
        }
    }

    [WebMethod]
    public void UpdateStatusToStop(string account, string status, string certaccount, string certname, string ID, string PD)
    {
        if (wsCheckAccountValid(ID, PD))
        {
            SqlCommand cmd_upd = new SqlCommand(@"update nv_accountinfo 
                                set ai_accountstatus=@status 
                                ,ai_whomadesuspend=@suspend
                                ,ai_whomadesuspendname=@suspendname
                                ,ai_madesuspenddate=getdate()
                                where ai_account=@account");
            cmd_upd.Parameters.AddWithValue("@status", status);
            cmd_upd.Parameters.AddWithValue("@suspend", certaccount);
            cmd_upd.Parameters.AddWithValue("@suspendname", certname);
            cmd_upd.Parameters.AddWithValue("@account", account);
            Common.Data_noisevalidation.runParaCmd1(cmd_upd);
        }
    }

    /// <summary>
    /// 修改帳號之帳號狀態
    /// </summary>
    /// <param name="account"></param>
    /// <param name="status"></param>
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    [WebMethod]
    public void UpdateStatus(string account, string status, string ID, string PD)
    {
        if (wsCheckAccountValid(ID, PD))
        {
            SqlCommand cmd_upd = new SqlCommand(@"update nv_accountinfo set ai_accountstatus=@status where ai_account=@account");
            cmd_upd.Parameters.AddWithValue("@status", status);
            cmd_upd.Parameters.AddWithValue("@account", account);
            Common.Data_noisevalidation.runParaCmd1(cmd_upd);
        }
    }

    [WebMethod]
    public void UpdateCompidno(string account, string compidnoE, string ID, string PD)
    {
        if (wsCheckAccountValid(ID, PD))
        {
            SqlCommand cmd_upd = new SqlCommand(@"update nv_accountinfo set ai_accountbelongcomp=@compidnoE where ai_account=@account");
            cmd_upd.Parameters.AddWithValue("@compidnoE", compidnoE);
            cmd_upd.Parameters.AddWithValue("@account", account);
            Common.Data_noisevalidation.runParaCmd1(cmd_upd);
        }
    }

    [WebMethod]
    public void UpdateAccountBase(string account, string username, string usertel, string userfax, string usercellphone
        , string email, string useraddrcity, string useraddrlocalarea, string useraddrpostno, string useraddr, string ID, string PD)
    {
        if (wsCheckAccountValid(ID, PD))
        {
            SqlCommand cmd_upd = new SqlCommand(@"UPDATE nv_accountinfo
                               SET ai_username=@username
                                  ,ai_usertel = @usertel
                                  ,ai_userfax = @userfax
                                  ,ai_usercellphone = @usercellphone
                                  ,ai_email = @email
                                  ,ai_useraddrcity = @useraddrcity
                                  ,ai_useraddrlocalarea = @useraddrlocalarea
                                  ,ai_useraddrpostno = @useraddrpostno
                                  ,ai_useraddr = @useraddr 
                                  ,ai_lastchangedate=getdate()     
                                 WHERE ai_account=@account");
            cmd_upd.Parameters.AddWithValue("@account", account);
            cmd_upd.Parameters.AddWithValue("@username", username);
            cmd_upd.Parameters.AddWithValue("@usertel", usertel);
            cmd_upd.Parameters.AddWithValue("@userfax", userfax);
            cmd_upd.Parameters.AddWithValue("@usercellphone", usercellphone);
            cmd_upd.Parameters.AddWithValue("@email", email);
            cmd_upd.Parameters.AddWithValue("@useraddrcity", useraddrcity);
            cmd_upd.Parameters.AddWithValue("@useraddrlocalarea", useraddrlocalarea);
            cmd_upd.Parameters.AddWithValue("@useraddrpostno", useraddrpostno);
            cmd_upd.Parameters.AddWithValue("@useraddr", useraddr);
            Common.Data_noisevalidation.runParaCmd1(cmd_upd);
        }
    }

    public void InsertPersonalInfodeclare(string account, string ID, string PD)
    {
        if (wsCheckAccountValid(ID, PD))
        {
            SqlCommand cmd_insaccount = new SqlCommand(@"

                                if not exists(select pid_account from nv_personalinfodeclare where pid_account=@account)
                                begin
                                        declare @declareinfo varchar(max)                                     
                                        set @declareinfo = '個人資料使用告知事項您好：財團法人車輛研究測試中心（以下簡稱本中心）為遵循個人資料保護法規定及本中心隱私權政策要求，並為保障您的權益，請您務必詳細閱讀本同意書之各項內容，謝謝！1.蒐集目的：■客戶服務（如環保署車輛噪音審驗合格證線上申請作業服務、案件聯繫及管理以及因執行環保署噪音審驗業務上之必要之行為；顧客滿意度問卷調查；客戶管理；不定期發送政府、本中心或產業相關之活動訊息等）；■其他業務必要範圍（如帳務/稅務管理、稽核/審計作業或其他合於營業登記項目或章程所定業務之需要）。2. 您所提供以下的個人資料：姓名、聯絡方式（通訊地址、電話、電子信箱）或其他得以直接或間接識別您個人之資料皆受本中心保全維護，並僅限於上述業務範圍內使用。3. 個人資料利用之期間、地區、對象及方式：(1) 本中心將於蒐集目的之存續期間內合理利用您的個人資料。(2) 除蒐集之目的涉及國際業務或活動外，本中心將僅於中華民國領域內利用您的個人資料。(3) 本中心業務承辦人員於蒐集之目的範圍內，以合理方式利用您的個人資料。(4) 本中心因業務需要而委託外部機關處理您的個人資料時，本中心將善盡監督之責。4. 您可自由選擇是否提供本中心您的個人資料，若您不願提供，本中心將無法為您提供蒐集目的之相關服務。但若您所提供之個人資料不正確，經檢舉、本中心發現或經他人冒用、盜用，有資料不實之情形，本中心有權終止您的權利。'

                                        INSERT INTO nv_personalinfodeclare
                                        (pid_account
                                        ,pid_declareinfo
                                        ,pid_createdate
                                        ,pid_createaccount
                                        ,pid_gemsys)
                                        VALUES
                                        (@account
                                        ,@declareinfo
                                        ,GETDATE()
                                        ,@account
                                        ,'機動車輛排氣暨噪音審驗系統')
                                  end ");

            cmd_insaccount.Parameters.AddWithValue("@account", account);

            Common.Data_noisevalidation.runParaCmd1(cmd_insaccount);
        }
    }
    //判斷個資同意
    public bool Isdeclareinfo(string account, string ID, string PD)
    {
        SqlCommand sqlCmd = new SqlCommand(@"
                                        if exists(select pid_account from nv_personalinfodeclare where pid_account=@account)
                                        begin
	                                        select 1
	                                        return
                                        end
                                        select 0
                                        ");
        sqlCmd.Parameters.AddWithValue("@account", account);
        
        object obj = Common.Data_noisevalidation.runScalar(sqlCmd);
        return obj.ToString() == "1" ? true : false;
    }
    /// <summary>
    /// 公司管理者的mail
    /// </summary>
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    /// <param name="compidno"></param>
    /// <returns>mail</returns>
    [WebMethod]
    public string getCompAdminEmail(string ID, string PD, string compidno)
    {
        if (wsCheckAccountValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@" select ai_account,ai_email from nv_accountinfo 
                                                   join nv_accountbelonggroup on ai_account=abg_account
                                                   where ai_accountbelongcomp=@compidno and abg_grpid='50'");
            cmd.Parameters.AddWithValue("@compidno", compidno);

            DataView dv_cmd = Common.Data_noisevalidation.runParaCmd(cmd);
            if (dv_cmd.Count == 0)
                return "";
            else
                return dv_cmd[0]["ai_email"].ToString();
        }
        else return "";
    }

    private bool wsCheckAccountValid(string ID, string PD)
    {
        string[][] x = new string[7][];
        x[0] = new string[2] { "AWS1", "qwed3435" };
        x[1] = new string[2] { "AWS2", "cvfg54y6" };
        x[2] = new string[2] { "AWS3", "aws6yju" };
        x[3] = new string[2] { "AWS4", "myju6tgf" };
        x[4] = new string[2] { "AWS5", "Qacef53" };
        x[5] = new string[2] { "AWS6", "hyR453f" };
        x[6] = new string[2] { "AWS7", "dghPki8" };

        for (int i = 0; i < 7; i++)
        {
            if (x[i][0].ToString() == ID && x[i][1].ToString() == PD)
            {
                return true;
            }
        }
        return false;
    }

}
