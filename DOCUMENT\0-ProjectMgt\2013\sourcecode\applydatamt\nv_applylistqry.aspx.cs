﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.IO;
using System.Text;

public partial class accountmt_nv_applylistqry : BaseAdminPage
{   
    protected void Page_Load(object sender, EventArgs e)
    {
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = '../accountmt/index.aspx';</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" && (GetAgentAccountGrpList().Contains("cam_adm") || GetAgentAccountGrpList().Contains("cga_adm") || GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("pg_adm")))
            {
                if (!IsPostBack)
                {
                    BindData();
                    BindDataStauts();
                    BindCarrType();
                    BindAdaptStandarddata();
                    BindTransportType();
                }
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }
    }
    public void BindData()
    {       
        lbl_compname.Text = GetAgentIDName(); //取得公司名稱
        Cryptography crypy = new Cryptography();
        int keynum = crypy.getRadNum();
        string[] arr = crypy.wsApplylistQryValid(keynum);
        wsApplylistQry oALQ = new wsApplylistQry();
        DataTable dtList = oALQ.get_applylistqryno_list(lbl_compname.Text, txt_applyno.Text, ddl_applystatus.SelectedValue, txt_enginefamily.Text, txt_factoryname.Text, txt_carstyleno.Text, ddl_cartype.SelectedValue, ddl_standarddate.SelectedValue, ddl_transporttype.SelectedValue, cal_applydate_S.GetDate, cal_applydate_E.GetDate, cal_validdate_S.GetDate, cal_validdate_E.GetDate, arr[0].ToString(), arr[1].ToString());
        gv_data.DataSource = dtList;
        gv_data.DataBind();


    }
    private void BindDataStauts()
    {
        ddl_applystatus.Items.Clear();
        wsCodetbl wscode = new wsCodetbl();
        string[] radom_cd = wsCodetblValidate(getRadNum());
        ddl_applystatus.DataSource = wscode.getApplyStatus(radom_cd[0].ToString(), radom_cd[1].ToString());
        ddl_applystatus.DataTextField = "ct_aps_desc";
        ddl_applystatus.DataValueField = "ct_aps_id";
        ddl_applystatus.DataBind();

        ddl_applystatus.Items.Insert(0, new ListItem("全部", ""));
    }

    private void BindCarrType()
    {
        ddl_cartype.Items.Clear();
        wsCodetbl wscode = new wsCodetbl();
        string[] radom_cd = wsCodetblValidate(getRadNum());
        ddl_cartype.DataSource = wscode.getCarType(radom_cd[0].ToString(), radom_cd[1].ToString());
        ddl_cartype.DataTextField = "ct_ct_desc";
        ddl_cartype.DataValueField = "ct_ct_id";
        ddl_cartype.DataBind();

        ddl_cartype.Items.Insert(0, new ListItem("全部", ""));
    }

    private void BindAdaptStandarddata()
    {
        ddl_standarddate.Items.Clear();
        wsCodetbl wscode = new wsCodetbl();
        string[] radom_cd = wsCodetblValidate(getRadNum());
        string Query = string.Empty;

        ddl_standarddate.DataSource = wscode.getStandardDate(radom_cd[0].ToString(), radom_cd[1].ToString());
        ddl_standarddate.DataTextField = "ct_sd_desc";
        ddl_standarddate.DataValueField = "ct_sd_id";
        ddl_standarddate.DataBind();
        ddl_standarddate.Items.Insert(0, new ListItem("全部", ""));
    }
    private void BindTransportType()
    {
        ddl_transporttype.Items.Clear();
        wsCodetbl wscode = new wsCodetbl();
        string[] radom_cd = wsCodetblValidate(getRadNum());
        ddl_transporttype.DataSource = wscode.getTransportTypeSearch(radom_cd[0].ToString(), radom_cd[1].ToString(), ddl_cartype.SelectedValue);
        ddl_transporttype.DataTextField = "ct_nvc_desc";
        ddl_transporttype.DataValueField = "ct_nvc_id";
        ddl_transporttype.DataBind();
        ddl_transporttype.Items.Insert(0, new ListItem("全部", ""));
    }
    protected void ddl_cartype_SelectedIndexChanged(object sender, EventArgs e)
    {
        // BindAdaptStandarddata();
        BindTransportType();

    }

    protected void gv_data_RowDataBound(object sender, GridViewRowEventArgs e)
    {

        string[] radom_AB = wsApplyInfoValidate(getRadNum());

        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            HyperLink hlk_abd_applyno = (HyperLink)e.Row.FindControl("hlk_abd_applyno");
            Label lbl_applystatus = (Label)e.Row.FindControl("lbl_applystatus");
            Label lbl_adaptstandarddate = (Label)e.Row.FindControl("lbl_adaptstandarddate");
            Label lbl_vechiletype = (Label)e.Row.FindControl("lbl_vechiletype");

            string sApplystatuscode = DataBinder.Eval(e.Row.DataItem, "abd_applystatus").ToString();
            string applyno = DataBinder.Eval(e.Row.DataItem, "abd_applyno").ToString();
            string applystatus = DataBinder.Eval(e.Row.DataItem, "ct_aps_desc").ToString();
            string adaptstandarddate = DataBinder.Eval(e.Row.DataItem, "abd_adaptstandarddate").ToString();
            string str_vechiletype = DataBinder.Eval(e.Row.DataItem, "abd_vechiletype").ToString().TrimEnd(',');
            str_vechiletype = str_vechiletype.Replace("G", "汽車").Replace("M", "機車").Replace("D", "柴油車");
            adaptstandarddate = adaptstandarddate.Replace("1", "第一期").Replace("2", "第二期").Replace("3", "第三期").Replace("4", "第四期");
            HyperLink hlk_carcompomodellist = (HyperLink)e.Row.FindControl("hlk_carcompomodellist");
            //判斷資料狀態不為填寫中、補件、拒件;申請型式為新車型、沿用、不異動、撤件
            if (sApplystatuscode.Equals("10") || sApplystatuscode.Equals("40") || sApplystatuscode.Equals("90") || applystatus.Equals("新申請") || applystatus.Equals("沿用") || applystatus.Equals("不異動") || applystatus.Equals("撤銷"))
            {
                hlk_carcompomodellist.NavigateUrl = "carcompomodelList.aspx?applyno=" + Server.UrlEncode(EnCode(applyno));
            }
            else
            {
                hlk_carcompomodellist.NavigateUrl = "applybasedata_detail.aspx?applyno=" + Server.UrlEncode(EnCode(applyno));
            }
            hlk_carcompomodellist.ImageUrl = "../images/show.gif";
            lbl_applystatus.Text = applystatus;
            lbl_vechiletype.Text = str_vechiletype;

            lbl_adaptstandarddate.Text = adaptstandarddate;// == "1" ? "一期" : adaptstandarddate == "2" ? "二期" : adaptstandarddate == "3" ? "三期" : "四期";
            hlk_abd_applyno.Attributes["ApplyID"] = DataBinder.Eval(e.Row.DataItem, "abd_id").ToString().Trim();
            hlk_abd_applyno.Text = applyno;

            if (sApplystatuscode == Library.code_status.Exe_Status.填單中.GetHashCode().ToString() || sApplystatuscode == Library.code_status.Exe_Status.補件中.GetHashCode().ToString() || sApplystatuscode == Library.code_status.Exe_Status.拒件.GetHashCode().ToString())
            {

                hlk_abd_applyno.NavigateUrl = "applybasedata.aspx?applyno=" + Server.UrlEncode(EnCode(applyno));
            }
            else
            {
                hlk_abd_applyno.NavigateUrl = "applybasedata_detail.aspx?applyno=" + Server.UrlEncode(EnCode(applyno));
            }

            //資料狀態

            if (sApplystatuscode == Library.code_status.Exe_Status.初審中.GetHashCode().ToString()
                || sApplystatuscode == Library.code_status.Exe_Status.複審中.GetHashCode().ToString()
                || sApplystatuscode == Library.code_status.Exe_Status.核閱中.GetHashCode().ToString()
                || sApplystatuscode == Library.code_status.Exe_Status.核決核閱退回.GetHashCode().ToString()
                || sApplystatuscode == Library.code_status.Exe_Status.複審核閱退回.GetHashCode().ToString())
            {
                lbl_applystatus.Text = "審核中";
            }
            else
            {
                lbl_applystatus.Text = Library.code_status.Exe_Status.填單中.GetHashCode().ToString();
            }
            lbl_applystatus.Text = applystatus;
        }
    }

    protected void btn_Search_Click(object sender, EventArgs e)
    {
        #region 特殊字元判斷-沒有MasterPage
        foreach (Control ctl in Page.Form.Controls)
        {
            Control content = (Control)ctl;
            foreach (Control c in content.Controls)
            {
                if (c is TextBox)
                {
                    TextBox objTextBox = default(TextBox);
                    objTextBox = (TextBox)c;
                    if ((tbWord(objTextBox.Text)))
                    {
                        MessageBox.Show(objTextBox.Text + ":含有系統不允許的特殊字元");
                        return;
                    }
                }
            }
        }      
        #endregion

        #region 特殊字元判斷-日期
        if (tbWord(cal_applydate_S.GetDate) || tbWord(cal_applydate_E.GetDate) || tbWord(cal_validdate_S.GetDate) || tbWord(cal_validdate_E.GetDate))
        {
            MessageBox.Show("日期選項含有系統不允許的特殊字元");
            return;
        }
        #endregion
        BindData();

    }
    protected void btn_AddApply_Click(object sender, EventArgs e)
    {
        Response.Redirect("applybasedata.aspx?applyno=", false);
    }
    protected void gv_data_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        gv_data.PageIndex = e.NewPageIndex;
        BindData();
    }



}