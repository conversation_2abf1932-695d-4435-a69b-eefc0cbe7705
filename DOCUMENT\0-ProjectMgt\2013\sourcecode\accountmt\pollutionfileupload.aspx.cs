﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.IO;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;

public partial class accountmt_pollutionfileupload : System.Web.UI.Page
{
    wsAccountInfo wsaccount = new wsAccountInfo();
    wsFileInfo wsfile = new wsFileInfo();
    wsCompany wscomp = new wsCompany();
    wsCodetbl wscode = new wsCodetbl();
    Cryptography crypt = new Cryptography();
    protected void Page_Load(object sender, EventArgs e)
    {
        if (string.IsNullOrEmpty(Request["account"]) || string.IsNullOrEmpty(Request["compidno"]))
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", string.Format(@"<script>alert('{0}');
                                                    window.location = 'index.aspx';</script>", ConfigurationManager.AppSettings["Msg.參數正確性"].ToString()));
            return;
        }
        bool resultVer = false;
        string account = Server.UrlDecode(crypt.DeCode(Request["account"].ToString()));
        string encrypttype = Session["encryfunc"].ToString();
        string originalVkey = Session["Vkey"].ToString();
        string IPValue = crypt.GetIP();
        switch (encrypttype)
        {
            case "M":
                if (crypt.GetMD5(account + IPValue) == originalVkey)
                    resultVer = true;
                else resultVer = false;
                break;
            case "H":
                if (crypt.GetHtmlEnCode(account + IPValue) == originalVkey)
                    resultVer = true;
                else resultVer = false;
                break;
            case "B":
                if (crypt.GetBig(account + IPValue) == originalVkey)
                    resultVer = true;
                else resultVer = false;
                break;
            case "U":
                if (crypt.GetUTF(account + IPValue) == originalVkey)
                    resultVer = true;
                else resultVer = false;
                break;
            default:
                resultVer = false;
                break;
        }

        if (!resultVer)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", string.Format(@"<script>alert('{0}');
                                                    window.location = 'index.aspx';</script>", ConfigurationManager.AppSettings["Msg.存取權限"].ToString()));
            return;
        }

        if (!IsPostBack)
        {
            BindData();
        }
    }

    private void BindData()
    {
        string[] radom_F = crypt.wsFileValidate(crypt.getRadNum());
        string[] radom_ct = crypt.wsCodetblValidate(crypt.getRadNum());
        string[] radom_c = crypt.wsCompanyValidate(crypt.getRadNum());
        string compidnoE = Server.UrlDecode(Request["compidno"].ToString());
        DataTable dtComp = wscomp.getCompanyBase(compidnoE, radom_c[0].ToString(), radom_c[1].ToString()).Tables[0];
        if (dtComp.Rows.Count > 0)
        {
            lbl_compidno.Text = crypt.DeCode(compidnoE);
            lbl_compname.Text = dtComp.Rows[0]["ci_compname"].ToString();
            lbl_compename.Text = dtComp.Rows[0]["ci_compename"].ToString();
            lbl_chargemane.Text = dtComp.Rows[0]["ci_chargeman"].ToString();
            lbl_comptel.Text = dtComp.Rows[0]["ci_comptel"].ToString();
            lbl_compfax.Text = dtComp.Rows[0]["ci_compfax"].ToString();
            lbl_compaddr.Text = (dtComp.Rows[0]["ci_compaddrpostno"].ToString() == "300") ?
                dtComp.Rows[0]["ci_compaddrpostno"].ToString()
                + wscode.getZipName(dtComp.Rows[0]["ci_compaddrcity"].ToString(), dtComp.Rows[0]["ci_compaddrlocalarea"].ToString(), radom_ct[0].ToString(), radom_ct[1].ToString())
                + dtComp.Rows[0]["ci_compaddr"].ToString()
                : dtComp.Rows[0]["ci_compaddrpostno"].ToString()
                + wscode.getCityName(dtComp.Rows[0]["ci_compaddrcity"].ToString(), radom_ct[0].ToString(), radom_ct[1].ToString())
                + wscode.getZipName(dtComp.Rows[0]["ci_compaddrcity"].ToString(), dtComp.Rows[0]["ci_compaddrlocalarea"].ToString(), radom_ct[0].ToString(), radom_ct[1].ToString())
                + dtComp.Rows[0]["ci_compaddr"].ToString();
            lbl_certaddr.Text = (dtComp.Rows[0]["ci_certaddrpostno"].ToString() == "300") ?
                dtComp.Rows[0]["ci_certaddrpostno"].ToString()
                + wscode.getZipName(dtComp.Rows[0]["ci_certaddrcity"].ToString(), dtComp.Rows[0]["ci_certaddrlocalarea"].ToString(), radom_ct[0].ToString(), radom_ct[1].ToString())
                + dtComp.Rows[0]["ci_certaddr"].ToString()
                : dtComp.Rows[0]["ci_certaddrpostno"].ToString()
                + wscode.getCityName(dtComp.Rows[0]["ci_certaddrcity"].ToString(), radom_ct[0].ToString(), radom_ct[1].ToString())
                + wscode.getZipName(dtComp.Rows[0]["ci_compaddrcity"].ToString(), dtComp.Rows[0]["ci_certaddrlocalarea"].ToString(), radom_ct[0].ToString(), radom_ct[1].ToString())
                + dtComp.Rows[0]["ci_certaddr"].ToString();
            //檔案
            DataTable dtFile = wsfile.getCompanyFileList(radom_F[0].ToString(), radom_F[1].ToString(), compidnoE);
            foreach (DataRow dr in dtFile.Rows)
            {
                string userfilename = dr["afi_userfilename"].ToString();
                string fileID = dr["afi_fileid"].ToString();
                if (dr["afi_filetype"].ToString() == "1")
                {
                    hlk_comDoc.NavigateUrl = "../FileDownloadSeal.aspx?File=" + Server.UrlEncode(crypt.EnCode(fileID));
                    hlk_comDoc.Text = userfilename;
                }
                else if (dr["afi_filetype"].ToString() == "2")
                {
                    hlk_facCert.NavigateUrl = "../FileDownloadSeal.aspx?File=" + Server.UrlEncode(crypt.EnCode(fileID));
                    hlk_facCert.Text = userfilename;
                }
                else if (dr["afi_filetype"].ToString() == "3")
                {
                    hlk_authLetter.NavigateUrl = "../FileDownloadSeal.aspx?File=" + Server.UrlEncode(crypt.EnCode(fileID));
                    hlk_authLetter.Text = userfilename;
                }
                else if (dr["afi_filetype"].ToString() == "4")
                {
                    hlk_importCert.NavigateUrl = "../FileDownloadSeal.aspx?File=" + Server.UrlEncode(crypt.EnCode(fileID));
                    hlk_importCert.Text = userfilename;
                }
            }

            lbl_updateman.Text = dtComp.Rows[0]["ci_lastchangename"].ToString();
            lbl_updatedate.Text = dtComp.Rows[0]["ci_lastchangedate"].ToString();
        }
    }
    protected void btn_tmpSave_Click(object sender, EventArgs e)
    {
        Save();        
        MessageBox.Show("暫存成功");
        BindData();
    }
    protected void btn_Apply_Click(object sender, EventArgs e)
    {
        wsGroupInfo wsgroup = new wsGroupInfo();
        string[] radom_gp = crypt.wsGroupValidate(crypt.getRadNum());
        DataTable dtFirstAdmList = wsgroup.getFirstAdmList(radom_gp[0].ToString(), radom_gp[1].ToString());
        if (dtFirstAdmList.Rows.Count > 0)
        {
            string certpersonlist = string.Empty;
            foreach (DataRow dr in dtFirstAdmList.Rows)
            {
                certpersonlist += dr["ai_email"].ToString() + ",";
            }
            certpersonlist = certpersonlist.Substring(0, certpersonlist.Length - 1);

            //email
            string[] _mailTo = new string[1];
            _mailTo[0] = certpersonlist;//收件人                
            string[] _mailCc = new string[1];//副本
            _mailCc[0] = "";
            string[] _mailBcc = new string[1];//密件
            _mailBcc[0] = "";
            if (Convert.ToBoolean(ConfigurationManager.AppSettings["DEBUGMode"].ToString()))
            {
                //測試
                _mailTo[0] = ConfigurationManager.AppSettings["MailTestTo"].ToString();
            }
            SendMail mail = new SendMail();
            if (mail.Mail_PollutionNote(_mailTo, _mailCc, _mailBcc, lbl_compname.Text.Trim()))
            {
                Save();
                string compidnoE = Server.UrlDecode(Request["compidno"].ToString());
                string account = Server.UrlDecode(crypt.DeCode(Request["account"].ToString()));
                string[] radom_A = crypt.wsAccountValidate(crypt.getRadNum());
                wsaccount.UpdateCompAccountStatusInPollution(radom_A[0].ToString(), radom_A[1].ToString(), account, compidnoE);
                Session.RemoveAll();
                Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已寄件通知，待審核人員審核');
                                                    window.location = 'index.aspx';</script>"); return;                
            }
            else
            {
                MessageBox.Show("寄件失敗");
            }
        }
        else
        {
            MessageBox.Show("尚未有審驗人員資料，請通知系統管理人員");
        } 
    }

    private void Save()
    {
        string[] radom_a = crypt.wsAccountValidate(crypt.getRadNum());
        string[] radom_F = crypt.wsFileValidate(crypt.getRadNum());
        string compidnoE = Server.UrlDecode(Request["compidno"].ToString());
        string compidno = crypt.DeCode(compidnoE);
        string account = Server.UrlDecode(crypt.DeCode(Request["account"].ToString()));
        DataTable dtAccount = wsaccount.getAccountBase(account, radom_a[0].ToString(), radom_a[1].ToString()).Tables[0];
        DataTable dtFile = wsfile.getCompanyFileList(radom_F[0].ToString(), radom_F[1].ToString(), compidnoE);
        if (dtFile.Rows.Count == 0 && fu_compDoc.FileName.Length == 0)
        {
            MessageBox.Show("公司證明文件為必上傳文件");
            return;
        }
        else
        {
            string filepath = ConfigurationManager.AppSettings["FilePath"].ToString();
            string strUpLoadPath = filepath + "Company\\" + compidno + "\\";
            //如果上傳路徑中沒有該目錄，則自動新增
            if (!Directory.Exists(strUpLoadPath.Substring(0, strUpLoadPath.LastIndexOf("\\"))))
            {
                Directory.CreateDirectory(strUpLoadPath.Substring(0, strUpLoadPath.LastIndexOf("\\")));
            }
            string[] strFileNm;
            string filename1 = "";
            string filename2 = "";
            string newfile = "";
            //公司證明文件
            if (fu_compDoc.FileName.Length > 0)
            {
                wsfile.DelPollutionFile(radom_F[0].ToString(), radom_F[1].ToString(), compidnoE, "1");
                strFileNm = fu_compDoc.PostedFile.FileName.Split('\\');
                filename1 = strFileNm[strFileNm.GetUpperBound(0)];
                filename2 = compidno + "_1" + "_" + DateTime.Now.ToString("yyyyMMddHHmmss") + "_" + filename1;
                wsfile.InsertCompanyFile("1", "1", filename1, filename2, strUpLoadPath, "公司證明文件", dtAccount.Rows[0]["ai_username"].ToString()
                            , compidnoE, radom_F[0].ToString(), radom_F[1].ToString(), "N");
                newfile = strUpLoadPath + filename2;
                fu_compDoc.PostedFile.SaveAs(newfile);
            }
            //工廠登記證
            if (fu_facCert.FileName.Length > 0)
            {
                wsfile.DelPollutionFile(radom_F[0].ToString(), radom_F[1].ToString(), compidnoE, "2");
                strFileNm = fu_facCert.PostedFile.FileName.Split('\\');
                filename1 = strFileNm[strFileNm.GetUpperBound(0)];
                filename2 = compidno + "_2" + "_" + DateTime.Now.ToString("yyyyMMddHHmmss") + "_" + filename1;
                wsfile.InsertCompanyFile("2", "1", filename1, filename2, strUpLoadPath, "工廠登記證", dtAccount.Rows[0]["ai_username"].ToString()
                            , compidnoE, radom_F[0].ToString(), radom_F[1].ToString(), "N");
                newfile = strUpLoadPath + filename2;
                fu_facCert.PostedFile.SaveAs(newfile);
            }
            //代理授權證明函
            if (fu_authLetter.FileName.Length > 0)
            {
                wsfile.DelPollutionFile(radom_F[0].ToString(), radom_F[1].ToString(), compidnoE, "3");
                strFileNm = fu_authLetter.PostedFile.FileName.Split('\\');
                filename1 = strFileNm[strFileNm.GetUpperBound(0)];
                filename2 = compidno + "_3" + "_" + DateTime.Now.ToString("yyyyMMddHHmmss") + "_" + filename1;
                wsfile.InsertCompanyFile("3", "1", filename1, filename2, strUpLoadPath, "代理授權證明函", dtAccount.Rows[0]["ai_username"].ToString()
                            , compidnoE, radom_F[0].ToString(), radom_F[1].ToString(), "N");
                newfile = strUpLoadPath + filename2;
                fu_authLetter.PostedFile.SaveAs(newfile);
            }
            //進口商證明文件
            if (fu_importCert.FileName.Length > 0)
            {
                wsfile.DelPollutionFile(radom_F[0].ToString(), radom_F[1].ToString(), compidnoE, "4");
                strFileNm = fu_importCert.PostedFile.FileName.Split('\\');
                filename1 = strFileNm[strFileNm.GetUpperBound(0)];
                filename2 = compidno + "_4" + "_" + DateTime.Now.ToString("yyyyMMddHHmmss") + "_" + filename1;
                wsfile.InsertCompanyFile("4", "1", filename1, filename2, strUpLoadPath, "進口商證明文件", dtAccount.Rows[0]["ai_username"].ToString()
                            , compidnoE, radom_F[0].ToString(), radom_F[1].ToString(), "N");
                newfile = strUpLoadPath + filename2;
                fu_importCert.PostedFile.SaveAs(newfile);
            }
        }
    }
}