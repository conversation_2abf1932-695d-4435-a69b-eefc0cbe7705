﻿<%@ Page Title="" Language="C#" MasterPageFile="~/masterpage/MasterPage.master" AutoEventWireup="true"
    CodeFile="applybasedata_detail.aspx.cs" Inherits="applydatamt_applybasedata_detail" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script language="javascript" type="text/javascript">
        function raiseAsyncPostback() { __doPostBack('ctl00$ContentPlaceHolder1$btnReflash', ''); }
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="fullheader">
        <table width="100%" cellspacing="0" cellpadding="0" class="fullheaderinfo">
            <tr>
                <td valign="top">
                    <b>噪音合格證明申請-車型組清單</b>
                </td>
                <td align="right" valign="top" class="font_loginInfo">
                </td>
            </tr>
        </table>
    </div>
    <!--{* fullheader end *}-->
    <div class="fullcontent">
        <div class="formstyle">
            <table border="0" cellspacing="0" cellpadding="0" width="100%" class="font_fullcontent">
                <tr>
                    <td align="right">
                        申請編號：
                    </td>
                    <td>
                        <asp:Label ID="lbl_applyno" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        申請廠商：
                    </td>
                    <td>
                        <asp:Label ID="lbl_databelongcompname" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        資料狀態：
                    </td>
                    <td>
                        <asp:Label ID="lbl_applystatus" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        交通工具種類別：
                    </td>
                    <td>
                        <asp:Label ID="lbl_vechiletype" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        聯絡人：
                    </td>
                    <td>
                        <asp:Label ID="lbl_contactname" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        聯絡人電話：
                    </td>
                    <td>
                        <asp:Label ID="lbl_contacttel" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        引擎族：
                    </td>
                    <td>
                        <asp:Label ID="lbl_enginefamily" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        車型年：
                    </td>
                    <td>
                        <asp:Label ID="lbl_carstyleyear" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        製造廠：
                    </td>
                    <td>
                        <asp:Label ID="lbl_factoryname" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        引擎流水號：
                    </td>
                    <td>
                        <asp:Label ID="lbl_enginesn" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        兩證合一申請方式：
                    </td>
                    <td>
                        <asp:Label ID="lbl_twocertmethod" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        適用期別：
                    </td>
                    <td>
                        <asp:Label ID="lbl_adaptstandarddate" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        申請函發文日期：
                    </td>
                    <td>
                        <asp:Label ID="lbl_officedocdate" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        申請函發文字號：
                    </td>
                    <td>
                        <asp:Label ID="lbl_officedocno" runat="server"></asp:Label>
                        <asp:HyperLink ID="hlk_userdocfileid" runat="server">
                            <asp:Image ID="Img_icon" runat="server" Width="24" Height="24" /></asp:HyperLink>
                    </td>
                    <td align="right">
                        動力來源：
                    </td>
                    <td>
                        <asp:Label ID="lbl_powerfrom" runat="server"></asp:Label>
                        
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        合格證寄發地址：
                    </td>
                    <td colspan="5">
                        <asp:Label ID="lbl_certidocaddr" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        送件時間：
                    </td>
                    <td>
                        <asp:Label ID="lbl_execdate" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        送件人：
                    </td>
                    <td colspan="3">
                        <asp:Label ID="lbl_execaccount" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        繳費資料：
                    </td>
                    <td align="left" colspan="5">
                        <asp:GridView ID="gv_paydata" runat="server" AutoGenerateColumns="False" EnableModelValidation="True"
                            BackColor="White" BorderColor="#CC9966" BorderWidth="1px" CellPadding="5" Height="16px"
                            Width="626px" OnRowDataBound="gv_paydata_RowDataBound">
                            <Columns>
                                <asp:BoundField HeaderText="局號" DataField="pd_bankaccount" ItemStyle-HorizontalAlign="Center"
                                    HeaderStyle-HorizontalAlign="Center" />
                                <asp:BoundField HeaderText="日期" DataField="pd_paydate" DataFormatString="{0:d}" ItemStyle-HorizontalAlign="Center"
                                    HeaderStyle-HorizontalAlign="Center" />
                                <asp:BoundField HeaderText="序號" DataField="pd_paysn" ItemStyle-HorizontalAlign="Center"
                                    HeaderStyle-HorizontalAlign="Center" />
                                <asp:BoundField HeaderText="金額" DataField="pd_paymoney" ItemStyle-HorizontalAlign="Center"
                                    HeaderStyle-HorizontalAlign="Center" />
                                <asp:TemplateField HeaderText="繳費單" HeaderStyle-Width="300" ItemStyle-HorizontalAlign="Center"
                                    HeaderStyle-HorizontalAlign="Center">
                                    <ItemTemplate>
                                        <asp:LinkButton ID="lbnt_userfilename" runat="server"></asp:LinkButton>
                                    </ItemTemplate>
                                </asp:TemplateField>
                            </Columns>
                        </asp:GridView>
                    </td>
                </tr>
                <tr>
                    <td align="center" colspan="6">
                        <asp:Button ID="btn_Accept" runat="server" Text="收件" CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'"
                            onmouseout="this.className='btn_mouseout'" OnClick="btn_Accept_Click" Visible="False" />
                        <asp:Button ID="btn_Refuse" runat="server" Text="拒件" CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'"
                            onmouseout="this.className='btn_mouseout'" OnClick="btn_Refuse_Click" Visible="False" />
                        <asp:Button ID="btn_Withdraw" runat="server" Text="撤件" CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'"
                            onmouseout="this.className='btn_mouseout'" OnClick="btn_Withdraw_Click" Visible="False" />
                    </td>
                </tr>
            </table>
        </div>
        <div class="tableoutcome">
            <asp:GridView ID="gv_data" runat="server" AutoGenerateColumns="false" AllowPaging="True"
                PageSize="20" Width="100%" CellSpacing="0" CellPadding="0" BorderWidth="0" GridLines="None"
                CssClass="font_fullcontent" OnRowDataBound="gv_data_RowDataBound" EmptyDataText="查無資料"
                OnPageIndexChanging="gv_data_PageIndexChanging">
                <Columns>
                    <asp:TemplateField HeaderText="車型組代號" HeaderStyle-Width="100px" ItemStyle-CssClass="lineleft"
                        HeaderStyle-CssClass="header" ItemStyle-HorizontalAlign="Center" HeaderStyle-HorizontalAlign="Center">
                        <ItemTemplate>
                            <asp:HyperLink ID="hlk_carcompomodelno" runat="server"></asp:HyperLink>
                        </ItemTemplate>
                        <HeaderStyle CssClass="header"></HeaderStyle>
                        <ItemStyle CssClass="lineleft"></ItemStyle>
                    </asp:TemplateField>
                    <asp:BoundField HeaderText="申請型式" DataField="ct_at_desc" ItemStyle-CssClass="lineleft"
                        HeaderStyle-CssClass="header" ItemStyle-HorizontalAlign="Center" HeaderStyle-HorizontalAlign="Center"
                        HeaderStyle-Width="80px" />
                    <asp:TemplateField HeaderText="代表車型名稱" ItemStyle-CssClass="lineleft" HeaderStyle-CssClass="header"
                        ItemStyle-HorizontalAlign="Center" HeaderStyle-HorizontalAlign="Center">
                        <ItemTemplate>
                            <div align="center">
                                <asp:Label ID="lbl_carengmodelname" runat="server"></asp:Label><br />
                                <asp:HyperLink ID="hlk_examineform" runat="server">審查表</asp:HyperLink>
                                <asp:HyperLink ID="hlk_testreport" runat="server">測定報告</asp:HyperLink>
                            </div>
                        </ItemTemplate>
                    </asp:TemplateField>
                    <asp:TemplateField HeaderText="車型數" HeaderStyle-Width="60px" ItemStyle-CssClass="lineleft"
                        HeaderStyle-CssClass="header" ItemStyle-HorizontalAlign="Center" HeaderStyle-HorizontalAlign="Center">
                        <ItemTemplate>
                            <asp:HyperLink ID="hlk_carcount" CausesValidation="false" runat="server"></asp:HyperLink>
                        </ItemTemplate>
                        <HeaderStyle CssClass="header"></HeaderStyle>
                        <ItemStyle CssClass="lineleft"></ItemStyle>
                    </asp:TemplateField>
                    <asp:TemplateField HeaderText="噪音防治對策" ItemStyle-CssClass="lineleft" HeaderStyle-CssClass="header"
                        ItemStyle-HorizontalAlign="Center" HeaderStyle-HorizontalAlign="Center" HeaderStyle-Width="100px">
                        <ItemTemplate>
                            <asp:HyperLink ID="hlk_noisedefenseplan" CausesValidation="false" runat="server"></asp:HyperLink>
                        </ItemTemplate>
                    </asp:TemplateField>
                    <asp:TemplateField HeaderText="修正目錄" ItemStyle-CssClass="lineleft" HeaderStyle-CssClass="header"
                        ItemStyle-HorizontalAlign="Center" HeaderStyle-HorizontalAlign="Center" HeaderStyle-Width="80px">
                        <ItemTemplate>
                            <asp:HyperLink ID="hlk_fixcatalog" CausesValidation="false" runat="server"></asp:HyperLink>
                        </ItemTemplate>
                    </asp:TemplateField>
                    <asp:TemplateField HeaderText="其他資料上傳" ItemStyle-CssClass="lineleft" HeaderStyle-CssClass="header"
                        ItemStyle-HorizontalAlign="Center" HeaderStyle-HorizontalAlign="Center" HeaderStyle-Width="110px">
                        <ItemTemplate>
                            <asp:HyperLink ID="hlk_uploadfileinfo" CausesValidation="false" runat="server"></asp:HyperLink>
                        </ItemTemplate>
                        <HeaderStyle CssClass="lineright"></HeaderStyle>
                        <ItemStyle CssClass="lineright"></ItemStyle>
                    </asp:TemplateField>
                </Columns>
                <EmptyDataRowStyle ForeColor="Red" />
                <PagerStyle HorizontalAlign="Center" Font-Bold="true" CssClass="S_PageCss" />
            </asp:GridView>
        </div>
        <asp:Button ID="btnReflash" runat="server" OnClick="btnReflash_Click" Style="width: 0px;
            padding: 0; border-width: 0px" />
    </div>
    <!--{* fullcontent end *}-->
    <div class="fullfooter">
    </div>
    <!--{* content end *}-->
</asp:Content>
