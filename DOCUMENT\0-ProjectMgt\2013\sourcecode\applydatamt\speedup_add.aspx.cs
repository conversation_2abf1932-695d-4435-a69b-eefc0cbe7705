﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.IO;
using System.Text;
using System.Drawing;
public partial class applydatamt_speedup_add : BaseAdminPage
{

    Cryptography crypy = new Cryptography();
    wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();

    string applyno = string.Empty;
    string carcompomodelno = string.Empty;
    string command = string.Empty;
    string fixid = string.Empty;

    protected void Page_Load(object sender, EventArgs e)
    {

    }
    protected void btn_Add_Click(object sender, EventArgs e)
    {
        int keynum = crypy.getRadNum();
        string[] arr = crypy.wsApplybasedataDetailValid(keynum);

        //wsApplyData.Insert_Nv_testrptspeedupinfo(lbl_carcompomodelno.Text.Trim(), dt_list.Rows[0]["cmdg_carmodelno"].ToString(), txt_testrptno.Text.Trim(), txt_speedupgear.Text.Trim(), txt_speed.Text.Trim(), arr[0].ToString(), arr[1].ToString());
        string strJs = string.Format(@"
<script>
parent.$('input[id$=h_new_txt_speedupgear]').val('{0}');
parent.$('input[id$=h_new_txt_speed]').val('{1}');
parent.jQuery.fn.colorbox.close();  
</script>
", txt_speedupgear.Text, txt_speed.Text);

        Page.ClientScript.RegisterStartupScript(Page.GetType(), "NewData", strJs);
        
    }
    
}