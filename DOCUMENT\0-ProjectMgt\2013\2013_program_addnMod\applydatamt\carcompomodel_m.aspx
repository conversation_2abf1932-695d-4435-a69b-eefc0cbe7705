﻿<%@ Page Title="" Language="C#" MasterPageFile="~/masterpage/MasterOutset.master"
    AutoEventWireup="true" CodeFile="carcompomodel_m.aspx.cs" Inherits="applydatamt_carcompomodel_m" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script language="javascript" type="text/javascript">
        function raiseAlterCarcompomodelnoback() { __doPostBack('ctl00$ContentPlaceHolder1$lbtn_update', ''); }
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <asp:ScriptManager ID="ScriptManager1" runat="server">
    </asp:ScriptManager>
    <div class="fullheader">
        <table width="100%" cellspacing="0" cellpadding="0" class="fullheaderinfo">
            <tr>
                <td valign="top">
                    <b>噪音合格證明申請-車型組基本資料填寫</b>
                </td>
                <td align="right" class="font_loginInfo">
                </td>
            </tr>
        </table>
    </div>
    <!--{* fullheader end *}-->
    <div class="fullcontent">
        <div class="formstyle">
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="font_fullcontent">
                <tr>
                    <td align="right">
                        申請編號：
                    </td>
                    <td>
                        <asp:Label ID="lbl_applyno" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        申請狀態：
                    </td>
                    <td>
                        <asp:Label ID="lbl_applystatus" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        <asp:LinkButton ID="lbtn_update" runat="server" OnClick="lbtn_update_Click" CausesValidation="False"></asp:LinkButton>
                        <span style="color: Red;">*</span>車型組編號：
                    </td>
                    <td>
                        <asp:TextBox ID="txt_carcompomodelno" runat="server" CssClass="inputTypeS" MaxLength="15"></asp:TextBox>
                        <asp:Button ID="btn_changecarcompomodelno" runat="server" Text="變更" CssClass="btn_mouseout"
                            onmouseover="this.className='btn_mouseover'" onmouseout="this.className='btn_mouseout'"
                            Visible="false" />
                        <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" ControlToValidate="txt_carcompomodelno"
                            ErrorMessage="車型組編號不可為空值"></asp:RequiredFieldValidator>
                    </td>
                    <td align="right">
                        複製車型組：
                    </td>
                    <td align="left">
                        <asp:DropDownList ID="ddl_carcompolist" runat="server">
                        </asp:DropDownList>
                        <asp:LinkButton ID="btn_copycarcompomodel_data" runat="server" Text="複製" OnClick="btn_copycarcompomodel_data_Click"
                            CausesValidation="true" ValidationGroup="carcompolist"></asp:LinkButton>
                        <asp:Label ID="lbl_copy_carcompomodel" Visible="false" Text="僅提供新增時使用" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td colspan="10" align="left">
                        <font style="font-size: medium; font-style: normal; font-weight: bold;">基本引擎</font>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        <span style="color: Red;">*</span>申請型式：
                    </td>
                    <td colspan="3">
                        <asp:RadioButtonList ID="rdBtnLst_applytype" runat="server" RepeatDirection="Horizontal"
                            RepeatLayout="Flow" AutoPostBack="True" OnSelectedIndexChanged="rdBtnLst_applytype_SelectedIndexChanged">
                        </asp:RadioButtonList>
                        <asp:RequiredFieldValidator ID="rf_applytype" runat="server" ControlToValidate="rdBtnLst_applytype"
                            ErrorMessage="申請型式必須挑選"></asp:RequiredFieldValidator>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        <span style="color: Red;">*</span>排檔型式：
                    </td>
                    <td>
                        <asp:RadioButtonList ID="rdBtnLst_transmissiontypetype" runat="server" RepeatDirection="Horizontal"
                            RepeatLayout="Flow">
                        </asp:RadioButtonList>
                        <asp:RequiredFieldValidator ID="rfv_transmissiontypetype" runat="server" ControlToValidate="rdBtnLst_transmissiontypetype"
                            ErrorMessage="排檔型式必須挑選"></asp:RequiredFieldValidator>
                    </td>
                    <td align="right">
                        <span style="color: Red;">*</span>以歐盟合格證申請：
                    </td>
                    <td>
                        <asp:RadioButtonList ID="rdBtnLst_useeurocert" runat="server" RepeatDirection="Horizontal"
                            RepeatLayout="Flow" AutoPostBack="True" OnSelectedIndexChanged="rdBtnLst_useeurocert_SelectedIndexChanged">
                        </asp:RadioButtonList>
                        <asp:RequiredFieldValidator ID="rfv_useeurocert" runat="server" ControlToValidate="rdBtnLst_useeurocert"
                            ErrorMessage="以歐盟合格證申請必須挑選"></asp:RequiredFieldValidator>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        使用燃料：
                    </td>
                    <td>
                        <asp:RadioButtonList ID="rdBtnLs_GasolineType" runat="server" RepeatDirection="Horizontal"
                            RepeatLayout="Flow">
                        </asp:RadioButtonList>
                    </td>
                    <td align="right">
                        <span style="color: Red;">*</span>燃燒循環：
                    </td>
                    <td>
                        <asp:RadioButtonList ID="rdBtnLst_powerexportway" runat="server" RepeatDirection="Horizontal"
                            RepeatLayout="Flow">
                        </asp:RadioButtonList>
                        <asp:RequiredFieldValidator ID="rfv_powerexportway" runat="server" ControlToValidate="rdBtnLst_powerexportway"
                            ErrorMessage="燃燒循環必須挑選"></asp:RequiredFieldValidator>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        供油方式：
                    </td>
                    <td>
                        <asp:RadioButtonList ID="rdBtnLst_provideoilmethod" runat="server" RepeatDirection="Horizontal"
                            RepeatLayout="Flow">
                        </asp:RadioButtonList>
                    </td>
                    <td align="right">
                        總排氣量：
                    </td>
                    <td>
                        <asp:TextBox ID="txt_exhuast" runat="server" CssClass="inputTypeS" CausesValidation="True"
                            MaxLength="9"></asp:TextBox>c.c.
                        <asp:RangeValidator ID="RV_exhuast" runat="server" ErrorMessage="排氣量須為數字" MaximumValue="999999"
                            MinimumValue="0" Type="Double" ControlToValidate="txt_exhuast"></asp:RangeValidator>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        汽缸缸數及排列：
                    </td>
                    <td>
                        缸數：
                        <asp:TextBox ID="txt_cylindernums" runat="server" CssClass="inputTypeS" MaxLength="4"></asp:TextBox><asp:RegularExpressionValidator
                            ID="rev_cylindernums" runat="server" ErrorMessage="請輸入數字" ValidationExpression="^[\d]+$"
                            ControlToValidate="txt_cylindernums"></asp:RegularExpressionValidator><br />
                        汽缸排列：
                        <asp:DropDownList ID="DrpDnList_cylindertype" runat="server">
                        </asp:DropDownList>
                    </td>
                    <td align="right">
                        <span style="color: Red;">*</span>引擎安裝位置及方式：
                    </td>
                    <td>
                        <asp:TextBox ID="txt_enginesetupposnmethod" runat="server" CssClass="inputTypeS"
                            Width="293px" MaxLength="50"></asp:TextBox>
                        <br />
                        <asp:RequiredFieldValidator ID="rfv_enginesetupposnmethod" runat="server" ErrorMessage="引擎安裝位置及方式不可空白"
                            ControlToValidate="txt_enginesetupposnmethod"></asp:RequiredFieldValidator>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        冷卻系統型式：
                    </td>
                    <td>
                        <asp:RadioButtonList ID="rdBtnLst_coolsystem" runat="server" RepeatDirection="Horizontal"
                            RepeatLayout="Flow">
                        </asp:RadioButtonList>
                    </td>
                    <td align="right">
                        <span style="color: Red;">*</span>檢測廠：
                    </td>
                    <td>
                        <asp:RadioButtonList ID="rdBtnLst_testfactory" runat="server" RepeatDirection="Horizontal"
                            RepeatLayout="Flow">
                        </asp:RadioButtonList>
                        <asp:RequiredFieldValidator ID="rfv_testfactory" runat="server" ControlToValidate="rdBtnLst_testfactory"
                            ErrorMessage="檢測廠必須挑選"></asp:RequiredFieldValidator>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        進口國：
                    </td>
                    <td>
                        <asp:DropDownList ID="DrpDnList_importarea" runat="server">
                        </asp:DropDownList>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        資料最後修改人：
                    </td>
                    <td>
                        <asp:Label ID="lbl_modifyuser" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        資料最後修改日期：
                    </td>
                    <td>
                        <asp:Label ID="lbl_modifydate" runat="server"></asp:Label>
                    </td>
                </tr>
            </table>
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="font_fullcontent">
                <tr>
                    <td colspan="4" align="center">
                        <asp:Button ID="btn_tmpsave" runat="server" Text="暫存" OnClick="btn_tmpsave_Click"
                            CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'" onmouseout="this.className='btn_mouseout'"
                            CausesValidation="False" />
                        <asp:Button ID="btn_apply" runat="server" Text="完成填單" OnClick="btn_apply_Click" CssClass="btn_mouseout"
                            onmouseover="this.className='btn_mouseover'" onmouseout="this.className='btn_mouseout'" />
                        <asp:ValidationSummary ID="ValidationSummary1" runat="server" ShowMessageBox="True"
                            ShowSummary="False" />
                    </td>
                </tr>
                <tr>
                    <td colspan="4" align="center">
                        <asp:HyperLink ID="applybase" runat="server" Text="申請表"></asp:HyperLink>
                        &nbsp;＞＞&nbsp;
                        <asp:HyperLink ID="carcompomodel" runat="server" Text="車型組清單"></asp:HyperLink>
                        &nbsp;＞＞&nbsp;
                        <asp:HyperLink ID="docreview" runat="server"></asp:HyperLink>
                    </td>
                </tr>
            </table>
        </div>
    </div>
    <!--{* fullcontent end *}-->
    <div class="fullfooter">
        <asp:HiddenField ID="hidden_flag" runat="server" />
    </div>
    <!--{* content end *}-->
</asp:Content>
