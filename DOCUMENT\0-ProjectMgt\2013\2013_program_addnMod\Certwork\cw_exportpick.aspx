﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="cw_exportpick.aspx.cs" Inherits="Certwork_cw_exportpick" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>匯出勾選</title>
    <link href="../css/style.css" rel="stylesheet" type="text/css" />
</head>
<body>
    <form id="form1" runat="server">
    <asp:ScriptManager ID="ScriptManager1" runat="server">
    </asp:ScriptManager>
    <div class="fullheader">
        <table cellpadding="0" cellspacing="0" class="fullheaderinfo">
            <tr>
                <td valign="top">
                    <b>匯出勾選</b>
                </td>
                <td align="right" class="font_loginInfo" valign="top">
                </td>
            </tr>
        </table>
    </div>
    <div class="fullcontent">
        <div id="div_maintain" runat="server" class="formstyle">
            <asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional">
                <ContentTemplate>
                    <table border="0" cellspacing="0" cellpadding="0" class="font_fullcontent" width="100%">
                        <tr>
                            <td align="left">
                                <font style="font-size: medium; font-style: normal; font-weight: bold;">申請表</font>
                            </td>
                        </tr>
                        <tr>
                            <td align="left">
                                &nbsp;<asp:CheckBox ID="cb_all_1" Text="全選" AutoPostBack="True" runat="server" OnCheckedChanged="cb_all_1_CheckedChanged" />
                                <asp:DataList ID="dl_export" runat="server" RepeatColumns="7" RepeatDirection="Horizontal"
                                    Width="100%">
                                    <ItemTemplate>
                                        <asp:CheckBox ID="cb_export" name="cb_export" runat="server" Key='<%#Eval("tmp_ep_id")%>'
                                            Text='<%#Eval("tmp_ep_desc") %>' />
                                    </ItemTemplate>
                                </asp:DataList>
                            </td>
                        </tr>
                        <tr>
                            <td align="left">
                                <font style="font-size: medium; font-style: normal; font-weight: bold;">車型組</font>
                            </td>
                        </tr>
                        <tr>
                            <td align="left">
                                &nbsp;<asp:CheckBox ID="cb_all_2" Text="全選" AutoPostBack="True" runat="server" OnCheckedChanged="cb_all_2_CheckedChanged" />
                                <asp:DataList ID="dl_export2" runat="server" RepeatColumns="5" RepeatDirection="Horizontal"
                                    Width="100%">
                                    <ItemTemplate>
                                        <asp:CheckBox ID="cb_export" runat="server" Key='<%#Eval("tmp_ep_id")%>' Text='<%#Eval("tmp_ep_desc") %>' />
                                    </ItemTemplate>
                                </asp:DataList>
                            </td>
                        </tr>
                        <tr>
                            <td align="left">
                                <font style="font-size: medium; font-style: normal; font-weight: bold;">規格表</font>
                            </td>
                        </tr>
                        <tr>
                            <td align="left">
                                &nbsp;<asp:CheckBox ID="cb_all_3" Text="全選" AutoPostBack="True" runat="server" OnCheckedChanged="cb_all_3_CheckedChanged" />
                                <asp:DataList ID="dl_export3" runat="server" RepeatColumns="6" RepeatDirection="Horizontal"
                                    Width="100%">
                                    <ItemTemplate>
                                        <asp:CheckBox ID="cb_export" runat="server" Key='<%#Eval("tmp_ep_id")%>' Text='<%#Eval("tmp_ep_desc") %>' />
                                    </ItemTemplate>
                                </asp:DataList>
                            </td>
                        </tr>
                    </table>
                </ContentTemplate>
            </asp:UpdatePanel>
            <table border="0" cellspacing="0" cellpadding="0" class="font_fullcontent" width="100%">
                <tr>
                    <td align="center">
                        <asp:Button ID="btn_Export" runat="server" Text="匯出" OnClick="btn_Export_Click" CssClass="btn_mouseout"
                            onmouseover="this.className='btn_mouseover'" onmouseout="this.className='btn_mouseout'" />&nbsp;&nbsp;
                        <asp:Button ID="btn_Close" runat="server" Text="離開" CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'"
                            onmouseout="this.className='btn_mouseout'" CausesValidation="False" OnClientClick="window.close();return false;" />
                    </td>
                </tr>
            </table>
        </div>
    </div>
    <!--{* fullcontent end *}-->
    <div class="fullfooter">
    </div>
    <!--{* content end
    *}-->
    </form>
</body>
</html>
