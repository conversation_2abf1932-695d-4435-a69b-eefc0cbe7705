﻿<%@ Master Language="C#" AutoEventWireup="true" CodeFile="MasterDialog.master.cs"
    Inherits="masterpage_MasterDialog" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head id="Head1" runat="server">
    <title>機動車輛噪音審驗電子化作業與網路申請系統</title>
    <asp:ContentPlaceHolder ID="head" runat="server">
    </asp:ContentPlaceHolder>
    <link href="../css/style.css" rel="stylesheet" type="text/css" />
    <link href="../css/SelfStyle.css" rel="stylesheet" type="text/css" />
    <script type="text/javascript" src="../js/jquery-1.2.2.pack.js"></script>
    <script type="text/javascript">
        document.oncontextmenu = function () { return false; } 
    </script>
</head>
<body>
    <%--<body oncontextmenu="window.event.returnValue=false" onSelectStart="event.returnValue=false">--%>
    <form id="form1" runat="server">
    <div id="main">
        <asp:ContentPlaceHolder ID="ContentPlaceHolder1" runat="server">
        </asp:ContentPlaceHolder>
    </div>
    </form>
</body>
</html>
