﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;

public partial class applydatamt_carmodeldatamt_g : BaseAdminPage
{
    COMEMYFAMILY.Validator.ValidatorHelper ValidatorHelper = new COMEMYFAMILY.Validator.ValidatorHelper();
    JSCommon JS = new JSCommon();
    int limtLength = 30;
    string Variable_speed_system_TextBox = "Variable_speed_system_TextBox";
    wsCarCompoModel wsCarCmpModel = new wsCarCompoModel();
    Cryptography crypy = new Cryptography();
    bool bCheckPcy = true;
    noisevalidation.db.nv_db_carcompomodel_carmodellist.nv_db_select db_nv = new noisevalidation.db.nv_db_carcompomodel_carmodellist.nv_db_select();
    protected void Page_Load(object sender, EventArgs e)
    {
        #region TextBox在Textmode=MultiLine狀態下MaxLength的字數限制無效
        txt_remark.Attributes.Add("maxlength", "500");
        txt_remark.Attributes.Add("onkeyup", "return ismaxlength(this)");



        tb_producercountry.Attributes.Add("maxlength", "100");
        tb_producercountry.Attributes.Add("onkeyup", "return ismaxlength(this)");

        #endregion
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = '../index.aspx';</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" && (GetAgentAccountGrpList().Contains("cam_adm") || GetAgentAccountGrpList().Contains("cga_adm") || GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("pg_adm")))
            {
                //檢查是否為可編輯狀態  申請狀態=10、40、90  且 為新申請 OR 修改 OR 延伸(cmdg_beextendmodel=1)  不可編輯導至為讀畫面
                if (!IsPostBack)
                {
                    if (Request["applyno"] != null && Request["carcompomodelno"] != "" && !string.IsNullOrEmpty(Request_mode()))
                    {
                        Regist_Client_ID();

                        //strApplyno = Request["applyno"].ToString();

                        wsCodetbl wsCodeTbl = new wsCodetbl();
                        Cryptography crypy = new Cryptography();
                        int keynum = crypy.getRadNum();

                        //

                        string strApplyno = crypy.DeCode(Request["applyno"].ToString());
                        string carcompomodelno = crypy.DeCode(Request["carcompomodelno"].ToString());
                        lbl_carcompomodelno.Text = carcompomodelno;

                        string[] arr = crypy.wsCarCompoModelValidate(keynum);
                        DataSet dsCarCmpModel = wsCarCmpModel.getDataByApplyNo_CarCompoModelNo(strApplyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());

                        if (dsCarCmpModel.Tables[0].Rows.Count > 0)
                        {
                            //不是系統管理員 要比對公司是否相同
                            if ((GetAgentAccountGrpList().Contains("cam_adm") || GetAgentAccountGrpList().Contains("cga_adm")) && !(GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("pg_adm")))
                            {
                                //需檢查是否為登入USER公司的資料
                                if (Session["compidno"].ToString() == dsCarCmpModel.Tables[0].Rows[0]["abd_databelongcompidno"].ToString())
                                {
                                    doGetSourceData(strApplyno, carcompomodelno, dsCarCmpModel);
                                }
                                else
                                {
                                    wsCarCmpModel.Dispose();
                                    wsCodeTbl.Dispose();
                                    crypy.Dispose();
                                    Response.Write("<script languge='javascript'>alert('統編比對錯誤，畫面將轉回首頁！');window.location.href='../index.aspx'</script>");
                                    Response.End();
                                }
                            }
                            else
                            {
                                doGetSourceData(strApplyno, carcompomodelno, dsCarCmpModel);

                            }

                        }
                        else
                        {
                            //取不到申請編號畫面轉走
                            wsCarCmpModel.Dispose();
                            wsCodeTbl.Dispose();
                            crypy.Dispose();
                            Response.Write("<script languge='javascript'>alert('無法取得申請編號資料，畫面將轉回首頁！');window.location.href='../index.aspx'</script>");
                            Response.End();
                            //MessageBox.Show("無法取得申請編號資料，畫面將轉回首頁！");
                            //Response.Redirect("../index.aspx");
                        }
                    }
                    else
                    {
                        //取不到申請編號畫面轉走
                        Response.Write("<script languge='javascript'>alert('無法取得申請編號，畫面將轉回首頁！');window.location.href='../index.aspx'</script>");
                        Response.End();
                        //MessageBox.Show("無法取得申請編號，畫面將轉回首頁！");
                        //Response.Redirect("../index.aspx");
                    }
                }
                else
                {
                    transmissionnumSelectChange();
                }
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }
    }

    #region 更改的麵包屑傳入的參數
    protected void Page_LoadComplete(object sender, EventArgs e)
    {
        ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text = ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text.Replace("applydatamt/carcompomodelList.aspx", "applydatamt/carcompomodelList.aspx?applyno=" + Server.UrlEncode(Request["applyno"].ToString()));
    }
    #endregion

    private void doGetSourceData(string strApplyno, string carcompomodelno, DataSet dsCarCmpModel)
    {
        wsCodetbl wsCodeTbl = new wsCodetbl();
        Cryptography crypy = new Cryptography();
        int keynum = crypy.getRadNum();
        string[] arr_as = crypy.wsCodetblValidate(keynum);

        string compidnoE = Session["compidno"].ToString(); //公司統編
        DataView dv = new MailParaControl().GetApplyBaseData(strApplyno);
        string EngineFamily = dv[0]["EngineFamily"].ToString(); //引擎族
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        string[] arr_ABD = wsApplybasedataDetailValid(getRadNum());
        //延伸、修改 沿用 如果為系統第一筆需調整為可編輯 abd_applystatus;cpm_applytype
        //if (wsApplyData.IsDateoneExist(compidnoE, carcompomodelno, EngineFamily, arr_ABD[0].ToString(), arr_ABD[1].ToString()) )
        //{

        //    wsCarCmpModel.Dispose();
        //    wsCodeTbl.Dispose();
        //    crypy.Dispose();
        //    Response.Write("<script languge='javascript'>alert('車型組申請型式必須為新申請 OR 修改 OR 延伸且申請狀態為[填單中、補件、拒件]才能進行編輯動作，畫面將轉回首頁！！');window.location.href='../index.aspx'</script>");
        //    Response.End();

        //}
        //else
        //{
        //檢查是否有車型編號  Request["carmodelno"].ToString()，如果沒有->新增
        lbl_testweightformula.Text = dsCarCmpModel.Tables[0].Rows[0]["ct_twf_desc"].ToString();
        lbl_applyno.Text = strApplyno;
        lbl_isimport.Text = dsCarCmpModel.Tables[0].Rows[0]["abd_isimport"].ToString();
        if (lbl_isimport.Text.Equals("1"))//國產
        {
            tb_producercountry.Text = "中華民國";
        }
        lbl_carcompomodelno.Text = carcompomodelno;
        lbl_applytype.Text = dsCarCmpModel.Tables[0].Rows[0]["cpm_applytype"].ToString();
        lbl_compname.Text = dsCarCmpModel.Tables[0].Rows[0]["abd_databelongcompname"].ToString();
        lbl_carstyleyear.Text = dsCarCmpModel.Tables[0].Rows[0]["abd_carstyleyear"].ToString();
        lbl_setuppos.Text = dsCarCmpModel.Tables[0].Rows[0]["ct_ep_desc_g"].ToString();
        lbl_exhaust.Text = dsCarCmpModel.Tables[0].Rows[0]["cpm_exhuast"].ToString().Replace(".00", "");   //排氣量
        lbl_exhaust2.Text = dsCarCmpModel.Tables[0].Rows[0]["cpm_exhuast"].ToString().Replace(".00", "");　//總排氣量
        if (dsCarCmpModel.Tables[0].Rows[0]["abd_carstyleyear"].ToString() == "G")
        {
            lbl_cylindernums.Text = dsCarCmpModel.Tables[0].Rows[0]["cpm_cylindernums"].ToString();
        }
        else
        {
            //取得排列方式
            DataSet ds_cylindertype = wsCodeTbl.getCylinderPos_M(arr_as[0].ToString(), arr_as[1].ToString());
            if (ds_cylindertype.Tables[0].Rows.Count > 0)
            {
                foreach (DataRow dr in ds_cylindertype.Tables[0].Rows)
                {
                    if (dr["ct_cp_id_m"].ToString() == dsCarCmpModel.Tables[0].Rows[0]["cpm_cylindertype"].ToString())
                        lbl_cylindernums.Text = dr["ct_cp_desc_m"].ToString();
                }
            }
            lbl_cylindernums.Text = lbl_cylindernums.Text + dsCarCmpModel.Tables[0].Rows[0]["cpm_cylindernums"].ToString() + "缸";
            lbl_powerexportway.Style.Add("display", "inline");
            lbl_powerexportway.Text = dsCarCmpModel.Tables[0].Rows[0]["ct_ebm_desc"].ToString(); //燃料循環
        }


        lbl_coolsystem.Text = dsCarCmpModel.Tables[0].Rows[0]["ct_cs_desc"].ToString();
        lbl_provideoilmethod.Text = dsCarCmpModel.Tables[0].Rows[0]["ct_le_desc"].ToString();
        lbl_use_fuel.Text = dsCarCmpModel.Tables[0].Rows[0]["ct_ft_desc"].ToString(); //使用燃料
        lbl_transmissiontype.Text = dsCarCmpModel.Tables[0].Rows[0]["ct_gm_desc"].ToString();
        lbl_Installation_location_manner.Text = dsCarCmpModel.Tables[0].Rows[0]["cpm_enginesetupposnmethod"].ToString();

        string carmodelno = crypy.DeCode(Request["carmodelno"].ToString());
        if (Request["carmodelno"].ToString() != null && Request["carmodelno"].ToString() != "")
        {
            hidden_flag.Value = "2"; //修改-會傳過來申請編號+車型組代碼
            lbl_carmodelno.Text = carmodelno;
        }
        else
        {
            hidden_flag.Value = "1"; //新增-只會傳過來申請編號      
            if (wsCarCmpModel.Isberepresentativevehicle(strApplyno, carcompomodelno)) //是否有代表車
                cb_berepresentativevehicle.Enabled = false;
            else
                cb_berepresentativevehicle.Enabled = true;
            lbl_carmodelno.Text = "";
        }
        BindBaseData();

        if (hidden_flag.Value == "2")  //修改-所以要取出該筆資料並顯示在畫面
        {
            string[] arr_carmodel = crypy.wsCarCompoModelValidate(crypy.getRadNum());
            DataTable dtCarData = wsCarCmpModel.Get_single_carmodel_data(strApplyno, carcompomodelno, carmodelno, arr_carmodel[0].ToString(), arr_carmodel[1].ToString());
            if (dtCarData.Rows.Count > 0)
            {

                BindDBData(dtCarData);
                //transmissionnumSelectChange();
                //DataTable dtCar_GearRatioData = wsCarCmpModel.Get_single_carmodel_GearRatio(strApplyno, carcompomodelno, carmodelno, arr_carmodel[0].ToString(), arr_carmodel[1].ToString());
                //BindGearRatio(dtCar_GearRatioData);
            }
            else
            {
                wsCarCmpModel.Dispose();
                wsCodeTbl.Dispose();
                crypy.Dispose();
                Response.Write("<script languge='javascript'>alert('無法取得車型組資料，畫面將轉回首頁！');window.location.href='../index.aspx'</script>");
                Response.End();
                //MessageBox.Show("無法取得車型組資料，畫面將轉回首頁！");
                //Response.Redirect("../index.aspx");
            }
        }
        else
        {
            transmissionnumSelectChange();
        }
        ControlObject();
        wsCarCmpModel.Dispose();
        wsCodeTbl.Dispose();
        crypy.Dispose();
        //}
    }

    private void BindDBData(DataTable dt_cardata)
    {
        //編輯取得資料
        lbl_applyno.Text = dt_cardata.Rows[0]["cmdg_applyno"].ToString();
        lbl_carcompomodelno.Text = dt_cardata.Rows[0]["cmdg_carcompomodelno"].ToString();

        ddl_carbodyshape.ClearSelection();
        ddl_carbodyshape.Items.FindByValue(dt_cardata.Rows[0]["cmdg_carbodyshape"].ToString()).Selected = true; //車身式樣-非其他 
        tb_carbodyshapedesc.Text = dt_cardata.Rows[0]["cmdg_carbodyshapedesc"].ToString(); //車身式樣-其他填寫欄位

        ////代表車一旦決定即不可置換，考量剛上線時舊資料直接申請狀況，先MARK限制
        //if (lbl_applytype.Text == "1")
        //{
        if (dt_cardata.Rows[0]["cmdg_berepresentativevehicle"].ToString().Trim().Equals("Y", StringComparison.CurrentCultureIgnoreCase))
            cb_berepresentativevehicle.Checked = true;
        else
            cb_berepresentativevehicle.Checked = false;
        if (dt_cardata.Rows[0]["berepresentativevehicle_num"].ToString() != "0" && dt_cardata.Rows[0]["cmdg_berepresentativevehicle"].ToString() == "N")
        {
            cb_berepresentativevehicle.Enabled = false;
        }
        //}
        //else
        //{
        //    cb_berepresentativevehicle.Enabled = false;
        //}

        ddl_cartype.ClearSelection();
        ddl_cartype.Items.FindByValue(dt_cardata.Rows[0]["cmdg_cartype"].ToString()).Selected = true;
        tb_baseenginename.Text = dt_cardata.Rows[0]["cmdg_baseenginename"].ToString();
        ddl_brand.ClearSelection();
        ddl_brand.Items.FindByValue(dt_cardata.Rows[0]["cmdg_carbrand"].ToString()).Selected = true;


        //製造地區        
        if (lbl_isimport.Text.Equals("1"))//國產
        {
            tb_producercountry.Text = dt_cardata.Rows[0]["cmdg_producercountry"].ToString() == "" ? "中華民國" : dt_cardata.Rows[0]["cmdg_producercountry"].ToString();
        }
        else
        {
            tb_producercountry.Text = dt_cardata.Rows[0]["cmdg_producercountry"].ToString();
        }

        tb_carchnmodelname.Text = dt_cardata.Rows[0]["cmdg_carchnmodelname"].ToString();
        tb_carengmodelname.Text = dt_cardata.Rows[0]["cmdg_carengmodelname"].ToString();
        tb_carmodelcode.Text = dt_cardata.Rows[0]["cmdg_carmodelcode"].ToString();

        ddl_transmissionnum.ClearSelection();
        if (lbl_transmissiontype.Text != "CVT")
        {
            ddl_transmissionnum.Items.FindByValue(dt_cardata.Rows[0]["cmdg_transmissionnum"].ToString()).Selected = true;
        }
        else
        {
            ddl_transmissionnum.Items.FindByValue("").Selected = true;
        }

        //ddl_transmissionnum.SelectedIndex = Convert.ToInt32(dt_cardata.Rows[0]["cmdg_transmissionnum"]);
        ddl_cardoorcount.ClearSelection();
        ddl_cardoorcount.Items.FindByValue(dt_cardata.Rows[0]["cmdg_cardoorcount"].ToString()).Selected = true;
        tb_carmodeladd.Text = dt_cardata.Rows[0]["cmdg_carmodeladd"].ToString();
        tb_carbodylenth.Text = dt_cardata.Rows[0]["cmdg_carbodylenth"].ToString();
        tb_carbodywidth.Text = dt_cardata.Rows[0]["cmdg_carbodywidth"].ToString();
        tb_carbodyhieght.Text = dt_cardata.Rows[0]["cmdg_carbodyhieght"].ToString();
        tb_carwheelbase.Text = dt_cardata.Rows[0]["cmdg_carwheelbase"].ToString();
        tb_carwheelbase_f.Text = dt_cardata.Rows[0]["cmdg_carwheelbase_f"].ToString();
        tb_carwheelbase_b.Text = dt_cardata.Rows[0]["cmdg_carwheelbase_b"].ToString();
        tb_caremptyweight.Text = dt_cardata.Rows[0]["cmdg_caremptyweight"].ToString();
        tb_cartotalweight.Text = dt_cardata.Rows[0]["cmdg_cartotalweight"].ToString();
        tb_passengers.Text = dt_cardata.Rows[0]["cmdg_passengers"].ToString();
        tb_cylindermeters.Text = dt_cardata.Rows[0]["cmdg_cylindermeters"].ToString();
        tb_cylinderstroke.Text = dt_cardata.Rows[0]["cmdg_cylinderstroke"].ToString();
        tb_compressionratio.Text = dt_cardata.Rows[0]["cmdg_compressionratio"].ToString();
        tb_maxhorsepower.Text = dt_cardata.Rows[0]["cmdg_maxhorsepower"].ToString();
        tb_maxHorsepowerspeed.Text = dt_cardata.Rows[0]["cmdg_maxHorsepowerspeed"].ToString();
        tb_torque.Text = dt_cardata.Rows[0]["cmdg_torque"].ToString();
        tb_torquespeed.Text = dt_cardata.Rows[0]["cmdg_torquespeed"].ToString();
        //"Y";//渦輪增壓器：";
        ddl_haveturbo.ClearSelection();
        ddl_haveturbo.Items.FindByValue(dt_cardata.Rows[0]["cmdg_turborchargers"].ToString()).Selected = true;
        tb_oilcapacity.Text = dt_cardata.Rows[0]["cmdg_oilcapacity"].ToString();
        tb_929598.Text = dt_cardata.Rows[0]["cmdg_929598"].ToString();
        tb_exhaustsystem.Text = dt_cardata.Rows[0]["cmdg_exhaustsystem"].ToString();
        tb_pcv.Text = dt_cardata.Rows[0]["cmdg_pcv"].ToString();
        tb_eec.Text = dt_cardata.Rows[0]["cmdg_eec"].ToString();
        tb_differentialmodel.Text = dt_cardata.Rows[0]["cmdg_differentialmodel"].ToString();//*
        tb_differentialgearratio.Text = dt_cardata.Rows[0]["cmdg_differentialgearratio"].ToString();
        tb_transmissionmodel.Text = dt_cardata.Rows[0]["cmdg_transmissionmodel"].ToString();//*
        tb_transmissionbackgearratio.Text = dt_cardata.Rows[0]["cmdg_transmissionbackgearratio"].ToString();
        tb_highestspeed.Text = dt_cardata.Rows[0]["cmdg_highestspeed"].ToString();
        tb_suspensionsystem_f.Text = dt_cardata.Rows[0]["cmdg_suspensionsystem_f"].ToString();
        tb_suspensionsystem_b.Text = dt_cardata.Rows[0]["cmdg_suspensionsystem_b"].ToString();
        tb_tirespec_std_f.Text = dt_cardata.Rows[0]["cmdg_tirespec_std_f"].ToString();
        tb_tirespec_std_b.Text = dt_cardata.Rows[0]["cmdg_tirespec_std_b"].ToString();
        tb_tirespec_cho_f.Text = dt_cardata.Rows[0]["cmdg_tirespec_cho_f"].ToString();
        tb_tirespec_cho_b.Text = dt_cardata.Rows[0]["cmdg_tirespec_cho_b"].ToString();
        tb_caremptyweight.Text = dt_cardata.Rows[0]["cmdg_caremptyweight"].ToString();
        tb_testgearreductionratio.Text = dt_cardata.Rows[0]["cmdg_testgearreductionratio"].ToString();
        tb_coolingdrivermethod.Text = dt_cardata.Rows[0]["cmdg_coolingdrivermethod"].ToString();
        tb_tiresnum.Text = dt_cardata.Rows[0]["cmdg_tiresnum"].ToString();
        tb_exhaustpipenum.Text = dt_cardata.Rows[0]["cmdg_exhaustpipenum"].ToString();
        tb_tirewidth.Text = dt_cardata.Rows[0]["cmdg_tirewidth"].ToString();
        ddl_inletmode.ClearSelection();
        ddl_inletmode.Items.FindByValue(dt_cardata.Rows[0]["cmdg_inletmode"].ToString()).Selected = true;       
        lbl_testweight.Text = Convert.ToDouble(dt_cardata.Rows[0]["cmdg_testweight"]).ToString();

        //機車的欄位 ,"cmdg_loadingweight","cmdg_firemethod","cmdg_strokeposdirection","cmdg_PM","cmdg_CO","cmdg_HC","cmdg_1stReductionratio","cmdg_2ndReductionratio"

        Load_weight_TextBox1.Text = dt_cardata.Rows[0]["cmdg_loadingweight"].ToString().Replace(".00", "");
        Start_up_mode_TextBox2.Text = dt_cardata.Rows[0]["cmdg_firemethod"].ToString();
        The_location_and_direction_of_the_exhaust_port_TextBox1.Text = dt_cardata.Rows[0]["cmdg_strokeposdirection"].ToString();
        tb_pm.Text = dt_cardata.Rows[0]["cmdg_PM"].ToString();
        tb_co.Text = dt_cardata.Rows[0]["cmdg_CO"].ToString();
        tb_hc.Text = dt_cardata.Rows[0]["cmdg_HC"].ToString();
        Primary_reduction_ratio_TextBox3.Text = dt_cardata.Rows[0]["cmdg_1stReductionratio"].ToString();
        Secondary_reduction_ratio_TextBox4.Text = dt_cardata.Rows[0]["cmdg_2ndReductionratio"].ToString();

        txt_remark.Text = dt_cardata.Rows[0]["cmdg_remark"].ToString();

        last_modaccount.Text = dt_cardata.Rows[0]["ai_username"].ToString();
        last_moddate.Text = dt_cardata.Rows[0]["cmdg_moddate"].ToString();
        //GetAgentAccount();
        string mode = Request_mode();

        if (mode.ToString().ToLower() == "g") //汽車
        {
            rbl_drivetype.SelectedValue = dt_cardata.Rows[0]["cmdg_drivetype"].ToString(); //驅動方式
        }
        if (mode.ToString().ToLower() == "d") //柴油車
        {
            rbl_drivetype.SelectedValue = dt_cardata.Rows[0]["cmdg_drivetype"].ToString(); //驅動方式
            txt_carbodyweight.Text = dt_cardata.Rows[0]["cmdg_carbodyweight"].ToString(); //車體總重量
            rbl_numofaxes_f.SelectedValue = dt_cardata.Rows[0]["cmdg_numofaxes_f"].ToString(); //前軸軸數
            rbl_numofaxes_b.SelectedValue = dt_cardata.Rows[0]["cmdg_numofaxes_b"].ToString(); //後軸軸數
            txt_increasepowerstyle.Text = dt_cardata.Rows[0]["cmdg_increasepowerstyle"].ToString(); //加力箱-型式
            txt_increasepowerratio.Text = dt_cardata.Rows[0]["cmdg_increasepowerratio"].ToString(); //加力箱-齒比
            txt_backgearnums.Text = dt_cardata.Rows[0]["cmdg_backgearnums"].ToString(); //後退檔
            txt_forwardgearnums.Text = dt_cardata.Rows[0]["cmdg_forwardgearnums"].ToString(); //前進檔
            txt_climbratio.Text = dt_cardata.Rows[0]["cmdg_climbratio"].ToString(); //爬坡檔
            txt_suspensionsystem_sub.Text = dt_cardata.Rows[0]["cmdg_suspensionsystem_supplement"].ToString(); //懸吊補檔
            txt_maxhorsepower_hb.Text = dt_cardata.Rows[0]["cmdg_maxhorsepower_hb"].ToString(); //馬達最大馬力
            txt_maxhorsepowerspeed_hb.Text = dt_cardata.Rows[0]["cmdg_maxhorsepowerspeed_hb"].ToString(); //馬達最大馬力轉速
            txt_torque_hb.Text = dt_cardata.Rows[0]["cmdg_torque_hb"].ToString(); //馬達最大扭力
            txt_torquespeed_hb.Text = dt_cardata.Rows[0]["cmdg_torquespeed_hb"].ToString(); //馬達最大扭力轉速
            ddl_D_highlowgear.ClearSelection();
            ddl_D_highlowgear.Items.FindByValue(dt_cardata.Rows[0]["cmdg_highlowgear"].ToString()).Selected = true;  //高低檔
        }
        transmissionnumSelectChange();


    }

    protected void BindGearRatio(string applyno, string carcompomodelno, string carmodelno)
    {
        DataSet ds_gearratio = new DataSet();
        //ds_gearratio = 
    }

    private void ControlObject()
    {
        if (!string.IsNullOrEmpty(Request_mode()))
        {
            string mode = Request_mode();

            if (mode.ToString().ToLower() == "g") //汽車
            {
                Label1_Title.Text = "噪音合格證明申請-汽油車(";
                lbl_powerfrom.Text = "電動汽車、複合動力電動車";
                Label2_Title.Text = ")車輛規格表資料填寫";
                // Classification_of_vehicle_types.Visible = true;//車種分類
                Gate_Count.Visible = true; //門數
                Car_driven_approach_title.Visible = true;//驅動方式
                Car_driven_approach_Dr.Visible = true;//驅動方式
                Automotive_fuel_title.Visible = true;//油料
                Automotive_fuel_title_content.Visible = true;//油料內容

                lbl_haveturbo.Text = "增壓器：";
                ddl_haveturbo.Visible = true;
                System_title.Visible = true;
                PollutionPreventionControlSystem.Visible = true;　//防治污染系統
                Automotive_Exhaust_System_title.Visible = true; //排氣系統
                Automotive_Exhaust_System_content.Visible = true;//排氣系統
                PCV_title.Visible = true; //PCV
                PCV_content.Visible = true;//PCV

                EEC_title.Visible = true; //EEC
                EEC_content.Visible = true;//EEC

                Automobile_differential_type_title.Visible = true; //差速器-型式
                Automobile_differential_type_content.Visible = true;//差速器-型式

                Automobile_differential_gear_ratio_title.Visible = true;//差速器-齒比
                Automobile_differential_gear_ratio_content.Visible = true;//差速器-齒比

                Transmission_system_reverse_gear_ratio_title.Visible = true;//變速系統-倒檔齒比
                Transmission_system_reverse_gear_ratio_content.Visible = true;//變速系統-倒檔齒比
                Installation_location_title.Visible = true;
                Installation_location_content.Visible = true;
                ///////////////////////////以上汽車////////////////////////////
                ///////////////////////////以下機車////////////////////////////

                The_location_and_direction_of_the_exhaust_port_title.Visible = false; //排氣口位置及方向
                The_location_and_direction_of_the_exhaust_port_content.Visible = false;//排氣口位置及方向
                Load_weight_title.Visible = false; //負重
                Load_weight_content.Visible = false; //負重
                Start_up_mode_title.Visible = false;//啟動方式
                Start_up_mode_content.Visible = false;//啟動方式
                Concentration_of_emissions.Visible = false;//碳氧化濃度
                Reduction_ratio.Visible = false; //二次減速比
                use_fuel_01.Visible = false;

                lbl_colheader_cylinder.Text = "汽缸數：";
                /////////////////////////修改名字////////////////////////////

                Label1_Bore_and_Inner_diameter01.Text = "缸徑";
                Stroke_stroke_Label1_title.Text = "衝程";

                /////////////////////////柴油車////////////////////////////  
                D_CarbodyWeight_title.Visible = false;//車體總重量

                D_numofaxes_Title.Visible = false;//軸數(傳動系統)
                lbl_suspensionsystem_sub.Visible = false; //懸吊系統(補) 
                txt_suspensionsystem_sub.Visible = false; //懸吊系統(補) 
                D_increasepower_title.Visible = false; //加力箱-型式

                div_D_highlowgear.Visible = false; //後退/高低檔
                D_climbratio_title.Visible = false;//爬坡檔

                D_maxhorsepower_Title.Visible = false;//馬達最大馬力
                D_torque_Title.Visible = false;//馬達最大扭力
            }
            else if (mode.ToString().ToLower() == "m")
            {
                Label1_Title.Text = "噪音合格證明申請-機器腳踏車(";
                lbl_powerfrom.Text = "複合動力電動機車";
                Label2_Title.Text = ")規格表資料填寫";
                // Classification_of_vehicle_types.Visible = false;//車種分類
                Gate_Count.Visible = false;//門數
                Car_driven_approach_title.Visible = false; //驅動方式
                Car_driven_approach_Dr.Visible = false;//驅動方式
                Automotive_fuel_title.Visible = false;//油料
                Automotive_fuel_title_content.Visible = false;//油料內容
                System_title.Visible = false;
                PollutionPreventionControlSystem.Visible = false;　//防治污染系統
                Automotive_Exhaust_System_title.Visible = false; //排氣系統
                Automotive_Exhaust_System_content.Visible = false;//排氣系統
                PCV_title.Visible = false;//PCV
                PCV_content.Visible = false;//PCV

                EEC_title.Visible = false; //EEC
                EEC_content.Visible = false;//EEC

                Automobile_differential_type_title.Visible = false; //差速器-型式
                Automobile_differential_type_content.Visible = false;//差速器-型式


                Automobile_differential_gear_ratio_title.Visible = false;//差速器-齒比
                Automobile_differential_gear_ratio_content.Visible = false;//差速器-齒比

                Transmission_system_reverse_gear_ratio_title.Visible = false;//變速系統-倒檔齒比
                Transmission_system_reverse_gear_ratio_content.Visible = false;//變速系統-倒檔齒比
                Installation_location_title.Visible = false;
                Installation_location_content.Visible = false;
                ///////////////////////////以上汽車////////////////////////////
                ///////////////////////////以下機車////////////////////////////
                Carwheelbase_f_title.Visible = false;//輪距-前
                Carwheelbase_f_content.Visible = false;//輪距-前
                Carwheelbase_b_title.Visible = false;//輪距-後
                Carwheelbase_b_content.Visible = false;//輪距-後
                The_location_and_direction_of_the_exhaust_port_title.Visible = true; //排氣口位置及方向
                The_location_and_direction_of_the_exhaust_port_content.Visible = true;//排氣口位置及方向
                Load_weight_title.Visible = true;//負重
                Load_weight_content.Visible = true; //負重
                Start_up_mode_title.Visible = true;//啟動方式
                Start_up_mode_content.Visible = true;//啟動方式
                Concentration_of_emissions.Visible = true;//碳氧化濃度
                Reduction_ratio.Visible = true; //一次減速比
                use_fuel_01.Visible = true;
                lbl_colheader_cylinder.Text = "汽缸缸數及排列：";

                lbl_haveturbo.Text = "燃燒循環：";
                ddl_haveturbo.Visible = false;
                /////////////////////////修改名字////////////////////////////

                Label1_Bore_and_Inner_diameter01.Text = "內徑";
                Stroke_stroke_Label1_title.Text = "行程";
                /////////////////////////柴油車////////////////////////////
                D_CarbodyWeight_title.Visible = false;//車體總重量                

                D_numofaxes_Title.Visible = false;//軸數(傳動系統)
                lbl_suspensionsystem_sub.Visible = false; //懸吊系統(補) 
                txt_suspensionsystem_sub.Visible = false; //懸吊系統(補) 
                D_increasepower_title.Visible = false; //加力箱-型式

                div_D_highlowgear.Visible = false; //後退/高低檔
                D_climbratio_title.Visible = false;//爬坡檔

                D_maxhorsepower_Title.Visible = false;//馬達最大馬力
                D_torque_Title.Visible = false;//馬達最大扭力
                D_torque_Title.Visible = false; //馬達最大扭力
            }
            else //(mode.ToString().ToLower() == "D")
            {

                Label1_Title.Text = "噪音合格證明申請-柴油車(";
                lbl_powerfrom.Text = "複合動力電動車";
                Label2_Title.Text = ")車輛規格表資料填寫";

                nonD_Cartotal_weight_title.Visible = false;//總重
                nonD_Cartotal_weight_content.Visible = false;//總重

                nonD_Passengers_title.Visible = false;//乘坐人數
                nonD_Passengers_content.Visible = false;//乘坐人數

                // Classification_of_vehicle_types.Visible = true;//車種分類
                Gate_Count.Visible = true; //門數
                Car_driven_approach_title.Visible = true;//驅動方式
                Car_driven_approach_Dr.Visible = true;//驅動方式
                Automotive_fuel_title.Visible = true;//油料
                Automotive_fuel_title_content.Visible = true;//油料內容


                lbl_haveturbo.Text = "增壓器：";
                ddl_haveturbo.Visible = true;
                System_title.Visible = true;
                PollutionPreventionControlSystem.Visible = false;　//防治污染系統
                Automotive_Exhaust_System_title.Visible = false; //排氣系統
                Automotive_Exhaust_System_content.Visible = false;//排氣系統
                PCV_title.Visible = false; //PCV
                PCV_content.Visible = false;//PCV

                EEC_title.Visible = false; //EEC
                EEC_content.Visible = false;//EEC

                Automobile_differential_type_title.Visible = true; //差速器-型式
                Automobile_differential_type_content.Visible = true;//差速器-型式

                Automobile_differential_gear_ratio_title.Visible = true;//差速器-齒比
                Automobile_differential_gear_ratio_content.Visible = true;//差速器-齒比

                Transmission_system_reverse_gear_ratio_title.Visible = true;//變速系統-倒檔齒比
                Transmission_system_reverse_gear_ratio_content.Visible = true;//變速系統-倒檔齒比
                Installation_location_title.Visible = true;
                Installation_location_content.Visible = true;
                ///////////////////////////以上汽車////////////////////////////
                ///////////////////////////以下機車////////////////////////////

                The_location_and_direction_of_the_exhaust_port_title.Visible = false; //排氣口位置及方向
                The_location_and_direction_of_the_exhaust_port_content.Visible = false;//排氣口位置及方向
                Load_weight_title.Visible = true; //負重
                Load_weight_content.Visible = true; //負重
                Start_up_mode_title.Visible = false;//啟動方式
                Start_up_mode_content.Visible = false;//啟動方式
                Concentration_of_emissions.Visible = false;//碳氧化濃度
                Reduction_ratio.Visible = false; //二次減速比
                use_fuel_01.Visible = false;

                lbl_colheader_cylinder.Text = "汽缸數：";
                /////////////////////////修改名字////////////////////////////

                Label1_Bore_and_Inner_diameter01.Text = "缸徑";
                Stroke_stroke_Label1_title.Text = "衝程";
                /////////////////////////柴油車////////////////////////////   
                D_CarbodyWeight_title.Visible = true;//車體總重量                

                D_numofaxes_Title.Visible = true;//軸數(傳動系統)
                lbl_suspensionsystem_sub.Visible = true; //懸吊系統(補) 
                txt_suspensionsystem_sub.Visible = true; //懸吊系統(補) 
                D_increasepower_title.Visible = true; //加力箱-型式

                div_D_highlowgear.Visible = true; //後退/高低檔

                D_climbratio_title.Visible = true;//爬坡檔

                D_maxhorsepower_Title.Visible = true;//馬達最大馬力
                D_torque_Title.Visible = true; //馬達最大扭力
            }


        }
    }

    /// <summary>
    /// 註冊前端ID
    /// </summary>
    private void Regist_Client_ID()
    {
        Regist_Select.Value = ddl_carbodyshape.ClientID;
    }

    private void BindBaseData()
    {
        Cryptography crypy = new Cryptography();
        int keynum = crypy.getRadNum();
        string[] arr_carcompomodel = crypy.wsCarCompoModelValidate(keynum);
        wsCodetbl wsCodeTbl = new wsCodetbl();
        string[] arr_wscodetbl = crypy.wsCodetblValidate(keynum);

        #region 車身式樣
        DataSet dsCarBodyShape_g = wsCodeTbl.get_CarBodyShape_g(arr_wscodetbl[0].ToString(), arr_wscodetbl[1].ToString(), Request_mode());
        ddl_carbodyshape.DataSource = dsCarBodyShape_g;
        ddl_carbodyshape.DataTextField = "text";
        ddl_carbodyshape.DataValueField = "value";
        ddl_carbodyshape.DataBind();
        dsCarBodyShape_g.Dispose();
        #endregion

        #region 驅動方式
        DataSet dsdriveType = wsCodeTbl.getCarDriveType(arr_wscodetbl[0].ToString(), arr_wscodetbl[1].ToString());
        rbl_drivetype.DataSource = dsdriveType;
        rbl_drivetype.DataTextField = "ct_dt_desc";
        rbl_drivetype.DataValueField = "ct_dt_id";
        rbl_drivetype.DataBind();
        dsdriveType.Dispose();
        #endregion

        #region 驅動軸數
        DataSet dsnumberofaxes = wsCodeTbl.getNumberOfAxes(arr_wscodetbl[0].ToString(), arr_wscodetbl[1].ToString());
        rbl_numofaxes_f.DataSource = dsnumberofaxes;
        rbl_numofaxes_f.DataTextField = "ct_na_desc";
        rbl_numofaxes_f.DataValueField = "ct_na_id";
        rbl_numofaxes_f.DataBind();

        rbl_numofaxes_b.DataSource = dsnumberofaxes;
        rbl_numofaxes_b.DataTextField = "ct_na_desc";
        rbl_numofaxes_b.DataValueField = "ct_na_id";
        rbl_numofaxes_b.DataBind();

        dsnumberofaxes.Dispose();


        #endregion

        //#region 使用燃料
        //DropDownList1_use_fuel.DataSource = db_nv.use_fuel();
        //DropDownList1_use_fuel.DataTextField = "ctDesc";
        //DropDownList1_use_fuel.DataValueField = "ctID";
        //DropDownList1_use_fuel.DataBind();
        //#endregion 使用燃料

        //#region 循環數
        //DropDownList1_Number_cycles.DataSource = db_nv.PowerExport();
        //DropDownList1_Number_cycles.DataTextField = "text";
        //DropDownList1_Number_cycles.DataValueField = "value";
        //DropDownList1_Number_cycles.DataBind();
        //#endregion 循環數

        #region 車種分類
        DataSet ds_noisevechilecategory = wsCodeTbl.get_noisevechilecategory(arr_wscodetbl[0].ToString(), arr_wscodetbl[1].ToString());

        ddl_cartype.DataSource = ds_noisevechilecategory;
        ddl_cartype.DataTextField = "ct_nvc_desc";
        ddl_cartype.DataValueField = "ct_nvc_id";
        ddl_cartype.DataBind();
        ds_noisevechilecategory.Dispose();
        #endregion

        #region 廠牌
        DataSet dscarbrand = wsCodeTbl.get_carbrand(arr_wscodetbl[0].ToString(), arr_wscodetbl[1].ToString());

        ddl_brand.DataSource = dscarbrand;
        ddl_brand.DataTextField = "ct_bd_desc";
        ddl_brand.DataValueField = "ct_bd_id";
        ddl_brand.DataBind();
        dscarbrand.Dispose();
        #endregion

        #region 門數
        DataSet dsCarDoorCount = wsCodeTbl.get_CarDoorCount(arr_wscodetbl[0].ToString(), arr_wscodetbl[1].ToString());

        ddl_cardoorcount.DataSource = dsCarDoorCount;
        ddl_cardoorcount.DataTextField = "ct_vr_desc";
        ddl_cardoorcount.DataValueField = "ct_vr_id";
        ddl_cardoorcount.DataBind();
        dsCarDoorCount.Dispose();
        #endregion

        #region 增壓器
        DataSet dshaveturbo = wsCodeTbl.getHaveTurboMode(arr_wscodetbl[0].ToString(), arr_wscodetbl[1].ToString());
        ddl_haveturbo.DataSource = dshaveturbo;
        ddl_haveturbo.DataTextField = "ct_htm_desc";
        ddl_haveturbo.DataValueField = "ct_htm_id";
        ddl_haveturbo.DataBind();
        dshaveturbo.Dispose();
        #endregion

        #region 進氣方式
        DataSet dsinletmode = wsCodeTbl.get_InletMode(arr_wscodetbl[0].ToString(), arr_wscodetbl[1].ToString());

        ddl_inletmode.DataSource = dsinletmode;
        ddl_inletmode.DataTextField = "ct_im_desc";
        ddl_inletmode.DataValueField = "ct_im_id";
        ddl_inletmode.DataBind();
        dsinletmode.Dispose();
        #endregion

        #region 高低檔
        DataSet dshighlowgear = wsCodeTbl.get_HighLowGear(arr_wscodetbl[0].ToString(), arr_wscodetbl[1].ToString());

        ddl_D_highlowgear.DataSource = dshighlowgear;
        ddl_D_highlowgear.DataTextField = "ct_hlg_desc";
        ddl_D_highlowgear.DataValueField = "ct_hlg_id";
        ddl_D_highlowgear.DataBind();
        dshighlowgear.Dispose();
        #endregion

        //#region 車身式樣
        //string[] arr_cy = crypy.wsCodetblValidate(keynum);
        //DataSet dsCountry = wsCodeTbl.getCountry(arr_cy[0].ToString(), arr_cy[1].ToString());

        //ddl_carbodyshape.DataSource = dsCountry;
        //ddl_carbodyshape.DataTextField = "ct_cy_desc";
        //ddl_carbodyshape.DataValueField = "ct_cy_id";
        //ddl_carbodyshape.DataBind();
        //#endregion
        BindCarcompoList();

    }

    #region 車型組清單
    private void BindCarcompoList()
    {
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        string[] Warr = crypy.wsApplybasedataDetailValid(getRadNum());
        ddl_carcompolist.Items.Clear();
        ddl_carcompolist.DataSource = wsApplyData.get_Nv_carmodelnames(lbl_applyno.Text.Trim(), lbl_carcompomodelno.Text.Trim(), Warr[0].ToString(), Warr[1].ToString());
        ddl_carcompolist.DataTextField = "fullcarstylename";
        ddl_carcompolist.DataValueField = "cmdg_carmodelno";
        ddl_carcompolist.DataBind();
    }
    #endregion
    protected void Button1_Click(object sender, EventArgs e)
    {
        //if (cb_berepresentativevehicle.Checked)
        //{
        //    //check if db had representativevehicle            
        //    Cryptography crypy = new Cryptography();
        //    int keynum = crypy.getRadNum();
        //    string[] arr = crypy.wsCarCompoModelValidate(keynum);
        //    if (wsCarCmpModel.check_repeat_representativevehicle(lbl_applyno.Text, lbl_carcompomodelno.Text, arr[0].ToString(), arr[1].ToString()))
        //    {
        //        MessageBox.Show("已經有代表車的存在！！此車型無法成為代表車。");
        //        return;
        //    }
        //}
        #region 特殊字元判斷-沒有MasterPage
        foreach (Control ctl in Page.Form.Controls)
        {
            Control content = (Control)ctl;
            foreach (Control c in content.Controls)
            {
                if (c is TextBox)
                {
                    TextBox objTextBox = default(TextBox);
                    objTextBox = (TextBox)c;
                    if ((tbWord(objTextBox.Text)))
                    {
                        MessageBox.Show(objTextBox.Text + ":含有系統不允許的特殊字元");
                        return;
                    }
                }
            }
        }
        #endregion
        //必填欄位中,因本次申請為進口車輛,車型代碼非必填、車身式樣也非必填、其他)車型名稱補充非必填。
        //傳動系統----輪胎規格----選擇前後(正確為”選配”)非必填。
        //測試檔位總減速比只有手排車必填。

        #region 判斷製造地區欄位
        CheckPct();
        if (!bCheckPcy)
        {
            return;
        }
        #endregion

        try
        {


            if (IsEmptyNull())
            {
                if (IsStringNull())
                {
                    string[] arrData = new string[82];
                    arrData[0] = lbl_applyno.Text.Trim();
                    arrData[1] = lbl_carcompomodelno.Text.Trim();
                    arrData[2] = ddl_carbodyshape.SelectedValue.Trim();
                    arrData[3] = tb_carbodyshapedesc.Text.Trim();
                    if (cb_berepresentativevehicle.Checked)
                        arrData[4] = "Y";
                    else
                        arrData[4] = "N";
                    arrData[5] = ddl_cartype.SelectedValue;
                    arrData[6] = tb_baseenginename.Text.Trim();
                    arrData[7] = ddl_brand.SelectedValue;
                    arrData[8] = tb_carchnmodelname.Text.Trim();
                    arrData[9] = tb_carengmodelname.Text.Trim();
                    arrData[10] = tb_carmodelcode.Text.Trim();
                    if (lbl_transmissiontype.Text != "CVT")
                    {
                        arrData[11] = ddl_transmissionnum.SelectedValue;
                    }
                    else
                    {
                        arrData[11] = "";
                    }
                    arrData[12] = ddl_cardoorcount.SelectedValue;
                    arrData[13] = tb_carmodeladd.Text.Trim();
                    arrData[14] = tb_carbodylenth.Text.Trim();
                    arrData[15] = tb_carbodywidth.Text.Trim();
                    arrData[16] = tb_carbodyhieght.Text.Trim();
                    arrData[17] = tb_carwheelbase.Text.Trim();
                    arrData[18] = tb_carwheelbase_f.Text.Trim();
                    arrData[19] = tb_carwheelbase_b.Text.Trim();
                    arrData[20] = tb_caremptyweight.Text.Trim();
                    arrData[21] = tb_cartotalweight.Text.Trim();
                    arrData[22] = tb_passengers.Text.Trim();
                    arrData[23] = tb_cylindermeters.Text.Trim();
                    arrData[24] = tb_cylinderstroke.Text.Trim();
                    arrData[25] = tb_compressionratio.Text.Trim();
                    arrData[26] = tb_maxhorsepower.Text.Trim();
                    arrData[27] = tb_maxHorsepowerspeed.Text.Trim();
                    arrData[28] = tb_torque.Text.Trim();
                    arrData[29] = tb_torquespeed.Text.Trim();
                    arrData[30] = ddl_haveturbo.SelectedValue;//增壓器：";
                    arrData[31] = tb_oilcapacity.Text.Trim();
                    arrData[32] = tb_929598.Text.Trim();
                    arrData[33] = tb_exhaustsystem.Text.Trim();
                    arrData[34] = tb_pcv.Text.Trim();
                    arrData[35] = tb_eec.Text.Trim();
                    arrData[36] = tb_differentialmodel.Text.Trim();
                    arrData[37] = tb_differentialgearratio.Text.Trim();
                    arrData[38] = tb_transmissionmodel.Text.Trim();
                    arrData[39] = tb_transmissionbackgearratio.Text.Trim();
                    arrData[40] = tb_highestspeed.Text.Trim();
                    arrData[41] = tb_suspensionsystem_f.Text.Trim();
                    arrData[42] = tb_suspensionsystem_b.Text.Trim();
                    arrData[43] = tb_tirespec_std_f.Text.Trim();
                    arrData[44] = tb_tirespec_std_b.Text.Trim();
                    arrData[45] = tb_tirespec_cho_f.Text.Trim();
                    arrData[46] = tb_tirespec_cho_b.Text.Trim();
                    //依據不同期別設定為空重+N
                    if (tb_caremptyweight.Text != "")
                        arrData[47] =  Convert.ToDouble((Convert.ToDecimal(tb_caremptyweight.Text) + Convert.ToDecimal(lbl_testweightformula.Text))).ToString();
                    else
                        arrData[47] = "";
                    arrData[48] = tb_testgearreductionratio.Text.Trim();
                    arrData[49] = tb_coolingdrivermethod.Text.Trim();
                    arrData[50] = tb_tiresnum.Text.Trim();
                    arrData[51] = tb_exhaustpipenum.Text.Trim();
                    arrData[52] = tb_tirewidth.Text.Trim();
                    arrData[53] = ddl_inletmode.SelectedValue;
                    arrData[54] = GetAgentAccount();
                    arrData[55] = GetAgentAccount();
                    arrData[56] = rbl_drivetype.SelectedValue;
                    arrData[57] = txt_remark.Text.Trim();
                    arrData[58] = tb_producercountry.Text.Trim("、/".ToCharArray());

                    /*機車的欄位 載重Cmdg_loadingweight、啟動方式Cmdg_firemethod、排氣口位置及方向Cmdg_strokeposdirection、粒狀汙染物Cmdg_PM、一氧化碳Cmdg_CO、碳氧化合物Cmdg_HC、一次減速比Cmdg_1stReductionratio、二次減速比Cmdg_2ndReductionratio*/
                    if (Load_weight_TextBox1.Text != "")
                    {
                        arrData[59] = Load_weight_TextBox1.Text.Trim();
                    }
                    else
                    {
                        arrData[59] = "0";
                    }

                    arrData[60] = Start_up_mode_TextBox2.Text.Trim();
                    arrData[61] = The_location_and_direction_of_the_exhaust_port_TextBox1.Text.Trim();
                    arrData[62] = tb_pm.Text.Trim();
                    arrData[63] = tb_co.Text.Trim();
                    arrData[64] = tb_hc.Text.Trim();
                    arrData[65] = Primary_reduction_ratio_TextBox3.Text.Trim();
                    arrData[66] = Secondary_reduction_ratio_TextBox4.Text.Trim();
                    /*柴油車的欄位 後退檔cmdg_backgearnums、高低半檔cmdg_highlowgear、車體總重cmdg_carbodyweight、最大馬力cmdg_maxhorsepower_hb、最大馬力轉速cmdg_maxhorsepowerspeed_hb、最大扭力cmdg_torque_hb、最大扭力轉速cmdg_torquespeed_hb、驅動軸數前cmdg_numofaxes_f、驅動軸數後cmdg_numofaxes_b、加力箱型式cmdg_increasepowerstyle、加力箱齒比cmdg_increasepowerratio、爬坡檔cmdg_climbratio、補軸cmdg_suspensionsystem_supplement*/
                    arrData[67] = txt_backgearnums.Text.ToString().Trim();
                    if (Request_mode() == "d")
                    {
                        arrData[68] = ddl_D_highlowgear.SelectedValue;
                    }
                    else
                    {
                        arrData[68] = "0";
                    }

                    arrData[69] = txt_carbodyweight.Text.ToString().Trim();
                    arrData[70] = txt_maxhorsepower_hb.Text.ToString().Trim();
                    arrData[71] = txt_maxhorsepowerspeed_hb.Text.ToString().Trim();
                    arrData[72] = txt_torque_hb.Text.ToString().Trim();
                    arrData[73] = txt_torquespeed_hb.Text.ToString().Trim();
                    arrData[74] = rbl_numofaxes_f.SelectedValue;
                    arrData[75] = rbl_numofaxes_b.SelectedValue;
                    arrData[76] = txt_increasepowerstyle.Text.ToString().Trim();
                    arrData[77] = txt_increasepowerratio.Text.ToString().Trim();
                    arrData[78] = txt_climbratio.Text.ToString().Trim();
                    arrData[79] = txt_suspensionsystem_sub.Text.ToString().Trim();
                    arrData[80] = txt_forwardgearnums.Text.ToString().Trim();

                    arrData[81] = lbl_carmodelno.Text.Trim();



                    wsCarCompoModel wsCarCmpModel = new wsCarCompoModel();
                    Cryptography crypy = new Cryptography();
                    int keynum = crypy.getRadNum();
                    string[] arr = crypy.wsCarCompoModelValidate(keynum);
                    string carmodelno = "";
                    string transmissionnum_for_delete = "";
                    if (lbl_transmissiontype.Text != "CVT")
                    {
                        transmissionnum_for_delete = ddl_transmissionnum.SelectedValue;
                    }
                    else
                    {
                        transmissionnum_for_delete = "2";
                    }
                    if (lbl_carmodelno.Text == "")
                    {
                        carmodelno = wsCarCmpModel.Insert_new_carmodeldata(arrData, arr[0].ToString(), arr[1].ToString());

                        //變更測試報告--判斷有無勾選代表車 
                        if (cb_berepresentativevehicle.Checked)
                        {
                            wsCarCmpModel.update_carmodelnoisetestrpt(lbl_applyno.Text, lbl_carcompomodelno.Text, carmodelno, arr[0].ToString(), arr[1].ToString());
                        }

                        if (carmodelno != "")
                        {
                            lbl_carmodelno.Text = carmodelno;
                            Insert_nv_db_carcompomodel_carmodellist();
                            //刪除多餘的齒筆資料  EX：原本挑8檔後來改成5檔  須將6 7 8 檔資料刪除
                            //wsCarCmpModel.delete_GearRatio_external(lbl_applyno.Text, lbl_carcompomodelno.Text, lbl_carmodelno.Text, ddl_transmissionnum.SelectedValue, arr[0].ToString(), arr[1].ToString());
                        }
                        else
                            MessageBox.Show("新增失敗！");
                    }
                    else
                    {
                        //變更測試報告--判斷有無勾選代表車 
                        if (cb_berepresentativevehicle.Checked)
                        {
                            wsCarCmpModel.update_carmodelnoisetestrpt(lbl_applyno.Text, lbl_carcompomodelno.Text, lbl_carmodelno.Text, arr[0].ToString(), arr[1].ToString());
                        }
                        wsCarCmpModel.update_carmodeldata(arrData, arr[0].ToString(), arr[1].ToString());
                        Insert_nv_db_carcompomodel_carmodellist();
                        //刪除多餘的齒筆資料  EX：原本挑8檔後來改成5檔  須將6 7 8 檔資料刪除
                        wsCarCmpModel.delete_GearRatio_external(lbl_applyno.Text, lbl_carcompomodelno.Text, lbl_carmodelno.Text, transmissionnum_for_delete, arr[0].ToString(), arr[1].ToString());
                    }
                    //延伸
                    // declare @CARID varchar(36) set @CARID=NEWID() Insert into " + strTable + " (" + strSql_Fields + ") Values (" + strSql_Values + ") 
                    //if exists(select cpm_applytype from nv_carcompomodeldata where cpm_applyno=@applyno and cpm_carcompomodelno=@carcompomodelno and cpm_applytype='3')
                    //begin
                    //  update nv_carmodeldata set cmdg_beextendmodel='1' where cmdg_applyno=@applyno and cmdg_carcompomodelno=@carcompomodelno and cmdg_carmodelono=@CARID
                    //end
                    //select @CARID
                    //修改   
                    //if exists(select cpm_applytype from nv_carcompomodeldata where cpm_applyno=@applyno and cpm_carcompomodelno=@carcompomodelno and cpm_applytype='4')
                    //begin
                    //  update nv_carmodeldata set cmdg_beenmod='1' where cmdg_applyno=@applyno and cmdg_carcompomodelno=@carcompomodelno and cmdg_carmodelono=@carmodelno
                    //end
                    //select @CARID
                }
            }
        }
        catch (Exception ex)
        {
            JS.AlertMessage(ex.Message);
        }
    }

    private void Insert_nv_db_carcompomodel_carmodellist()
    {
        noisevalidation.db.nv_db_carcompomodel_carmodellist.nv_db_carcompomodel_carmodellist insert_nv_gearratio = new noisevalidation.db.nv_db_carcompomodel_carmodellist.nv_db_carcompomodel_carmodellist();
        int n = Variable_speed_system.Controls.Count;
        int i = 0;
        string error = "";

        for (i = 0; i < n; i++)
        {
            TextBox VirTextBox = (TextBox)Variable_speed_system.Controls[i].FindControl(Variable_speed_system_TextBox + i);
            if (VirTextBox != null)
            {
                if (!insert_nv_gearratio.nv_db_carcompomodel_carmodellist_insert(lbl_applyno.Text, lbl_carcompomodelno.Text, lbl_carmodelno.Text, i.ToString(), VirTextBox.Text))
                {
                    error = "排檔數" + i + "新增失敗"; ;
                    break;
                }
            }
        }

        if (!string.IsNullOrEmpty(error))
        {
            JS.AlertMessage(error);
        }
        else
        {
            JS.AlertMessage("儲存完成");
        }
    }

    /// <summary>
    /// 驗證字串
    /// </summary>
    /// <returns></returns>
    private bool IsStringNull()
    {
        bool success = false;
        string value = string.Empty;
        value += IsStrLimt(tb_baseenginename.Text, 50, "基本引擎");
        value += IsStrLimt(tb_carengmodelname.Text + tb_carengmodelname.Text, 130, "中/英文車型名稱");
        //value += IsStrLimt(tb_carengmodelname.Text, 80, "英文車型名稱");
        //value += IsStrLimt(tb_carmodeladd.Text, 20, "其他(車型名稱補充)");
        value += IsStrLimt(tb_carmodelcode.Text, 30, "車型代碼");
        value += IsStrLimt(tb_carbodylenth.Text, 25, "車身尺度與重量：全長");
        value += IsStrLimt(tb_carbodywidth.Text, 25, "車身尺度與重量：全寬");
        value += IsStrLimt(tb_carbodyhieght.Text, 25, "車身尺度與重量：全高");
        value += IsStrLimt(tb_carwheelbase.Text, 15, "車身尺度與重量：軸距");
        value += IsStrLimt(tb_carwheelbase_f.Text, 25, "車身尺度與重量：輪距-前");
        value += IsStrLimt(tb_carwheelbase_b.Text, 25, "車身尺度與重量：輪距-後");
        value += IsStrLimt(tb_caremptyweight.Text, 25, "車身尺度與重量：空重");
        //value += IsStrLimt(tb_cartotalweight.Text, "車身尺度與重量：總重", true, 25);
        //value += IsStrLimt(tb_passengers.Text, "車身尺度與重量：乘坐人數", true, 0);
        //value += IsStrLimt(tb_cylinderstroke.Text, "基本引擎：缸徑X衝程：缸徑", false, 0);
        //value += IsStrLimt(tb_cylindermeters.Text, "基本引擎：缸徑X衝程：衝程", false, 0);
        //value += IsStrLimt(tb_compressionratio.Text, "基本引擎：壓縮比", true, 20);
        value += IsStrLimt(tb_maxhorsepower.Text, "基本引擎：最大馬力", false, 20);
        //value += IsStrLimt(tb_maxHorsepowerspeed.Text, "基本引擎：最大馬力轉速", true, 20);
        //value += IsStrLimt(tb_torque.Text, "基本引擎：最大扭力", false, 20);
        //value += IsStrLimt(tb_torquespeed.Text, "基本引擎：最大扭力轉速", true, 20);
        //value += IsStrLimt(tb_oilcapacity.Text, "基本引擎：油箱容量", true, 25);

        value += IsStrLimt(tb_transmissionmodel.Text, 50, "傳動系統：變速系統~型式");
        //value += IsStrLimt(tb_highestspeed.Text, "傳動系統：最高車速", true, 25);

        value += IsStrLimt(tb_suspensionsystem_f.Text, 50, "傳動系統：懸吊系統：前");
        value += IsStrLimt(tb_suspensionsystem_b.Text, 50, "傳動系統：懸吊系統：後");
        value += IsStrLimt(tb_tirespec_std_f.Text, 20, "傳動系統：輪胎規格：標準~前");
        value += IsStrLimt(tb_tirespec_std_b.Text, 20, "傳動系統：輪胎規格：標準~後");
        value += IsStrLimt(tb_tirespec_cho_f.Text, 100, "傳動系統：輪胎規格：選配~前");
        value += IsStrLimt(tb_tirespec_cho_b.Text, 100, "傳動系統：輪胎規格：選配~後");
        value += IsStrLimt(tb_testgearreductionratio.Text, 20, "傳動系統：★測試檔位總減速比");
        // value += IsStrLimt(tb_coolingdrivermethod.Text, 20, "傳動系統：冷卻風扇驅動方式");
        value += IsStrLimt(tb_tiresnum.Text, "傳動系統：輪胎數量（不含備胎）", true, 0);
        //value += IsStrLimt(tb_exhaustpipenum.Text, "傳動系統：排氣管開口數量", true, 0);
        value += IsStrLimt(tb_tirewidth.Text, 20, "傳動系統：輪胎寬度");
        //製造地區長度       
        value += IsStrLimt(tb_producercountry.Text, 100, "製造地區"); //製造地區長度       


        if (Request_mode() == "g")
        {
            //value += IsStrLimt(tb_929598.Text, "基本引擎：油料", true, 20);
            value += IsStrLimt(tb_exhaustsystem.Text, 100, "防治污染系統：排氣系統");
            value += IsStrLimt(tb_pcv.Text, 100, "防治污染系統：PCV");
            value += IsStrLimt(tb_eec.Text, 100, "防治污染系統：EEC");
            //value += IsStrLimt(tb_differentialmodel.Text, 50, "傳動系統：差速器-型式");
            //value += IsStrLimt(tb_differentialgearratio.Text, 50, "傳動系統：差速器-齒比");
            //value += IsStrLimt(tb_transmissionbackgearratio.Text, 20, "傳動系統：變速系統~倒檔齒比");
        }
        else if (Request_mode() == "m")
        {
            value += IsStrLimt(tb_pm.Text, 100, "排放廢氣濃度：粒狀汙染物");
            value += IsStrLimt(tb_co.Text, 100, "排放廢氣濃度：一氧化碳");
            value += IsStrLimt(tb_hc.Text, 100, "排放廢氣濃度：碳氧化合物");
            value += IsStrLimt(Primary_reduction_ratio_TextBox3.Text, 20, "傳動系統：一次減速比");
            value += IsStrLimt(Secondary_reduction_ratio_TextBox4.Text, 20, "傳動系統：二次減速比");
            value += IsStrLimt(The_location_and_direction_of_the_exhaust_port_TextBox1.Text, 50, "傳動系統：排氣口位置及方向");

            value += IsStrLimt(Start_up_mode_TextBox2.Text, 20, "基本引擎：啟動方式");
        }
        else if (Request_mode() == "d")
        {

            value += IsStrLimt(txt_carbodyweight.Text, 6, "車身尺度與重量：車體總重量");
            value += IsStrLimt(txt_maxhorsepower_hb.Text, 20, "基本引擎：馬達最大馬力");
            value += IsStrLimt(txt_maxhorsepowerspeed_hb.Text, 20, "基本引擎：馬達最大馬力轉速");
            value += IsStrLimt(txt_torque_hb.Text, 20, "基本引擎：馬達最大扭力");
            value += IsStrLimt(txt_torquespeed_hb.Text, 20, "基本引擎：馬達最大扭力轉速");
            value += IsStrLimt(txt_suspensionsystem_sub.Text, 50, "傳動系統：懸吊系統補軸");

            value += IsStrLimt(txt_forwardgearnums.Text, 2, "防治污染系統：前進檔");
            value += IsStrLimt(txt_backgearnums.Text, 2, "防治污染系統：後退檔");
            value += IsStrLimt(tb_exhaustsystem.Text, 100, "防治污染系統：排氣系統");


        }
        else
        {
            value = string.Empty;
        }
        if (!string.IsNullOrEmpty(value))
        {
            JS.AlertMessage(value);
        }
        else { success = true; }

        return success;
    }

    /// <summary>
    /// 驗證字串
    /// </summary>
    /// <param name="ValidatorString">驗證的字串</param>
    /// <param name="limt">字串長度</param>
    /// <param name="ShowErrorString">秀出錯誤訊息</param>
    /// <param name="ValidatorType">驗證的類型 str,int</param>
    /// <returns></returns>
    private string IsStrLimt(string ValidatorString, int limt, string ShowErrorString)
    {
        string value = string.Empty;

        if (ValidatorString.Length > limt)
        {
            value = ShowErrorString + "字數必須小於" + limt + "\\n";
        }
        return value;
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="ValidatorString"></param>
    /// <param name="ShowErrorString"></param>
    /// <param name="ISNUM">true 驗證整數, false  驗證浮點數</param>
    /// <param name="limt">0 不驗證字串長度</param>
    /// <returns></returns>
    private string IsStrLimt(string ValidatorString, string ShowErrorString, bool ISINT, int limt)
    {
        bool success = false;
        string value = string.Empty;
        if (ISINT)
        {
            if (!ValidatorHelper.IsInt(ValidatorString.Trim()))
            {
                success = true;
                value = ShowErrorString + "必須為整數 \\n";
            }
        }
        else
        {
            if (!ValidatorHelper.IsFloat(ValidatorString.Trim()) && !ValidatorHelper.IsInt(ValidatorString.Trim()))
            {
                success = true;
                value = ShowErrorString + "必須為數字 \\n";
            }
        }
        if (!success)
        {
            if (limt != 0)
            {
                if (ValidatorString.Trim().Length > limt)
                {
                    value = ShowErrorString + "字數不可以大於" + limt + "\\n";
                }
            }
        }
        return value;
    }


    /// <summary>
    /// 驗證是否為空
    /// </summary>
    /// <returns></returns>
    private bool IsEmptyNull()
    {
        bool success = false; string ErrorString = string.Empty;
        if (SelectChange_Verifity())
        {
            Boolean transmissiontype = false;
            if (lbl_transmissiontype.Text == "M")
            {
                transmissiontype = string.IsNullOrEmpty(tb_testgearreductionratio.Text.Trim());
            }
            //2 進口車輛的必填欄位只有廠牌,英文車型,其他都可填可不填
            //1 國產車必填欄位只有廠牌,車型代碼其他都可填可不填
            Boolean need_carmodelname = false;
            if (lbl_isimport.Text == "1") //1國產2進口
            {
                if (string.IsNullOrEmpty(tb_carmodelcode.Text.Trim()))
                {
                    need_carmodelname = true;
                }
            }
            else
            {
                if (string.IsNullOrEmpty(tb_carengmodelname.Text.Trim()))
                {
                    need_carmodelname = true;
                }
            }

            if (Request_mode() == "g")
            {


                if (!string.IsNullOrEmpty(tb_baseenginename.Text.Trim()) &&
                    !string.IsNullOrEmpty(tb_producercountry.Text.Trim()) &&
                    //!(string.IsNullOrEmpty(tb_carchnmodelname.Text.Trim()) && string.IsNullOrEmpty(tb_carengmodelname.Text.Trim())) &&
                    //!string.IsNullOrEmpty(tb_carmodeladd.Text.Trim()) &&
                      !(need_carmodelname) &&
                     !string.IsNullOrEmpty(tb_carmodelcode.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_carbodylenth.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_carbodywidth.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_carbodyhieght.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_carwheelbase.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_carwheelbase_f.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_carwheelbase_b.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_caremptyweight.Text.Trim()) &&
                    //!string.IsNullOrEmpty(tb_cartotalweight.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_passengers.Text.Trim()) &&
                    //!string.IsNullOrEmpty(tb_cylinderstroke.Text.Trim()) &&
                    //!string.IsNullOrEmpty(tb_compressionratio.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_maxhorsepower.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_maxHorsepowerspeed.Text.Trim()) &&
                    //!string.IsNullOrEmpty(tb_cylindermeters.Text.Trim()) &&
                    //!string.IsNullOrEmpty(tb_torque.Text.Trim()) &&
                    //!string.IsNullOrEmpty(tb_torquespeed.Text.Trim()) &&
                    //!string.IsNullOrEmpty(tb_oilcapacity.Text.Trim()) &&
                    //!string.IsNullOrEmpty(tb_929598.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_exhaustsystem.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_pcv.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_eec.Text.Trim()) &&
                    //!string.IsNullOrEmpty(tb_differentialmodel.Text.Trim()) &&
                    //!string.IsNullOrEmpty(tb_differentialgearratio.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_transmissionmodel.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_highestspeed.Text.Trim()) &&
                    //!string.IsNullOrEmpty(tb_transmissionbackgearratio.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_suspensionsystem_f.Text.Trim()) &&

                     !string.IsNullOrEmpty(tb_suspensionsystem_b.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_tirespec_std_f.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_tirespec_std_b.Text.Trim()) &&
                    //!string.IsNullOrEmpty(tb_tirespec_cho_f.Text.Trim()) &&
                    //!string.IsNullOrEmpty(tb_tirespec_cho_b.Text.Trim()) &&
                    //測試檔位總減速比只有手排車必填
                     !(transmissiontype) &&
                    // !string.IsNullOrEmpty(tb_coolingdrivermethod.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_tiresnum.Text.Trim()) &&
                    //!string.IsNullOrEmpty(tb_exhaustpipenum.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_tirewidth.Text.Trim())
                    // !string.IsNullOrEmpty(tb_inletmode.Text.Trim())
                     ) { success = true; }
                else
                {

                    if (string.IsNullOrEmpty(tb_baseenginename.Text.Trim())) { ErrorString += "基本引擎-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_carchnmodelname.Text.Trim()) &&string.IsNullOrEmpty(tb_carengmodelname.Text.Trim()) ) { ErrorString += "中/英文車型名稱-擇一必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_carengmodelname.Text.Trim())) { ErrorString += "英文車型名稱-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_carmodeladd.Text.Trim())) { ErrorString += "其他(車型名稱補充)-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_carmodelcode.Text.Trim())) { ErrorString += "車型代碼-必填 \\n"; } 

                    if (need_carmodelname) { ErrorString += "英文車型名稱(進口車)、車型代碼(國產車)-擇一必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_producercountry.Text.Trim())) { ErrorString += "製造地區-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_carbodylenth.Text.Trim())) { ErrorString += "車身尺度與重量：全長-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_carbodywidth.Text.Trim())) { ErrorString += "車身尺度與重量：全寬-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_carbodyhieght.Text.Trim())) { ErrorString += "車身尺度與重量：全高-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_carwheelbase.Text.Trim())) { ErrorString += "車身尺度與重量：軸距-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_carwheelbase_f.Text.Trim())) { ErrorString += "車身尺度與重量：輪距-前-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_carwheelbase_b.Text.Trim())) { ErrorString += "車身尺度與重量：輪距-後-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_caremptyweight.Text.Trim())) { ErrorString += "車身尺度與重量：空重-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_cartotalweight.Text.Trim())) { ErrorString += "車身尺度與重量：總重-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_passengers.Text.Trim())) { ErrorString += "車身尺度與重量：乘坐人數-必填 \\n"; }              
                    //if (string.IsNullOrEmpty(tb_cylinderstroke.Text.Trim())) { ErrorString += "基本引擎：缸徑X衝程：mm-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_compressionratio.Text.Trim())) { ErrorString += "基本引擎：壓縮比-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_maxhorsepower.Text.Trim())) { ErrorString += "基本引擎：最大馬力-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_maxHorsepowerspeed.Text.Trim())) { ErrorString += "基本引擎：最大馬力轉速-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_cylindermeters.Text.Trim())) { ErrorString += "基本引擎：缸徑X衝程：X-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_torque.Text.Trim())) { ErrorString += "基本引擎：最大扭力-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_torquespeed.Text.Trim())) { ErrorString += "基本引擎：最大扭力轉速-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_oilcapacity.Text.Trim())) { ErrorString += "基本引擎：油箱容量-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_929598.Text.Trim())) { ErrorString += "基本引擎：油料-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_exhaustsystem.Text.Trim())) { ErrorString += "防治污染系統：排氣系統-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_pcv.Text.Trim())) { ErrorString += "防治污染系統：PCV-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_eec.Text.Trim())) { ErrorString += "防治污染系統：EEC-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_differentialmodel.Text.Trim())) { ErrorString += "傳動系統：差速器-型式-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_differentialgearratio.Text.Trim())) { ErrorString += "傳動系統：差速器-齒比-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_transmissionmodel.Text.Trim())) { ErrorString += "傳動系統：變速系統~型式-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_highestspeed.Text.Trim())) { ErrorString += "傳動系統：最高車速：-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_transmissionbackgearratio.Text.Trim())) { ErrorString += "傳動系統：變速系統~倒檔齒比-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_suspensionsystem_f.Text.Trim())) { ErrorString += "傳動系統：懸吊系統：前-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_suspensionsystem_b.Text.Trim())) { ErrorString += "傳動系統：懸吊系統：後-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_tirespec_std_f.Text.Trim())) { ErrorString += "傳動系統：輪胎規格：標準~前-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_tirespec_std_b.Text.Trim())) { ErrorString += "傳動系統：輪胎規格：標準~後-必填-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_tirespec_cho_f.Text.Trim())) { ErrorString += "傳動系統：輪胎規格：選佩~前-必填-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_tirespec_cho_b.Text.Trim())) { ErrorString += "傳動系統：輪胎規格：選佩~後-必填-必填-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_testgearreductionratio.Text.Trim())) { ErrorString += "傳動系統：★測試檔位總減速比-必填 \\n"; }
                    if (transmissiontype) { ErrorString += "傳動系統：★測試檔位總減速比-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_coolingdrivermethod.Text.Trim())) { ErrorString += "傳動系統：冷卻風扇驅動方式-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_tiresnum.Text.Trim())) { ErrorString += "傳動系統：輪胎數量（不含備胎-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_exhaustpipenum.Text.Trim())) { ErrorString += "傳動系統：排氣管開口數量-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_tirewidth.Text.Trim())) { ErrorString += "傳動系統：輪胎寬度-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_inletmode.Text.Trim())) { ErrorString += "傳動系統：進氣方式-必填 \\n"; }

                    if (string.IsNullOrEmpty(ErrorString.Trim()))
                        success = true;
                    else
                        JS.AlertMessage(ErrorString);
                }
            }
            else if (Request_mode() == "m")
            {
                if (!string.IsNullOrEmpty(tb_baseenginename.Text.Trim()) &&
                    !string.IsNullOrEmpty(tb_producercountry.Text.Trim()) &&
                    //!(string.IsNullOrEmpty(tb_carchnmodelname.Text.Trim()) &&  string.IsNullOrEmpty(tb_carengmodelname.Text.Trim())) &&
                    //!string.IsNullOrEmpty(tb_carmodeladd.Text.Trim()) &&

                    !(need_carmodelname) &&
                     !string.IsNullOrEmpty(tb_carmodelcode.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_carbodylenth.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_carbodywidth.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_carbodyhieght.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_carwheelbase.Text.Trim()) &&
                    //!string.IsNullOrEmpty(tb_carwheelbase_f.Text.Trim()) &&
                    //!string.IsNullOrEmpty(tb_carwheelbase_b.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_caremptyweight.Text.Trim()) &&
                    //!string.IsNullOrEmpty(tb_cartotalweight.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_passengers.Text.Trim()) &&
                    //!string.IsNullOrEmpty(tb_cylinderstroke.Text.Trim()) &&
                    //!string.IsNullOrEmpty(tb_compressionratio.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_maxhorsepower.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_maxHorsepowerspeed.Text.Trim()) &&
                    // !string.IsNullOrEmpty(tb_cylindermeters.Text.Trim()) &&
                    // !string.IsNullOrEmpty(tb_torque.Text.Trim()) &&
                    // !string.IsNullOrEmpty(tb_torquespeed.Text.Trim()) &&
                    //!string.IsNullOrEmpty(tb_oilcapacity.Text.Trim()) &&
                    //!string.IsNullOrEmpty(tb_929598.Text.Trim()) &&
                    // !string.IsNullOrEmpty(tb_exhaustsystem.Text.Trim()) &&
                    //  !string.IsNullOrEmpty(tb_pcv.Text.Trim()) &&
                    //  !string.IsNullOrEmpty(tb_eec.Text.Trim()) &&
                    // !string.IsNullOrEmpty(tb_differentialmodel.Text.Trim()) &&
                    // !string.IsNullOrEmpty(tb_differentialgearratio.Text.Trim()) &&
                    !string.IsNullOrEmpty(tb_transmissionmodel.Text.Trim()) &&
                    !string.IsNullOrEmpty(tb_highestspeed.Text.Trim()) &&
                    //   !string.IsNullOrEmpty(tb_transmissionbackgearratio.Text.Trim()) &&
                    !string.IsNullOrEmpty(tb_suspensionsystem_f.Text.Trim()) &&

                    !string.IsNullOrEmpty(tb_suspensionsystem_b.Text.Trim()) &&
                    !string.IsNullOrEmpty(tb_tirespec_std_f.Text.Trim()) &&
                    !string.IsNullOrEmpty(tb_tirespec_std_b.Text.Trim()) &&
                    //!string.IsNullOrEmpty(tb_tirespec_cho_f.Text.Trim()) &&
                    //!string.IsNullOrEmpty(tb_tirespec_cho_b.Text.Trim()) &&
                    //!string.IsNullOrEmpty(tb_testgearreductionratio.Text.Trim()) &&
                    //測試檔位總減速比只有手排車必填
                    !(transmissiontype) &&
                    //!string.IsNullOrEmpty(tb_coolingdrivermethod.Text.Trim()) &&
                    !string.IsNullOrEmpty(tb_tiresnum.Text.Trim()) &&

                     //   !string.IsNullOrEmpty(tb_exhaustpipenum.Text.Trim()) &&
                        !string.IsNullOrEmpty(tb_tirewidth.Text.Trim()) &&
                    //!string.IsNullOrEmpty(tb_inletmode.Text.Trim()) &&


                       // !string.IsNullOrEmpty(Load_weight_TextBox1.Text.Trim()) &&
                        !string.IsNullOrEmpty(tb_pm.Text.Trim()) &&
                        !string.IsNullOrEmpty(tb_co.Text.Trim()) &&
                        !string.IsNullOrEmpty(tb_hc.Text) &&
                        !string.IsNullOrEmpty(Primary_reduction_ratio_TextBox3.Text.Trim()) &&
                        !string.IsNullOrEmpty(Secondary_reduction_ratio_TextBox4.Text.Trim()) &&
                        !string.IsNullOrEmpty(The_location_and_direction_of_the_exhaust_port_TextBox1.Text.Trim()) &&
                        !string.IsNullOrEmpty(Start_up_mode_TextBox2.Text.Trim())
                     ) { success = true; }
                else/**/
                {

                    if (string.IsNullOrEmpty(tb_baseenginename.Text.Trim())) { ErrorString += "基本引擎-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_producercountry.Text.Trim())) { ErrorString += "製造地區-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_carchnmodelname.Text.Trim()) && string.IsNullOrEmpty(tb_carengmodelname.Text.Trim())) { ErrorString += "中/英文車型名稱-擇一必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_carengmodelname.Text.Trim())) { ErrorString += "英文車型名稱-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_carmodeladd.Text.Trim())) { ErrorString += "其他(車型名稱補充)-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_carmodelcode.Text.Trim())) { ErrorString += "車型代碼-必填 \\n"; }
                    if (need_carmodelname) { ErrorString += "英文車型名稱(進口車)、車型代碼(國產車)-擇一必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_carbodylenth.Text.Trim())) { ErrorString += "車身尺度與重量：全長-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_carbodywidth.Text.Trim())) { ErrorString += "車身尺度與重量：全寬-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_carbodyhieght.Text.Trim())) { ErrorString += "車身尺度與重量：全高-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_carwheelbase.Text.Trim())) { ErrorString += "車身尺度與重量：軸距-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_carwheelbase_f.Text.Trim())) { ErrorString += "車身尺度與重量：輪距-前-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_carwheelbase_b.Text.Trim())) { ErrorString += "車身尺度與重量：輪距-後-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_caremptyweight.Text.Trim())) { ErrorString += "車身尺度與重量：空重-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_cartotalweight.Text.Trim())) { ErrorString += "車身尺度與重量：總重-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_passengers.Text.Trim())) { ErrorString += "車身尺度與重量：乘坐人數-必填 \\n"; }

                    //if (string.IsNullOrEmpty(tb_cylinderstroke.Text.Trim())) { ErrorString += "基本引擎：缸徑X衝程：mm-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_compressionratio.Text.Trim())) { ErrorString += "基本引擎：壓縮比-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_maxhorsepower.Text.Trim())) { ErrorString += "基本引擎：最大馬力-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_maxHorsepowerspeed.Text.Trim())) { ErrorString += "基本引擎：最大馬力轉速-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_cylindermeters.Text.Trim())) { ErrorString += "基本引擎：缸徑X衝程：X-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_torque.Text.Trim())) { ErrorString += "基本引擎：最大扭力-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_torquespeed.Text.Trim())) { ErrorString += "基本引擎：最大扭力轉速-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_oilcapacity.Text.Trim())) { ErrorString += "基本引擎：油箱容量-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_929598.Text.Trim())) { ErrorString += "基本引擎：油料-必填 \\n"; }
                    // if (string.IsNullOrEmpty(tb_exhaustsystem.Text.Trim())) { ErrorString += "防治污染系統：排氣系統-必填 \\n"; }
                    // if (string.IsNullOrEmpty(tb_pcv.Text.Trim())) { ErrorString += "防治污染系統：PCV-必填 \\n"; }
                    // if (string.IsNullOrEmpty(tb_eec.Text.Trim())) { ErrorString += "防治污染系統：EEC-必填 \\n"; }
                    // if (string.IsNullOrEmpty(tb_differentialmodel.Text.Trim())) { ErrorString += "傳動系統：差速器-型式-必填 \\n"; }
                    // if (string.IsNullOrEmpty(tb_differentialgearratio.Text.Trim())) { ErrorString += "傳動系統：差速器-齒比-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_transmissionmodel.Text.Trim())) { ErrorString += "傳動系統：變速系統~型式-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_highestspeed.Text.Trim())) { ErrorString += "傳動系統：最高車速：-必填 \\n"; }
                    // if (string.IsNullOrEmpty(tb_transmissionbackgearratio.Text.Trim())) { ErrorString += "傳動系統：變速系統~倒檔齒比-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_suspensionsystem_f.Text.Trim())) { ErrorString += "傳動系統：懸吊系統：前-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_suspensionsystem_b.Text.Trim())) { ErrorString += "傳動系統：懸吊系統：後-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_tirespec_std_f.Text.Trim())) { ErrorString += "傳動系統：輪胎規格：標準~前-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_tirespec_std_b.Text.Trim())) { ErrorString += "傳動系統：輪胎規格：標準~後-必填-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_tirespec_cho_f.Text.Trim())) { ErrorString += "傳動系統：輪胎規格：選擇~前-必填-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_tirespec_cho_b.Text.Trim())) { ErrorString += "傳動系統：輪胎規格：選擇~後-必填-必填-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_testgearreductionratio.Text.Trim())) { ErrorString += "傳動系統：★測試檔位總減速比-必填 \\n"; }
                    if (transmissiontype) { ErrorString += "傳動系統：★測試檔位總減速比-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_coolingdrivermethod.Text.Trim())) { ErrorString += "傳動系統：冷卻風扇驅動方式-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_tiresnum.Text.Trim())) { ErrorString += "傳動系統：輪胎數量（不含備胎-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_exhaustpipenum.Text.Trim())) { ErrorString += "傳動系統：排氣管開口數量-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_tirewidth.Text.Trim())) { ErrorString += "傳動系統：輪胎寬度-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_inletmode.Text.Trim())) { ErrorString += "傳動系統：進氣方式-必填 \\n"; }


                    if (string.IsNullOrEmpty(tb_pm.Text.Trim())) { ErrorString += "排放廢氣濃度：粒狀汙染物-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_co.Text.Trim())) { ErrorString += "排放廢氣濃度：一氧化碳-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_hc.Text.Trim())) { ErrorString += "排放廢氣濃度：碳氧化合物-必填 \\n"; }
                    if (string.IsNullOrEmpty(Primary_reduction_ratio_TextBox3.Text.Trim())) { ErrorString += "傳動系統：一次減速比-必填 \\n"; }
                    if (string.IsNullOrEmpty(Secondary_reduction_ratio_TextBox4.Text.Trim())) { ErrorString += "傳動系統：二次減速比-必填 \\n"; }
                    if (string.IsNullOrEmpty(The_location_and_direction_of_the_exhaust_port_TextBox1.Text.Trim())) { ErrorString += "傳動系統：排氣口位置及方向-必填 \\n"; }
                    if (string.IsNullOrEmpty(Start_up_mode_TextBox2.Text)) { ErrorString += "基本引擎：啟動方式-必填 \\n"; }
                    //!string.IsNullOrEmpty(The_location_and_direction_of_the_exhaust_port_TextBox1.Text.Trim())

                    if (string.IsNullOrEmpty(ErrorString.Trim()))
                        success = true;
                    else
                        JS.AlertMessage(ErrorString);
                }
            }
            else //D
            {
                if (!string.IsNullOrEmpty(tb_baseenginename.Text.Trim()) &&
                    !string.IsNullOrEmpty(tb_producercountry.Text.Trim()) &&
                    //!(string.IsNullOrEmpty(tb_carchnmodelname.Text.Trim()) && string.IsNullOrEmpty(tb_carengmodelname.Text.Trim())) &&
                    //!string.IsNullOrEmpty(tb_carmodeladd.Text.Trim()) &&

                    !(need_carmodelname) &&
                     !string.IsNullOrEmpty(tb_carmodelcode.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_carbodylenth.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_carbodywidth.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_carbodyhieght.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_carwheelbase.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_carwheelbase_f.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_carwheelbase_b.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_caremptyweight.Text.Trim()) &&
                    //!string.IsNullOrEmpty(tb_cartotalweight.Text.Trim()) &&
                    // !string.IsNullOrEmpty(tb_passengers.Text.Trim()) &&
                    //!string.IsNullOrEmpty(tb_cylinderstroke.Text.Trim()) &&
                    //!string.IsNullOrEmpty(tb_compressionratio.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_maxhorsepower.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_maxHorsepowerspeed.Text.Trim()) &&
                    //!string.IsNullOrEmpty(tb_cylindermeters.Text.Trim()) &&
                    //!string.IsNullOrEmpty(tb_torque.Text.Trim()) &&
                    //!string.IsNullOrEmpty(tb_torquespeed.Text.Trim()) &&
                    //!string.IsNullOrEmpty(tb_oilcapacity.Text.Trim()) &&
                    //!string.IsNullOrEmpty(tb_929598.Text.Trim()) &&
                    //!string.IsNullOrEmpty(tb_exhaustsystem.Text.Trim()) &&
                    //!string.IsNullOrEmpty(tb_pcv.Text.Trim()) &&
                    //!string.IsNullOrEmpty(tb_eec.Text.Trim()) &&
                    //!string.IsNullOrEmpty(tb_differentialmodel.Text.Trim()) &&
                    //!string.IsNullOrEmpty(tb_differentialgearratio.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_transmissionmodel.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_highestspeed.Text.Trim()) &&
                    //!string.IsNullOrEmpty(tb_transmissionbackgearratio.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_suspensionsystem_f.Text.Trim()) &&

                     !string.IsNullOrEmpty(tb_suspensionsystem_b.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_tirespec_std_f.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_tirespec_std_b.Text.Trim()) &&
                    //!string.IsNullOrEmpty(tb_tirespec_cho_f.Text.Trim()) &&
                    //!string.IsNullOrEmpty(tb_tirespec_cho_b.Text.Trim()) &&
                    //測試檔位總減速比只有手排車必填
                     !(transmissiontype) &&
                    // !string.IsNullOrEmpty(tb_coolingdrivermethod.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_tiresnum.Text.Trim()) &&
                    //!string.IsNullOrEmpty(tb_exhaustpipenum.Text.Trim()) &&
                     !string.IsNullOrEmpty(tb_tirewidth.Text.Trim())
                    // !string.IsNullOrEmpty(tb_inletmode.Text.Trim())
                     ) { success = true; }
                else
                {

                    if (string.IsNullOrEmpty(tb_baseenginename.Text.Trim())) { ErrorString += "基本引擎-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_producercountry.Text.Trim())) { ErrorString += "製造地區-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_carchnmodelname.Text.Trim()) &&string.IsNullOrEmpty(tb_carengmodelname.Text.Trim()) ) { ErrorString += "中/英文車型名稱-擇一必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_carengmodelname.Text.Trim())) { ErrorString += "英文車型名稱-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_carmodeladd.Text.Trim())) { ErrorString += "其他(車型名稱補充)-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_carmodelcode.Text.Trim())) { ErrorString += "車型代碼-必填 \\n"; }
                    if (need_carmodelname) { ErrorString += "英文車型名稱(進口車)、車型代碼(國產車)-擇一必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_carbodylenth.Text.Trim())) { ErrorString += "車身尺度與重量：全長-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_carbodywidth.Text.Trim())) { ErrorString += "車身尺度與重量：全寬-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_carbodyhieght.Text.Trim())) { ErrorString += "車身尺度與重量：全高-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_carwheelbase.Text.Trim())) { ErrorString += "車身尺度與重量：軸距-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_carwheelbase_f.Text.Trim())) { ErrorString += "車身尺度與重量：輪距-前-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_carwheelbase_b.Text.Trim())) { ErrorString += "車身尺度與重量：輪距-後-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_caremptyweight.Text.Trim())) { ErrorString += "車身尺度與重量：空重-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_cartotalweight.Text.Trim())) { ErrorString += "車身尺度與重量：總重-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_passengers.Text.Trim())) { ErrorString += "車身尺度與重量：乘坐人數-必填 \\n"; }
                    if (string.IsNullOrEmpty(txt_carbodyweight.Text.Trim())) { ErrorString += "車身尺度與重量：車體總重量-必填 \\n"; }

                    //if (string.IsNullOrEmpty(tb_cylinderstroke.Text.Trim())) { ErrorString += "基本引擎：缸徑X衝程：mm-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_compressionratio.Text.Trim())) { ErrorString += "基本引擎：壓縮比-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_maxhorsepower.Text.Trim())) { ErrorString += "基本引擎：最大馬力-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_maxHorsepowerspeed.Text.Trim())) { ErrorString += "基本引擎：最大馬力轉速-必填 \\n"; }

                    if (string.IsNullOrEmpty(txt_maxhorsepower_hb.Text.Trim())) { ErrorString += "基本引擎：馬達最大馬力-必填 \\n"; }
                    if (string.IsNullOrEmpty(txt_maxhorsepowerspeed_hb.Text.Trim())) { ErrorString += "基本引擎：馬達最大馬力轉速-必填 \\n"; }
                    if (string.IsNullOrEmpty(txt_torque_hb.Text.Trim())) { ErrorString += "基本引擎：馬達最大扭力-必填 \\n"; }
                    if (string.IsNullOrEmpty(txt_torquespeed_hb.Text.Trim())) { ErrorString += "基本引擎：馬達最大扭力轉速-必填 \\n"; }

                    //if (string.IsNullOrEmpty(tb_cylindermeters.Text.Trim())) { ErrorString += "基本引擎：缸徑X衝程：X-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_torque.Text.Trim())) { ErrorString += "基本引擎：最大扭力-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_torquespeed.Text.Trim())) { ErrorString += "基本引擎：最大扭力轉速-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_oilcapacity.Text.Trim())) { ErrorString += "基本引擎：油箱容量-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_929598.Text.Trim())) { ErrorString += "基本引擎：油料-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_exhaustsystem.Text.Trim())) { ErrorString += "防治污染系統：排氣系統-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_pcv.Text.Trim())) { ErrorString += "防治污染系統：PCV-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_eec.Text.Trim())) { ErrorString += "防治污染系統：EEC-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_differentialmodel.Text.Trim())) { ErrorString += "傳動系統：差速器-型式-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_differentialgearratio.Text.Trim())) { ErrorString += "傳動系統：差速器-齒比-必填 \\n"; }

                    //if (string.IsNullOrEmpty(txt_increasepowerstyle.Text.Trim())) { ErrorString += "傳動系統：加力箱-型式-必填 \\n"; }
                    //if (string.IsNullOrEmpty(txt_increasepowerratio.Text.Trim())) { ErrorString += "傳動系統：加力箱-齒比-必填 \\n"; }

                    if (string.IsNullOrEmpty(tb_transmissionmodel.Text.Trim())) { ErrorString += "傳動系統：變速系統~型式-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_highestspeed.Text.Trim())) { ErrorString += "傳動系統：最高車速：-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_transmissionbackgearratio.Text.Trim())) { ErrorString += "傳動系統：變速系統~倒檔齒比-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_suspensionsystem_f.Text.Trim())) { ErrorString += "傳動系統：懸吊系統：前-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_suspensionsystem_b.Text.Trim())) { ErrorString += "傳動系統：懸吊系統：後-必填 \\n"; }
                    if (string.IsNullOrEmpty(txt_suspensionsystem_sub.Text.Trim())) { ErrorString += "傳動系統：懸吊系統：補-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_tirespec_std_f.Text.Trim())) { ErrorString += "傳動系統：輪胎規格：標準~前-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_tirespec_std_b.Text.Trim())) { ErrorString += "傳動系統：輪胎規格：標準~後-必填-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_tirespec_cho_f.Text.Trim())) { ErrorString += "傳動系統：輪胎規格：選佩~前-必填-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_tirespec_cho_b.Text.Trim())) { ErrorString += "傳動系統：輪胎規格：選佩~後-必填-必填-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_testgearreductionratio.Text.Trim())) { ErrorString += "傳動系統：★測試檔位總減速比-必填 \\n"; }
                    if (transmissiontype) { ErrorString += "傳動系統：★測試檔位總減速比-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_coolingdrivermethod.Text.Trim())) { ErrorString += "傳動系統：冷卻風扇驅動方式-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_tiresnum.Text.Trim())) { ErrorString += "傳動系統：輪胎數量(不含備胎)-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_exhaustpipenum.Text.Trim())) { ErrorString += "傳動系統：排氣管開口數量-必填 \\n"; }
                    if (string.IsNullOrEmpty(tb_tirewidth.Text.Trim())) { ErrorString += "傳動系統：輪胎寬度-必填 \\n"; }
                    //if (string.IsNullOrEmpty(tb_inletmode.Text.Trim())) { ErrorString += "傳動系統：進氣方式-必填 \\n"; }

                    if (string.IsNullOrEmpty(ErrorString.Trim()))
                        success = true;
                    else
                        JS.AlertMessage(ErrorString);
                }
            }
        }

        return success;
    }




    private void Create_GearRatio(System.Web.UI.HtmlControls.HtmlGenericControl div, int count, DataTable dt_gearratio)
    {
        int i;

        div.InnerHtml = "";
        if (lbl_transmissiontype.Text != "CVT")
        {
            for (i = 1; i <= count; i++)
            {
                Label LabelVirtual = new Label();
                LabelVirtual.ID = "Variable_speed_system_Label" + i;
                LabelVirtual.Text = i + "檔：";

                TextBox TextBoxVirtual = new TextBox();
                TextBoxVirtual.ID = Variable_speed_system_TextBox + i;
                TextBoxVirtual.MaxLength = limtLength;
                if (dt_gearratio.Rows.Count > 0 && dt_gearratio.Rows.Count >= i)
                {
                    TextBoxVirtual.Text = dt_gearratio.Rows[i - 1]["gr_gearratio"].ToString();
                }
                else
                {
                    TextBoxVirtual.Text = "";
                }

                System.Web.UI.HtmlControls.HtmlGenericControl br = new System.Web.UI.HtmlControls.HtmlGenericControl();

                br.TagName = "div";
                br.Controls.Add(LabelVirtual);
                br.Controls.Add(TextBoxVirtual);
                div.Controls.Add(br);
            }
        }
        else
        {
            for (i = 1; i <= 2; i++)
            {
                Label LabelVirtual = new Label();
                LabelVirtual.ID = "Variable_speed_system_Label" + i;
                if (i == 1)
                {
                    LabelVirtual.Text = "UD檔：";
                }
                else
                {
                    LabelVirtual.Text = "OD檔：";
                }

                TextBox TextBoxVirtual = new TextBox();
                TextBoxVirtual.ID = Variable_speed_system_TextBox + i;
                TextBoxVirtual.MaxLength = limtLength;
                if (dt_gearratio.Rows.Count > 0 && dt_gearratio.Rows.Count >= i)
                {
                    TextBoxVirtual.Text = dt_gearratio.Rows[i - 1]["gr_gearratio"].ToString();
                }
                else
                {
                    TextBoxVirtual.Text = "";
                }

                System.Web.UI.HtmlControls.HtmlGenericControl br = new System.Web.UI.HtmlControls.HtmlGenericControl();

                br.TagName = "div";
                br.Controls.Add(LabelVirtual);
                br.Controls.Add(TextBoxVirtual);
                div.Controls.Add(br);
            }
        }
        int n = Variable_speed_system.Controls.Count;




    }
    protected void ddl_transmissionnum_SelectedIndexChanged(object sender, EventArgs e)
    {
        transmissionnumSelectChange();
    }

    private bool SelectChange_Verifity()
    {

        bool success = false;
        int n = Variable_speed_system.Controls.Count;
        int i = 0;
        string error = string.Empty;

        for (i = 0; i < n; i++)
        {

            TextBox VirTextBox = (TextBox)Variable_speed_system.Controls[i].FindControl("Variable_speed_system_TextBox" + i);
            if (VirTextBox != null)
            {
                if (string.IsNullOrEmpty(VirTextBox.Text.Trim()))
                {
                    if (lbl_transmissiontype.Text != "CVT")
                    {
                        error += "排檔數" + i + "未填寫 \\n";
                    }
                    else
                    {
                        if (i == 1)
                        {
                            error += "OD檔未填寫 \\n";
                        }
                        else
                        {
                            error += "UD檔未填寫 \\n";
                        }
                    }
                }
                else
                {
                    if (VirTextBox.Text.Trim().Length > limtLength)
                    {
                        error += "排檔數" + i + "字數請小於" + limtLength + "，目前字數" + VirTextBox.Text.Trim().Length + " \\n";
                    }
                }
            }

        }

        if (string.IsNullOrEmpty(error))
        {
            success = true;
        }
        else
        {
            JS.AlertMessage(error);
        }
        return success;
    }
    private void transmissionnumSelectChange()
    {
        if (lbl_transmissiontype.Text != "CVT" && ddl_transmissionnum.Items[ddl_transmissionnum.SelectedIndex].Value == "" && lbl_carmodelno.Text != "")
        {
            MessageBox.Show("排檔型式不為CVT，排檔數不可挑選為空白");
            return;
        }

        string[] arr_carmodel = crypy.wsCarCompoModelValidate(crypy.getRadNum());
        int itransmissionnum = 0;
        if (ddl_transmissionnum.Items[ddl_transmissionnum.SelectedIndex].Value != "")
        {
            itransmissionnum = int.Parse(ddl_transmissionnum.Items[ddl_transmissionnum.SelectedIndex].Value);
        }
        else
        {
            if (lbl_transmissiontype.Text == "CVT")
            {
                ddl_transmissionnum.Enabled = false;
                itransmissionnum = 2;
            }
        }
        DataTable dtCar_GearRatioData = wsCarCmpModel.Get_single_carmodel_GearRatio(lbl_applyno.Text, lbl_carcompomodelno.Text, lbl_copycarmodelno.Text.Equals("") ? lbl_carmodelno.Text : lbl_copycarmodelno.Text, arr_carmodel[0].ToString(), arr_carmodel[1].ToString());
        Create_GearRatio(Variable_speed_system, itransmissionnum, dtCar_GearRatioData);
        dtCar_GearRatioData.Dispose();
        if (ddl_carbodyshape.SelectedValue.Equals("99"))
        {
            tb_carbodyshapedesc.Style.Add("display", "inline");//車身式樣-其他填寫欄位

        }
    }

    private void ClearDdl()
    {
        ddl_carbodyshape.ClearSelection();
        ddl_cartype.ClearSelection();
        ddl_brand.ClearSelection();
        ddl_D_highlowgear.ClearSelection();
        ddl_transmissionnum.ClearSelection();
        ddl_cardoorcount.ClearSelection();
        ddl_haveturbo.ClearSelection();
    }

    private string Request_mode()
    {
        string value = string.Empty;

        if (Request.QueryString["mode"] != null)
        {
            if (!string.IsNullOrEmpty(Request.QueryString["mode"].ToString()))
            {
                try
                {
                    value = crypy.DeCode(Request.QueryString["mode"].ToString());
                }
                catch { }
            }
        }

        return value.Trim().ToLower();
    }
    protected void btn_copycarcompomodel_data_Click(object sender, EventArgs e)
    {

        string[] arr_carmodel = crypy.wsCarCompoModelValidate(crypy.getRadNum());
        string carcompomodelno = crypy.DeCode(Request["carcompomodelno"].ToString());

        DataTable dtCarData = wsCarCmpModel.Get_single_carmodel_data(lbl_applyno.Text, lbl_carcompomodelno.Text, ddl_carcompolist.SelectedValue, arr_carmodel[0].ToString(), arr_carmodel[1].ToString());
        lbl_copycarmodelno.Text = ddl_carcompolist.SelectedValue;
        if (dtCarData.Rows.Count > 0)
        {
            //取出該筆資料並顯示在畫面
            BindDBData(dtCarData);
            cb_berepresentativevehicle.Checked = false;
        }
    }

    protected void btn_airpolcar_Click(object sender, EventArgs e)
    {
        string strApplyno = crypy.DeCode(Request["applyno"].ToString());
        DataView dv = new MailParaControl().GetApplyBaseData(strApplyno);
        int success = 0;
        string smode = "2"; //機車
        switch (Request_mode().ToUpper())
        {
            case "G":
                smode = "1"; //汽車
                break;
            default:
                smode = "2"; //機車
                break;
        }
        string EngineFamily = dv[0]["EngineFamily"].ToString();

        connectCarCert.CarCert CarCert = new connectCarCert.CarCert();
        //connectARTC_D.ARTC ARTC_D = new connectARTC_D.ARTC();
        string[] radom_CarCert = crypy.wsCarCertValid(Convert.ToInt16(DateTime.Now.DayOfWeek));
        DataSet ds_CarCert_data = new DataSet();

        if (!(Request_mode().Equals("D")))
        {



            switch (Request_mode().ToUpper())
            {
                case "G":
                case "M":
                    ds_CarCert_data = CarCert.CheckApplyBasicData(crypy.EnCode(smode), Session["compidno"].ToString(), crypy.EnCode(lbl_carstyleyear.Text), crypy.EnCode(EngineFamily), radom_CarCert[0].ToString(), radom_CarCert[1].ToString());//汙染車資料
                    break;
                //case "D":
                //    ds_CarCert_data = ARTC_D.CheckconsReceiveCaseInfo(Session["compidno"].ToString(), crypy.EnCode(lbl_carstyleyear.Text), EnCode(EngineFamily), radom_CarCert[0].ToString(), radom_CarCert[1].ToString());//汙染車資料
                //    break;
            }
            DataRow[] dt1;
            if (ds_CarCert_data.Tables[0].Rows.Count > 0)
            {
                for (int i = 0; i < ds_CarCert_data.Tables[0].Rows.Count; i++)
                {

                    switch (Request_mode().ToUpper())
                    {
                        case "G":
                        case "M":
                            dt1 = ds_CarCert_data.Tables[0].Select("Brand='" + ddl_brand.SelectedItem.Text.Trim() + "' and CCarModel='" + tb_carchnmodelname.Text.Trim() + "' and ECarModel= '" + tb_carengmodelname.Text.Trim() + "' and CarModelType= '" + tb_carmodelcode.Text.Trim() + "' and Exhaust= '" + lbl_exhaust.Text.Trim() + "' and TransmissionType= '" + lbl_transmissiontype.Text.Trim() + "' and TransmissionNum= '" + ddl_transmissionnum.SelectedValue.Trim() + "' and CarDoorCount='" + ddl_cardoorcount.SelectedItem.Text.Trim() + "' and Others='" + tb_carmodeladd.Text.Trim() + "'");//汙染車資料
                            foreach (DataRow dr1 in dt1)
                            {
                                BindDSData(ds_CarCert_data);
                                success = success + 1;
                            }
                            break;
                        case "D": //柴油車的欄位名稱 與G、M不同 故不能通用
                            dt1 = ds_CarCert_data.Tables[0].Select("Brand='" + ddl_brand.SelectedItem.Text.Trim() + "' and CarStyleName_CName='" + tb_carchnmodelname.Text.Trim() + "' and CarStyleName_E= '" + tb_carengmodelname.Text.Trim() + "' and CarStyleID= '" + tb_carmodelcode.Text.Trim() + "' and CC= '" + lbl_exhaust.Text.Trim() + "' and Gear= '" + lbl_transmissiontype.Text.Trim() + "' and GearCount= '" + ddl_transmissionnum.SelectedValue.Trim() + "' and DoorCount='" + ddl_cardoorcount.SelectedItem.Text.Trim() + "' and Other='" + tb_carmodeladd.Text.Trim() + "'");//汙染車資料
                            foreach (DataRow dr1 in dt1)
                            {
                                BindDSData(ds_CarCert_data);
                                success = success + 1;
                            }
                            break;
                    }
                }
            }
            if (success == 0)
            {
                MessageBox.Show("目前車型名稱，從污染系統找不到相對應的資料！");
            }
        }
    }

    private void BindDSData(DataSet dsCarCertdata)
    {
        switch (Request_mode().ToUpper())
        {
            case "G":
            case "M":
                tb_baseenginename.Text = dsCarCertdata.Tables[0].Rows[0]["BasicEngine"].ToString(); //基本引擎
                tb_cylindermeters.Text = dsCarCertdata.Tables[0].Rows[0]["Bore"].ToString(); //缸徑
                tb_cylinderstroke.Text = dsCarCertdata.Tables[0].Rows[0]["Stroke"].ToString(); //衝程
                tb_compressionratio.Text = dsCarCertdata.Tables[0].Rows[0]["Compression"].ToString(); //壓縮比
                tb_maxhorsepower.Text = dsCarCertdata.Tables[0].Rows[0]["EngineKw"].ToString(); //最大馬力
                tb_maxHorsepowerspeed.Text = dsCarCertdata.Tables[0].Rows[0]["EngineRpm"].ToString();//最大馬力轉速

                //資料比對cmdg_transmissionmodel-->Transmission(變速系統)
                tb_differentialgearratio.Text = dsCarCertdata.Tables[0].Rows[0]["FinalDriveRatio"].ToString();//差速器-齒比   
                tb_transmissionmodel.Text = dsCarCertdata.Tables[0].Rows[0]["Transmission"].ToString();//變速系統~型式


                //機車的欄位 ,"cmdg_loadingweight","cmdg_firemethod","cmdg_strokeposdirection","cmdg_PM","cmdg_CO","cmdg_HC","cmdg_1stReductionratio","cmdg_2ndReductionratio"

                Primary_reduction_ratio_TextBox3.Text = dsCarCertdata.Tables[0].Rows[0]["FirstRetardRatio"].ToString();  //一次減速比
                Secondary_reduction_ratio_TextBox4.Text = dsCarCertdata.Tables[0].Rows[0]["SecondRetardRatio"].ToString();//二次減速比

                if (!(dsCarCertdata.Tables[0].Rows[0]["DrivenWheels"].ToString().ToUpper().Trim().Equals("NULL") || dsCarCertdata.Tables[0].Rows[0]["DrivenWheels"].ToString().Trim().Equals("")))
                {
                    rbl_drivetype.SelectedValue = dsCarCertdata.Tables[0].Rows[0]["DrivenWheels"].ToString(); //驅動方式
                }

                break;

            case "D":
                tb_baseenginename.Text = dsCarCertdata.Tables[0].Rows[0]["BasicEngineNo"].ToString(); //基本引擎
                tb_cylindermeters.Text = dsCarCertdata.Tables[0].Rows[0]["Bore"].ToString(); //缸徑
                tb_cylinderstroke.Text = dsCarCertdata.Tables[0].Rows[0]["Stroke"].ToString(); //衝程
                tb_compressionratio.Text = dsCarCertdata.Tables[0].Rows[0]["CompressionRatio_Nominal"].ToString(); //壓縮比
                txt_maxhorsepower_hb.Text = dsCarCertdata.Tables[0].Rows[0]["MaxKW"].ToString(); //最大馬力
                txt_maxhorsepowerspeed_hb.Text = dsCarCertdata.Tables[0].Rows[0]["Maxrpm"].ToString(); //最大馬力轉速
                txt_torque_hb.Text = dsCarCertdata.Tables[0].Rows[0]["MaxLB"].ToString(); //馬達最大扭力
                txt_torquespeed_hb.Text = dsCarCertdata.Tables[0].Rows[0]["MaxLBRPM"].ToString(); //馬達最大扭力轉速
                //資料比對cmdg_transmissionmodel-->Transmission(變速系統)
                tb_differentialgearratio.Text = dsCarCertdata.Tables[0].Rows[0]["FDR"].ToString();//差速器-齒比   
                tb_transmissionmodel.Text = dsCarCertdata.Tables[0].Rows[0]["TSD"].ToString();//變速系統~型式              

                if (!(dsCarCertdata.Tables[0].Rows[0]["DW"].ToString().ToUpper().Trim().Equals("NULL") || dsCarCertdata.Tables[0].Rows[0]["DW"].ToString().Trim().Equals("")))
                {
                    rbl_drivetype.SelectedValue = dsCarCertdata.Tables[0].Rows[0]["DW"].ToString(); //驅動方式
                }

                break;
        }

        GetAgentAccount();

        CarCert_transmissionnumSelectChange(dsCarCertdata);
    }

    private void CarCert_transmissionnumSelectChange(DataSet dsCarCertdata)
    {
        int itransmissionnum = 0;
        if (ddl_transmissionnum.Items[ddl_transmissionnum.SelectedIndex].Value != "")
        {
            itransmissionnum = int.Parse(ddl_transmissionnum.Items[ddl_transmissionnum.SelectedIndex].Value);
        }
        else
        {
            if (lbl_transmissiontype.Text == "CVT")
            {
                ddl_transmissionnum.Enabled = false;
                itransmissionnum = 2;
            }
        }
        CarCert_Create_GearRatio(Variable_speed_system, itransmissionnum, dsCarCertdata);
    }

    private void CarCert_Create_GearRatio(System.Web.UI.HtmlControls.HtmlGenericControl div, int count, DataSet dsCarCertdata)
    {
        int i;

        div.InnerHtml = "";
        if (lbl_transmissiontype.Text != "CVT")
        {
            for (i = 1; i <= count; i++)
            {
                Label LabelVirtual = new Label();
                LabelVirtual.ID = "Variable_speed_system_Label" + i;
                LabelVirtual.Text = i + "檔：";

                TextBox TextBoxVirtual = new TextBox();
                TextBoxVirtual.ID = Variable_speed_system_TextBox + i;
                TextBoxVirtual.MaxLength = limtLength;
                if (count > 0 && count >= i)
                {
                    switch (Request_mode().ToUpper())
                    {
                        case "G":
                        case "M":
                            TextBoxVirtual.Text = dsCarCertdata.Tables[0].Rows[0]["GearRatios" + i].ToString();
                            break;
                        case "D":
                            string sr_GN = dsCarCertdata.Tables[0].Rows[0]["GN"].ToString();
                            //string sr_GN = "12,526,5,6,4,1,2";
                            string gnLine = sr_GN.Trim();//加Trim
                            string[] aryGn = gnLine.Split(',');
                            for (int g = 1; g < aryGn.Length; g++)
                            {
                                TextBoxVirtual.Text = aryGn[i - 1];
                            }
                            break;
                    }

                }
                else
                {
                    TextBoxVirtual.Text = "";
                }

                System.Web.UI.HtmlControls.HtmlGenericControl br = new System.Web.UI.HtmlControls.HtmlGenericControl();

                br.TagName = "div";
                br.Controls.Add(LabelVirtual);
                br.Controls.Add(TextBoxVirtual);
                div.Controls.Add(br);
            }
        }
        else
        {
            for (i = 1; i <= 2; i++)
            {
                Label LabelVirtual = new Label();
                LabelVirtual.ID = "Variable_speed_system_Label" + i;
                if (i == 1)
                {
                    LabelVirtual.Text = "UD檔：";
                }
                else
                {
                    LabelVirtual.Text = "OD檔：";
                }

                TextBox TextBoxVirtual = new TextBox();
                TextBoxVirtual.ID = Variable_speed_system_TextBox + i;
                TextBoxVirtual.MaxLength = limtLength;
                if (count > 0 && count >= i)
                {
                    switch (Request_mode().ToUpper())
                    {
                        case "G":
                        case "M":
                            TextBoxVirtual.Text = dsCarCertdata.Tables[0].Rows[0]["GearRatios" + i].ToString();
                            break;

                        case "D":
                            string sr_GN = dsCarCertdata.Tables[0].Rows[0]["GN"].ToString();
                            string gnLine = sr_GN.Trim();//加Trim
                            string[] aryGn = gnLine.Split(',');
                            for (int g = 1; g < aryGn.Length; g++)
                            {
                                TextBoxVirtual.Text = aryGn[i - 1];
                            }
                            break;
                    }
                }
                else
                {
                    TextBoxVirtual.Text = "";
                }

                System.Web.UI.HtmlControls.HtmlGenericControl br = new System.Web.UI.HtmlControls.HtmlGenericControl();

                br.TagName = "div";
                br.Controls.Add(LabelVirtual);
                br.Controls.Add(TextBoxVirtual);
                div.Controls.Add(br);
            }
        }

        int n = Variable_speed_system.Controls.Count;
    }

    #region 判斷製造地區欄位
    private void CheckPct()
    {
        Cryptography crypy = new Cryptography();
        int keynum = crypy.getRadNum();
        wsCodetbl wsCodeTbl = new wsCodetbl();
        string[] arr_wscodetbl = crypy.wsCodetblValidate(keynum);
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        // 1.只能用、或/ 分隔字串 
        // 2.輸入的國家要與代碼中的國家相同

        DataSet dsCountry = wsCodeTbl.getProdCountry(arr_wscodetbl[0].ToString(), arr_wscodetbl[1].ToString());
        DataTable dtCountry = dsCountry.Tables["dtCountry"];
        dtCountry = dsCountry.Tables[0];
        if (tb_producercountry.Text.Length > 0)
        {
            string sr_pctDESC = tb_producercountry.Text.Trim("、/".ToCharArray());
            sr_pctDESC = sr_pctDESC.Replace("/", "、");//把/換成、比對國家
            string[] aryPctDesc = sr_pctDESC.Split('、');
            string sUnSame = "";
            for (int i = 0; i < aryPctDesc.Length; i++)
            {
                if (LinqToDS(dtCountry, aryPctDesc[i]) != "1")
                    sUnSame += LinqToDS(dtCountry, aryPctDesc[i]) + ",";
            }


            #region 只能用、或/ 分隔字串
            if (!ValidatorHelper.IsChineseTotem(tb_producercountry.Text.Trim("、/".ToCharArray())))
            {
                MessageBox.AjaxShow(UpdatePanel1, "只能輸入中文和、");
                tb_producercountry.Focus();
                return;
            }
            #endregion
            #region 輸入的國家要與代碼中的國家相同
            if (sUnSame.Length > 0)
            {
                MessageBox.AjaxShow(UpdatePanel1, sUnSame.TrimEnd(',') + ":不在製造國代碼裡");
                tb_producercountry.Focus();
                return;
            }
            #endregion
        }





    }
    #endregion

    #region LINQ to DataSet 比對製造地區
    private string LinqToDS(DataTable sourcedt, string sPct)
    {
        IEnumerable<DataRow> myrows = from dtCountry in sourcedt.AsEnumerable()
                                      where dtCountry.Field<string>("ct_pcy_desc") == sPct
                                      select dtCountry;
        if (myrows.Count() == 0)
        {
            bCheckPcy = false;
            return string.Format("{0}", sPct);
        }
        else
        {
            bCheckPcy = true;
            return string.Format("{0}", myrows.Count().ToString());
        }
    }
    #endregion

    protected void tb_producercountry_TextChanged(object sender, EventArgs e)
    {
        CheckPct();
    }
}