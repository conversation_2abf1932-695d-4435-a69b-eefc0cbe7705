﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using System.Configuration;

public partial class accountmt_personalinfo_declare : Rac_Example.RAC_BasePage
{
    private string Account
    {
        get
        {
            Cryptography crypt = new Cryptography();
            return Request["account"] == null ? string.Empty : crypt.DeCode(Request["account"].ToString());
        }
    }
    protected void Page_Load(object sender, EventArgs e)
    {

    }

    protected void btn_Disagree_Click(object sender, EventArgs e)
    {
        Session.RemoveAll();
        Response.Redirect("index.aspx", false);
    }
    protected void btn_Agree_Click(object sender, EventArgs e)
    {
        //個資         
        wsAccountInfo wsaccount = new wsAccountInfo();
        Cryptography crypt = new Cryptography();
        string[] radom_A = crypt.wsAccountValidate(crypt.getRadNum());
        wsaccount.InsertPersonalInfodeclare(Account, radom_A[0].ToString(), radom_A[1].ToString());
        ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已同意個資聲明');
                                                    window.location = 'index.aspx?Error=NULL';</script>");


    }
}