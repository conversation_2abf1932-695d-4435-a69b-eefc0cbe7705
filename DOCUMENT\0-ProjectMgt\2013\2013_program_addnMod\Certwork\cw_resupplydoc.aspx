﻿<%@ Page Title="" Language="C#" MasterPageFile="~/masterpage/MasterPage.master" AutoEventWireup="true"
    CodeFile="cw_resupplydoc.aspx.cs" Inherits="Certwork_cw_resupplydoc" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="fullheader">
        <table width="100%" cellspacing="0" cellpadding="0" class="fullheaderinfo">
            <tr>
                <td valign="top">
                    <b>補件通知單</b>
                </td>
                <td align="right" valign="top" class="font_loginInfo">
                </td>
            </tr>
        </table>
    </div>
    <!--{* fullheader end *}-->
    <div class="fullcontent">
        <div id="div_maintain" runat="server" class="formstyle">
            <div class="tableoutcome">
                補件通知單：<asp:Label ID="lbl_CarCompoNo" runat="server"></asp:Label>
                <asp:GridView ID="gv_data" runat="server" AutoGenerateColumns="False" Width="100%"
                    CellPadding="0" BorderWidth="0px" GridLines="None" CssClass="font_fullcontent"
                    EnableModelValidation="True" OnRowDataBound="gv_data_RowDataBound">
                    <Columns>
                        <asp:TemplateField HeaderText="項次">
                            <ItemTemplate>
                                <asp:Label ID="lbl_no" runat="server" Text='<%# ((GridViewRow)Container).RowIndex %>'></asp:Label>
                            </ItemTemplate>
                            <HeaderStyle CssClass="lineleft" />
                            <ItemStyle CssClass="lineleft" HorizontalAlign="Center" />
                        </asp:TemplateField>
                        <asp:BoundField HeaderText="審查項目" ItemStyle-CssClass="lineleft" HeaderStyle-CssClass="lineleft"
                            DataField="ValidationItemCn">
                            <HeaderStyle CssClass="lineleft"></HeaderStyle>
                            <ItemStyle CssClass="lineleft"></ItemStyle>
                        </asp:BoundField>
                        <%--                        <asp:BoundField HeaderText="審查意見" ItemStyle-CssClass="lineright" HeaderStyle-CssClass="lineright"
                            DataField="Opinion">
                            <HeaderStyle CssClass="lineright"></HeaderStyle>
                            <ItemStyle CssClass="lineright"></ItemStyle>
                        </asp:BoundField>--%>
                        <asp:TemplateField HeaderText="審查意見" ItemStyle-CssClass="lineright"
                            HeaderStyle-CssClass="lineright" ItemStyle-HorizontalAlign="Center" HeaderStyle-HorizontalAlign="Center">
                            <ItemTemplate>
                                <asp:Label ID="lbl_Opinion" runat="server"></asp:Label>
                            </ItemTemplate>
                            <ItemStyle CssClass="lineright"></ItemStyle>
                            <HeaderStyle CssClass="lineright"></HeaderStyle>
                        </asp:TemplateField>
                    </Columns>
                    <PagerStyle HorizontalAlign="Center" Font-Bold="true" CssClass="S_PageCss" />
                </asp:GridView>
            </div>
        </div>
    </div>
    <!--{* fullcontent end *}-->
    <div class="fullfooter">
    </div>
    <!--{* content end *}-->
</asp:Content>
