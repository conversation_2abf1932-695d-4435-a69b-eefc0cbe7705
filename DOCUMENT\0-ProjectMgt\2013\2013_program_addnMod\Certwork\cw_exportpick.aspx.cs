﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Configuration;
using System.IO;
using System.Text;
using System.Web.UI.HtmlControls;

using System.Data;
using System.Data.SqlClient;

using System.Globalization;
using System.Xml;
using Common;
using System.Data.OleDb;




public partial class Certwork_cw_exportpick : BaseAdminPage
{
    protected void Page_Load(object sender, EventArgs e)
    {
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.close;</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" && (GetAgentAccountGrpList().Contains("epa_adm") || GetAgentAccountGrpList().Contains("ac_adm") || GetAgentAccountGrpList().Contains("tc_adm") || GetAgentAccountGrpList().Contains("rv_adm") || GetAgentAccountGrpList().Contains("rs_adm") || GetAgentAccountGrpList().Contains("fis_adm") || GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("pg_adm")))
            {
                if (!IsPostBack)
                {
                    BindData();

                }
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }
    }

    public void BindData()
    {
        //匯出資料       
        dl_export.DataSource = new FirstProcessControl().ListExport("1");
        dl_export.DataBind();
        dl_export2.DataSource = new FirstProcessControl().ListExport("2");
        dl_export2.DataBind();
        dl_export3.DataSource = new FirstProcessControl().ListExport("3");
        dl_export3.DataBind();
    }

    #region 按鈕事件

    protected void btn_Export_Click(object sender, EventArgs e)
    {
        Cryptography crypy = new Cryptography();
        string sFileName = string.Format("Export_{0}.xls", DateTime.Now.ToString("yyyyMMddhhmmss"));

        CheckBox cb;


        string s_sql = string.Empty;
        int i_sum = 0;
        foreach (DataListItem it in dl_export.Items)
        {
            cb = (CheckBox)it.FindControl("cb_export");
            if (cb != null)
            {
                if (cb.Checked)
                {
                    string[] lines = ((cb.Attributes["key"].ToString().Replace("a_", "abd_")).Replace("b_", "cpm_")).Replace("c_", "cmdg_").Split('_');
                    string subCName = lines[lines.GetUpperBound(0)];
                    #region 顯示欄位調整
                    switch (subCName)
                    {
                        case "compname": //公司名稱
                            cb.Attributes["key"] = "ci_compname";
                            break;
                        case "twocertmethod": //兩證合一申請方式ct_twct_desc
                            cb.Attributes["key"] = "ct_twct_desc";
                            break;
                        case "vechiletype": //車輛種類 ct_ct_desc 
                            cb.Attributes["key"] = "ct_ct_desc";
                            break;
                        case "adaptstandarddate":  //適用期別 ct_sd_desc
                            cb.Attributes["key"] = "ct_sd_desc";
                            break;
                        case "applystatus":  //資料狀態ct_aps_desc
                            cb.Attributes["key"] = "ct_aps_desc";
                            break;
                        case "powerfrom":  //abd_powerfrom --動力來源ct_pf_desc
                            cb.Attributes["key"] = "ct_pf_desc";
                            break;
                    }
                    #endregion
                    s_sql += (((cb.Attributes["key"].ToString().Replace("a1_", "abd_")).Replace("a2_", "cpm_")).Replace("a3_", "cmdg_")).Replace("a2ntr_", "ntr_") + ",";
                    i_sum += 1;
                }
            }
        }
        foreach (DataListItem it in dl_export2.Items)
        {
            cb = (CheckBox)it.FindControl("cb_export");
            if (cb != null)
            {
                if (cb.Checked)
                {
                    string[] lines = (((cb.Attributes["key"].ToString().Replace("a_", "abd_")).Replace("b_", "cpm_")).Replace("c_", "cmdg_")).Replace("a2ntr_", "ntr_").Split('_');
                    string subCName = lines[lines.GetUpperBound(0)];
                    #region 顯示欄位調整
                    switch (subCName)
                    {
                        case "useeurocert": //cpm_useeurocert --以歐盟合格證申請ct_eu_desc
                            cb.Attributes["key"] = "ct_eu_desc";
                            break;
                        case "vechiletype": //車輛種類 ct_ct_desc 
                            cb.Attributes["key"] = "ct_ct_desc";
                            break;
                        case "adaptstandarddate":  //適用期別 ct_sd_desc
                            cb.Attributes["key"] = "ct_sd_desc";
                            break;
                        case "applytype":  //申請型式 ct_at_desc
                            cb.Attributes["key"] = "ct_at_desc";
                            break;
                        case "transmissiontypetype":  //排檔型式 ct_gm_desc
                            cb.Attributes["key"] = "ct_gm_desc";
                            break;
                        case "applystatus":  //資料狀態ct_aps_desc
                            cb.Attributes["key"] = "ct_aps_desc";
                            break;
                        case "powerfrom":  //abd_powerfrom --動力來源ct_pf_desc
                            cb.Attributes["key"] = "ct_pf_desc";
                            break;
                        case "cylindertype":  //cpm_cylindertype --汽缸排列ct_cp_desc_m
                            cb.Attributes["key"] = "ct_cp_desc_m";
                            break;
                        case "gasolinetype":  //cpm_gasolinetype --使用燃料ct_ft_desc
                            cb.Attributes["key"] = "ct_ft_desc";
                            break;
                        case "powerexportway":  //cpm_powerexportway --燃燒循環ct_ebm_desc
                            cb.Attributes["key"] = "ct_ebm_desc";
                            break;
                        case "provideoilmethod":  //provideoilmethod --供油方式ct_le_desc
                            cb.Attributes["key"] = "ct_le_desc";
                            break;
                        case "coolsystem":  //coolsystem--冷卻系統型式 ct_cs_desc
                            cb.Attributes["key"] = "ct_cs_desc";
                            break;
                        case "testfactory":  //cpm_testfactory--檢測廠 ct_tf_desc
                            cb.Attributes["key"] = "ct_tf_desc";
                            break;
                        case "importarea":  //製造地區 ct_cy_desc
                            cb.Attributes["key"] = "ct_cy_desc";
                            break;

                        case "testrptno":  //報告編號
                            cb.Attributes["key"] = "ntr_testrptno";
                            break;
                        case "carbodyno":  //車身碼
                            cb.Attributes["key"] = "ntr_carbodyno";
                            break;
                        case "engineno":  //引擎碼
                            cb.Attributes["key"] = "ntr_engineno";
                            break;
                        case "staynoise":  //代表車原地噪音值
                            cb.Attributes["key"] = "ntr_staynoise";
                            break;
                        case "speedupnoise":  //代表車加速噪音值
                            cb.Attributes["key"] = "ntr_speedupnoise";
                            break;
                        case "remarkdesc":  //測試報告備註
                            cb.Attributes["key"] = "dbo.fn_GetRemarkForRptInfo(cpm_carcompomodelno) 'fnRemark'";
                            break;
                        case "Estaynoise":  //代表車加速噪音值
                            cb.Attributes["key"] = "'－' 'Estaynoise'";
                            break;
                        case "Espeedupnoise":  //代表車加速噪音值
                            cb.Attributes["key"] = "'－' 'Espeedupnoise'";
                            break;


                    }
                    #endregion
                    s_sql += (((cb.Attributes["key"].ToString().Replace("a1_", "abd_")).Replace("a2_", "cpm_")).Replace("a3_", "cmdg_")).Replace("a2ntr_", "ntr_") + ",";
                    i_sum += 1;
                }
            }
        }
        foreach (DataListItem it in dl_export3.Items)
        {
            cb = (CheckBox)it.FindControl("cb_export");
            if (cb != null)
            {
                if (cb.Checked)
                {
                    string[] lines = ((cb.Attributes["key"].ToString().Replace("a_", "abd_")).Replace("b_", "cpm_")).Replace("c_", "cmdg_").Split('_');
                    string subCName = lines[lines.GetUpperBound(0)];
                    //CC改
                    string s_G_fullcarstylename = "dbo.fn_FullCarstyleNameStr(abd_applyno,cpm_carcompomodelno,nv_carmodeldata.cmdg_carmodelno) ";
                    string s_M_fullcarstylename = "dbo.fn_FullCarstyleNameStr(abd_applyno,cpm_carcompomodelno,nv_carmodeldata.cmdg_carmodelno) ";
                    #region 顯示欄位調整
                    switch (subCName)
                    {
                        case "fullcarstylename":  //車型名稱(10個欄位組合)
                            if (crypy.DeCode(Request["cartype"].ToString()).ToUpper().Equals("G") || crypy.DeCode(Request["cartype"].ToString()).ToUpper().Equals("D"))
                            {
                                cb.Attributes["key"] = s_G_fullcarstylename + "as 'fullcarstylename'";
                            }
                            else if (crypy.DeCode(Request["cartype"].ToString()).ToUpper().Equals("M"))
                            {
                                cb.Attributes["key"] = s_M_fullcarstylename + "as 'fullcarstylename'";
                            }
                            else
                            {
                                cb.Attributes["key"] = "case dbo.fn_CastCarTypeCn(abd_vechiletype) when '機車' then '' else " + s_G_fullcarstylename + " end as 'fullcarstylename',case dbo.fn_CastCarTypeCn(abd_vechiletype) when '汽車' then '' else " + s_M_fullcarstylename + " end as 'fullcarstylename'";
                                i_sum += 1;
                            }
                            break;
                       
                        case "producercountry": //製造國
                            cb.Attributes["key"] = "dbo.fn_CastCountryDateCn(cmdg_producercountry) 'PCountry' ";
                            break;
                        case "cartype":  //車種分類 ct_nvc_desc
                            cb.Attributes["key"] = "ct_nvc_desc";
                            break;
                        case "carbrand":  //廠牌ct_bd_desc
                            cb.Attributes["key"] = "ct_bd_desc";
                            break;
                        case "cardoorcount":  //車門 ct_vr_desc
                            cb.Attributes["key"] = "ct_vr_desc";
                            break;
                        case "turborchargers":  //cmdg_turborchargers --增壓器 ct_htm_desc
                            cb.Attributes["key"] = "ct_htm_desc";
                            break;
                        case "inletmode":  //cmdg_inletmode --進氣方式 ct_im_desc
                            cb.Attributes["key"] = "ct_im_desc";
                            break;
                        case "drivetype":  //cmdg_drivetype  --驅動方式ct_dt_desc
                            cb.Attributes["key"] = "ct_dt_desc";
                            break;
                        case "carbodyshape":  //carbodyshape  --車身樣式ct_cbsg_desc (汽車);ct_cbsm_desc (機車)
                            if (crypy.DeCode(Request["cartype"].ToString()).ToUpper().Equals("G") || crypy.DeCode(Request["cartype"].ToString()).ToUpper().Equals("D"))
                            {
                                cb.Attributes["key"] = "ct_cbsg_desc";
                            }
                            else if (crypy.DeCode(Request["cartype"].ToString()).ToUpper().Equals("M"))
                            {
                                cb.Attributes["key"] = "ct_cbsm_desc";
                            }
                            else
                            {
                                cb.Attributes["key"] = "case dbo.fn_CastCarTypeCn(abd_vechiletype) when '機車' then '' else ct_cbsg_desc end as 'ct_cbsg_desc',case dbo.fn_CastCarTypeCn(abd_vechiletype) when '汽車' then '' else ct_cbsm_desc end as 'ct_cbsm_desc'";
                                i_sum += 1;
                            }
                            break;
                    }
                    #endregion
                    s_sql += (((cb.Attributes["key"].ToString().Replace("a1_", "abd_")).Replace("a2_", "cpm_")).Replace("a3_", "cmdg_")).Replace("a2ntr_", "ntr_") + ",";
                    i_sum += 1;
                }
            }
        }
        if (i_sum < 1)
        {
            MessageBox.Show("請先勾選欲匯出的檔案");
            return;
        }

        DataView dv = new FirstProcessControl().ListExportData(s_sql.Substring(0, s_sql.Length - 1), crypy.DeCode(Request["compname"].ToString()), crypy.DeCode(Request["applyno"].ToString()), crypy.DeCode(Request["applystatus"].ToString()), crypy.DeCode(Request["enginefamily"].ToString()), crypy.DeCode(Request["factoryname"].ToString()), crypy.DeCode(Request["carstyleno"].ToString()), crypy.DeCode(Request["cartype"].ToString()), crypy.DeCode(Request["standarddate"].ToString()), crypy.DeCode(Request["transporttype"].ToString()), crypy.DeCode(Request["applydate_SDate"].ToString()), crypy.DeCode(Request["applydate_EDate"].ToString()), crypy.DeCode(Request["validdate_SDate"].ToString()), crypy.DeCode(Request["validdate_EDate"].ToString()));

        Response.Charset = "utf-8";
        Response.AddHeader("Content-Disposition", "attachment;filename=" + Server.UrlEncode(sFileName)); //下載名稱
        Response.ContentType = "application/vnd.ms-excel";
        Response.ContentEncoding = System.Text.Encoding.GetEncoding("utf-8");
        Response.Write("<html><head><style type='text/css'><!--    tr { height:26px; } //-->    </style></head><body>");
        Response.Write("<meta http-equiv=Content-Type content=text/html; charset=utf-8>"); //避免亂碼
        Response.Write("<style type=text/css>");
        Response.Write("td{mso-number-format:\"\\@\";}"); //將所有欄位格式改為"文字"
        Response.Write(".formCaption1{background-color:#CECFCE;font-size:12px;height:24px;}");
        Response.Write("</style>");

        System.IO.StringWriter sw = new System.IO.StringWriter();
        System.Web.UI.HtmlTextWriter tw = new System.Web.UI.HtmlTextWriter(sw);


        #region Htmlstring
        string Htmlstring = string.Empty;

        #region 標題
        Htmlstring += @"<table border='1'><tr>";
        foreach (DataListItem it in dl_export.Items)
        {
            cb = (CheckBox)it.FindControl("cb_export");
            if (cb != null)
            {
                if (cb.Checked)
                {
                    Htmlstring += @"<th class='formCaption1'>" + cb.Text.ToString() + "</th>";  //標題
                }
            }
        }
        foreach (DataListItem it in dl_export2.Items)
        {
            cb = (CheckBox)it.FindControl("cb_export");
            if (cb != null)
            {
                if (cb.Checked)
                {
                    Htmlstring += @"<th class='formCaption1'>" + cb.Text.ToString() + "</th>";  //標題
                }
            }
        }
        foreach (DataListItem it in dl_export3.Items)
        {
            cb = (CheckBox)it.FindControl("cb_export");
            if (cb != null)
            {
                if (cb.Checked)
                {
                    if (cb.Text.ToString().Equals("車身式樣"))
                    {
                        if (crypy.DeCode(Request["cartype"].ToString()).Equals("") || crypy.DeCode(Request["cartype"].ToString()).ToUpper().Equals("N") || crypy.DeCode(Request["cartype"].ToString()).ToUpper().Equals("E"))
                        {
                            Htmlstring += @"<th class='formCaption1'>車身式樣(G、D)</th><th class='formCaption1'>車身式樣(M)</th>";
                        }
                        else
                        {
                            Htmlstring += @"<th class='formCaption1'>" + cb.Text.ToString() + "</th>";  //標題
                        }
                    }
                    else if (cb.Text.ToString().Equals("車型名稱(10個欄位組合)"))
                    {
                        if (crypy.DeCode(Request["cartype"].ToString()).Equals("") || crypy.DeCode(Request["cartype"].ToString()).ToUpper().Equals("N") || crypy.DeCode(Request["cartype"].ToString()).ToUpper().Equals("E"))
                        {
                            Htmlstring += @"<th class='formCaption1'>車型名稱(G、D)</th><th class='formCaption1'>車型名稱(M)</th>";
                        }
                        else
                        {
                            Htmlstring += @"<th class='formCaption1'>" + cb.Text.ToString() + "</th>";  //標題
                        }
                    }
                    else
                    {
                        Htmlstring += @"<th class='formCaption1'>" + cb.Text.ToString() + "</th>";  //標題
                    }

                }
            }
        }
        Htmlstring += @"</tr>";
        #endregion

        #region 內容
        if (dv.Count > 0)
        {
            for (int i = 0; i < dv.Count; i++) //資料數
            {
                Htmlstring += @"<tr>";

                for (int j = 0; j < i_sum; j++)//欄位名稱
                {
                    string[] Titlelines = dv.Table.Columns[j].ToString().Split('_');//取得資料表表頭名稱
                    string TitleName = Titlelines[Titlelines.GetUpperBound(0)];
                    #region 欄位值轉換
                    switch (TitleName)
                    {
                        case "databelongcompidno": //公司統編         
                            Htmlstring += @"<td>" + crypy.DeCode(dv[i][j].ToString()) + "</td>";
                            break;
                        case "exhuast":           //總排氣量    
                            Htmlstring += @"<td>" + dv[i][j].ToString().Replace(".00", "") + "</td>";
                            break;
                        case "loadingweight":     //載重    
                            Htmlstring += @"<td>" + dv[i][j].ToString().Replace(".00", "") + "</td>";
                            break;
                        case "cylindermeters":    //缸徑    
                            Htmlstring += @"<td>" + dv[i][j].ToString().Replace(".00", "") + "</td>";
                            break;
                        case "cylinderstroke":    //衝徑       
                            Htmlstring += @"<td>" + dv[i][j].ToString().Replace(".00", "") + "</td>";
                            break;
                        case "testweight":       //受車重量
                            Htmlstring += @"<td>" + dv[i][j].ToString().Replace(".00", "") + "</td>";
                            break;
                        default:
                            Htmlstring += @"<td>" + dv[i][j].ToString() + "</td>";
                            break;
                    }
                    #endregion
                }
                Htmlstring += @"</tr>";
            }

        }
        Htmlstring += @"</table>";
        #endregion
        sw.WriteLine(Htmlstring);

        Response.Write(sw.ToString());
        Response.End();

        #endregion

    }

    #endregion


    protected void cb_all_1_CheckedChanged(object sender, EventArgs e)
    {
        CheckBox cb;
        if (cb_all_1.Checked)
        {
            foreach (DataListItem it in dl_export.Items)
            {
                cb = (CheckBox)it.FindControl("cb_export");
                cb.Checked = true;
            }
        }
        else if (!cb_all_1.Checked)
        {
            foreach (DataListItem it in dl_export.Items)
            {
                cb = (CheckBox)it.FindControl("cb_export");
                cb.Checked = false;
            }

        }
    }
    protected void cb_all_2_CheckedChanged(object sender, EventArgs e)
    {
        CheckBox cb;
        if (cb_all_2.Checked)
        {
            foreach (DataListItem it in dl_export2.Items)
            {
                cb = (CheckBox)it.FindControl("cb_export");
                cb.Checked = true;
            }
        }
        else if (!cb_all_2.Checked)
        {
            foreach (DataListItem it in dl_export2.Items)
            {
                cb = (CheckBox)it.FindControl("cb_export");
                cb.Checked = false;
            }

        }
    }
    protected void cb_all_3_CheckedChanged(object sender, EventArgs e)
    {
        CheckBox cb;
        if (cb_all_3.Checked)
        {
            foreach (DataListItem it in dl_export3.Items)
            {
                cb = (CheckBox)it.FindControl("cb_export");
                cb.Checked = true;
            }
        }
        else if (!cb_all_3.Checked)
        {
            foreach (DataListItem it in dl_export3.Items)
            {
                cb = (CheckBox)it.FindControl("cb_export");
                cb.Checked = false;
            }

        }
    }
}