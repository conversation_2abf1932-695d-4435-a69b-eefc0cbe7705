﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class usercontrol_uc_menu : System.Web.UI.UserControl
{
    protected void Page_Load(object sender, EventArgs e)
    {
        if (this.IsPostBack == false)
        {
            this.InitPageControl();
        }
    }

    protected void InitPageControl()
    {
        BaseAdminPage EGPAdmin = new BaseAdminPage();
        //設定使用者的所有角色        
        List<string> liRoleUIDList = new List<string>();
        char[] separator1 = { ',' };
        if (EGPAdmin.GetAgentAccountGrpList() != "")
        {
            string[] Role = EGPAdmin.GetAgentAccountGrpList().Split(separator1);
            for (int intRow = 0; intRow < Role.Length; intRow++)
            {
                liRoleUIDList.Add(Role[intRow]);
            }
        }
        else
        {
            liRoleUIDList.Add("all_adm");
        }

        //設定URL參數
        List<ISC_RAC.Model.RowItem_NavigateURLParameter> liNavigateURLParameters = new List<ISC_RAC.Model.RowItem_NavigateURLParameter>();
        //ISC_RAC.Model.RowItem_NavigateURLParameter item = new ISC_RAC.Model.RowItem_NavigateURLParameter();
        //item.Key = "{0}";
        //item.Value = "xxxx";
        //liNavigateURLParameters.Add(item);

        this.MainMenu.FunctionGroup = 0;//決定menu的種類
        //this.MainMenu.MakeMenu(Rac_Example.GeneralFunction.UIDOfCurrentUser, liNavigateURLParameters);
        this.MainMenu.MainMenu(Rac_Example.GeneralFunction.UIDOfCurrentUser, liRoleUIDList, liNavigateURLParameters);
    }
}