﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;

public partial class accountmt_accountvalidate_notfirstaspx : BaseAdminPage
{
    wsCompany wscompany = new wsCompany();
    wsAccountInfo wsaccount = new wsAccountInfo();
    wsGroupInfo wsgroup = new wsGroupInfo();
    wsCodetbl wscodetbl = new wsCodetbl();
    wsApplyDeny wsapplydeny = new wsApplyDeny();
    protected void Page_Load(object sender, EventArgs e)
    {        
        #region TextBox在Textmode=MultiLine狀態下MaxLength的字數限制無效
        txt_denybackreason.Attributes.Add("maxlength", "120");
        txt_denybackreason.Attributes.Add("onkeyup", "return ismaxlength(this)");

        #endregion
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = 'index.aspx';</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" && (GetAgentAccountGrpList().Contains("fis_adm") || GetAgentAccountGrpList().Contains("pg_adm") || GetAgentAccountGrpList().Contains("sys_adm")))
            {
                if (!IsPostBack)
                {
                    if (Request["status"] == null || Request["type"] == null || Request["account"] == null || Request["compidno"] == null)
                    {
                        Response.Write(ConfigurationManager.AppSettings["Msg.參數正確性"].ToString());
                        Response.End();
                        return;
                    }
                    else
                    {
                        string type = DeCode(Request["type"].ToString());
                        string status = DeCode(Request["status"].ToString());
                        if (type != "非第一次申請")
                        {
                            Response.Write(ConfigurationManager.AppSettings["Msg.無對應資料"].ToString());
                            Response.End();
                            return;
                        }
                        else
                        {
                            if (status == "退件")
                            {
                                BindDenyAccount();
                                BindCompany();
                            }
                            else
                            {
                                BindCompany();
                                BindAccount();
                            }

                            if (status == "已確認" || status == "申請中")
                            {
                                lbl_loginname.Text = GetAgentName();
                                lbl_logindate.Text = DateTime.Now.ToString("yyyy/MM/dd");
                            }
                        }
                    }
                }
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;

            }
        }
    }
    private void BindCompany()
    {
        string[] radom_C = wsCompanyValidate(getRadNum());
        string[] radom_Code = wsCodetblValidate(getRadNum());

        DataSet ds_company = wscompany.getCompanyBase(Request["compidno"].ToString(), radom_C[0].ToString(), radom_C[1].ToString());
        lbl_compidno.Text = DeCode(Request["compidno"].ToString());
        lbl_compname.Text = ds_company.Tables[0].Rows[0]["ci_compname"].ToString();
        lbl_compename.Text = ds_company.Tables[0].Rows[0]["ci_compename"].ToString();
        
    }
    private void BindAccount()
    {
        string[] radom_A = wsAccountValidate(getRadNum());
        string[] radom_Code = wsCodetblValidate(getRadNum());
        int issuedaccountcount = wsaccount.hIssuedAccountbyComp(Request["compidno"].ToString(), radom_A[0].ToString(), radom_A[1].ToString());
        lbl_appyliedcount.Text = issuedaccountcount.ToString();
        DataSet ds_account = wsaccount.getAccountBase(DeCode(Request["account"].ToString()), radom_A[0].ToString(), radom_A[1].ToString());
        lbl_username.Text = ds_account.Tables[0].Rows[0]["ai_username"].ToString();
        lbl_usertel.Text = ds_account.Tables[0].Rows[0]["ai_usertel"].ToString();
        lbl_userfax.Text = (ds_account.Tables[0].Rows[0]["ai_userfax"].ToString() == "") ? "&nbsp;" : ds_account.Tables[0].Rows[0]["ai_userfax"].ToString();
        lbl_usercellphone.Text = (ds_account.Tables[0].Rows[0]["ai_usercellphone"].ToString() == "") ? "&nbsp;" : ds_account.Tables[0].Rows[0]["ai_usercellphone"].ToString();
        lbl_userEMail.Text = ds_account.Tables[0].Rows[0]["ai_email"].ToString();
        lbl_useraddr.Text = ds_account.Tables[0].Rows[0]["ai_useraddrpostno"].ToString()
            + "  " + wscodetbl.getCityName(ds_account.Tables[0].Rows[0]["ai_useraddrcity"].ToString(), radom_Code[0].ToString(), radom_Code[1].ToString())
            + "  " + wscodetbl.getZipName(ds_account.Tables[0].Rows[0]["ai_useraddrcity"].ToString(), ds_account.Tables[0].Rows[0]["ai_useraddrlocalarea"].ToString(), radom_Code[0].ToString(), radom_Code[1].ToString())
            + "  " + ds_account.Tables[0].Rows[0]["ai_useraddr"].ToString();
        
        lbl_applydate.Text = ds_account.Tables[0].Rows[0]["ai_applydate"].ToString();

        if (DeCode(Request["status"].ToString()) != "申請中" && DeCode(Request["status"].ToString()) != "已確認")
        {
            lbl_loginname.Text = ds_account.Tables[0].Rows[0]["ai_enabledaccountname"].ToString();
            lbl_logindate.Text = ds_account.Tables[0].Rows[0]["ai_enableddate"].ToString();
        }

        if (ds_account.Tables[0].Rows[0]["ai_compmgrcheckaccount"].ToString() != "")
        {
            //lbl_errorreason.Text = ds_account.Tables[0].Rows[0]["ai_compmgrcheckdesc"].ToString();
            lbl_accountreuslt.Text = (ds_account.Tables[0].Rows[0]["ai_compmgrcheckdesc"].ToString() == "") ? "確認無誤" : "確認有誤";
        }

        //取得帳號管理人確認結果資料及需加判斷帳號管理人確認結果；若確認有誤，則不顯示【核可】按鈕。審核人員僅能退回。
        //除了「已確認」，其他狀態都不可編輯
        if (DeCode(Request["status"].ToString()) == "已確認")
        {
            if (lbl_accountreuslt.Text == "確認無誤")
            {
                btn_OK.Visible = true;
                btn_NO.Visible = true;
            }
            else
            {
                btn_OK.Visible = false;
                btn_NO.Visible = true;
            }
            txt_denybackreason.ReadOnly = false;
            txt_denybackreason.Enabled = true;
        }
        else
        {
            btn_OK.Visible = false;
            btn_NO.Visible = false;
            txt_denybackreason.ReadOnly = true;
            txt_denybackreason.Enabled = false;
        }
        
        ViewState["grpaccount"] = ds_account.Tables[0].Rows[0]["ai_account"].ToString();
        ViewState["applystatus"] = ds_account.Tables[0].Rows[0]["ai_accountstatus"].ToString();
    }
    private void BindDenyAccount()
    {
        string[] radom_A = wsAccountValidate(getRadNum());
        int issuedaccountcount = wsaccount.hIssuedAccountbyComp(Request["compidno"].ToString(), radom_A[0].ToString(), radom_A[1].ToString());
        lbl_appyliedcount.Text = issuedaccountcount.ToString();
        DataTable dtData = new DataTable();
        string[] radom_Deny = wsApplyDenyValidate(getRadNum());

        dtData = wsapplydeny.getApplyDenyAccount(DeCode(Request["account"].ToString()), radom_Deny[0].ToString(), radom_Deny[1].ToString());
               
        if (dtData.Rows.Count > 0)
        {
            lbl_applydate.Text = dtData.Rows[0]["aiad_applydate"].ToString();
            lbl_username.Text = dtData.Rows[0]["aiad_username"].ToString();
            lbl_usertel.Text = dtData.Rows[0]["aiad_usertel"].ToString();
            lbl_userfax.Text = (dtData.Rows[0]["aiad_userfax"].ToString() == "") ? "&nbsp;" : dtData.Rows[0]["aiad_userfax"].ToString();
            lbl_usercellphone.Text = (dtData.Rows[0]["aiad_usercellphone"].ToString() == "") ? "&nbsp;" : dtData.Rows[0]["aiad_usercellphone"].ToString();
            lbl_userEMail.Text = dtData.Rows[0]["aiad_email"].ToString();
            lbl_useraddr.Text = dtData.Rows[0]["aiad_useraddrpostno"].ToString()
                + "  " + wscodetbl.getCityName(dtData.Rows[0]["aiad_useraddrcity"].ToString(), radom_Deny[0].ToString(), radom_Deny[1].ToString())
                + "  " + wscodetbl.getZipName(dtData.Rows[0]["aiad_useraddrcity"].ToString(), dtData.Rows[0]["aiad_useraddrlocalarea"].ToString(), radom_Deny[0].ToString(), radom_Deny[1].ToString())
                + "  " + dtData.Rows[0]["aiad_useraddr"].ToString();
            txt_denybackreason.Text = dtData.Rows[0]["aiad_denyreason"].ToString();

            if (DeCode(Request["status"].ToString()) != "送件" && DeCode(Request["status"].ToString()) != "已確認")
            {
                lbl_loginname.Text = dtData.Rows[0]["aiad_enabledaccountname"].ToString(); ;
                lbl_logindate.Text = dtData.Rows[0]["aiad_enableddate"].ToString(); ;
            }

            //lbl_errorreason.Text = dtData.Rows[0]["aiad_compmgrcheckdesc"].ToString();
            lbl_accountreuslt.Text = (dtData.Rows[0]["aiad_compmgrcheckdesc"].ToString() == "") ? "確認無誤" : "確認有誤";
            
            ViewState["grpaccount"] = dtData.Rows[0]["aiad_account"].ToString();
            lbl_applydate.Text = dtData.Rows[0]["aiad_applydate"].ToString();
        }
    }

    protected void btn_OK_Click(object sender, EventArgs e)
    {
        if (txt_denybackreason.Text.Trim() != "")
        {
            MessageBox.Show("無需填寫退件原因");
        }
        else
        {
            #region Accountmt特殊字元判斷-有MasterPage
            ContentPlaceHolder mpContentPlaceHolder;
            mpContentPlaceHolder = (ContentPlaceHolder)Master.FindControl("ContentPlaceHolder1");
            foreach (object ctrl in mpContentPlaceHolder.Controls)
            {
                if (ctrl is TextBox)
                {
                    TextBox objTextBox = default(TextBox);
                    objTextBox = (TextBox)ctrl;
                    if ((tbWord(objTextBox.Text)))
                    {
                        MessageBox.Show(objTextBox.Text + ":含有系統不允許的特殊字元");
                        return;
                    }
                }
            }
            #endregion

            string[] radom_A = wsAccountValidate(getRadNum());
            string account = DeCode(Request["account"].ToString());
            try
            {
                //mail
                string[] _mailTo = new string[1];
                _mailTo[0] = lbl_userEMail.Text;//收件人                
                string[] _mailCc = new string[1];//副本
                _mailCc[0] = "";
                string[] _mailBcc = new string[1];//密件
                _mailBcc[0] = "";
                if (Convert.ToBoolean(ConfigurationManager.AppSettings["DEBUGMode"].ToString()))
                {
                    //測試
                    _mailTo[0] = ConfigurationManager.AppSettings["MailTestTo"].ToString();
                }
                SendMail mail=new SendMail();
                string PWD = GetRandomPassword(6);
                bool result;
                if (ViewState["applystatus"].ToString() == "8")
                {
                    wsaccount.ApproveAccount(account, GetAgentAccount(), DateTime.Now.ToString(), GetIP(), "2", radom_A[0].ToString(), radom_A[1].ToString());
                    result = mail.Mail_ValidatePollution_OK(_mailTo, _mailCc, _mailBcc, lbl_username.Text.Trim());
                }
                else
                {
                    wsaccount.ApproveAccount(account, GetAgentAccount(), DateTime.Now.ToString(), GetIP(), "1", radom_A[0].ToString(), radom_A[1].ToString());
                    result = mail.Mail_ValidateNoise_OK(_mailTo, _mailCc, _mailBcc, lbl_compname.Text.Trim(), lbl_username.Text.Trim(), PWD);
                    wsaccount.UpdataPWD(account, GetPAWDHash(PWD), radom_A[0].ToString(), radom_A[1].ToString());
                }
                //非第一組帳號，將設定為 公司一般帳號權限(51)
                wsaccount.NonFristInsertCompManageGrp(account, Request["compidno"].ToString(), "51", radom_A[0].ToString(), radom_A[1].ToString());
                //result = mail.Mail_ValidateNoise_OK(_mailTo, _mailCc, _mailBcc, lbl_compname.Text.Trim(), lbl_username.Text.Trim(), PWD);
                if (result)
                {
                    string compidno = DeCode(Request["compidno"].ToString());
                    wsaccount.ApproveAccount(account, GetAgentAccount(), DateTime.Now.ToString(), GetIP(), "1", radom_A[0].ToString(), radom_A[1].ToString());
                    wsaccount.UpdataPWD(account, GetPAWDHash(PWD), radom_A[0].ToString(), radom_A[1].ToString());
                    ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('系統已寄發核發通知');
                                                    window.location = 'applyaccountvalidaionlist.aspx';</script>");
                }
                else
                {
                    MessageBox.Show("寄件失敗");
                }
            }
            catch
            {
                wsaccount.ApproveAccount(account, "", "", "", "0", radom_A[0].ToString(), radom_A[1].ToString());
                MessageBox.Show("失敗");
            }
        }
    }
    protected void btn_NO_Click(object sender, EventArgs e)
    {
        if (txt_denybackreason.Text.Trim().Length > 200)
        {
            MessageBox.Show("字數超出200，請確認");
            return;
        }
        if (txt_denybackreason.Text.Trim() != "")
        {
            #region Accountmt特殊字元判斷-有MasterPage
            ContentPlaceHolder mpContentPlaceHolder;
            mpContentPlaceHolder = (ContentPlaceHolder)Master.FindControl("ContentPlaceHolder1");
            foreach (object ctrl in mpContentPlaceHolder.Controls)
            {
                if (ctrl is TextBox)
                {
                    TextBox objTextBox = default(TextBox);
                    objTextBox = (TextBox)ctrl;
                    if ((tbWord(objTextBox.Text)))
                    {
                        MessageBox.Show(objTextBox.Text + ":含有系統不允許的特殊字元");
                        return;
                    }
                }
            }
            #endregion

            string account = DeCode(Request["account"].ToString());
            try
            {
                string[] radom_D = wsApplyDenyValidate(getRadNum());
                wsapplydeny.DenyAgain(EnCode(account), GetAgentAccount(), GetIP(), txt_denybackreason.Text.Trim(), radom_D[0].ToString(), radom_D[1].ToString());
                ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('退件完成');
                                                   window.location = 'applyaccountvalidaionlist.aspx';</script>");
            }
            catch
            {
                MessageBox.Show("失敗");
            }
//            //mail
//            string[] _mailTo = new string[1];
//            _mailTo[0] = lbl_userEMail.Text;//收件人                
//            string[] _mailCc = new string[1];//副本
//            _mailCc[0] = "";
//            string[] _mailBcc = new string[1];//密件
//            _mailBcc[0] = "";
//            if (Convert.ToBoolean(ConfigurationManager.AppSettings["DEBUGMode"].ToString()))
//            {
//                //測試
//                _mailTo[0] = "<EMAIL>";
//            }
//            EGPSendMail wsmail = new EGPSendMail();
//            if (wsmail.Mail_DenyAccount(_mailTo, _mailCc, _mailBcc, EnCode(account), Request["compidno"].ToString(), lbl_username.Text, lbl_userEMail.Text, lbl_compname.Text, txt_denybackreason.Text.Trim()))
//            {
//                wsapplydeny.DenyAgain(EnCode(account), GetAgentAccount(), GetAgentName(), GetIP(), txt_denybackreason.Text.Trim(), radom_D[0].ToString(), radom_D[1].ToString());
//                ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('系統已寄發退件通知');
//                                                    window.location = 'applyaccountvalidaionlist.aspx';</script>");
//            }
//            else
//            {
//                MessageBox.Show("寄件失敗");
//            }
        }
        else
        {
            MessageBox.Show("請輸入退件原因");
        }
    }
    protected void btn_Cancel_Click(object sender, EventArgs e)
    {
        Response.Redirect("applyaccountvalidaionlist.aspx", false);
    }
}