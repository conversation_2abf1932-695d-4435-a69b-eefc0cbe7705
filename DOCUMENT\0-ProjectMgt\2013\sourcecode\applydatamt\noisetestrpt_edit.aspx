﻿<%@ Page Title="" Language="C#" MasterPageFile="~/masterpage/MasterPage.master" AutoEventWireup="true"
    CodeFile="noisetestrpt_edit.aspx.cs" Inherits="applydatamt_noisetestrpt_edit" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="fullheader">
        <table width="100%" cellspacing="0" cellpadding="0" class="fullheaderinfo">
            <tr>
                <td valign="top">
                    <b>噪音合格證明申請-測試報告填寫</b>
                </td>
                <td align="right" valign="top" class="font_loginInfo">
                </td>
            </tr>
        </table>
    </div>
    <div class="fullcontent">
        <div id="div_maintain" runat="server" class="formstyle">
            <asp:ScriptManager ID="ScriptManager1" runat="server">
            </asp:ScriptManager>
            <asp:UpdatePanel ID="pnl_table" runat="server" UpdateMode="Conditional">
                <ContentTemplate>
                    <table border="0" cellspacing="0" cellpadding="0" width="100%" class="font_fullcontent">
                        <tr>
                            <td align="right" width="120px">
                                申請編號：
                            </td>
                            <td>
                                <asp:Label ID="lbl_applyno" runat="server"></asp:Label>
                            </td>
                            <td align="right" width="120px">
                                車型組代碼：
                            </td>
                            <td>
                                <asp:Label ID="lbl_carcompomodelno" runat="server"></asp:Label>
                            </td>
                        </tr>
                        <tr>
                            <td align="right">
                                <span style="color: Red;">*</span>測試車製造地區：
                            </td>
                            <td align="left" colspan="3">
                                <asp:DropDownList ID="ddl_producercountry" runat="server">
                                </asp:DropDownList>
                            </td>
                        </tr>
                        <tr>
                            <td align="right" width="120px">
                                車型名稱：
                            </td>
                            <td colspan="3">
                                <div>
                                    <asp:Repeater ID="rpt_representvehicle" runat="server" OnItemDataBound="rpt_representvehicle_ItemDataBound">
                                        <ItemTemplate>
                                            <asp:Label runat="server" ID="lbl_carengmodelname" />
                                        </ItemTemplate>
                                        <SeparatorTemplate>
                                            <asp:Label ID="lbl_dot" runat="server" Text=";"></asp:Label><br>
                                        </SeparatorTemplate>
                                    </asp:Repeater>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td align="right">
                                <span style="color: Red;">*</span>報告號碼：
                            </td>
                            <td>
                                <asp:TextBox ID="txt_testrptno" runat="server" MaxLength="20" Enabled="False"></asp:TextBox>
                                <asp:TextBox ID="txt_testrptnolod" runat="server" MaxLength="20" Visible="False"></asp:TextBox>
                            </td>
                            <td align="right">
                                <span style="color: Red;">*</span>檢測廠：
                            </td>
                            <td>
                                <asp:Label ID="lbl_testfactory" runat="server"></asp:Label>
                            </td>
                        </tr>
                        <tr>
                            <td align="right">
                                <span style="color: Red;">*</span>引擎號碼：
                            </td>
                            <td>
                                <asp:TextBox ID="txt_engineno" runat="server" MaxLength="20" Enabled="False"></asp:TextBox>
                            </td>
                            <td align="right">
                                <span style="color: Red;">*</span>車身號碼：
                            </td>
                            <td>
                                <asp:TextBox ID="txt_carbodyno" runat="server" MaxLength="20" Enabled="False"></asp:TextBox>
                            </td>
                        </tr>
                        <tr>
                            <td align="right">
                                <span style="color: Red;">*</span>加速進入檔位與<br />
                                進場車速：
                            </td>
                            <td colspan="3">
                                <asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional">
                                    <ContentTemplate>
                                        <asp:LinkButton ID="lbtn_speed_add" runat="server" OnClick="lbtn_speed_add_Click"
                                            CausesValidation="False">新增</asp:LinkButton><br />
                                        <div id="div_insert" runat="server" class="formstyle" style="display: none;">
                                            <table border="0" cellspacing="0" cellpadding="0" width="500" class="font_fullcontent">
                                                <tr>
                                                    <td align="right">
                                                        <asp:Label ID="lbl_trsui_id" runat="server" Enabled="False" Visible="False"></asp:Label>加速進入檔位：
                                                    </td>
                                                    <td>
                                                        <asp:TextBox ID="txt_rptspeedupgear" runat="server"></asp:TextBox>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td align="right">
                                                        加速進場車速：
                                                    </td>
                                                    <td>
                                                        <asp:TextBox ID="txt_rptspeed" runat="server"></asp:TextBox>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td align="center" colspan="2">
                                                        <asp:Button ID="btn_Ins" runat="server" Text="存檔" CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'"
                                                            onmouseout="this.className='btn_mouseout'" OnClick="btn_Ins_Click" CausesValidation="False" />&nbsp;&nbsp;&nbsp;&nbsp;
                                                        <asp:Button ID="btn_Canl" runat="server" Text="取消" CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'"
                                                            onmouseout="this.className='btn_mouseout'" OnClick="btn_Canl_Click" CausesValidation="False" />
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                        <asp:GridView ID="gv_speedupinfo" runat="server" AutoGenerateColumns="False" EnableModelValidation="True"
                                            OnRowDataBound="gv_speedupinfo_RowDataBound" BackColor="White" BorderColor="#CC9966"
                                            BorderWidth="1px" CellPadding="4" Width="292px">
                                            <Columns>
                                                <asp:TemplateField ItemStyle-CssClass="lineleft" HeaderStyle-CssClass="header" ItemStyle-HorizontalAlign="Center"
                                                    HeaderStyle-HorizontalAlign="Center">
                                                    <ItemTemplate>
                                                        <asp:LinkButton ID="lbtn_speed_edit" runat="server" OnClick="lbtn_speed_edit_Click"
                                                            CausesValidation="False">編輯</asp:LinkButton>
                                                        <asp:LinkButton ID="lbtn_speed_delete" runat="server" OnClick="lbtn_speed_delete_Click"
                                                            CausesValidation="False">刪除</asp:LinkButton>
                                                    </ItemTemplate>
                                                    <HeaderStyle CssClass="lineright"></HeaderStyle>
                                                    <ItemStyle CssClass="lineright"></ItemStyle>
                                                </asp:TemplateField>
                                                <asp:BoundField HeaderText="檔位" DataField="trsui_speedupgear" />
                                                <asp:BoundField HeaderText="車速(km/hr)" DataField="trsui_speed" />
                                            </Columns>
                                        </asp:GridView>
                                    </ContentTemplate>
                                </asp:UpdatePanel>
                            </td>
                        </tr>
                        <tr>
                            <td align="right">
                                <span style="color: Red;">*</span>加速選定條件：
                            </td>
                            <td colspan="3">
                                <asp:TextBox ID="txt_speedupcondi" runat="server" MaxLength="50" Width="300px"></asp:TextBox>
                                <asp:DropDownList ID="ddl_speedupcondi" runat="server" AutoPostBack="True" OnSelectedIndexChanged="ddl_speedupcondi_SelectedIndexChanged">
                                </asp:DropDownList>
                            </td>
                        </tr>
                        <tr>
                            <td align="right">
                                原地引擎轉速設定：
                            </td>
                            <td>
                                <asp:TextBox ID="txt_stayrpmparam" runat="server" MaxLength="50"></asp:TextBox>(rpm)<br />
                                <span style="color: blue;">如有限速,請於轉速後加註:(限)</span>
                            </td>
                            <td align="right">
                                原地設定條件：
                            </td>
                            <td>
                                <asp:TextBox ID="txt_stayrpmcondi" runat="server" MaxLength="50"></asp:TextBox>
                                <asp:DropDownList ID="ddl_stayrpmcondi" runat="server" AutoPostBack="True" OnSelectedIndexChanged="ddl_stayrpmcondi_SelectedIndexChanged">
                                </asp:DropDownList>
                            </td>
                        </tr>
                        <tr>
                            <td align="right">
                                <span style="color: Red;">*</span>加速噪音值：
                            </td>
                            <td>
                                <asp:TextBox ID="txt_speedupnoise" runat="server" MaxLength="6" AutoPostBack="True"
                                    OnTextChanged="txt_speedupnoise_TextChanged"></asp:TextBox>
                                <b>&nbsp;dB(A)</b><br />
                                <asp:Label ID="lab_speedupnoise" runat="server" Style="display: none;" ForeColor="Red"></asp:Label>
                            </td>
                            <td align="right">
                                <span style="color: Red;">*</span>原地噪音值：
                            </td>
                            <td>
                                <asp:TextBox ID="txt_staynoise" runat="server" MaxLength="6" AutoPostBack="True"
                                    OnTextChanged="txt_staynoise_TextChanged"></asp:TextBox>&nbsp;<b>dB(A)</b><br />
                                <asp:Label ID="lab_staynoise" runat="server" Style="display: none;" ForeColor="Red"></asp:Label>
                            </td>
                        </tr>
                        <tr>
                            <td align="right">
                                加速噪音標準值：
                            </td>
                            <td>
                                <asp:Label ID="lbl_speedupnoisestandardvalue" runat="server"></asp:Label>&nbsp;<b>dB(A)</b>
                            </td>
                            <td align="right">
                                原地噪音標準值：
                            </td>
                            <td>
                                <asp:Label ID="lbl_staynoisestandardvalue" runat="server"></asp:Label>&nbsp;<b>dB(A)</b>
                            </td>
                        </tr>
                        <tr>
                            <td align="right">
                                測定報告上傳：
                            </td>
                            <td colspan="3">
                                <asp:FileUpload ID="fupload_companfile" runat="server" CssClass="inputType" />
                                <asp:HyperLink ID="hlk_rptfileid" runat="server"></asp:HyperLink>&nbsp;&nbsp;<span
                                    style="color: #A9A9A9;">按下<b>完成修改</b>檔案才會上傳 </span>
                            </td>
                        </tr>
                        <tr>
                            <td align="right">
                                備註：
                            </td>
                            <td colspan="3">
                                <asp:Repeater ID="rpt_remark" runat="server" DataSourceID="SqlDataSource2" OnItemDataBound="rpt_remark_ItemDataBound">
                                    <HeaderTemplate>
                                        <table>
                                    </HeaderTemplate>
                                    <FooterTemplate>
                                        <tr>
                                            <td>
                                                <asp:CheckBox ID="cbOthers" runat="server" Text="其他" key='99' />
                                                (&nbsp;<asp:TextBox ID="tbxOthers" runat="server" Width="60px"></asp:TextBox>&nbsp;)
                                            </td>
                                        </tr>
                                        </table></FooterTemplate>
                                    <ItemTemplate>
                                        <tr>
                                            <td>
                                                <asp:CheckBox ID="cb" runat="server" Text='<%#Eval("ct_trr_desc") %>' Key='<%#Eval("ct_trr_id")%>' />
                                            </td>
                                        </tr>
                                    </ItemTemplate>
                                </asp:Repeater>
                                <asp:SqlDataSource ID="SqlDataSource2" runat="server" ConnectionString="<%$ ConnectionStrings:noise_t %>"
                                    SelectCommand="SELECT [ct_trr_id], [ct_trr_desc] FROM [nv_codetbl_testrptremark]">
                                </asp:SqlDataSource>
                            </td>
                        </tr>
                        <tr>
                            <td align="right">
                                資料最後修改人：
                            </td>
                            <td>
                                <asp:Label ID="lbl_modaccount" runat="server"></asp:Label>
                            </td>
                            <td align="right">
                                資料最後修改日期：
                            </td>
                            <td>
                                <asp:Label ID="lbl_moddate" runat="server"></asp:Label>
                            </td>
                        </tr>
                    </table>
                </ContentTemplate>
            </asp:UpdatePanel>
            <table align="center">
                <tr>
                    <td align="center" colspan="4">
                        <asp:Button ID="btn_Finish" runat="server" Text="完成修改" CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'"
                            onmouseout="this.className='btn_mouseout'" OnClick="btn_Finish_Click" AutoPostBack="True" />
                    </td>
                </tr>
            </table>
        </div>
    </div>
    <!--{* fullcontent end *}-->
    <div class="fullfooter">
    </div>
    <!--{* content end *}-->
</asp:Content>
