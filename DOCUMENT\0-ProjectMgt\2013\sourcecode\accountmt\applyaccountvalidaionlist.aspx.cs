﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;

public partial class accountmt_applyaccountvalidaionlist : BaseAdminPage
{
    wsCodetbl wscodetbl = new wsCodetbl();
    wsAccountInfo wsaccount = new wsAccountInfo();
    public DataTable dt_list
    {
        get { return (DataTable)ViewState["_dt_list"]; }
        set { ViewState["_dt_list"] = value; }
    }
    protected void Page_Load(object sender, EventArgs e)
    {
        #region 註冊轉成大寫的 javascript
        //統一編號
        txt_compidno.Attributes.Add("onkeypress", "javascript:this.value = this.value.toUpperCase();");
        txt_compidno.Attributes.Add("onblur", "javascript:this.value = this.value.toUpperCase();");
        #endregion
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = 'index.aspx';</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" && (GetAgentAccountGrpList().Contains("fis_adm") || GetAgentAccountGrpList().Contains("pg_adm") || GetAgentAccountGrpList().Contains("sys_adm")))
            {
                if (!IsPostBack)
                {
                    BindAccountStatus();

                    //D4 
                    cal_applyS.GetDate = DateTime.Now.Date.ToString("yyyyMMdd");
                    cal_applyE.GetDate = DateTime.Now.Date.ToString("yyyyMMdd");
                }
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }
    }
    private void BindAccountStatus()
    {
        string[] radom_codetbl = wsCodetblValidate(getRadNum());
        ddl_accountstatus.DataSource = wscodetbl.getAccountStatus(radom_codetbl[0].ToString(), radom_codetbl[1].ToString());
        ddl_accountstatus.DataTextField = "ct_as_desc";
        ddl_accountstatus.DataValueField = "ct_as_id";
        ddl_accountstatus.DataBind();

        //D4:申請中
        ddl_accountstatus.SelectedValue = "0";
    }
    private void BindData()
    {
        string[] radom_a = wsAccountValidate(getRadNum());
        DataTable dt_Search = wsaccount.getApplyAccountValidationList(radom_a[0].ToString(), radom_a[1].ToString(), ddl_accountstatus.SelectedValue
            , cal_applyS.GetDate, cal_applyE.GetDate, txt_compname.Text.Trim());
        if (dt_Search.Rows.Count > 0)
        {
            foreach (DataRow dr in dt_Search.Rows)
            {
                dr["compidno"] = DeCode(dr["compidno"].ToString());
            }
            string compQuery = "";
            if (txt_compidno.Text.Trim() != "")
            {
                compQuery = string.Format("compidno like '%{0}%'", txt_compidno.Text.Trim());
                DataRow[] dRow = dt_Search.Select(compQuery);
                dt_list = dt_Search.Clone();
                foreach (DataRow dr in dRow)
                {
                    dt_list.ImportRow(dr);
                }
                gv_Data.DataSource = dt_list;
                gv_Data.DataBind();
            }
            else
            {
                dt_list = dt_Search;
                gv_Data.DataSource = dt_list;
                gv_Data.DataBind();
            }
            this.gv_Data.PageIndex = 0;
        }
        else
        {
            gv_Data.DataSource = null;
            gv_Data.DataBind();
        }
    }
    protected void btn_Search_Click(object sender, EventArgs e)
    {
        Library.ExamineProcedure ep = new Library.ExamineProcedure();

        #region 特殊字元判斷-日期
        if (tbWord(cal_applyS.GetDate) || tbWord(cal_applyE.GetDate))
        {
            MessageBox.Show("日期選項含有系統不允許的特殊字元");
            return;
        }
        #endregion
        if (txt_compidno.Text.Trim() != "")
        {
            if (ep.IsLegal(txt_compidno.Text.Trim()))
            {
                MessageBox.Show("統一編號查詢不可輸入 ' -- / * 等字元");
                return;
            }
        }
        if (txt_compname.Text.Trim() != "")
        {
            if (ep.IsLegal(txt_compname.Text.Trim()))
            {
                MessageBox.Show("公司名稱查詢不可輸入 ' -- / * % 等字元");
                return;
            }
        }
        if (cal_applyS.GetDate != "")
        {
            if (!ep.IsInteger(cal_applyS.GetDate))
            {
                MessageBox.Show("申請日期查詢條件不正確");
                return;
            }
        }
        if (cal_applyE.GetDate != "")
        {
            if (!ep.IsInteger(cal_applyE.GetDate))
            {
                MessageBox.Show("申請日期查詢條件不正確");
                return;
            }
        }
        BindData();
    }
    protected void gv_Data_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        this.gv_Data.PageIndex = e.NewPageIndex;
        gv_Data.DataSource = dt_list;
        gv_Data.DataBind();
    }
    protected void gv_Data_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            Label lbl_compidno = (Label)e.Row.FindControl("lbl_compidno");
            string compidno = DataBinder.Eval(e.Row.DataItem, "compidno").ToString();
            lbl_compidno.Text = compidno;

            Button btn_Edit = (Button)e.Row.FindControl("btn_Edit");
            btn_Edit.Attributes["statusdesc"] = DataBinder.Eval(e.Row.DataItem, "statusdesc").ToString();
            btn_Edit.Attributes["kindtype"] = DataBinder.Eval(e.Row.DataItem, "kindtype").ToString();
            btn_Edit.Attributes["accountEdit"] = DataBinder.Eval(e.Row.DataItem, "accountEdit").ToString();
            btn_Edit.Attributes["compidnoEdit"] = DataBinder.Eval(e.Row.DataItem, "compidnoEdit").ToString();
        }
    }
    protected void Edit_Click(object sender, EventArgs e)
    {
        Button btn_Edit = (Button)sender;
        string statusdesc = btn_Edit.Attributes["statusdesc"].ToString();
        string kindtype = btn_Edit.Attributes["kindtype"].ToString();
        string accountEdit = btn_Edit.Attributes["accountEdit"].ToString();
        string compidnoEdit = btn_Edit.Attributes["compidnoEdit"].ToString();

        if (kindtype == "第一次申請")
        {
            //第一次申請accountvalidate.aspx
            Response.Redirect(string.Format("accountvalidate.aspx?status={0}&type={1}&account={2}&compidno={3}", Server.UrlEncode(EnCode(statusdesc)), Server.UrlEncode(EnCode(kindtype)), Server.UrlEncode(EnCode(accountEdit)), Server.UrlEncode(compidnoEdit)), false);
        }
        else if (kindtype == "非第一次申請")
        {
            //非第一次申請accountvalidate_notfirstaspx.aspx
            Response.Redirect(string.Format("accountvalidate_notfirstaspx.aspx?status={0}&type={1}&account={2}&compidno={3}", Server.UrlEncode(EnCode(statusdesc)), Server.UrlEncode(EnCode(kindtype)), Server.UrlEncode(EnCode(accountEdit)), Server.UrlEncode(compidnoEdit)), false);
        }
        else if (kindtype == "公司資料異動")
        {
            //公司資料異動accountvalidate_compdatachange.aspx
            Response.Redirect(string.Format("accountvalidate_compdatachange.aspx?status={0}&type={1}&account={2}&compidno={3}", Server.UrlEncode(EnCode(statusdesc)), Server.UrlEncode(EnCode(kindtype)), Server.UrlEncode(EnCode(accountEdit)), Server.UrlEncode(compidnoEdit)), false);
        }
    }
    protected void gv_Data_Sorting(object sender, GridViewSortEventArgs e)
    {
        if (ViewState["sortDirection"] == null)
        {
            ViewState["sortDirection"] = " ASC";
        }
        if (ViewState["SortExpression"] != null && ViewState["SortExpression"].ToString() == e.SortExpression)
        {
            if (ViewState["sortDirection"].ToString() == " DESC")
            {
                ViewState["sortDirection"] = " ASC";
            }
            else
            {
                ViewState["sortDirection"] = " DESC";
            }
        }
        else
        {
            ViewState["sortDirection"] = " ASC";

        }
        ViewState["SortExpression"] = e.SortExpression;

        dt_list.DefaultView.Sort = ViewState["SortExpression"].ToString() + ViewState["sortDirection"].ToString();
        gv_Data.DataSource = dt_list;
        gv_Data.DataBind();
    }
}