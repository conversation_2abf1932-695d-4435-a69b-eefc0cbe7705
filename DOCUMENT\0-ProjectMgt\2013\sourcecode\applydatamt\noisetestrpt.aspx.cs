﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.IO;
using System.Text;
using System.Drawing;
public partial class applydatamt_noisetestrpt : BaseAdminPage
{

    public DataTable dt_list
    {
        get { return (DataTable)ViewState["_dt_list"]; }
        set { ViewState["_dt_list"] = value; }
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = '../accountmt/index.aspx';</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" &&
                (
                (
                 GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("pg_adm") || GetAgentAccountGrpList().Contains("rs_adm") || GetAgentAccountGrpList().Contains("fis_adm") || GetAgentAccountGrpList().Contains("rv_adm")
                )
                ||
                (
                  (GetAgentAccountGrpList().Contains("cga_adm") || GetAgentAccountGrpList().Contains("cam_adm")) && CheckSingleApplyDataAccessRight(DeCode(Request["applyno"].ToString()))
                )
                )
                )
            {
                if (!IsPostBack)
                {

                    if (Request["applyno"].ToString() != "" && Request["carcompomodelno"].ToString() != "")
                    {
                        Cryptography crypy = new Cryptography();
                        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
                        int keynum = crypy.getRadNum();
                        string[] arr = crypy.wsApplybasedataDetailValid(keynum);

                        DataTable dt_applydatadetail = wsApplyData.get_applybasedate_detail(crypy.DeCode(Request["applyno"].ToString()), arr[0].ToString(), arr[1].ToString());
                        if (dt_applydatadetail.Rows.Count > 0)
                        {
                            //if (dt_applydatadetail.Rows[0]["abd_databelongcompidno"].ToString() == Session["compidno"].ToString())
                            //{
                            BindData();
                            dt_applydatadetail.Dispose();
                            //}
                            //else
                            //{
                            //    dt_applydatadetail.Dispose();
                            //    Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                            //    Response.End();
                            //}
                        }
                        else
                        {
                            dt_applydatadetail.Dispose();
                            Response.Write("<script languge='javascript'>alert('發生取不到申請編號錯誤，系統將導回首頁！！');window.location.href='../index.aspx'</script>");
                            Response.End();
                        }
                    }
                    else
                    {
                        Response.Write("<script languge='javascript'>alert('發生取不到申請編號錯誤，系統將導回首頁！！');window.location.href='../index.aspx'</script>");
                        Response.End();
                    }


                }
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }


    }

    #region 更改的麵包屑傳入的參數
    protected void Page_LoadComplete(object sender, EventArgs e)
    {
        ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text = ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text.Replace("applydatamt/carcompomodelList.aspx", "applydatamt/carcompomodelList.aspx?applyno=" + Server.UrlEncode(Request["applyno"].ToString()));
    }
    #endregion

    #region 申請型式除了 新申請之外，其餘皆不可填寫並導回查詢頁面
    public void CheckApplyType(string sApplyType)
    {
        string compidnoE = Session["compidno"].ToString(); //公司統編
        DataView dv = new MailParaControl().GetApplyBaseData(lbl_applyno.Text);
        string carcompomodelno = DeCode(Request["carcompomodelno"].ToString());//車型組編號
        string EngineFamily = dv[0]["EngineFamily"].ToString(); //引擎族
        Cryptography crypy = new Cryptography();
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        int keynum = getRadNum();
        string[] arr = wsApplybasedataDetailValid(keynum);
        //延伸、修改 沿用 且非系統第一筆不可編輯;非新車型 不可編輯
        if (wsApplyData.IsDateoneExistForTRP(compidnoE, carcompomodelno, EngineFamily, arr[0].ToString(), arr[1].ToString()))
        {
            Response.Write("<script languge='javascript'>alert('申請型式為「" + sApplyType + "」，不可編輯！！');window.location.href='carcompomodelList.aspx?applyno=" + Server.UrlEncode(Request["applyno"].ToString()) + "'</script>");
            Response.End();
        }

    }
    #endregion

    #region 判斷資料狀態不為填寫中、補件、拒件，則不可填寫並導回查詢頁面
    public void CheckApplyStatus(string sStatusCode, string sStatusDesc)
    {
        if (!(sStatusCode.Equals("10") || sStatusCode.Equals("40") || sStatusCode.Equals("90")))
        {
            Response.Write("<script languge='javascript'>alert('資料狀態為「" + sStatusDesc + "」，不可編輯！！');window.close();</script>");
            Response.End();
        }

    }
    #endregion

    public void BindData()
    {
        Cryptography crypy = new Cryptography();
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        wsCodetbl wsCodeTbl = new wsCodetbl();
        string applyno = DeCode(Request["applyno"].ToString());
        string carcompomodelno = DeCode(Request["carcompomodelno"].ToString());
        string carmodelno = DeCode(Request["carmodelno"].ToString());
        string strStatusCode = "";
        string strStatusDesc = "";
        string strApplyType = "";
        lbl_applyno.Text = applyno;
        lbl_carcompomodelno.Text = carcompomodelno;
        int keynum = crypy.getRadNum();
        string[] arr = crypy.wsApplybasedataDetailValid(keynum);

        dt_list = wsApplyData.get_Nv_cardetails(applyno, carcompomodelno, carmodelno, arr[0].ToString(), arr[1].ToString());

        if (dt_list.Rows.Count > 0)
        {
            lbl_testfactory.Text = dt_list.Rows[0]["ct_tf_desc"].ToString();
            //如果為電動車 加速噪音值和原地噪音值 自動帶出全形的"－"
            if (dt_list.Rows[0]["abd_powerfrom"].ToString().Equals("3"))
            {
                txt_speedupnoise.Text = "－";
                txt_staynoise.Text = "－";
            }

        }
        else
        {
            lbl_testfactory.Text = "-";
        }

        //申請型式
        DataTable dt_applyno = wsApplyData.get_applyno(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
        if (dt_applyno.Rows.Count > 0)
        {
            strApplyType = dt_applyno.Rows[0]["ct_at_desc"].ToString();
            CheckApplyType(strApplyType);
        }

        //資料狀態
        DataTable dt_applystatus = wsApplyData.get_applystatus(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
        if (dt_applystatus.Rows.Count > 0)
        {
            strStatusDesc = dt_applystatus.Rows[0]["ct_aps_desc"].ToString();
            strStatusCode = dt_applystatus.Rows[0]["abd_applystatus"].ToString();
            CheckApplyStatus(strStatusCode, strStatusDesc);
        }
        //取得所有車型名稱
        DataTable dt_carmodelnames = wsApplyData.get_Nv_carmodelnames(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
        if (dt_carmodelnames.Rows.Count > 0)
        {
            rpt_representvehicle.DataSource = dt_carmodelnames;
            rpt_representvehicle.DataBind();
        }
        //噪音標準值
        DataTable dt_noisestandarddata = wsApplyData.get_Nv_noisestandarddata(applyno, carcompomodelno, carmodelno, arr[0].ToString(), arr[1].ToString());
        if (dt_noisestandarddata.Rows.Count > 0)
        {
            lbl_speedupnoisestandardvalue.Text = dt_noisestandarddata.Rows[0]["nsd_speedupstandardvalue"].ToString();
            lbl_staynoisestandardvalue.Text = dt_noisestandarddata.Rows[0]["nsd_stationarystandardvalue"].ToString();
        }

        //加速進入檔位與進場車速
        if (txt_testrptno.Text.ToString() != "")
        {
            DataTable dt_speedupinfo = wsApplyData.get_Nv_speedupinfo(carcompomodelno, carmodelno, txt_testrptno.Text.ToString(), arr[0].ToString(), arr[1].ToString());
            gv_speedupinfo.DataSource = dt_speedupinfo;
            gv_speedupinfo.DataBind();
        }
        //加速選定條件
        BindSpeedupCondi();
        //原地設定條件
        BindStayrpmCondi();

        //製造地區
        Bindpct();


    }
    #region 加速選定條件
    private void BindSpeedupCondi()
    {
        wsCodetbl wscode = new wsCodetbl();
        ddl_speedupcondi.Items.Clear();

        string[] radom_cd = wsCodetblValidate(getRadNum());
        ddl_speedupcondi.DataSource = wscode.getSpeedupCondi(radom_cd[0].ToString(), radom_cd[1].ToString());
        ddl_speedupcondi.DataTextField = "ct_supc_desc";
        ddl_speedupcondi.DataValueField = "ct_supc_id";
        ddl_speedupcondi.DataBind();
        txt_speedupcondi.Text = ddl_speedupcondi.SelectedItem.Text;
    }
    #endregion

    #region 原地設定條件
    private void BindStayrpmCondi()
    {
        wsCodetbl wscode = new wsCodetbl();
        ddl_stayrpmcondi.Items.Clear();

        string[] radom_cd = wsCodetblValidate(getRadNum());
        ddl_stayrpmcondi.DataSource = wscode.getStayrpmCondi(radom_cd[0].ToString(), radom_cd[1].ToString());
        ddl_stayrpmcondi.DataTextField = "ct_srpmc_desc";
        ddl_stayrpmcondi.DataValueField = "ct_srpmc_id";
        ddl_stayrpmcondi.DataBind();
        txt_stayrpmcondi.Text = ddl_stayrpmcondi.SelectedItem.Text;
    }
    #endregion
    protected void btn_Finish_Click(object sender, EventArgs e)
    {

        #region 特殊字元判斷-有MasterPage(含Repeater.FooterTemplate.TextBox)
        ContentPlaceHolder mpContentPlaceHolder;
        mpContentPlaceHolder = (ContentPlaceHolder)Master.FindControl("ContentPlaceHolder1");
        foreach (object ctrl in mpContentPlaceHolder.Controls)
        {
            Control content = (Control)ctrl;
            foreach (Control c in content.Controls)
            {
                if (c is TextBox)
                {
                    TextBox objTextBox = default(TextBox);
                    objTextBox = (TextBox)c;
                    if ((tbWord(objTextBox.Text)))
                    {
                        MessageBox.Show(objTextBox.Text + ":含有系統不允許的特殊字元");
                        return;
                    }
                }
                else if (c is Repeater)
                {
                    Repeater myRepeater1 = (Repeater)mpContentPlaceHolder.FindControl("rp_remark");
                    foreach (object Rpc in myRepeater1.Controls[myRepeater1.Controls.Count - 1].Controls)
                    {

                        if (Rpc is TextBox)
                        {
                            TextBox objTextBox = default(TextBox);
                            objTextBox = (TextBox)Rpc;
                            if (tbWord(objTextBox.Text))
                            {
                                MessageBox.Show(objTextBox.Text + ":含有系統不允許的特殊字元");
                                return;
                            }
                        }

                    }
                }
            }
        }
        #endregion

        try
        {

            #region 檢查必填欄位
            //除沿用外製造地區為必填              
            if (ddl_producercountry.SelectedValue.ToString().Trim() == "0" && !(dt_list.Rows[0]["cpm_applytype"].ToString().Equals("2")))
            {
                MessageBox.Show("請選擇測試車製造地區");
                ddl_producercountry.Focus();
                return;
            }

            if (txt_testrptno.Text.Trim() == "")
            {
                ScriptManager.RegisterStartupScript(this.Page, this.Page.GetType(), "Fail", "alert('報告號碼未填寫');", true);

                txt_testrptno.Focus();
                return;
            }

            if (txt_engineno.Text.ToString().Trim() == "" && txt_carbodyno.Text.ToString().Trim() == "")
            {
                MessageBox.Show("引擎號碼或車身號碼請擇一填寫");
                txt_engineno.Focus();
                return;
            }
            if (txt_speedupcondi.Text.ToString().Trim() == "")
            {
                MessageBox.Show("加速選定條件尚未填寫");
                txt_speedupcondi.Focus();
                return;
            }
            if (txt_speedupnoise.Text.ToString().Trim() == "")
            {
                MessageBox.Show("加速噪音值尚未填寫");
                txt_speedupnoise.Focus();
                return;
            }
            if (txt_staynoise.Text.ToString().Trim() == "")
            {
                MessageBox.Show("原地噪音值尚未填寫");
                txt_staynoise.Focus();
                return;
            }
            if (fupload_companfile.FileName.Length > 0)
            {
                string[] strFileType = fupload_companfile.PostedFile.FileName.Split('.');
                string subFileName = strFileType[strFileType.GetUpperBound(0)];
            }
            if (fupload_companfile.FileName.Length == 0)
            {
                ScriptManager.RegisterStartupScript(this.Page, this.Page.GetType(), "Fail", "alert('您尚未選擇欲上傳的檔案');", true);
                return;
            }
            #endregion

            wsFileInfo wsfile = new wsFileInfo();
            Cryptography crypy = new Cryptography();
            wsCompany wscompany = new wsCompany();
            wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
            string applyno = DeCode(Request["applyno"].ToString());
            string carcompomodelno = DeCode(Request["carcompomodelno"].ToString());
            string[] radom_C = wsCompanyValidate(getRadNum());
            string[] radom_F = crypy.wsFileValidate(crypy.getRadNum());
            string[] radom_A = crypy.wsApplybasedataDetailValid(crypy.getRadNum());
            string strFileId = "";
            DataSet ds_company = wscompany.getCompanyBase(Session["compidno"].ToString(), radom_C[0].ToString(), radom_C[1].ToString());

            #region 測定報告上傳、新增噪音測定報告
            string filepath = ConfigurationManager.AppSettings["FilePath"].ToString();
            string strUpLoadPath = filepath + DateTime.Now.ToString("yyyy") + "\\" + applyno + "\\";

            //如果上傳路徑中沒有該目錄，則自動新增

            if (!Directory.Exists(strUpLoadPath.Substring(0, strUpLoadPath.LastIndexOf("\\"))))
            {
                Directory.CreateDirectory(strUpLoadPath.Substring(0, strUpLoadPath.LastIndexOf("\\")));
            }
            //原始檔名
            string[] strFileNm = fupload_companfile.PostedFile.FileName.Split('\\');
            string filename1 = strFileNm[strFileNm.GetUpperBound(0)];
            //系統名稱        
            string filename2 = DateTime.Now.ToString("yyyyMMddhhmmss") + "_" + strFileNm[strFileNm.GetUpperBound(0)];
            string strDescr = "測定報告上傳";
            strFileId = wsfile.insertapplydatafile("08", "1", filename1, filename2, strUpLoadPath, strDescr, GetAgentAccount(), radom_F[0].ToString(), radom_F[1].ToString());
            if (strFileId != "-1")
            {
                if (dt_list.Rows[0]["cmdg_carmodelno"].ToString() == "")
                {
                    //判斷噪音測定報告是否存在
                    wsApplyData.CheakDel_Nv_testrptspeedupinfo(lbl_carcompomodelno.Text.Trim(), txt_testrptno.Text.Trim(), radom_A[0].ToString(), radom_A[1].ToString());
                    gv_speedupinfo.DataBind();
                    MessageBox.Show("查無車型編號無法新增資料，請洽系統管理者");
                    return;

                }
                else
                {

                    //新增噪音測定報告            
                    wsApplyData.Insert_Nv_noisetestrpt(lbl_carcompomodelno.Text.Trim(), dt_list.Rows[0]["cmdg_carmodelno"].ToString(), txt_testrptno.Text.Trim(), txt_carbodyno.Text.Trim(), txt_engineno.Text.Trim(), txt_speedupcondi.Text.Trim(), txt_stayrpmparam.Text.Trim(), txt_stayrpmcondi.Text.Trim(), txt_speedupnoise.Text.Trim(), txt_staynoise.Text.Trim(), strFileId, GetAgentAccount(), ddl_producercountry.SelectedItem.Text.Trim(), radom_A[0].ToString(), radom_A[1].ToString()); /*table 的欄位不夠長，所以不加密儲存*/

                    #region 新增noisetestrpt_remark
                    //取得 Item 的控制項           
                    CheckBox cb;
                    foreach (RepeaterItem it in rp_remark.Items)
                    {
                        cb = (CheckBox)it.FindControl("cb");
                        if (cb != null)
                        {
                            if (cb.Checked)
                                wsApplyData.Insert_Nv_noisetestrpt_remark(lbl_carcompomodelno.Text.Trim(), dt_list.Rows[0]["cmdg_carmodelno"].ToString(), txt_testrptno.Text.Trim(), cb.Attributes["key"].ToString(), radom_A[0].ToString(), radom_A[1].ToString());
                        }
                    }

                    //取得 Footer 的控制項
                    CheckBox cbOthers = (CheckBox)rp_remark.Controls[rp_remark.Controls.Count - 1].Controls[0].FindControl("cbOthers");
                    TextBox tbxOthers = (TextBox)rp_remark.Controls[rp_remark.Controls.Count - 1].Controls[0].FindControl("tbxOthers");
                    if (cbOthers != null)
                    {
                        if (cbOthers.Checked)
                            //備註的存檔
                            wsApplyData.Insert_Nv_remark99(lbl_carcompomodelno.Text.Trim(), dt_list.Rows[0]["cmdg_carmodelno"].ToString(), txt_testrptno.Text.Trim(), cbOthers.Attributes["key"].ToString(), tbxOthers.Text, radom_A[0].ToString(), radom_A[1].ToString());
                    }
                    #endregion
                }

            }

            //實體檔案上傳
            string newfile = strUpLoadPath + filename2;
            fupload_companfile.PostedFile.SaveAs(newfile);
            #endregion

            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('存檔成功');window.close();</script>");
            BindData();
        }
        catch (Exception ex)
        {
            ex.Message.ToString();
            ScriptManager.RegisterStartupScript(this.Page, this.Page.GetType(), "Fail", "alert('存檔失敗');", true);
        }
    }
    protected void rp_remark_ItemDataBound(object sender, RepeaterItemEventArgs e)
    {
        if (e.Item.ItemType == ListItemType.Item || e.Item.ItemType == ListItemType.AlternatingItem)
        {
            if (DataBinder.Eval(e.Item.DataItem, "ct_trr_desc").ToString() == "其他")
            {
                e.Item.Visible = false;
            }
        }
    }

    protected void gv_speedupinfo_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {

            LinkButton lbtn_speed_edit = (LinkButton)e.Row.FindControl("lbtn_speed_edit");
            lbtn_speed_edit.Attributes["trsui_id"] = DataBinder.Eval(e.Row.DataItem, "trsui_id").ToString().Trim();
            lbtn_speed_edit.Attributes["trsui_speedupgear"] = DataBinder.Eval(e.Row.DataItem, "trsui_speedupgear").ToString().Trim();
            lbtn_speed_edit.Attributes["trsui_speed"] = DataBinder.Eval(e.Row.DataItem, "trsui_speed").ToString().Trim();
            LinkButton lbtn_speed_delete = (LinkButton)e.Row.FindControl("lbtn_speed_delete");
            lbtn_speed_delete.Attributes["trsui_id"] = DataBinder.Eval(e.Row.DataItem, "trsui_id").ToString().Trim();
            lbtn_speed_delete.Attributes["onclick"] = @"if (!confirm('是否確定要刪除?'))return false;";

        }
    }


    protected void lbtn_speed_edit_Click(object sender, EventArgs e)
    {
        LinkButton lbtn_editor = (LinkButton)sender;
        txt_rptspeedupgear.Text = lbtn_editor.Attributes["trsui_speedupgear"].ToString().Trim();
        txt_rptspeed.Text = lbtn_editor.Attributes["trsui_speed"].ToString().Trim();
        lbl_trsui_id.Text = lbtn_editor.Attributes["trsui_id"].ToString().Trim();
        ViewState["div_insert"] = "edit";

        div_insert.Style.Add("display", "inline");

    }

    protected void lbtn_speed_add_Click(object sender, EventArgs e)
    {
        //當報告號碼存在後才能編輯nv_testrptspeedupinfo
        ViewState["div_insert"] = "ins";
        ClearData();
        div_insert.Style.Add("display", "inline");

    }

    protected void lbtn_speed_delete_Click(object sender, EventArgs e)
    {
        LinkButton lbtn_speed_delete = (LinkButton)sender;

        try
        {

            Cryptography crypy = new Cryptography();
            wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
            int keynum = crypy.getRadNum();
            string[] arr = crypy.wsApplybasedataDetailValid(keynum);

            wsApplyData.Del_Nv_testrptspeedupinfo(lbtn_speed_delete.Attributes["trsui_id"].ToString().Trim(), arr[0].ToString(), arr[1].ToString());

            // ScriptManager.RegisterStartupScript(this.Page, this.Page.GetType(), "Success", "alert('刪除成功');", true);
            ClearData();
            div_insert.Style.Add("display", "none");
            ViewState["div_insert"] = "";
            BindData();
        }
        catch (Exception ex)
        {
            ex.Message.ToString();
            ScriptManager.RegisterStartupScript(this.Page, this.Page.GetType(), "Fail", "alert('刪除失敗');", true);
        }

    }
    protected void btn_Canl_Click(object sender, EventArgs e)
    {
        ClearData();
        div_insert.Style.Add("display", "none");
        ViewState["div_insert"] = "";
    }
    protected void btn_Ins_Click(object sender, EventArgs e)
    {
        Cryptography crypy = new Cryptography();
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        int keynum = crypy.getRadNum();
        string[] arr = crypy.wsApplybasedataDetailValid(keynum);

        #region 特殊字元判斷-有MasterPage(UpdatePanel裡的TextBox)
        ContentPlaceHolder mpContentPlaceHolder;
        mpContentPlaceHolder = (ContentPlaceHolder)Master.FindControl("ContentPlaceHolder1");
        foreach (object ctrl in mpContentPlaceHolder.Controls)
        {
            Control content = (Control)ctrl;
            foreach (Control ctr in content.Controls)
            {
                if (ctr is UpdatePanel)
                {
                    UpdatePanel myUpdatePanel1 = (UpdatePanel)mpContentPlaceHolder.FindControl("UpdatePanel1");
                    foreach (object Upc in myUpdatePanel1.Controls[0].Controls)
                    {
                        Control Upcontent = (Control)Upc;
                        foreach (Control Upctr in Upcontent.Controls)
                        {
                            if (Upctr is TextBox)
                            {
                                TextBox objTextBox = default(TextBox);
                                objTextBox = (TextBox)Upctr;
                                if (tbWord(objTextBox.Text))
                                {
                                    MessageBox.AjaxShow(UpdatePanel1, objTextBox.Text + ":含有系統不允許的特殊字元");
                                    return;
                                }
                            }
                        }
                    }

                }
            }

        }
        #endregion

        try
        {

            if (ViewState["div_insert"].ToString() == "ins")
            {
                if (txt_testrptno.Text.Trim() == "")
                {
                    MessageBox.AjaxShow(UpdatePanel1, "報告號碼未填寫");
                    txt_testrptno.Focus();
                    return;
                }
                else if (txt_rptspeedupgear.Text.Trim() == "")
                {
                    MessageBox.AjaxShow(UpdatePanel1, "加速進入檔位未填寫");
                    txt_rptspeedupgear.Focus();
                    return;
                }
                else if (txt_rptspeed.Text.Trim() == "")
                {
                    MessageBox.AjaxShow(UpdatePanel1, "加速進場車速未填寫");
                    txt_rptspeed.Focus();
                    return;
                }
                wsApplyData.Insert_Nv_testrptspeedupinfo(lbl_carcompomodelno.Text.Trim(), dt_list.Rows[0]["cmdg_carmodelno"].ToString(), txt_testrptno.Text.Trim(), txt_rptspeedupgear.Text.Trim(), txt_rptspeed.Text.Trim(), arr[0].ToString(), arr[1].ToString());
            }
            else if (ViewState["div_insert"].ToString() == "edit")
            {
                wsApplyData.Update_Nv_testrptspeedupinfo(txt_rptspeedupgear.Text.Trim(), txt_rptspeed.Text.Trim(), lbl_trsui_id.Text.Trim(), arr[0].ToString(), arr[1].ToString());
            }
            BindData();
            ClearData();
            div_insert.Style.Add("display", "none");
            ViewState["div_insert"] = "";

            //ScriptManager.RegisterStartupScript(this.Page, this.Page.GetType(), "Success", "alert('存檔成功');", true);
        }
        catch (Exception ex)
        {
            ex.Message.ToString();

            ScriptManager.RegisterStartupScript(this.Page, this.Page.GetType(), "Fail", "alert('存檔失敗');", true);
        }
    }

    private void ClearData()
    {
        txt_rptspeedupgear.Text = "";
        txt_rptspeed.Text = "";

    }
    protected void rpt_representvehicle_ItemDataBound(object sender, RepeaterItemEventArgs e)
    {

        Label lbl_carengmodelname = (Label)e.Item.FindControl("lbl_carengmodelname");

        if (e.Item.ItemType == ListItemType.Item || e.Item.ItemType == ListItemType.AlternatingItem)
        {
            //判斷是否為代表車
            if (DataBinder.Eval(e.Item.DataItem, "cmdg_berepresentativevehicle").ToString().Trim().Equals("Y", StringComparison.CurrentCultureIgnoreCase) || DataBinder.Eval(e.Item.DataItem, "cmdg_berepresentativevehicle").ToString() == "1")
            {
                lbl_carengmodelname.Text = DataBinder.Eval(e.Item.DataItem, "fullcarstylename").ToString().Trim() + "(代表車)";
            }
            else
            {
                lbl_carengmodelname.Text = DataBinder.Eval(e.Item.DataItem, "fullcarstylename").ToString().Trim();
            }

        }

    }
    protected void ddl_speedupcondi_SelectedIndexChanged(object sender, EventArgs e)
    {
        speedupcondiSelectChange();
    }
    protected void ddl_stayrpmcondi_SelectedIndexChanged(object sender, EventArgs e)
    {
        stayrpmcondiSelectChange();
    }

    private void speedupcondiSelectChange()
    {

        if (ddl_speedupcondi.SelectedValue.Equals("99"))
        {
            txt_speedupcondi.Text = ""; //加速選定條件            
        }
        else
        {
            txt_speedupcondi.Text = ddl_speedupcondi.SelectedItem.Text.Trim(); //加速選定條件
        }
    }

    private void stayrpmcondiSelectChange()
    {
        if (ddl_stayrpmcondi.SelectedValue.Equals("99"))
        {
            txt_stayrpmcondi.Text = ""; //加速選定條件            
        }
        else
        {
            txt_stayrpmcondi.Text = ddl_stayrpmcondi.SelectedItem.Text.Trim(); //加速選定條件           
        }
    }

    #region 測定報告上傳、新增噪音測定報告

    #endregion

    protected void txt_speedupnoise_TextChanged(object sender, EventArgs e)
    {
        Library.ExamineProcedure ep = new Library.ExamineProcedure();
        if (ep.IsNum(txt_speedupnoise.Text))
        {
            if (Convert.ToDouble(txt_speedupnoise.Text.Trim()) > Convert.ToDouble(lbl_speedupnoisestandardvalue.Text.Trim()))
            {
                lab_speedupnoise.Style.Add("display", "inline");
                lab_speedupnoise.Text = "提醒您本筆資料測試值已超過標準";
            }
            else
            {
                lab_speedupnoise.Style.Add("display", "none");
            }
        }
        else
        {
            lab_speedupnoise.Style.Add("display", "none");
        }
    }
    protected void txt_staynoise_TextChanged(object sender, EventArgs e)
    {
        #region 判斷是否超過標準
        Library.ExamineProcedure ep = new Library.ExamineProcedure();
        if (ep.IsNum(txt_staynoise.Text))
        {
            if (Convert.ToDouble(txt_staynoise.Text.Trim()) > Convert.ToDouble(lbl_staynoisestandardvalue.Text.Trim()))
            {
                lab_staynoise.Style.Add("display", "inline");
                lab_staynoise.Text = "提醒您本筆資料測試值已超過標準";
            }
            else
            {
                lab_staynoise.Style.Add("display", "none");
            }
        }
        else
        {
            lab_staynoise.Style.Add("display", "none");
        }
        #endregion
    }



    #region Bind製造地區
    private void Bindpct()
    {
        #region 製造地區
        Cryptography crypy = new Cryptography();
        int keynum = crypy.getRadNum();
        wsCodetbl wsCodeTbl = new wsCodetbl();
        string[] arr_wscodetbl = crypy.wsCodetblValidate(keynum);
        DataSet dsCountry = wsCodeTbl.getProdCountry(arr_wscodetbl[0].ToString(), arr_wscodetbl[1].ToString());

        ddl_producercountry.DataSource = dsCountry;
        ddl_producercountry.DataTextField = "ct_pcy_desc";
        ddl_producercountry.DataValueField = "ct_pcy_id";
        ddl_producercountry.DataBind();
        //ddl_producercountry.Items.Remove(ddl_producercountry.Items.FindByValue("CN1"));
        //ddl_producercountry.Items.Remove(ddl_producercountry.Items.FindByValue("KP1"));
        //ddl_producercountry.Items.Remove(ddl_producercountry.Items.FindByValue("ID1"));
        //ddl_producercountry.Items.Remove(ddl_producercountry.Items.FindByValue("FR1"));
        //ddl_producercountry.Items.Remove(ddl_producercountry.Items.FindByValue("KR1"));
        //ddl_producercountry.Items.Remove(ddl_producercountry.Items.FindByValue("KR2"));
        //ddl_producercountry.Items.Remove(ddl_producercountry.Items.FindByValue("DE1"));
        //ddl_producercountry.Items.Remove(ddl_producercountry.Items.FindByValue("AU1"));
        #endregion
    }
    #endregion

}