﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

using System.Data.SqlClient;
using System.Data;
using Microsoft.Reporting.WebForms;
using System.Configuration;
public partial class Certwork_cw_export_collection_rpt : BaseAdminPage
{
    protected void Page_Load(object sender, EventArgs e)
    {
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = '../accountmt/index.aspx';</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" && (GetAgentAccountGrpList().Contains("epa_adm") || GetAgentAccountGrpList().Contains("ac_adm") || GetAgentAccountGrpList().Contains("tc_adm") || GetAgentAccountGrpList().Contains("rv_adm") || GetAgentAccountGrpList().Contains("rs_adm") || GetAgentAccountGrpList().Contains("fis_adm") || GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("pg_adm")))
            {
                if (!IsPostBack)
                {
                    //BindData();
                    BindVechileTypeList();               
                    BindIsImport();

                }
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }
    }
    private void BindIsImport()
    {
        #region 國產/進口
        Cryptography crypy = new Cryptography();
        wsCodetbl wsCodeTbl = new wsCodetbl();
        string[] arr_im = crypy.wsCodetblValidate(crypy.getRadNum());
        DataSet dsIsImport = wsCodeTbl.getIsImport(arr_im[0].ToString(), arr_im[1].ToString());

        rdBtnLst_isimport.DataSource = dsIsImport;
        rdBtnLst_isimport.DataTextField = "ct_im_desc";
        rdBtnLst_isimport.DataValueField = "ct_im_id";
        rdBtnLst_isimport.DataBind();
        #endregion
    }

    private void BindVechileTypeList()
    {
        wsCodetbl wsCodeTbl = new wsCodetbl();
        string[] arr_nvc = wsCodetblValidate(getRadNum());
        cbl_vechiletypelist.DataSource = wsCodeTbl.getCarType(arr_nvc[0].ToString(), arr_nvc[1].ToString());
        cbl_vechiletypelist.DataTextField = "ct_ct_desc";
        cbl_vechiletypelist.DataValueField = "ct_ct_id";
        cbl_vechiletypelist.DataBind();

        #region 動力來源
        DataSet dsPowerFrom = wsCodeTbl.getPowerFrom(arr_nvc[0].ToString(), arr_nvc[1].ToString());
        rdBtnLst_powerfrom.DataSource = dsPowerFrom;
        rdBtnLst_powerfrom.DataTextField = "ct_pf_desc";
        rdBtnLst_powerfrom.DataValueField = "ct_pf_id";
        rdBtnLst_powerfrom.DataBind();
        #endregion
    }
    public void BindData()
    {
        Cryptography crypy = new Cryptography();
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        int keynum = crypy.getRadNum();
        string[] arr = crypy.wsApplybasedataDetailValid(keynum);
        string sVechiletype = string.Empty;
        string s_vechiletype = sVechiletype == "" ? " " : sVechiletype;
        string s_compname = txt_compname.Text.Trim() == "" ? " " : txt_compname.Text.Trim();
        foreach (ListItem item in cbl_vechiletypelist.Items)
        {
            if (item.Selected)
            {
                sVechiletype += item.Value + ",";
                s_vechiletype += item.Text + ",";
            }
        }
        #region 去逗號        
            sVechiletype = sVechiletype.TrimEnd(',');
            s_vechiletype = s_vechiletype.TrimEnd(',');       
        #endregion
        DataTable dt_exportcollection = wsApplyData.get_Nv_Exportcollection(txt_compname.Text.Trim(), rdBtnLst_isimport.SelectedValue, sVechiletype, rdBtnLst_powerfrom.SelectedValue.ToString(),cal_applydate_S.GetDate, cal_applydate_E.GetDate, arr[0].ToString(), arr[1].ToString());
       
        string s_isimport = rdBtnLst_isimport.SelectedValue.ToString().Trim() == "" ? " " : rdBtnLst_isimport.SelectedItem.ToString().Trim();
        //開啟Viewer顯示   
        rpv_MyData.Visible = true;
        //指定Viewer的rdlc檔   
        rpv_MyData.LocalReport.ReportPath = Server.MapPath("rpt_export_collection.rdlc");
        //清掉之前的狀態，重新把資料丟進去，並做Viewer的Refresh   
        rpv_MyData.LocalReport.DataSources.Clear();
        rpv_MyData.LocalReport.DataSources.Add(new ReportDataSource("DT_exportcollection", dt_exportcollection));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_vechiletype", s_vechiletype));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_isimport", s_isimport));
        rpv_MyData.LocalReport.SetParameters(new ReportParameter("P_compname", s_compname));
        rpv_MyData.LocalReport.Refresh();
    }
    protected void btn_Search_Click(object sender, EventArgs e)
    {
        #region 特殊字元判斷-沒有MasterPage
        foreach (Control ctl in Page.Form.Controls)
        {
            Control content = (Control)ctl;
            foreach (Control c in content.Controls)
            {
                if (c is TextBox)
                {
                    TextBox objTextBox = default(TextBox);
                    objTextBox = (TextBox)c;
                    if ((tbWord(objTextBox.Text)))
                    {
                        MessageBox.Show(objTextBox.Text + ":含有系統不允許的特殊字元");
                        return;
                    }
                }
            }
        }
        #endregion
        #region 特殊字元判斷-日期
        if (tbWord(cal_applydate_S.GetDate) || tbWord(cal_applydate_E.GetDate))
        {
            MessageBox.Show("日期選項含有系統不允許的特殊字元");
            return;
        }
        #endregion
        BindData();
    }
}