﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.IO;
using System.Text;

using System.Xml;
using Common;

public partial class applydatamt_fixcatalog : BaseAdminPage
{

    public DataTable dt_list
    {
        get { return (DataTable)ViewState["_dt_list"]; }
        set { ViewState["_dt_list"] = value; }
    }
    protected void Page_Load(object sender, EventArgs e)
    {
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = '../accountmt/index.aspx';</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" &&
                (
                (
                 GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("pg_adm") || GetAgentAccountGrpList().Contains("rs_adm") || GetAgentAccountGrpList().Contains("fis_adm") || GetAgentAccountGrpList().Contains("rv_adm")
                )
                ||
                (
                  (GetAgentAccountGrpList().Contains("cga_adm") || GetAgentAccountGrpList().Contains("cam_adm")) && CheckSingleApplyDataAccessRight(DeCode(Request["applyno"].ToString()))
                )
                )
                )
            {
                if (!IsPostBack)
                {
                    if (Request["applyno"].ToString() != "")
                    {
                        Cryptography crypy = new Cryptography();
                        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
                        int keynum = crypy.getRadNum();
                        string[] arr = crypy.wsApplybasedataDetailValid(keynum);
                        DataTable dt_applydatadetail = wsApplyData.get_applybasedate_detail(crypy.DeCode(Request["applyno"].ToString()), arr[0].ToString(), arr[1].ToString());
                        if (dt_applydatadetail.Rows.Count > 0)
                        {
                            //if (dt_applydatadetail.Rows[0]["abd_databelongcompidno"].ToString() == Session["compidno"].ToString())
                            //{

                            string[] arrA = crypy.wsApplybasedataDetailValid(keynum);
                            string applyno = DeCode(Request["applyno"].ToString());
                            string carcompomodelno = DeCode(Request["carcompomodelno"].ToString());
                            BindData();
                            //wsApplyData.Insert_toldfixcatalog(applyno, carcompomodelno, arrA[0].ToString(), arrA[1].ToString());
                            dt_applydatadetail.Dispose();
                            //}
                            //else
                            //{
                            //    dt_applydatadetail.Dispose();
                            //    Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                            //    Response.End();
                            //}
                        }
                        else
                        {
                            dt_applydatadetail.Dispose();
                            Response.Write("<script languge='javascript'>alert('發生取不到申請編號錯誤，系統將導回首頁！！');window.location.href='../index.aspx'</script>");
                            Response.End();
                        }
                    }
                    else
                    {
                        Response.Write("<script languge='javascript'>alert('發生取不到申請編號錯誤，系統將導回首頁！！');window.location.href='../index.aspx'</script>");
                        Response.End();
                    }
                }
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }
    }

    #region 更改的麵包屑傳入的參數
    protected void Page_LoadComplete(object sender, EventArgs e)
    {
        ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text = ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text.Replace("applydatamt/carcompomodelList.aspx", "applydatamt/carcompomodelList.aspx?applyno=" + Server.UrlEncode(Request["applyno"].ToString()));
    }
    #endregion

    #region 申請型式為沿用、不異動、撤件，則不可填寫並導回查詢頁面
    public void CheckApplyType(string sApplyType)
    {
        string compidnoE = Session["compidno"].ToString(); //公司統編
        DataView dv = new MailParaControl().GetApplyBaseData(lbl_applyno.Text);
        string carcompomodelno = DeCode(Request["carcompomodelno"].ToString());//車型組編號
        string EngineFamily = dv[0]["EngineFamily"].ToString(); //引擎族
        Cryptography crypy = new Cryptography();
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        int keynum = getRadNum();
        string[] arr = wsApplybasedataDetailValid(keynum);
        //延伸、修改 沿用 且非系統第一筆不可編輯;沿用(非第一筆)、不異動、撤銷不可編輯         
        //if (wsApplyData.IsDateoneExist(compidnoE, carcompomodelno, EngineFamily, arr[0].ToString(), arr[1].ToString()) )
        //{
        //    Response.Write("<script languge='javascript'>alert('申請型式為「" + sApplyType + "」，不可編輯！！');window.location.href='carcompomodelList.aspx?applyno=" + Server.UrlEncode(Request["applyno"].ToString()) + "'</script>");
        //    Response.End();
        //}        
    }
    #endregion

    #region 判斷資料狀態不為填寫中、補件、拒件，則不可填寫並導回查詢頁面
    public void CheckApplyStatus(string sStatusCode, string sStatusDesc)
    {
        string compidnoE = Session["compidno"].ToString(); //公司統編
        DataView dv = new MailParaControl().GetApplyBaseData(lbl_applyno.Text);
        string carcompomodelno = DeCode(Request["carcompomodelno"].ToString());//車型組編號
        string EngineFamily = dv[0]["EngineFamily"].ToString(); //引擎族
        Cryptography crypy = new Cryptography();
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        int keynum = getRadNum();
        string[] arr = wsApplybasedataDetailValid(keynum);

        if (!(sStatusCode.Equals("10") || sStatusCode.Equals("40") || sStatusCode.Equals("90")))
        {
            Response.Write("<script languge='javascript'>alert('資料狀態為「" + sStatusDesc + "」，不可編輯，系統將導回查詢頁面！！');window.location.href='nv_applylistqry.aspx'</script>");
            Response.End();
        }

    }
    #endregion

    public void BindData()
    {
        Cryptography crypy = new Cryptography();
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        int keynum = crypy.getRadNum();
        string[] arr = crypy.wsApplybasedataDetailValid(keynum);
        string applyno = DeCode(Request["applyno"].ToString());
        string carcompomodelno = DeCode(Request["carcompomodelno"].ToString());
        string strStatusCode = "";
        string strStatusDesc = "";
        string strApplyType = "";
        lbl_applyno.Text = applyno;
        lbl_carcompomodelno.Text = carcompomodelno;
        //申請型式
        DataTable dt_applyno = wsApplyData.get_applyno(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
        if (dt_applyno.Rows.Count > 0)
        {
            strApplyType = dt_applyno.Rows[0]["ct_at_desc"].ToString();
            CheckApplyType(strApplyType);
        }
        //資料狀態
        DataTable dt_applystatus = wsApplyData.get_applystatus(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
        if (dt_applystatus.Rows.Count > 0)
        {
            lbl_applystatus.Text = dt_applystatus.Rows[0]["ct_aps_desc"].ToString();
            strStatusDesc = dt_applystatus.Rows[0]["ct_aps_desc"].ToString();
            strStatusCode = dt_applystatus.Rows[0]["abd_applystatus"].ToString();
            CheckApplyStatus(strStatusCode, strStatusDesc);

        }
        dt_list = wsApplyData.get_fix_catalog2(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
        gv_data.DataSource = dt_list;
        gv_data.DataBind();
        if (new FirstProcessControl().IsValidationApplyType(applyno, carcompomodelno, "6") || new FirstProcessControl().IsValidationApplyType(applyno, carcompomodelno, "9"))
        {
            btn_Add.Visible = false;
        }
    }
    protected void gv_data_RowDataBound(object sender, GridViewRowEventArgs e)
    {

        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            string applyno = DeCode(Request["applyno"].ToString());
            string carcompomodelno = DeCode(Request["carcompomodelno"].ToString());
            LinkButton lbtn_editor = (LinkButton)e.Row.FindControl("lbtn_editor");
            //Label lbl_fixitem = (Label)e.Row.FindControl("lbl_fixitem");
            //Label lbl_beforefix = (Label)e.Row.FindControl("lbl_beforefix");
            //Label lbl_afterfix = (Label)e.Row.FindControl("lbl_afterfix");

            lbtn_editor.Attributes["fc_fixid"] = DataBinder.Eval(e.Row.DataItem, "fc_fixid").ToString().Trim();
            //lbl_fixitem.Text = DataBinder.Eval(e.Row.DataItem, "fc_fixitem").ToString().Trim();
            //lbl_beforefix.Text = DataBinder.Eval(e.Row.DataItem, "fc_beforefix").ToString().Trim();
            //lbl_afterfix.Text = DataBinder.Eval(e.Row.DataItem, "fc_afterfix").ToString().Trim();

            //當次新增才可以編輯
            if (DataBinder.Eval(e.Row.DataItem, "fc_applyno").ToString() != DataBinder.Eval(e.Row.DataItem, "fc_oriapplyno").ToString())
            {
                lbtn_editor.Enabled = false;
            }
            else if (new FirstProcessControl().IsValidationApplyType(applyno, carcompomodelno, "6") || new FirstProcessControl().IsValidationApplyType(applyno, carcompomodelno, "9"))
            {
                lbtn_editor.Enabled = false;
            }
            else
            {
                lbtn_editor.Enabled = true;
            }
        }


    }
    protected void btn_Add_Click(object sender, EventArgs e)
    {
        Response.Redirect(string.Format("fixcatalog_editor.aspx?command=add&applyno={0}&carcompomodelno={1}", Server.UrlEncode(Request["applyno"].ToString()), Server.UrlEncode(Request["carcompomodelno"].ToString()), false));
    }

    protected void lbtn_editor_Click(object sender, EventArgs e)
    {
        LinkButton lbtn_editor = (LinkButton)sender;
        Response.Redirect(string.Format("fixcatalog_editor.aspx?command=edit&applyno={0}&carcompomodelno={1}&fixid={2}", Server.UrlEncode(Request["applyno"].ToString()), Server.UrlEncode(Request["carcompomodelno"].ToString()), Server.UrlEncode(EnCode(lbtn_editor.Attributes["fc_fixid"].ToString().Trim())), false));
    }

    protected void gv_data_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        gv_data.PageIndex = e.NewPageIndex;
        BindData();
    }
    protected void btn_Save_Click(object sender, EventArgs e)
    {
        Response.Redirect(string.Format("carcompomodelList.aspx?applyno={0}", Server.UrlEncode(Request["applyno"].ToString()), false));
    }
}