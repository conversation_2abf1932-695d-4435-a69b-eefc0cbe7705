﻿<%@ Page Title="" Language="C#" MasterPageFile="~/masterpage/MasterPage.master" AutoEventWireup="true"
    CodeFile="compaccountlist.aspx.cs" Inherits="accountmt_compaccountlist" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
    <script language="javascript" type="text/javascript">
        function OpenCheckNG(account, username) {
            window.showModalDialog("AccountCheckNG.aspx?account=" + escape(account) + "&username=" + escape(username), "", "dialogWidth:600px;dialogHeight:150px;");
        }
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="fullheader">
        <table width="100%" cellspacing="0" cellpadding="0" class="fullheaderinfo">
            <tr>
                <td valign="top">
                    <b>公司帳號管理</b>
                </td>
                <td align="right" valign="top" class="font_loginInfo">
                </td>
            </tr>
        </table>
    </div>
    <!--{* fullheader end *}-->
    <div class="fullcontent">
        <div class="tableoutcome">
            <asp:GridView ID="gv_body" runat="server" AutoGenerateColumns="false" OnRowDataBound="gv_body_RowDataBound"
                Width="100%" CellSpacing="0" CellPadding="0" BorderWidth="0" GridLines="None"
                CssClass="font_fullcontent">
                <Columns>
                    <asp:TemplateField HeaderText="新帳號申請<br>確認結果">
                        <ItemTemplate>
                            <asp:Button ID="btn_OK" runat="server" Text="OK" OnClick="OK_Click" Visible="false"
                                CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'" onmouseout="this.className='btn_mouseout'" />
                            <asp:Button ID="btn_Error" runat="server" Text="有誤" OnClick="NG_Click" Visible="false"
                                CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'" onmouseout="this.className='btn_mouseout'" />
                        </ItemTemplate>                       
                        <HeaderStyle HorizontalAlign="Center" CssClass="lineright"></HeaderStyle>
                        <ItemStyle HorizontalAlign="Center" CssClass="lineright"></ItemStyle>
                    </asp:TemplateField>
                    <asp:TemplateField HeaderText="停用帳號" ItemStyle-CssClass="lineleft" HeaderStyle-CssClass="header">
                        <ItemTemplate>
                            <asp:Button ID="btn_Stop" runat="server" Text="停用" Visible="false" OnClick="Stop_Click"
                                OnClientClick="if (confirm('是否確定要停用此帳號?')==false) {return false;}" CssClass="btn_mouseout"
                                onmouseover="this.className='btn_mouseover'" onmouseout="this.className='btn_mouseout'" />
                            <asp:Label ID="lbl_account" runat="server" Text='<%# Eval("ai_account").ToString() %>'
                                Visible="false"></asp:Label>
                        </ItemTemplate>
                    </asp:TemplateField>
                    <asp:TemplateField HeaderText="帳號權限" ItemStyle-CssClass="lineleft" HeaderStyle-CssClass="header">
                        <ItemTemplate>
                            <asp:CheckBox ID="cb_usesystemN" runat="server" Enabled="false" Text="噪音" Checked="true" />
                            <asp:CheckBox ID="cb_usesystemP" runat="server" Enabled="false" Text="污染" />
                        </ItemTemplate>
                    </asp:TemplateField>
                    <asp:BoundField HeaderText="序號" DataField="RowNum" ItemStyle-CssClass="lineleft"
                        HeaderStyle-CssClass="header" ItemStyle-HorizontalAlign="Center" />
                    <asp:BoundField HeaderText="使用者姓名" DataField="ai_username" ItemStyle-CssClass="lineleft"
                        HeaderStyle-CssClass="header" />
                    <asp:BoundField HeaderText="使用者電話" DataField="ai_usertel" ItemStyle-CssClass="lineleft"
                        HeaderStyle-CssClass="header" />
                    <asp:BoundField HeaderText="使用者EMAIL" DataField="ai_email" ItemStyle-CssClass="lineleft"
                        HeaderStyle-CssClass="header" />
                    <asp:BoundField HeaderText="帳號狀態" DataField="ai_accountstatusdesc" ItemStyle-CssClass="lineright"
                        HeaderStyle-CssClass="lineright" ItemStyle-HorizontalAlign="Center" />
                </Columns>
            </asp:GridView>
        </div>
        <div id="div_subcontent" runat="server" >
            <table border="0" cellspacing="0" cellpadding="0" width="100%" class="font_fullcontent">
                <tr>
                    <td align="center">
                        <asp:Button ID="btn_Save" runat="server" Text="存檔" OnClick="btn_Save_Click" CssClass="btn_mouseout"
                            onmouseover="this.className='btn_mouseover'" onmouseout="this.className='btn_mouseout'" />
                    </td>
                </tr>
            </table>
        </div>
    </div>
    <!--{* fullcontent end *}-->
    <div class="fullfooter">
    </div>
    <!--{* content end *}-->
</asp:Content>
