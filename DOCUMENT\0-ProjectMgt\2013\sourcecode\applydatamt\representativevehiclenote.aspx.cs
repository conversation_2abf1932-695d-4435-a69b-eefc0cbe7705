﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.IO;
using System.Text;
using System.Drawing;

public partial class applydatamt_representativevehiclenote : BaseAdminPage
{

    public DataTable dt_list
    {
        get { return (DataTable)ViewState["_dt_list"]; }
        set { ViewState["_dt_list"] = value; }
    }
    protected void Page_Load(object sender, EventArgs e)
    {


        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = '../accountmt/index.aspx';</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" &&
                (
                (
                 GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("pg_adm") || GetAgentAccountGrpList().Contains("rs_adm") || GetAgentAccountGrpList().Contains("fis_adm") || GetAgentAccountGrpList().Contains("rv_adm")
                )
                ||
                (
                  (GetAgentAccountGrpList().Contains("cga_adm") || GetAgentAccountGrpList().Contains("cam_adm")) && CheckSingleApplyDataAccessRight(DeCode(Request["applyno"].ToString()))
                )
                )
                )
            {
                if (!IsPostBack)
                {
                    if (Request["applyno"].ToString() != "")
                    {
                        Cryptography crypy = new Cryptography();
                        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
                        int keynum = crypy.getRadNum();
                        string[] arr = crypy.wsApplybasedataDetailValid(keynum);
                        DataTable dt_applydatadetail = wsApplyData.get_applybasedate_detail(crypy.DeCode(Request["applyno"].ToString()), arr[0].ToString(), arr[1].ToString());
                        if (dt_applydatadetail.Rows.Count > 0)
                        {
                            //if (dt_applydatadetail.Rows[0]["abd_databelongcompidno"].ToString() == Session["compidno"].ToString())
                            //{
                            BindData();
                            dt_applydatadetail.Dispose();
                            //}
                            //else
                            //{
                            //    dt_applydatadetail.Dispose();
                            //    Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                            //    Response.End();
                            //}
                        }
                        else
                        {
                            dt_applydatadetail.Dispose();
                            Response.Write("發生取不到申請編號錯誤，系統將導回首頁！！");
                            Response.Redirect("../index.aspx");
                        }
                    }
                    else
                    {
                        Response.Write("發生取不到申請編號錯誤，系統將導回首頁！！");
                        Response.Redirect("../index.aspx");
                    }
                }
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }
    }
    #region 更改的麵包屑傳入的參數
    protected void Page_LoadComplete(object sender, EventArgs e)
    {
        ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text = ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text.Replace("applydatamt/carcompomodelList.aspx", "applydatamt/carcompomodelList.aspx?applyno=" + Server.UrlEncode(Request["applyno"].ToString()));
    }
    #endregion
    public void BindData()
    {
        Cryptography crypy = new Cryptography();
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        string applyno = DeCode(Request["applyno"].ToString());
        string carcompomodelno = DeCode(Request["carcompomodelno"].ToString());
        int keynum = crypy.getRadNum();
        string[] arr = crypy.wsApplybasedataDetailValid(keynum);
        lbl_applyno.Text = applyno;
        lbl_carcompomodelno.Text = carcompomodelno;

        dt_list = wsApplyData.get_Nv_combinecarlist(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());

        if (dt_list.Rows.Count > 0)
        {
            lbl_applystatus.Text = dt_list.Rows[0]["ct_aps_desc"].ToString();
            lbl_carstyleyear.Text = dt_list.Rows[0]["abd_carstyleyear"].ToString();
            lbl_databelongcompname.Text = dt_list.Rows[0]["abd_databelongcompname"].ToString();
            lbl_enginefamily.Text = dt_list.Rows[0]["abd_enginefamily"].ToString();

            lbl_carbrand.Text = dt_list.Rows[0]["cmdg_carbrand"].ToString();
            lbl_factoryname.Text = dt_list.Rows[0]["abd_factoryname"].ToString();
            //如果abd_isimport為1(國產)，cpm_importarea顯示在製造地區；若為2(進口)則cpm_importarea顯示在進口地區
            if (dt_list.Rows[0]["abd_isimport"].ToString() == "1")
            {
                //lbl_contacttel.Text = dt_list.Rows[0]["ct_cy_desc"].ToString();
                lbl_contacttel.Text = "中華民國";
            }
            else if (dt_list.Rows[0]["abd_isimport"].ToString() == "2")
            {
                lbl_importarea.Text = dt_list.Rows[0]["ct_cy_desc"].ToString();
            }
        }
        else
        {
            lbl_applystatus.Text = "-";
            lbl_carstyleyear.Text = "-";
            lbl_databelongcompname.Text = "-";
            lbl_enginefamily.Text = "-";
            lbl_carbrand.Text = "-";
            lbl_factoryname.Text = "-";
            lbl_contacttel.Text = "-";
        }

        DataTable dt_carmodeldatarpt = wsApplyData.get_Nv_carmodeldatarpt(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
        gv_carmodelrpt.DataSource = dt_carmodeldatarpt;
        gv_carmodelrpt.DataBind();

        #region 資料狀態-當申請型式 不為新申請 且有 50,55資料 取得上次申請代表車資料 否 擇讀取自己本身填寫資料
        DataTable dt_applystatus = wsApplyData.get_applystatus(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
        bool b_IsApplied = wsApplyData.IsApplied(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
        if (dt_applystatus.Rows.Count > 0 && !(dt_applystatus.Rows[0]["ct_aps_desc"].Equals("新申請")) && b_IsApplied)
        {

            DataTable dt_unNewApplyData = wsApplyData.get_unNewApplyData(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());
            if (dt_unNewApplyData.Rows.Count > 0)
            {
                applyno = dt_unNewApplyData.Rows[0]["cmdg_applyno"].ToString();
                carcompomodelno = dt_unNewApplyData.Rows[0]["cmdg_carcompomodelno"].ToString();
            }
        }
        #endregion
        //噪音測定報告
        DataTable dt_representativevehicle = wsApplyData.get_Nv_representativevehiclerpt(applyno, carcompomodelno, arr[0].ToString(), arr[1].ToString());

        if (dt_representativevehicle.Rows.Count > 0)
        {

            lbl_stayrpmparam.Text = dt_representativevehicle.Rows[0]["ntr_stayrpmparam"].ToString();
            lbl_stayrpmcondi.Text = dt_representativevehicle.Rows[0]["ntr_stayrpmcondi"].ToString();
            lbl_speedupcondi.Text = dt_representativevehicle.Rows[0]["ntr_speedupcondi"].ToString();

            //加速測定數值
            DataTable dt_vehiclespeedrpt = wsApplyData.get_Nv_vehiclespeedrpt(dt_representativevehicle.Rows[0]["ntr_testrptno"].ToString(), dt_representativevehicle.Rows[0]["ntr_carmodelno"].ToString(), arr[0].ToString(), arr[1].ToString());

            if (dt_vehiclespeedrpt.Rows.Count > 0)
            {
                rpt_speedupgear.DataSource = dt_vehiclespeedrpt;
                rpt_speedupgear.DataBind();
                rpt_speed.DataSource = dt_vehiclespeedrpt;
                rpt_speed.DataBind();

            }
        }


    }

    protected void gv_carmodelrpt_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        gv_carmodelrpt.PageIndex = e.NewPageIndex;
        BindData();
    }
    protected void gv_carmodelrpt_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        if (e.Row.RowType == DataControlRowType.DataRow)
        {
            Label lbl_carbodyno = (Label)e.Row.FindControl("lbl_carbodyno");
            Label lbl_maxhorsepower = (Label)e.Row.FindControl("lbl_maxhorsepower");
            string carbodyno = DataBinder.Eval(e.Row.DataItem, "ntr_carbodyno").ToString();
            string engineno = DataBinder.Eval(e.Row.DataItem, "ntr_engineno").ToString();
            string maxhorsepower = DataBinder.Eval(e.Row.DataItem, "cmdg_maxhorsepower").ToString();// +"kw / " + DataBinder.Eval(e.Row.DataItem, "cmdg_maxhorsepowerspeed").ToString() + "rpm"; //引擎最大馬力+ 轉速

            Cryptography crypy = new Cryptography();
            wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
            string applyno = DeCode(Request["applyno"].ToString());
            string carcompomodelno = DeCode(Request["carcompomodelno"].ToString());
            int keynum = crypy.getRadNum();
            string[] arr = crypy.wsApplybasedataDetailValid(keynum);
            //車身編號/引擎號碼
            DataTable dt_additionalbodyno = wsApplyData.get_Nv_additionalbodyno(applyno, carcompomodelno, DataBinder.Eval(e.Row.DataItem, "cmdg_carmodelno").ToString(), arr[0].ToString(), arr[1].ToString());

            if (dt_additionalbodyno.Rows.Count > 0)
            {
                string add_bodyno = dt_additionalbodyno.Rows[0]["abn_bodyno"].ToString().Trim() == "" ? carbodyno : dt_additionalbodyno.Rows[0]["abn_bodyno"].ToString().Trim();
                string add_engineno = dt_additionalbodyno.Rows[0]["abn_engineno"].ToString().Trim() == "" ? engineno : dt_additionalbodyno.Rows[0]["abn_engineno"].ToString().Trim();

                if (add_engineno == "")
                {
                    lbl_carbodyno.Text = add_bodyno;
                }
                else if (add_bodyno != "" && add_engineno != "")
                {
                    lbl_carbodyno.Text = add_bodyno + "/" + add_engineno;
                }
                else if (add_bodyno == "")
                {
                    lbl_carbodyno.Text = add_engineno;
                }
            }
            else
            {
                if (engineno == "")
                {
                    lbl_carbodyno.Text = carbodyno;
                }
                else if (carbodyno != "" && engineno != "")
                {
                    lbl_carbodyno.Text = carbodyno + "/" + engineno;
                }
                else if (carbodyno == "")
                {
                    lbl_carbodyno.Text = engineno;
                }
            }



            lbl_maxhorsepower.Text = maxhorsepower;
        }
    }



    protected void rpt_speedupgear_ItemDataBound(object sender, RepeaterItemEventArgs e)
    {
        Cryptography crypy = new Cryptography();
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        int keynum = crypy.getRadNum();
        string[] arr = crypy.wsApplybasedataDetailValid(keynum);      
        Label lbl_speedupgear = (Label)e.Item.FindControl("lbl_speedupgear");

        if (e.Item.ItemType == ListItemType.Item || e.Item.ItemType == ListItemType.AlternatingItem)
        {
            lbl_speedupgear.Text = DataBinder.Eval(e.Item.DataItem, "trsui_speedupgear").ToString().Trim();
        }
    }
    protected void rpt_speed_ItemDataBound(object sender, RepeaterItemEventArgs e)
    {
        Cryptography crypy = new Cryptography();
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        int keynum = crypy.getRadNum();
        string[] arr = crypy.wsApplybasedataDetailValid(keynum);
        Label lbl_speed = (Label)e.Item.FindControl("lbl_speed");

        if (e.Item.ItemType == ListItemType.Item || e.Item.ItemType == ListItemType.AlternatingItem)
        {
            //沿用不帶"-"表示
            lbl_speed.Text = DataBinder.Eval(e.Item.DataItem, "trsui_speed").ToString().Trim();

        }
    }
}
