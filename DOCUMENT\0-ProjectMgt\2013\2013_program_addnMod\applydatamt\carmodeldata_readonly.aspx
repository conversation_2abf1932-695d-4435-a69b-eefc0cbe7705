﻿<%@ Page Title="" Language="C#" MasterPageFile="~/masterpage/MasterPage.master" AutoEventWireup="true"
    CodeFile="carmodeldata_readonly.aspx.cs" Inherits="applydatamt_carmodeldata_readonly" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <asp:ScriptManager ID="ScriptManager1" runat="server">
    </asp:ScriptManager>
    <div class="fullheader">
        <table width="100%" cellspacing="0" cellpadding="0" class="fullheaderinfo">
            <tr>
                <td valign="top">
                    <b>
                        <asp:Label ID="Label1_Title" runat="server" Text="噪音合格證明申請-汽油車("></asp:Label>
                        <asp:Label ID="lbl_powerfrom" runat="server"></asp:Label>
                        <asp:Label ID="Label2_Title" runat="server" Text=")車輛規格表資料"></asp:Label>
                    </b>
                </td>
                <td align="right" class="font_loginInfo">
                </td>
            </tr>
        </table>
    </div>
    <div class="fullcontent">
        <div class="formstyle">
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="font_fullcontent">
                <tr>
                    <td align="right">
                        申請編號：
                    </td>
                    <td align="left">
                        <asp:Label ID="lbl_applyno" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        車型組代號：
                    </td>
                    <td align="left">
                        <asp:Label ID="lbl_carcompomodelno" runat="server"></asp:Label>
                        <asp:Label ID="lbl_applytype" runat="server" Visible="false"></asp:Label>
                        <asp:Label ID="lbl_carmodelno" runat="server" Visible="false"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        廠商名稱：
                    </td>
                    <td align="left">
                        <asp:Label ID="lbl_compname" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        車型年：
                    </td>
                    <td align="left">
                        <asp:Label ID="lbl_carstyleyear" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        代表車：
                    </td>
                    <td align="left" colspan="3">
                        <asp:Label ID="lbl_berepresentativevehicle" runat="server" />
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        製造地區：
                    </td>
                    <td align="left" colspan="3">
                        <asp:Label ID="lbl_producercountry" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        基本引擎：
                    </td>
                    <td align="left" colspan="3">
                        <asp:Label ID="tb_baseenginename" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        車型名稱：
                    </td>
                    <td align="left" colspan="3">
                        廠牌：<asp:Label ID="lbl_brand" runat="server"></asp:Label><br />
                        中文車型名稱：<asp:Label ID="tb_carchnmodelname" runat="server" Width="498px"></asp:Label><br />
                        英文車型名稱：<asp:Label ID="tb_carengmodelname" runat="server" Width="498px"></asp:Label><br />
                        車型代碼：<asp:Label ID="tb_carmodelcode" runat="server" Width="524px"></asp:Label><br />
                        排氣量：<asp:Label ID="lbl_exhaust" runat="server"></asp:Label><br />
                        排檔方式：<asp:Label ID="lbl_transmissiontype" runat="server"></asp:Label><br />
                        <div id="div_Ctv" runat="server">
                        排檔數：<asp:Label ID="lbl_transmissionnum" runat="server"></asp:Label><br />
                        </div>
                        <div id="Gate_Count" runat="server">
                            門數：<asp:Label ID="lbl_cardoorcount" runat="server"></asp:Label>
                        </div>
                        其他(車型名稱補充)：<asp:Label ID="tb_carmodeladd" runat="server" Width="300px"></asp:Label>
                        <div id="div_D_highlowgear" runat="server">
                            前進檔：<asp:Label ID="lbl_forwardgearnums" runat="server"></asp:Label>&nbsp;&nbsp;
                            後退檔：<asp:Label ID="lbl_backgearnums" runat="server"></asp:Label>&nbsp;&nbsp;
                            <asp:Label ID="lbl_D_highlowgear" runat="server"></asp:Label>
                        </div>
                    </td>
                </tr>
                <tr runat="server" id="Classification_of_vehicle_types">
                    <td align="right">
                        車種分類：
                    </td>
                    <td align="left" colspan="3">
                        <asp:Label ID="lbl_cartype" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        車身式樣：
                    </td>
                    <td align="left">
                        <asp:Label ID="tb_carbodyshapedesc" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="left" colspan="4">
                        <strong>車身尺度與重量</strong>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        全長：
                    </td>
                    <td align="left">
                        <asp:Label ID="tb_carbodylenth" runat="server"></asp:Label>mm
                    </td>
                    <td align="right">
                        全寬：
                    </td>
                    <td align="left">
                        <asp:Label ID="tb_carbodywidth" runat="server"></asp:Label>
                        mm
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        全高：
                    </td>
                    <td align="left">
                        <asp:Label ID="tb_carbodyhieght" runat="server"></asp:Label>
                        mm
                    </td>
                    <td align="right">
                        軸距：
                    </td>
                    <td align="left">
                        <asp:Label ID="tb_carwheelbase" runat="server"></asp:Label>
                        mm
                    </td>
                </tr>
                <tr>
                    <td id="Carwheelbase_f_title" runat="server" align="right">
                        輪距-前：
                    </td>
                    <td id="Carwheelbase_f_content" runat="server" align="left">
                        <asp:Label ID="tb_carwheelbase_f" runat="server"></asp:Label>
                        mm
                    </td>
                    <td id="Carwheelbase_b_title" runat="server" align="right">
                        輪距-後：
                    </td>
                    <td id="Carwheelbase_b_content" runat="server" align="left">
                        <asp:Label ID="tb_carwheelbase_b" runat="server"></asp:Label>
                        mm
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        空重：
                    </td>
                    <td align="left">
                        <asp:Label ID="tb_caremptyweight" runat="server"></asp:Label>
                        kg
                    </td>
                    <td id="Cartotal_weight_title" runat="server" align="right">
                        總重：
                    </td>
                    <td id="Cartotal_weight_content" runat="server" align="left">
                        <asp:Label ID="tb_cartotalweight" runat="server"></asp:Label>
                        kg
                    </td>
                </tr>
                <tr>
                    <td id="Passengers_title" runat="server" align="right">
                        乘坐人數：
                    </td>
                    <td id="Passengers_content" runat="server" align="left">
                        <asp:Label ID="tb_passengers" runat="server"></asp:Label>
                        人
                    </td>
                    <td id="Load_weight_title" runat="server" align="right">
                        載重：
                    </td>
                    <td id="Load_weight_content" runat="server" align="left">
                        <asp:Label ID="Load_weight_TextBox1" runat="server"></asp:Label>
                        kg
                    </td>
                </tr>
                <tr id="D_CarbodyWeight_title" runat="server">
                    <td align="right">
                        車體總重量：
                    </td>
                    <td id="Td1" runat="server" colspan="3">
                        <asp:Label ID="lbl_carbodyweight" runat="server"></asp:Label>&nbsp;kg
                    </td>
                </tr>
                <tr>
                    <td align="left" colspan="4">
                        <strong>基本引擎</strong>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        型式：
                    </td>
                    <td align="left">
                        <asp:Label ID="lbl_baseenginename" runat="server"></asp:Label>
                    </td>
                    <td id="Installation_location_title" runat="server" align="right">
                        安裝位置：
                    </td>
                    <td id="Installation_location_content" runat="server" align="left">
                        <asp:Label ID="lbl_setuppos" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        總排氣量：
                    </td>
                    <td align="left">
                        <asp:Label ID="lbl_exhaust2" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        <asp:Label ID="Label1_Bore_and_Inner_diameter01" runat="server" Text="Label"></asp:Label>X<asp:Label
                            ID="Stroke_stroke_Label1_title" runat="server"></asp:Label>：
                    </td>
                    <td id="Stroke_stroke_Label1_content" runat="server" align="left">
                        <asp:Label ID="tb_cylindermeters" runat="server" Width="79px"></asp:Label>X<asp:Label
                            ID="tb_cylinderstroke" runat="server" Width="84px"></asp:Label>mm
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        壓縮比：
                    </td>
                    <td align="left">
                        <asp:Label ID="tb_compressionratio" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        <asp:Label ID="lbl_colheader_cylinder" runat="server" Text="汽缸數："></asp:Label>
                    </td>
                    <td align="left">
                        <asp:Label ID="lbl_cylindernums" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        最大馬力：
                    </td>
                    <td align="left">
                        <asp:Label ID="tb_maxhorsepower" runat="server"></asp:Label>
                        kW
                    </td>
                    <td align="right">
                        最大馬力轉速：
                    </td>
                    <td align="left">
                        <asp:Label ID="tb_maxHorsepowerspeed" runat="server"></asp:Label>
                        rpm
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        最大扭力：
                    </td>
                    <td align="left">
                        <asp:Label ID="tb_torque" runat="server"></asp:Label>
                        kg-m
                    </td>
                    <td align="right">
                        最大扭力轉速：
                    </td>
                    <td align="left">
                        <asp:Label ID="tb_torquespeed" runat="server"></asp:Label>
                        rpm
                    </td>
                </tr>
                <tr id="D_maxhorsepower_Title" runat="server">
                    <td align="right">
                        馬達最大馬力：<br />
                        (僅供HYBID填寫)
                    </td>
                    <td align="left">
                        <asp:Label ID="lbl_maxhorsepower_hb" runat="server"></asp:Label>&nbsp;kW
                    </td>
                    <td align="right">
                        馬達最大馬力轉速：<br />
                        (僅供HYBID填寫)
                    </td>
                    <td align="left">
                        <asp:Label ID="lbl_maxhorsepowerspeed_hb" runat="server"></asp:Label>&nbsp;rpm
                    </td>
                </tr>
                <tr id="D_torque_Title" runat="server">
                    <td align="right">
                        馬達最大扭力：<br />
                        (僅供HYBID填寫)
                    </td>
                    <td align="left">
                        <asp:Label ID="lbl_torque_hb" runat="server"></asp:Label>&nbsp;kg-m
                    </td>
                    <td align="right">
                        馬達最大扭力轉速：<br />
                        (僅供HYBID填寫)
                    </td>
                    <td align="left">
                        <asp:Label ID="lbl_torquespeed_hb" runat="server"></asp:Label>&nbsp;rpm
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        <asp:Label ID="lbl_haveturbo" runat="server" Text="增壓器："></asp:Label>
                    </td>
                    <td align="left">
                        <asp:Label ID="lbl_turbo_cycles" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        冷卻系統：
                    </td>
                    <td align="left">
                        <asp:Label ID="lbl_coolsystem" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        供油方式：
                    </td>
                    <td align="left">
                        <asp:Label ID="lbl_provideoilmethod" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        油箱容量：
                    </td>
                    <td align="left">
                        <asp:Label ID="tb_oilcapacity" runat="server"></asp:Label>
                        公升
                    </td>
                </tr>
                <tr id="use_fuel_01" runat="server">
                    <td align="right">
                        使用燃料：
                    </td>
                    <td align="left">
                        <asp:Label ID="lbl_use_fuel" runat="server">
                        </asp:Label>
                    </td>
                    <td align="right">
                        安裝位置及方式：
                    </td>
                    <td align="left">
                        <asp:Label ID="lbl_Installation_location_manner" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right" id="Automotive_fuel_title" runat="server">
                        油料：
                    </td>
                    <td id="Automotive_fuel_title_content" runat="server" align="left">
                        <asp:Label ID="tb_929598" runat="server"></asp:Label>
                        辛烷值
                    </td>
                    <td id="Car_driven_approach_title" runat="server" align="right">
                        驅動方式：
                    </td>
                    <td id="Car_driven_approach_Dr" runat="server" align="left">
                        <asp:Label ID="lbl_drivetype" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr id="D_numofaxes_Title" runat="server">
                    <td align="right">
                        前軸軸數：
                    </td>
                    <td align="left">
                        <asp:Label ID="lbl_numofaxes_f" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        後軸軸數：
                    </td>
                    <td>
                        <asp:Label ID="lbl_numofaxes_b" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right" id="Start_up_mode_title" runat="server">
                        啟動方式：
                    </td>
                    <td id="Start_up_mode_content" runat="server" align="left">
                        <asp:Label ID="Start_up_mode_TextBox2" runat="server"></asp:Label>
                    </td>
                    <td id="The_location_and_direction_of_the_exhaust_port_title" runat="server" align="right">
                        排氣口位置及方向：
                    </td>
                    <td id="The_location_and_direction_of_the_exhaust_port_content" runat="server" align="left">
                        <asp:Label ID="The_location_and_direction_of_the_exhaust_port_TextBox1" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr id="Concentration_of_emissions" runat="server">
                    <td align="right">
                        排放廢氣濃度：
                    </td>
                    <td align="left" colspan="3">
                        <table>
                            <tr>
                                <td>
                                    粒狀汙染物：
                                </td>
                                <td>
                                    <asp:Label ID="tb_pm" runat="server" Width="120px"></asp:Label>
                                </td>
                                <td>
                                    一氧化碳：
                                </td>
                                <td>
                                    <asp:Label ID="tb_co" runat="server" Width="120px"></asp:Label>
                                </td>
                                <td>
                                    碳氫化合物：
                                </td>
                                <td>
                                    <asp:Label ID="tb_hc" runat="server" Width="120px"></asp:Label>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr id="PollutionPreventionControlSystem" runat="server">
                    <td align="left" colspan="4" class="style1">
                        <strong>防治污染系統</strong>
                    </td>
                </tr>
                <tr>
                    <td id="Automotive_Exhaust_System_title" runat="server" align="right">
                        排氣系統：
                    </td>
                    <td id="Automotive_Exhaust_System_content" runat="server" align="left">
                        <asp:Label ID="tb_exhaustsystem" runat="server"></asp:Label>
                    </td>
                    <td id="PCV_title" runat="server" align="right">
                        PCV：
                    </td>
                    <td id="PCV_content" runat="server" align="left">
                        <asp:Label ID="tb_pcv" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td id="EEC_title" runat="server" align="right">
                        EEC：
                    </td>
                    <td id="EEC_content" runat="server" align="left">
                        <asp:Label ID="tb_eec" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        &nbsp;
                    </td>
                    <td align="left">
                    </td>
                </tr>
                <tr>
                    <td align="left" colspan="4">
                        <strong>傳動系統</strong>
                    </td>
                </tr>
                <tr id="Reduction_ratio" runat="server">
                    <td align="right">
                        一次減速比：
                    </td>
                    <td align="left">
                        <asp:Label ID="Primary_reduction_ratio_TextBox3" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        二次減速比：
                    </td>
                    <td align="left">
                        <asp:Label ID="Secondary_reduction_ratio_TextBox4" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td id="Automobile_differential_type_title" runat="server" align="right">
                        差速器-型式：
                    </td>
                    <td id="Automobile_differential_type_content" runat="server" align="left">
                        <asp:Label ID="tb_differentialmodel" runat="server"></asp:Label>
                    </td>
                    <td id="Automobile_differential_gear_ratio_title" runat="server" align="right">
                        差速器-齒比：
                    </td>
                    <td id="Automobile_differential_gear_ratio_content" runat="server" align="left">
                        <asp:Label ID="tb_differentialgearratio" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        變速系統~型式：
                    </td>
                    <td align="left">
                        <asp:Label ID="tb_transmissionmodel" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        最高車速：
                    </td>
                    <td align="left">
                        <asp:Label ID="tb_highestspeed" runat="server"></asp:Label>
                        km/hr
                    </td>
                </tr>
                <tr id="D_increasepower_title" runat="server">
                    <td align="right">
                        加力箱-型式：
                    </td>
                    <td align="left">
                        <asp:Label ID="lbl_increasepowerstyle" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        加力箱-齒比：
                    </td>
                    <td align="left">
                        <asp:Label ID="lbl_increasepowerratio" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        變速系統~各檔齒比：
                    </td>
                    <td align="left">
                        <div id="Variable_speed_system" runat="server">
                        </div>
                    </td>
                    <td id="Transmission_system_reverse_gear_ratio_title" runat="server" align="right">
                        變速系統~倒檔齒比：
                    </td>
                    <td id="Transmission_system_reverse_gear_ratio_content" runat="server" align="left">
                        <asp:Label ID="tb_transmissionbackgearratio" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr id="D_climbratio_title" runat="server">
                    <td runat="server" align="right">
                        爬坡檔：
                    </td>
                    <td runat="server" align="left" colspan="4">
                        <asp:Label ID="lbl_climbratio" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        懸吊系統：
                    </td>
                    <td align="left" colspan="3">
                        前：<asp:Label ID="tb_suspensionsystem_f" runat="server"></asp:Label>
                        後：<asp:Label ID="tb_suspensionsystem_b" runat="server"></asp:Label>
                        <asp:Label ID="lbl_suspensionsystem_sub_title" runat="server" Text="補："></asp:Label>
                        <asp:Label ID="lbl_suspensionsystem_sub_content" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        輪胎規格：
                    </td>
                    <td align="left" colspan="3">
                        標準~前：<asp:Label ID="tb_tirespec_std_f" runat="server"></asp:Label>後：<asp:Label ID="tb_tirespec_std_b"
                            runat="server"></asp:Label><br />
                        選擇~前：<asp:Label ID="tb_tirespec_cho_f" runat="server"></asp:Label>後：<asp:Label ID="tb_tirespec_cho_b"
                            runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        受驗車重（kg）：
                    </td>
                    <td align="left">
                        <asp:Label ID="lbl_testweight" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        ★測試檔位總減速比：
                    </td>
                    <td align="left">
                        <asp:Label ID="tb_testgearreductionratio" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        冷卻風扇驅動方式：
                    </td>
                    <td align="left">
                        <asp:Label ID="tb_coolingdrivermethod" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        輪胎數量（不含備胎）：
                    </td>
                    <td align="left">
                        <asp:Label ID="tb_tiresnum" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        排氣管開口數量：
                    </td>
                    <td align="left">
                        <asp:Label ID="tb_exhaustpipenum" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        輪胎寬度：
                    </td>
                    <td align="left">
                        <asp:Label ID="tb_tirewidth" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        進氣方式：
                    </td>
                    <td align="left">
                        <asp:Label ID="tb_inletmode" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                    </td>
                    <td align="left">
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        備註：
                    </td>
                    <td align="left" colspan="3">
                        <asp:TextBox ID="txt_remark" runat="server" TextMode="MultiLine" Width="780px" Height="120px"
                            Enabled="False" ReadOnly="True"></asp:TextBox>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        資料最後修改人：
                    </td>
                    <td align="left">
                        <asp:Label ID="last_modaccount" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        資料最後修改日期：
                    </td>
                    <td align="left">
                        <asp:Label ID="last_moddate" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="center" colspan="4">
                        <asp:Button ID="btn_print" runat="server" Text="列印" CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'"
                            onmouseout="this.className='btn_mouseout'" OnClick="btn_print_Click" />
                    </td>
                </tr>
            </table>
        </div>
    </div>
    <div id="Regist_Select_div">
        <input runat="server" type="hidden" id="Regist_Select" />
    </div>
    <asp:HiddenField ID="hidden_flag" runat="server" />
    <script type="text/javascript">
        (function () { Users_Enter_value(); })();

        function return_Server_ID() {

            var Regist_Select_div = document.getElementById('Regist_Select_div');
            var ClientID = Regist_Select_div.getElementsByTagName('input')[0].value;

            return ClientID;
        }

        function Users_Enter_value() {

            var id = return_Server_ID();

            var DoropDown = document.getElementById(id);

            DoropDown.onchange = function () {
                var parent = this.parentNode;
                var input = parent.getElementsByTagName('input')[0];



                var n = this.options.length - 1;
                var currSelectIndex = this.selectedIndex;


                if (n == currSelectIndex) {
                    input.style.display = 'inline';
                } else { input.style.display = 'none'; }
            }
        }
    
    </script>
</asp:Content>
