﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Data;
using System.Data.SqlClient;

/// <summary>
/// Summary description for BaseAdminPage
/// </summary>
public class BaseAdminPage : Cryptography
{
    public BaseAdminPage()
	{
	}

    /// <summary>
    /// 取得帳號作業聯絡人資料
    /// </summary>
    /// <returns></returns>
    public string GetAccountContact()
    {
        wsCodetbl wscode = new wsCodetbl();
        string[] radom_CD = wsCodetblValidate(getRadNum());
        DataTable dt = wscode.getAccountContactList(radom_CD[0].ToString(), radom_CD[1].ToString());
        if (dt.Rows.Count > 0)
        {
            return dt.Rows[0]["ct_cat_desc"].ToString().Replace("\r\n", "<br/>");
        } return "";
    }

    /// <summary>
    /// 取得登入者之Email
    /// </summary>
    /// <returns></returns>
    public string GetAgentEmail()
    {
        if (Session["account"] == null)
            return "";
        string[] array = wsAccountValidate(getRadNum());
        wsAccountInfo wsaccount = new wsAccountInfo();
        DataSet ds = wsaccount.getAccountBase(DeCode(Session["account"].ToString()), array[0], array[1]);
        if (ds.Tables[0].Rows.Count > 0)
        {
            //ai_email
            return ds.Tables[0].Rows[0]["ai_email"].ToString();
        }
        else return "";
    }

    /// <summary>
    /// 取得登入者之聯絡電話
    /// </summary>
    /// <returns></returns>
    public string GetAgentTel()
    {
        if (Session["account"] == null)
            return "";
        string[] array = wsAccountValidate(getRadNum());
        wsAccountInfo wsaccount = new wsAccountInfo();
        DataSet ds = wsaccount.getAccountBase(DeCode(Session["account"].ToString()), array[0], array[1]);
        if (ds.Tables[0].Rows.Count > 0)
        {
            //ai_usertel
            return ds.Tables[0].Rows[0]["ai_usertel"].ToString();
        }
        else return "";
    }

    /// <summary>
    ///  取得登入者 的權限代碼清單
    /// </summary>
    /// <returns></returns>
    public string GetAgentAccountGrpList()
    {
        //ct_gd_roleID
        if (Session["account"] == null)
        { return ""; }
        else
        {
            string RoleIDList = string.Empty;
            wsGroupInfo wsgroup = new wsGroupInfo();
            string[] radom_G = wsGroupValidate(getRadNum());
            DataTable dt_list = wsgroup.getAccountBelongGrpRoleID(DeCode(Session["account"].ToString()), radom_G[0].ToString(), radom_G[1].ToString());
            if (dt_list.Rows.Count > 0)
            {
                foreach (DataRow dr in dt_list.Rows)
                {
                    RoleIDList += dr["ct_gd_roleID"].ToString() + ",";
                }
                ////權限：RD人員
                //if (Convert.ToBoolean(ConfigurationManager.AppSettings["DEBUGMode"].ToString()))
                //    if (DeCode(Session["account"].ToString()) == "528782") RoleIDList += "rd_adm" + ",";               
            }
            return RoleIDList == string.Empty ? "" : RoleIDList.Substring(0, RoleIDList.Length - 1);
        }
    }

    /// <summary>
    /// 取得登入者的申請狀態
    /// </summary>
    /// <returns></returns>
    public string GetAgentAccountstatus()
    {
        string[] array = wsAccountValidate(getRadNum());
        wsAccountInfo wsaccount = new wsAccountInfo();
        DataSet ds = wsaccount.getAccountBase(DeCode(Session["account"].ToString()), array[0], array[1]);
        if (ds.Tables[0].Rows.Count > 0)
        {
            //ai_accountstatus
            return ds.Tables[0].Rows[0]["ai_accountstatus"].ToString();
        }
        else return "";
    }

    /// <summary>
    /// 取得登入者的姓名
    /// </summary>
    /// <returns></returns>
    public string GetAgentName()
    {
        if (Session["account"] == null)
            return "";
        string[] array = wsAccountValidate(getRadNum());
        wsAccountInfo wsaccount = new wsAccountInfo();
        DataSet ds = wsaccount.getAccountBase(DeCode(Session["account"].ToString()), array[0], array[1]);
        if (ds.Tables[0].Rows.Count > 0)
        {
            //ai_username
            return ds.Tables[0].Rows[0]["ai_username"].ToString();
        }
        else return "";
    }
    /// <summary>
    /// 取得登入者的密碼
    /// </summary>
    /// <returns></returns>
    public string GetAgentPassword()
    {
        if (Session["account"] == null)
            return "";
        string[] array = wsAccountValidate(getRadNum());
        wsAccountInfo wsaccount = new wsAccountInfo();
        DataSet ds = wsaccount.getAccountBase(DeCode(Session["account"].ToString()), array[0], array[1]);
        if (ds.Tables[0].Rows.Count > 0)
        {
            //ai_password
            return ds.Tables[0].Rows[0]["ai_password"].ToString();
        }
        else return "";
    }

    /// <summary>
    ///  取得登入者的帳號
    /// </summary>
    /// <returns></returns>
    public string GetAgentAccount()
    {
        if (Session["account"] == null)
            return "";
        return DeCode(Session["account"].ToString());
    }

    /// <summary>
    /// 取得登入者的統一編號/身份証字號
    /// </summary>
    /// <returns></returns>
    public string GetAgentIDNo()
    {
        wsCompany wscompany = new wsCompany();
        string[] radom_C = wsCompanyValidate(getRadNum());
        DataSet ds = wscompany.getCompanyBase(Session["compidno"].ToString(), radom_C[0].ToString(), radom_C[1].ToString());
        if (ds.Tables[0].Rows.Count > 0)
        {
            //ci_compidno
            return DeCode(ds.Tables[0].Rows[0]["ci_compidno"].ToString());
        }
        else return "";
    }
    /// <summary>
    /// 取得登入者的公司名稱
    /// </summary>
    /// <returns></returns>
    public string GetAgentIDName()
    {
        wsCompany wscompany = new wsCompany();
        string[] radom_C = wsCompanyValidate(getRadNum());
        DataSet ds = wscompany.getCompanyBase(Session["compidno"].ToString(), radom_C[0].ToString(), radom_C[1].ToString());
        if (ds.Tables[0].Rows.Count > 0)
        {
            //ci_compname
            return ds.Tables[0].Rows[0]["ci_compname"].ToString();
        }
        else return "";
    }

    /// <summary>
    /// 取得登入者的地址
    /// </summary>
    /// <returns></returns>
    public string GetAgentAddr()
    {
        if (Session["account"] == null)
            return "";
        string[] array = wsAccountValidate(getRadNum());
        wsAccountInfo wsaccount = new wsAccountInfo();
        DataSet ds = wsaccount.getAccountBase(DeCode(Session["account"].ToString()), array[0], array[1]);
        if (ds.Tables[0].Rows.Count > 0)
        {
            //地址
            return ds.Tables[0].Rows[0]["ai_useraddrpostno"].ToString() + ds.Tables[0].Rows[0]["ct_city_desc"].ToString()
                + ds.Tables[0].Rows[0]["ct_zipcode_desc"].ToString() + ds.Tables[0].Rows[0]["ai_useraddr"].ToString();
        }
        else return "";
    }

    /// <summary>
    /// 登入者傳真
    /// </summary>
    /// <returns></returns>
    public string GetAgentFax()
    {
        if (Session["account"] == null)
            return "";
        string[] array = wsAccountValidate(getRadNum());
        wsAccountInfo wsaccount = new wsAccountInfo();
        DataSet ds = wsaccount.getAccountBase(DeCode(Session["account"].ToString()), array[0], array[1]);
        if (ds.Tables[0].Rows.Count > 0)
        {
            return ds.Tables[0].Rows[0]["ai_userfax"].ToString();
        }
        else return "";
    }

    #region 取代特殊字元
    public string Replace_Word(string word)
    {
        string p1 = "";
        if (word != null)
        {
            p1 = word.Replace("|", "｜");
            p1 = p1.Replace("&", "＆");
            p1 = p1.Replace(";", "；");
            p1 = p1.Replace("$", "＄");
            //p1 = p1.Replace("%", "％");
            //p1 = p1.Replace("@", "＠");
            p1 = p1.Replace("'", "’");
            p1 = p1.Replace("<", "＜");
            //p1 = p1.Replace("(", "（");
            p1 = p1.Replace("\"", "＼");
            p1 = p1.Replace(">", "＞");
            //p1 = p1.Replace(")", "）");
            p1 = p1.Replace("+", "＋");
            p1 = p1.Replace("#", "＃");
            //p1 = p1.Replace("CR", "ＣＲ");
            //p1 = p1.Replace("LF", "ＬＦ");
            p1 = p1.Replace("\\", "＼＼");
            p1 = p1.Replace("&lt", "＆ｌｔ");
            p1 = p1.Replace("&gt", "＆ｇｔ");
            p1 = p1.Replace(" on ", " ｏｎ ");
            p1 = p1.Replace("--", "－－");
        }
        return p1;

    }
    #endregion

    #region 去除特殊字元
    public string Clearn_Word(string word)
    {
        string p1 = "";
        p1 = word.Replace("|", "");
        p1 = p1.Replace("&", "");
        p1 = p1.Replace("$", "");
        p1 = p1.Replace("%", "");
        p1 = p1.Replace("@", "");
        p1 = p1.Replace("'", "");
        //p1 = p1.Replace("<", "");          
        p1 = p1.Replace("\"", "");
        p1 = p1.Replace("\'", "");
        //p1 = p1.Replace(">", "");           
        p1 = p1.Replace("#", "");
        p1 = p1.Replace("CR", "");
        p1 = p1.Replace("LF", "");
        p1 = p1.Replace("\\", "");
        p1 = p1.Replace("&lt", "");
        p1 = p1.Replace("&gt", "");
        p1 = p1.Replace(" on ", "");
        p1 = p1.Replace("--", "");
        p1 = p1.Replace("*", "");
        p1 = p1.Replace("ASCII 0x0d", "");
        p1 = p1.Replace("ASCII 0x0a", "");
        p1 = p1.Replace("ASCII 0x08", "");
        return p1;
    }
    #endregion

    #region 有特殊字元直接回傳true
    //傳URL用，請誤亂更改
    public bool tbWord(string word)
    {
        if (word.IndexOf("|") > -1 ||
        word.IndexOf("$") > -1 ||
        word.IndexOf("%") > -1 ||
        word.IndexOf("'") > -1 ||
            // (word.IndexOf("<") > -1 && word.IndexOf("<=") < 0) ||
        word.IndexOf("\"") > -1 ||
        word.IndexOf("\'") > -1 ||
            // (word.IndexOf(">") > -1 && word.IndexOf(">=") < 0) ||
        word.IndexOf("#") > -1 ||
         word.IndexOf("CR ") > -1 ||
        //word.IndexOf("LF ") > -1 ||
        word.IndexOf(" CR ") > -1 ||
        word.IndexOf(" LF ") > -1 ||
        word.IndexOf("\\") > -1 ||
        word.IndexOf("&lt") > -1 ||
        word.IndexOf("&gt") > -1 ||
        word.IndexOf(" on ") > -1 ||
        word.IndexOf("--") > -1 ||
        word.IndexOf("*") > -1 ||
        word.ToLower().IndexOf("script") > -1 ||
        word.ToLower().IndexOf("src=") > -1 ||
        word.ToLower().IndexOf("ASCII 0x0d") > -1 ||
        word.ToLower().IndexOf("ASCII 0x08") > -1 ||
        word.ToLower().IndexOf("alert") > -1 ||
        word.ToLower().IndexOf("ASCII 0x0a") > -1)
            return true;
        else
            return false;
    }
    #endregion
    
    public bool CheckSingleApplyDataAccessRight(string applyno)
    {
        wsCompany wscompany = new wsCompany();
        string[] radom_C = wsCompanyValidate(getRadNum());
        DataSet ds = wscompany.IsApplynoExist(applyno,Session["compidno"].ToString(), radom_C[0].ToString(), radom_C[1].ToString());

        if (!(ds.Tables[0].Rows[0]["applyno"].Equals(0)))
        {
            return true;
        }
        else return false;
                
    }

}
