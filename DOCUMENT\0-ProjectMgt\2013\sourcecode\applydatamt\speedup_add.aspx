﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="speedup_add.aspx.cs" Inherits="applydatamt_speedup_add" %>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head id="Head1" runat="server">
    <link href="../css/style.css" rel="stylesheet" type="text/css" />
     <script src="../js/jquery-1.6.4.min.js" type="text/javascript"></script>

    <title>新增加速測定數值</title>

</head>
<body>
    <form id="form1" runat="server">
    <div class="fullheader">
        <table cellpadding="0" cellspacing="0" class="fullheaderinfo" width="100%">
            <tr>
                <td valign="top">
                    <b>新增加速測定數值</b>
                </td>
                <td align="right" class="font_loginInfo" valign="top">
                </td>
            </tr>
        </table>
    </div>
    <div class="fullcontent">
        <div id="div_maintain" runat="server" class="formstyle">
            <table border="0" cellspacing="0" cellpadding="0" width="500" class="font_fullcontent">
                <tr>
                    <td align="right">
                        加速進入檔位：
                    </td>
                    <td>
                        <asp:TextBox ID="txt_speedupgear" runat="server"></asp:TextBox>
                    </td>
                     </tr>
                      <tr>
                    <td align="right">
                        加速進場車速：
                    </td>
                    <td>
                        <asp:TextBox ID="txt_speed" runat="server"></asp:TextBox>
                    </td>
                </tr>
                <tr>
                    <td align="center" colspan="2">
                        <asp:Button ID="btn_Add" runat="server" Text="存檔" CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'"
                            onmouseout="this.className='btn_mouseout'" OnClick="btn_Add_Click" />
                    </td>
                </tr>
            </table>
        </div>
    </div>
    <!--{* fullcontent end *}-->
    <div class="fullfooter">
    </div>
    <!--{* content end
    *}-->
    </form>
</body>
</html>
