﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Configuration;
using System.Text;
using System.Data;
using System.IO;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;
using System.Globalization;
using System.Xml;
using Common;
using System.Data.OleDb;



using System.Web.Security;
using System.Web.UI.WebControls.WebParts;



using iTextSharp.text;
using iTextSharp.text.pdf;
using iTextSharp.text.html;




public partial class Certwork_cw_rejectapplyprocess : BaseAdminPage
{
    protected void Page_Load(object sender, EventArgs e)
    {
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = '../accountmt/index.aspx';</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" && (GetAgentAccountGrpList().Contains("epa_adm") || GetAgentAccountGrpList().Contains("ac_adm") || GetAgentAccountGrpList().Contains("tc_adm") || GetAgentAccountGrpList().Contains("rv_adm") || GetAgentAccountGrpList().Contains("rs_adm") || GetAgentAccountGrpList().Contains("fis_adm") || GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("pg_adm")))
            {
                if (!IsPostBack)
                {
                    InitData();
                }
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }
    }

    private string ApplyNo
    {
        get
        {
            return Request["p"] == null ? string.Empty : DeCode(Request["p"].ToString());
        }
    }

    private void InitData()
    {
        lbl_CompanyName.Text = new RejectControl().GetCompanyName(ApplyNo);
        lbl_ApplyNo.Text = ApplyNo;
        lbl_Account.Text = GetAgentAccount();
        lbl_DenyTime.Text = DateTime.Now.ToString("yyyy/MM/dd");
    }

    private bool chkForm()
    {
        StringBuilder sb = new StringBuilder();
        if (string.IsNullOrEmpty(txt_DenyNo.Text)) { sb.Append("退件發文字號未填寫！\n"); }
        if (string.IsNullOrEmpty(cal_DenyDate.GetDate)) { sb.Append("退件發文日期未填寫！\n"); }
        if (string.IsNullOrEmpty(txt_DenyReason.Text)) { sb.Append("退件原因未填寫！\n"); }
        #region 特殊字元判斷-日期
        if (tbWord(cal_DenyDate.GetDate))
        {
            sb.Append(cal_DenyDate.GetDate + ":日期選項含有系統不允許的特殊字元！\n");
        }
        #endregion

        #region Reject特殊字元判斷-有MasterPage
        ContentPlaceHolder mpContentPlaceHolder;
        mpContentPlaceHolder = (ContentPlaceHolder)Master.FindControl("ContentPlaceHolder1");
        foreach (object ctrl in mpContentPlaceHolder.Controls)
        {
            if (ctrl is TextBox)
            {
                TextBox objTextBox = default(TextBox);
                objTextBox = (TextBox)ctrl;
                if ((tbWord(objTextBox.Text)))
                {
                    sb.Append(objTextBox.Text + ":含有系統不允許的特殊字元！\n");

                }
            }
        }
        #endregion
        if (sb.Length.Equals(0))
        {
            return true;
        }
        else
        {
            MessageBox.Show(sb.ToString());
            return false;
        }
    }

    //退件完成
    protected void btn_Search_Click(object sender, EventArgs e)
    {

        if (chkForm())
        {
            new RejectControl().UpdateDeny(ApplyNo, txt_DenyNo.Text, cal_DenyDate.GetDate, txt_DenyReason.Text);
            new FirstProcessControl().ChangeApplyStatus(ApplyNo, "70");//退件
            new FirstProcessControl().LogValidationProcess(ApplyNo, DBNull.Value, "70");//Log
            MailBreakNotify();//SendMail
            RegisterStartupScript("script", @"
<script>alert('已完成退件！'); window.location='cw_firstvalidationqry.aspx';</script>
");
        }
    }

    private void MailBreakNotify()
    {
        DataView dv = new MailParaControl().GetApplyBaseData(ApplyNo);
        string[] mailTo = new string[] { dv[0]["ContactMail"].ToString() };
        string[] mailCc = new string[1];//副本
        mailCc[0] = "";
        string[] mailBcc = new string[1];//密件
        mailBcc[0] = "";
        string ContactName = dv[0]["ContactName"].ToString();
        string CompName = dv[0]["CompName"].ToString();
        string EngineFamily = dv[0]["EngineFamily"].ToString();
        string BreakDate = DateTime.Now.ToString("yyyy/MM/dd");
        string ValidationMail = dv[0]["AccontMail"].ToString();
        new SendMail().Mail_BreakNotify(mailTo, mailCc, mailBcc, ContactName, ApplyNo, CompName, BreakDate, ValidationMail);
    }

    //產生退件說明單
    protected void btn_ReturnTable_Click(object sender, EventArgs e)
    {
        Cryptography crypy = new Cryptography();
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        string[] arr_apd = wsApplybasedataDetailValid(getRadNum());

        string scriptString = string.Empty;

        #region Reject特殊字元判斷-有MasterPage
        ContentPlaceHolder mpContentPlaceHolder;
        mpContentPlaceHolder = (ContentPlaceHolder)Master.FindControl("ContentPlaceHolder1");
        foreach (object ctrl in mpContentPlaceHolder.Controls)
        {
            if (ctrl is TextBox)
            {
                TextBox objTextBox = default(TextBox);
                objTextBox = (TextBox)ctrl;
                if ((tbWord(objTextBox.Text)))
                {
                    MessageBox.Show(objTextBox.Text + ":含有系統不允許的特殊字元");
                    return;
                }
            }
        }
        #endregion
        
            new RejectControl().UpdateDenyReason(ApplyNo, txt_DenyReason.Text);           
       
        //退件說明資料 -基本申請資料
        DataTable dt_rejectapply = wsApplyData.get_rejectapplydata(ApplyNo, arr_apd[0].ToString(), arr_apd[1].ToString()); 
        //車型號碼
        DataSet ds_carmodeno = wsApplyData.get_carcompomodeno(ApplyNo, arr_apd[0].ToString(), arr_apd[1].ToString());
        //退件說明資料 -退件表單
        DataSet ds_rejectItlist = wsApplyData.get_rejectapplyItlist(ApplyNo, arr_apd[0].ToString(), arr_apd[1].ToString());
        DataTable dt_rejectItlist = new DataTable();
        string sFileName = string.Format("Reject_{0}.pdf", DateTime.Now.ToString("yyyyMMddhhmmss"));
        string s_sql = string.Empty;

        #region 設定字型
        //BaseFont bfChinese = BaseFont.CreateFont(@"C:\Windows\Fonts\kaiu.ttf", BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);       
        string FontPath = Server.MapPath("kaiu.ttf");
        BaseFont bfChinese = BaseFont.CreateFont(FontPath, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
        Font ChFont = new Font(bfChinese, 12);
        Font ChFont_Title = new Font(bfChinese, 16);
        Font ChFont_msg = new Font(bfChinese, 10);
        Font ChFont_WHITE = new Font(bfChinese, 12,Font.ITALIC,iTextSharp.text.Color.WHITE); 
        #endregion
        Response.ContentType = "application/pdf";
        Response.ContentEncoding = System.Text.Encoding.UTF8;
        Response.AddHeader("Content-Disposition", "attachment;filename=" + Server.UrlEncode(sFileName)); //下載名稱    
        Response.Cache.SetCacheability(HttpCacheability.NoCache);


        StringWriter sw = new StringWriter();
        HtmlTextWriter hw = new HtmlTextWriter(sw);


        StringReader sr = new StringReader(sw.ToString());
        Document pdfDoc = new Document(PageSize.A4, 10f, 10f, 10f, 0f);
        MemoryStream ms = new MemoryStream();
        PdfWriter pw = PdfWriter.GetInstance(pdfDoc, ms);
        PdfWriter.GetInstance(pdfDoc, Response.OutputStream);

        #region 開啟檔案寫入內容後，將檔案關閉
        pdfDoc.Open();

        //設定表格需要幾攔幾列 (如果不設列,可能會在使用RowSpan或ColSpan發生錯誤)
        iTextSharp.text.Table Tb = new iTextSharp.text.Table(4,9);
        //設定表格的Padding
        Tb.Padding = 4;
        //自動填滿欄位(如果沒有填滿欄位,不會畫出欄位的線條)       
        Tb.AutoFillEmptyCells = true;       
        //抬頭
        string s_title = "申請編號( " + dt_rejectapply.Rows[0]["ApplyNo"].ToString() + " ) - 退件說明單";
        Paragraph Title = new Paragraph(s_title, ChFont_Title);
        //內容水平置中
        Title.SetAlignment("center");
        #region 基本申請資料
        string[][] x = new string[17][];
        x[0] = new string[2] { "申請編號：", dt_rejectapply.Rows[0]["ApplyNo"].ToString() };
        x[1] = new string[2] { "", "" };
        x[2] = new string[2] { "申請廠商：", dt_rejectapply.Rows[0]["Compname"].ToString() };
        x[3] = new string[2] { "聯絡人：", dt_rejectapply.Rows[0]["ContactName"].ToString() };
        x[4] = new string[2] { "聯絡人電話：", dt_rejectapply.Rows[0]["ContactTel"].ToString() };
        x[5] = new string[2] { "聯絡人電話Email：", dt_rejectapply.Rows[0]["abd_contactemail"].ToString() };
        x[6] = new string[2] { "交通工具種類：", dt_rejectapply.Rows[0]["CarType"].ToString() };
        x[7] = new string[2] { "引擎族：", dt_rejectapply.Rows[0]["EngineFamily"].ToString() };
        x[8] = new string[2] { "適用期別：", dt_rejectapply.Rows[0]["ApplicationDate"].ToString() };
        x[9] = new string[2] { "兩證合一申請：", dt_rejectapply.Rows[0]["TwoCertMethod"].ToString() };
        x[10] = new string[2] { "國產/進口：", dt_rejectapply.Rows[0]["isimport"].ToString() };
        x[11] = new string[2] { "製造廠：", dt_rejectapply.Rows[0]["FactoryName"].ToString() };
        x[12] = new string[2] { "車型年：", dt_rejectapply.Rows[0]["CarYear"].ToString() };
        x[13] = new string[2] { "申請函發文日期：", dt_rejectapply.Rows[0]["OfficeDocDate"].ToString() };
        x[14] = new string[2] { "申請函發文字號：", dt_rejectapply.Rows[0]["OfficeDocNo"].ToString() };
        x[15] = new string[2] { "合格證郵寄地址：", dt_rejectapply.Rows[0]["abd_mailcertidocaddr"].ToString() };
        x[16] = new string[2] { "退件原因：",  dt_rejectapply.Rows[0]["abd_denyreason"].ToString() };


        for (int i = 0; i <= 16; i++)
        {

            iTextSharp.text.Cell Tc;
            
            //Cell欄位名稱
            Tc = new iTextSharp.text.Cell(new Phrase(x[i][0].ToString(), ChFont));
            //內容水平置中
            Tc.HorizontalAlignment = Element.ALIGN_RIGHT;            
            //內容高度置中 (Top,Middle感覺不到有沒有移動)
            Tc.VerticalAlignment = Element.ALIGN_CENTER;
            //將Cell加入表格
            Tc.BorderColor = iTextSharp.text.Color.WHITE;
           
            Tb.BorderColor = iTextSharp.text.Color.WHITE;
            Tb.AddCell(Tc);

            //Cell欄位內容
            Tc = new iTextSharp.text.Cell(new Phrase(x[i][1].ToString(), ChFont_msg));
            //內容水平置中
            Tc.HorizontalAlignment = Element.ALIGN_LEFT;
            //內容高度置中(Top,Middle感覺不到有沒有移動)
            Tc.VerticalAlignment = Element.ALIGN_CENTER;
            if (i == 16)
            {
                Tc.Colspan = 3;
            }
            Tc.BorderColor = iTextSharp.text.Color.WHITE;
            Tb.BorderColor = iTextSharp.text.Color.WHITE;
            //Tb.Width = 120.0f;
           
            Tb.AddCell(Tc);

        }
        #endregion
        
        //加入抬頭
        pdfDoc.Add(Title);
        //把表格加入文件
        pdfDoc.Add(Tb);

        //開啟新的一頁
        pdfDoc.NewPage();

        #region 產生退件說明單
        for (int z = 0; z < ds_rejectItlist.Tables.Count; z++)
        {
            dt_rejectItlist = ds_rejectItlist.Tables[z]; // 有幾個車型資料就有幾個Table數           
            float[] widths = new float[] { 0.5f,5.0f, 4.0f };
            PdfPTable ptb = new PdfPTable(widths);                       
            //把表格加入文件
            for (int i = 0; i < dt_rejectItlist.Rows.Count; i++)
            {
                for (int j = 0; j < dt_rejectItlist.Columns.Count; j++)
                {
                    ptb.AddCell(new Phrase(dt_rejectItlist.Rows[i][j].ToString(), ChFont));
                    
                }
            }
            Paragraph subTitle = new Paragraph("車型組(" + ds_carmodeno.Tables[0].Rows[z]["CarCompoModel"].ToString() + ") - 審查表", ChFont_Title); //subTitle
            subTitle.SetAlignment("center");
            Paragraph subTitle2 = new Paragraph(".", ChFont_WHITE);
            subTitle2.SetAlignment("center");
            
            //新增車型組退件說明清單
            pdfDoc.Add(subTitle);
            pdfDoc.Add(subTitle2);
            pdfDoc.Add(ptb);
            pdfDoc.NewPage(); //開啟新的一頁
        }
        #endregion
        pdfDoc.Close();
        #endregion
        Response.Write(pdfDoc);
        Response.End();

        // 在Client端顯示PDF檔，讓USER可以下載
        Response.Clear();
        Response.AddHeader("Content-Disposition", "attachment;filename=" + Server.UrlEncode(sFileName)); //下載名稱        
        Response.ContentType = "application/octet-stream";
        Response.OutputStream.Write(ms.GetBuffer(), 0, ms.GetBuffer().Length);
        Response.OutputStream.Flush();
        Response.OutputStream.Close();
        Response.Flush();
        Response.End();
    }
}