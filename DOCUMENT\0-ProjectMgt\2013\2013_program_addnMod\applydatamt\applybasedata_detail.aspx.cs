﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.IO;
using System.Text;
using System.Globalization;

public partial class applydatamt_applybasedata_detail : BaseAdminPage
{

    public DataTable dt_list
    {
        get { return (DataTable)ViewState["_dt_list"]; }
        set { ViewState["_dt_list"] = value; }
    }

    protected void Page_Load(object sender, EventArgs e)
    {
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = '../accountmt/index.aspx';</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" &&
                (
                (
                GetAgentAccountGrpList().Contains("epa_adm") || GetAgentAccountGrpList().Contains("ac_adm") || GetAgentAccountGrpList().Contains("tc_adm") || GetAgentAccountGrpList().Contains("rv_adm") || GetAgentAccountGrpList().Contains("rs_adm") || GetAgentAccountGrpList().Contains("fis_adm") || GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("pg_adm")
                )
                ||
                (
                  (GetAgentAccountGrpList().Contains("cga_adm") || GetAgentAccountGrpList().Contains("cam_adm")) && CheckSingleApplyDataAccessRight(DeCode(Request["applyno"].ToString()))
                )
                )
                )
            {
                if (!IsPostBack)
                {
                    if (Request["applyno"].ToString() != "")
                    {
                        Cryptography crypy = new Cryptography();
                        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
                        int keynum = crypy.getRadNum();
                        string[] arr = crypy.wsApplybasedataDetailValid(keynum);
                        DataTable dt_applydatadetail = wsApplyData.get_applybasedate_detail(crypy.DeCode(Request["applyno"].ToString()), arr[0].ToString(), arr[1].ToString());
                        if (dt_applydatadetail.Rows.Count > 0)
                        {                            
                            BindData();
                            dt_applydatadetail.Dispose();                            
                        }
                        else
                        {
                            dt_applydatadetail.Dispose();
                            Response.Write("發生取不到申請編號錯誤，系統將導回首頁！！");
                            Response.Redirect("../index.aspx");
                        }
                    }
                    else
                    {
                        Response.Write("發生取不到申請編號錯誤，系統將導回首頁！！");
                        Response.Redirect("../index.aspx");
                    }
                }


            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }
    }

    #region 更改的麵包屑傳入的參數
    protected void Page_LoadComplete(object sender, EventArgs e)
    {
        ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text = ((Label)this.Master.FindControl("lbl_Breadcrumbs")).Text.Replace("applydatamt/carcompomodelList.aspx", "applydatamt/carcompomodelList.aspx?applyno=" + Server.UrlEncode(Request["applyno"].ToString()));
    }
    #endregion

    public void BindData()
    {

        Cryptography crypy = new Cryptography();
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        wsChangeCode ChangeCode = new wsChangeCode();
        int keynum = crypy.getRadNum();
        string[] arr_apd = crypy.wsApplybasedataDetailValid(keynum);
        string[] arr_A = crypy.wsChangeCodeValid(keynum);
        string applyno = DeCode(Request["applyno"].ToString());
        dt_list = wsApplyData.get_applybasedate_detail(applyno, arr_apd[0].ToString(), arr_apd[1].ToString());
        if (dt_list.Rows.Count > 0)
        {
            lbl_applyno.Text = dt_list.Rows[0]["abd_applyno"].ToString();  //申請編號
            lbl_applystatus.Text = dt_list.Rows[0]["ct_aps_desc"].ToString();//申請狀態(代碼)
            lbl_databelongcompname.Text = dt_list.Rows[0]["abd_databelongcompname"].ToString();
            lbl_vechiletype.Text = dt_list.Rows[0]["ct_ct_desc"].ToString();
            lbl_contactname.Text = dt_list.Rows[0]["abd_contactname"].ToString();
            lbl_contacttel.Text = dt_list.Rows[0]["abd_contacttel"].ToString();
            lbl_enginefamily.Text = dt_list.Rows[0]["abd_enginefamily"].ToString();
            lbl_carstyleyear.Text = dt_list.Rows[0]["abd_carstyleyear"].ToString();
            lbl_factoryname.Text = dt_list.Rows[0]["abd_factoryname"].ToString();
            lbl_enginesn.Text = dt_list.Rows[0]["abd_enginesn"].ToString();
            lbl_twocertmethod.Text = dt_list.Rows[0]["ct_twct_desc"].ToString();
            lbl_adaptstandarddate.Text = dt_list.Rows[0]["ct_sd_desc"].ToString();
            lbl_officedocdate.Text = dt_list.Rows[0]["abd_officedocdate"].ToString();
            lbl_officedocno.Text = dt_list.Rows[0]["abd_officedocno"].ToString();
            lbl_certidocaddr.Text = dt_list.Rows[0]["abd_mailcertidocaddr"].ToString();
            lbl_powerfrom.Text = dt_list.Rows[0]["ct_pf_desc"].ToString();
            Img_icon.ImageUrl = "../images/download-icon.png";
            hlk_userdocfileid.NavigateUrl = string.Format("../FileDownloadSeal.aspx?File={0}", Server.UrlEncode(EnCode(dt_list.Rows[0]["abd_userdocfileid"].ToString())));

        }
        DataTable dt_valprocess = wsApplyData.get_validationprocess(applyno, arr_apd[0].ToString(), arr_apd[1].ToString());
        if (dt_valprocess.Rows.Count > 0)
        {
            lbl_execdate.Text = dt_valprocess.Rows[0]["vp_execdate"].ToString();
            lbl_execaccount.Text = dt_valprocess.Rows[0]["vp_execaccount"].ToString();
        }
        else
        {
            lbl_execdate.Text = "-";
            lbl_execaccount.Text = "-";
        }

        DataTable dt_pay = wsApplyData.get_paydata(applyno, arr_apd[0].ToString(), arr_apd[1].ToString());
        gv_paydata.DataSource = dt_pay;
        gv_paydata.DataBind();

        DataTable dt_carmodeldata = wsApplyData.get_carmodeldata(applyno, arr_apd[0].ToString(), arr_apd[1].ToString());
        gv_data.DataSource = dt_carmodeldata;
        gv_data.DataBind();

        //送件且依條件顯示按鈕
        if ((dt_list.Rows[0]["abd_applystatus"].ToString() == "20" && (GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("rs_adm") || GetAgentAccountGrpList().Contains("fis_adm") || GetAgentAccountGrpList().Contains("rv_adm"))) || (dt_list.Rows[0]["abd_applystatus"].ToString() == "21" && (GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("rs_adm") || GetAgentAccountGrpList().Contains("fis_adm") || GetAgentAccountGrpList().Contains("rv_adm"))))
        {
            //收件
            btn_Accept.Visible = true;
        }
        if (dt_list.Rows[0]["abd_applystatus"].ToString() == "20" && (GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("rs_adm") || GetAgentAccountGrpList().Contains("fis_adm") || GetAgentAccountGrpList().Contains("rv_adm")))
        {
            //拒件
            btn_Accept.Visible = true;
            btn_Refuse.Visible = true;
        }
        if ((dt_list.Rows[0]["abd_applystatus"].ToString() == "20" && (GetAgentAccountGrpList().Contains("cam_adm") || GetAgentAccountGrpList().Contains("cga_adm"))))
        {
            //撤件
            btn_Withdraw.Visible = true;
        }

    }
    protected void gv_data_RowDataBound(object sender, GridViewRowEventArgs e)
    {
        Cryptography crypy = new Cryptography();
        
        if (e.Row.RowType == DataControlRowType.DataRow)
        {           
            Label lbl_carengmodelname = (Label)e.Row.FindControl("lbl_carengmodelname");

            HyperLink hlk_examineform = (HyperLink)e.Row.FindControl("hlk_examineform");
            HyperLink hlk_testreport = (HyperLink)e.Row.FindControl("hlk_testreport");
            HyperLink hlk_carcompomodelno = (HyperLink)e.Row.FindControl("hlk_carcompomodelno");
            HyperLink hlk_carcount = (HyperLink)e.Row.FindControl("hlk_carcount");

            string sCarcompomodelno = DataBinder.Eval(e.Row.DataItem, "cpm_carcompomodelno").ToString();
            string sCarcount = DataBinder.Eval(e.Row.DataItem, "carmodelnums").ToString();
            string sFullcarstylename = DataBinder.Eval(e.Row.DataItem, "fullcarstylename").ToString();

            lbl_carengmodelname.Text = sFullcarstylename;
            hlk_carcompomodelno.Text = sCarcompomodelno;
            hlk_carcount.Text = sCarcount;
            if (!sCarcount.Equals("0"))
            {
                hlk_examineform.NavigateUrl = string.Format("representativevehiclenote_readnoly.aspx?applyno={0}&carcompomodelno={1}", Server.UrlEncode(crypy.EnCode(lbl_applyno.Text)), Server.UrlEncode(crypy.EnCode(sCarcompomodelno)));
                hlk_testreport.NavigateUrl = string.Format("noisetestrpt_readnoly.aspx?applyno={0}&carcompomodelno={1}&carmodelno={2}", Server.UrlEncode(crypy.EnCode(lbl_applyno.Text)), Server.UrlEncode(crypy.EnCode(sCarcompomodelno)), Server.UrlEncode(EnCode(DataBinder.Eval(e.Row.DataItem, "carmodelno").ToString().Trim())));
                hlk_carcount.NavigateUrl = string.Format("carcompomodel_readnoly.aspx?applyno={0}&carcompomodelno={1}", Server.UrlEncode(crypy.EnCode(lbl_applyno.Text)), Server.UrlEncode(crypy.EnCode(sCarcompomodelno)));
            } 
            hlk_carcompomodelno.NavigateUrl = string.Format("carcompomodel_readnoly.aspx?applyno={0}&carcompomodelno={1}", Server.UrlEncode(crypy.EnCode(lbl_applyno.Text)), Server.UrlEncode(crypy.EnCode(sCarcompomodelno)));

            HyperLink hlk_noisedefenseplan = (HyperLink)e.Row.FindControl("hlk_noisedefenseplan");
            hlk_noisedefenseplan.ImageUrl = "../images/show.GIF";
            hlk_noisedefenseplan.NavigateUrl = string.Format("noisedefenseplan_readnoly.aspx?applyno={0}&carcompomodelno={1}", Server.UrlEncode(crypy.EnCode(lbl_applyno.Text)), Server.UrlEncode(crypy.EnCode(sCarcompomodelno)));
            HyperLink hlk_fixcatalog = (HyperLink)e.Row.FindControl("hlk_fixcatalog");
            hlk_fixcatalog.ImageUrl = "../images/show.GIF";
            hlk_fixcatalog.NavigateUrl = string.Format("fixcatalog_readnoly.aspx?applyno={0}&carcompomodelno={1}", Server.UrlEncode(crypy.EnCode(lbl_applyno.Text)), Server.UrlEncode(crypy.EnCode(sCarcompomodelno)));
            HyperLink hlk_uploadfileinfo = (HyperLink)e.Row.FindControl("hlk_uploadfileinfo");
            hlk_uploadfileinfo.ImageUrl = "../images/show.GIF";
            hlk_uploadfileinfo.NavigateUrl = string.Format("otherfileupload_readnoly.aspx?applyno={0}&carcompomodelno={1}", Server.UrlEncode(crypy.EnCode(lbl_applyno.Text)), Server.UrlEncode(crypy.EnCode(sCarcompomodelno)));

        }
    }

    protected void btn_Accept_Click(object sender, EventArgs e)
    {
        //收件
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        Cryptography crypy = new Cryptography();
        int keynum = crypy.getRadNum();
        string[] arr = crypy.wsApplybasedataDetailValid(keynum);
        string applyno = DeCode(Request["applyno"].ToString());
        //新增審驗歷程並更新nv_applybasedata的狀態為初審
        string return_result = wsApplyData.Insert_Nv_validationprocess(applyno, "31", "80", "", GetAgentAccount(), arr[0].ToString(), arr[1].ToString());
        //MailReceiverNotify();//SendMail
        MessageBox.Show(return_result);
        //收件後，導到收件查詢
        ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>window.location = '../certwork/cw_applyreceiveqryt.aspx';</script>");

    }
    protected void btn_Refuse_Click(object sender, EventArgs e)
    {
        //拒件
        Button btn_Refuse = (Button)sender;
        Cryptography crypy = new Cryptography();
        int keynum = crypy.getRadNum();
        string scriptString = "";
        scriptString = string.Format(@"var Mleft = (screen.width - 600) / 2;var Mtop = (screen.height - 250) / 2;window.open('editRefuseData.aspx?applyno={0}&applystatus={1}',10,'height=250,width=550,top='+Mtop+',left='+Mleft+''); ", Server.UrlEncode(Request["applyno"].ToString()), Server.UrlEncode(crypy.EnCode(dt_list.Rows[0]["abd_applystatus"].ToString())));
        this.ClientScript.RegisterStartupScript(this.GetType(), "", scriptString, true);

    }
    protected void btn_Withdraw_Click(object sender, EventArgs e)
    {
        //撤件
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        Cryptography crypy = new Cryptography();
        int keynum = crypy.getRadNum();
        string[] arr = crypy.wsApplybasedataDetailValid(keynum);
        string applyno = DeCode(Request["applyno"].ToString());
        //更新nv_applybasedata的狀態填單中
        wsApplyData.Update_Nv_applystatus(applyno, "10", arr[0].ToString(), arr[1].ToString());
        MailRetractNotify();//SendMail
        //撤件後，導到申請資料查詢
        ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>window.location = 'nv_applylistqry.aspx';</script>");
    }
    protected void gv_data_PageIndexChanging(object sender, GridViewPageEventArgs e)
    {
        gv_data.PageIndex = e.NewPageIndex;
        BindData();
    }
    protected void btnReflash_Click(object sender, EventArgs e)
    {
        //拒件後，導到收件查詢
        ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>window.location = '../certwork/cw_applyreceiveqryt.aspx';</script>");
    }
    //收件mail通知
    private void MailReceiverNotify()
    {
        string applyno = DeCode(Request["applyno"].ToString());
        DataView dv = new MailParaControl().GetApplyBaseData(applyno);
        string CompName = dv[0]["CompName"].ToString();
        string EngineFamily = dv[0]["EngineFamily"].ToString();
        string[] arr_GroupAccount = { "02" };//審驗人員02,03,04,05
        //通知全部審驗人員02,03,04,05
        for (int j = 0; j < arr_GroupAccount.Length; j++)
        {
            DataView GropuAccount = new MailParaControl().GetGroupAccount(arr_GroupAccount[j]);

            for (int i = 0; i < GropuAccount.Count; i++)
            {
                string AccountName = GropuAccount[i]["AccountName"].ToString();
                string AccountMail = GropuAccount[i]["AccountMail"].ToString();
                AccountMail = "<EMAIL>";
                string[] mailTo = new string[] { AccountMail };
                string[] mailCc = new string[1];//副本
                mailCc[0] = "";
                string[] mailBcc = new string[1];//密件
                mailBcc[0] = "";

                new SendMail().Mail_ReceiverNotify(mailTo, mailCc, mailBcc, AccountName, lbl_applyno.Text.Trim(), CompName);

            }
        }
    }
    //撤件通知
    private void MailRetractNotify()
    {
        DataView dv = new MailParaControl().GetApplyBaseData(lbl_applyno.Text.Trim());
        string CompName = dv[0]["CompName"].ToString();
        string ContactName = dv[0]["ContactName"].ToString();
        string EngineFamily = dv[0]["EngineFamily"].ToString();
        string StrCarCompoNo = new FirstProcessControl().StrCarCompoNo(lbl_applyno.Text.Trim());
        string[] arr_GroupAccount = { "02" };//審驗人員02,03,04,05
        //通知全部審驗人員02,03,04,05
        for (int j = 0; j < arr_GroupAccount.Length; j++)
        {
            DataView GropuAccount = new MailParaControl().GetGroupAccount(arr_GroupAccount[j]);

            for (int i = 0; i < GropuAccount.Count; i++)
            {
                string AccountName = GropuAccount[i]["AccountName"].ToString();
                string AccountMail = GropuAccount[i]["AccountMail"].ToString();
                string[] mailTo = new string[] { AccountMail };
                string[] mailCc = new string[1];//副本
                mailCc[0] = "";
                string[] mailBcc = new string[1];//密件
                mailBcc[0] = "";
                new SendMail().Mail_RetractNotify(mailTo, mailCc, mailBcc, AccountName, lbl_applyno.Text.Trim(), CompName);
            }
        }
    }

    protected void gv_paydata_RowDataBound(object sender, GridViewRowEventArgs e)
    {
          
        if (e.Row.RowType == DataControlRowType.DataRow)
        {                       
            LinkButton lbnt_userfilename = (LinkButton)e.Row.FindControl("lbnt_userfilename");
            string userfilename = DataBinder.Eval(e.Row.DataItem, "afi_userfilename").ToString();
            lbnt_userfilename.Attributes["afi_fileid"] = DataBinder.Eval(e.Row.DataItem, "afi_fileid").ToString().Trim();
            lbnt_userfilename.Text = userfilename;
            lbnt_userfilename.Attributes.Add("onclick", "javascript:window.open('../FileDownloadSeal.aspx?File=" + Server.UrlEncode(EnCode(DataBinder.Eval(e.Row.DataItem, "afi_fileid").ToString().Trim())) + "','_blank');");

        }
    
    }
}
