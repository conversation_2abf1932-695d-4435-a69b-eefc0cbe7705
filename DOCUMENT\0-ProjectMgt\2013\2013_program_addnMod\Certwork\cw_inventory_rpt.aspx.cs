﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

using System.Data.SqlClient;
using System.Data;
using Microsoft.Reporting.WebForms;
using System.Configuration;
public partial class Certwork_cw_inventory_rpt : BaseAdminPage
{
    protected void Page_Load(object sender, EventArgs e)
    {
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = '../accountmt/index.aspx';</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" && (GetAgentAccountGrpList().Contains("epa_adm") || GetAgentAccountGrpList().Contains("ac_adm") || GetAgentAccountGrpList().Contains("tc_adm") || GetAgentAccountGrpList().Contains("rv_adm") || GetAgentAccountGrpList().Contains("rs_adm") || GetAgentAccountGrpList().Contains("fis_adm") || GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("pg_adm")))
            {
                if (!IsPostBack)
                {
                    //BindData();
                    BindVechileTypeList();
                    BindTransportType();
                    BindIsImport();

                }
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }
    }
    private void BindIsImport()
    {
        #region 國產/進口
        Cryptography crypy = new Cryptography();
        wsCodetbl wsCodeTbl = new wsCodetbl();
        string[] arr_im = crypy.wsCodetblValidate(crypy.getRadNum());
        DataSet dsIsImport = wsCodeTbl.getIsImport(arr_im[0].ToString(), arr_im[1].ToString());

        rdBtnLst_isimport.DataSource = dsIsImport;
        rdBtnLst_isimport.DataTextField = "ct_im_desc";
        rdBtnLst_isimport.DataValueField = "ct_im_id";
        rdBtnLst_isimport.DataBind();
        #endregion

    }
    private void BindTransportType()
    {
        ddl_transporttype.Items.Clear();
        wsCodetbl wsCodeTbl = new wsCodetbl();
        #region 車種分類
        string[] arr_nvc = wsCodetblValidate(getRadNum());
        DataSet dsNoiseVechileCategory = wsCodeTbl.get_noisevechilecategory(arr_nvc[0].ToString(), arr_nvc[1].ToString());
        ddl_transporttype.DataSource = dsNoiseVechileCategory;
        ddl_transporttype.DataTextField = "ct_nvc_desc";
        ddl_transporttype.DataValueField = "ct_nvc_id";
        ddl_transporttype.DataBind();
        ddl_transporttype.Items.Insert(0, new ListItem("全部", ""));
        #endregion

        #region 動力來源
        DataSet dsPowerFrom = wsCodeTbl.getPowerFrom(arr_nvc[0].ToString(), arr_nvc[1].ToString());
        rdBtnLst_powerfrom.DataSource = dsPowerFrom;
        rdBtnLst_powerfrom.DataTextField = "ct_pf_desc";
        rdBtnLst_powerfrom.DataValueField = "ct_pf_id";
        rdBtnLst_powerfrom.DataBind();
        #endregion
    }
    private void BindVechileTypeList()
    {
        wsCodetbl wscode = new wsCodetbl();
        string[] radom_cd = wsCodetblValidate(getRadNum());
        cbl_vechiletypelist.DataSource = wscode.getCarType(radom_cd[0].ToString(), radom_cd[1].ToString());
        cbl_vechiletypelist.DataTextField = "ct_ct_desc";
        cbl_vechiletypelist.DataValueField = "ct_ct_id";
        cbl_vechiletypelist.DataBind();
    }
    public void BindData()
    {
        Cryptography crypy = new Cryptography();
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        int keynum = crypy.getRadNum();
        string[] arr = crypy.wsApplybasedataDetailValid(keynum);
        string sVechiletype = string.Empty;
        foreach (ListItem item in cbl_vechiletypelist.Items)
        {
            if (item.Selected)
            {
                sVechiletype += item.Value + ",";

            }
        }
        #region 去逗號

        sVechiletype = sVechiletype.TrimEnd(',');

        #endregion
        DataTable dt_inventory = wsApplyData.get_Nv_Inventory(txt_compname.Text.Trim(), rdBtnLst_isimport.SelectedValue, sVechiletype, ddl_transporttype.SelectedValue, txt_carcompomodelno.Text.Trim(), txt_enginefamily.Text.Trim(), rdBtnLst_powerfrom.SelectedValue.ToString(), cal_applydate_S.GetDate, cal_applydate_E.GetDate, arr[0].ToString(), arr[1].ToString());

        DataView dvRemark = new DataView();       

        for (int z = 0; z < dt_inventory.Rows.Count; z++)
        {
            //取得測定報告備註資料

            #region 資料狀態-當申請型式 不為新申請 且有 50,55資料 取得上次申請代表車資料 否 擇讀取自己本身填寫資料

            DataTable dt_applystatus = wsApplyData.get_applystatus(dt_inventory.Rows[z]["abd_applyno"].ToString(), dt_inventory.Rows[z]["cpm_carcompomodelno"].ToString(), arr[0].ToString(), arr[1].ToString());
            bool b_IsApplied = wsApplyData.IsApplied(dt_inventory.Rows[z]["abd_applyno"].ToString(), dt_inventory.Rows[z]["cpm_carcompomodelno"].ToString(), arr[0].ToString(), arr[1].ToString());
            if (dt_applystatus.Rows.Count > 0 && !(dt_applystatus.Rows[0]["ct_aps_desc"].Equals("新申請")) && b_IsApplied)
            {

                DataTable dt_unNewApplyData = wsApplyData.get_unNewApplyData(dt_inventory.Rows[z]["abd_applyno"].ToString(), dt_inventory.Rows[z]["cpm_carcompomodelno"].ToString(), arr[0].ToString(), arr[1].ToString());
                if (dt_unNewApplyData.Rows.Count > 0)
                {

                    dvRemark = new CertificatedControl().ListRemarkForRptInfo(dt_unNewApplyData.Rows[0]["cmdg_carcompomodelno"].ToString(), dt_unNewApplyData.Rows[0]["cmdg_carmodelno"].ToString());
                }
            }
            else
            {
                dvRemark = new CertificatedControl().ListRemarkForRptInfo(dt_inventory.Rows[z]["cpm_carcompomodelno"].ToString(), dt_inventory.Rows[z]["cmdg_carmodelno"].ToString());
            }
            #endregion
            for (int r = 0; r < dvRemark.Count; r++)
            {
                dt_inventory.Rows[z]["fullcarstylename"] = dt_inventory.Rows[z]["fullcarstylename"].ToString() + "\n" + dvRemark[r]["Remark"].ToString();
            }

        }




        //開啟Viewer顯示   
        rpv_MyData.Visible = true;
        //指定Viewer的rdlc檔   
        rpv_MyData.LocalReport.ReportPath = Server.MapPath("rpt_inventory.rdlc");
        //清掉之前的狀態，重新把資料丟進去，並做Viewer的Refresh   
        rpv_MyData.LocalReport.DataSources.Clear();
        rpv_MyData.LocalReport.DataSources.Add(new ReportDataSource("DT_inventory", dt_inventory));
        rpv_MyData.LocalReport.Refresh();
    }
    protected void btn_Search_Click(object sender, EventArgs e)
    {
        #region 特殊字元判斷-沒有MasterPage
        foreach (Control ctl in Page.Form.Controls)
        {
            Control content = (Control)ctl;
            foreach (Control c in content.Controls)
            {
                if (c is TextBox)
                {
                    TextBox objTextBox = default(TextBox);
                    objTextBox = (TextBox)c;
                    if ((tbWord(objTextBox.Text)))
                    {
                        MessageBox.Show(objTextBox.Text + ":含有系統不允許的特殊字元");
                        return;
                    }
                }
            }
        }
        #endregion
        #region 特殊字元判斷-日期
        if (tbWord(cal_applydate_S.GetDate) || tbWord(cal_applydate_E.GetDate))
        {
            MessageBox.Show("日期選項含有系統不允許的特殊字元");
            return;
        }
        #endregion
        BindData();
    }
    #region 取得下拉選單資料
    private void GetddlValue(DropDownList ddl, string colname, DataSet ds)
    {
        foreach (ListItem it in ddl.Items)
        {
            if (it.Value == ds.Tables[0].Rows[0][colname].ToString())
                it.Selected = true;
            else
                it.Selected = false;
        }
    }
    #endregion
}