﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Services;
using System.Data;
using System.Data.SqlClient;

/// <summary>
/// Summary description for wsLoginCheck
/// </summary>
[WebService(Namespace = "http://tempuri.org/")]
[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
// To allow this Web Service to be called from script, using ASP.NET AJAX, uncomment the following line. 
// [System.Web.Script.Services.ScriptService]
public class wsLoginCheck : System.Web.Services.WebService {

    public wsLoginCheck () {

        //Uncomment the following line if using designed components 
        //InitializeComponent(); 
    }

    [WebMethod]
    public bool AccountCheckByPollution(string ID, string PD, string account, string password, string compidnoE)
    {
        if (!wsCheckLoginValid(ID, PD)) return false;
        SqlCommand cmd = new SqlCommand(@"select * 
                        from nv_accountinfo
                        where ai_accountstatus='7'
                        and ai_account=@account and ai_password=@password
                        and ai_accountbelongcomp=@compidno");
        cmd.Parameters.AddWithValue("@account", account);
        cmd.Parameters.AddWithValue("@password", password);
        cmd.Parameters.AddWithValue("@compidno", compidnoE);
        DataTable dt = Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        if (dt.Rows.Count > 0)
            return true;
        else return false;
    }

    [WebMethod]
    public bool AccountCheck(string accountid, string password, string compidnoE, string ID, string PD)
    {
        //必須審核過才可使用登入功能
        if (!wsCheckLoginValid(ID, PD)) return false;
        SqlCommand cmd_check = new SqlCommand(@"select * from nv_accountinfo ,nv_companyinfo
                                            where ai_accountbelongcomp=ci_compidno and ai_accountstatus in ('1','2')
                                            and ai_account=@accountid and ai_password=@password 
                                            and ci_compidno=@compidno");
        cmd_check.Parameters.AddWithValue("@accountid", accountid);
        cmd_check.Parameters.AddWithValue("@password", password);
        cmd_check.Parameters.AddWithValue("@compidno", compidnoE);
        DataView dv = Common.Data_noisevalidation.runParaCmd(cmd_check);
        if (dv.Count > 0)
            return true;
        else return false;
    }


    [WebMethod]
    public bool pollutionCheck(string account, string password, string compidnoE, string ID, string PD)
    {
        if (wsCheckLoginValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select *
                        from nv_accountinfo
                        where ai_accountstatus='7'
                        and ai_account=@account
                        and ai_password=@password
                        and ai_accountbelongcomp=@compidno");
            cmd.Parameters.AddWithValue("@account", account);
            cmd.Parameters.AddWithValue("@password", password);
            cmd.Parameters.AddWithValue("@compidno", compidnoE);
            DataTable dt = Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
            if (dt.Rows.Count > 0)
                return true;
            else return false;
        } return false;
    }

    //紀錄登入次數資料
    [WebMethod]
    public string InsertAccountLog(string account, string loginreload, string ID, string PD)
    {
        if (wsCheckLoginValid(ID, PD))
        {
            try
            {
                SqlCommand cmd_insaccount = new SqlCommand(@"INSERT INTO nv_account_log
                                    (la_account
                                    ,la_longindate                                    
                                    ,la_loginresult)
                                    VALUES
                                    (@account
                                    ,GETDATE()
                                    ,@loginreload)  select '紀錄完成'");

                cmd_insaccount.Parameters.AddWithValue("@account", account);
                cmd_insaccount.Parameters.AddWithValue("@loginreload", loginreload);
                

                object obj_return = Common.Data_noisevalidation.runScalar(cmd_insaccount);
                return (string)obj_return;
            }
            catch (Exception ex)
            {
                if (ex.ToString().IndexOf("Base") > -1)
                    return "Base-64字元陣列的無效長度";
                else
                    return "紀錄失敗";
            }
        }
        else
        {
            return "驗證失敗";
        }
    }

    //計算失敗登入次數RemoveAccountLoginLog
    [WebMethod]
    public bool FailAccountLog(string account, string ID, string PD)
    {
        if (wsCheckLoginValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"EXEC pr_count_accountstatus @account");
            cmd.Parameters.AddWithValue("@account", account);
            DataView dv_Sum = Common.Data_noisevalidation.runParaCmd(cmd);
            int iFiletypeSum = (int)Common.Data_noisevalidation.runScalar(cmd);
            if (iFiletypeSum >= 4)
                return false;
            else return true;
        } return false;
    }

    //將AccountLogin紀錄移到nv_account_loginlog_his
    [WebMethod]   
    public string RemoveAccountLoginLog(string account, string ID, string PD)
    {
        if (wsCheckLoginValid(ID, PD))
        {
            try
            {
                SqlCommand cmd_insaccount = new SqlCommand(@" 
                                                        /*開始事務*/
                                                        begin tran
                                                        /*連續執行兩個插入操作*/  
                                                         Insert into nv_account_loginlog_his(la_account_his, la_longindate_his, la_loginresult_his,la_remove_account_his,la_remove_date_his) 
                                                         SELECT la_account,la_longindate,la_loginresult,la_account,GETDATE() FROM nv_account_log WHERE la_account =@account
 
                                                         delete from nv_account_log where la_account=@account

                                                        /*如果上面兩次操作中有一次失敗了，那麼回滾事務，即讓兩次操作都不生效*/
                                                        if @@error<>0
                                                        begin
                                                           rollback tran;
                                                           select '解除鎖定失敗'
                                                        end
                                                        else
                                                        /*如果沒有錯誤，則提交事務！*/
                                                        begin
                                                        commit tran;
                                                        end  select '解除鎖定完成'");

                cmd_insaccount.Parameters.AddWithValue("@account", account);
               

                object obj_return = Common.Data_noisevalidation.runScalar(cmd_insaccount);
                return (string)obj_return;
            }
            catch (Exception ex)
            {
                if (ex.ToString().IndexOf("Base") > -1)
                    return "Base-64字元陣列的無效長度";
                else
                    return "解除鎖定失敗";
            }
        }
        else
        {
            return "驗證失敗";
        }
    }

    private bool wsCheckLoginValid(string ID, string PD)
    {
        string[][] x = new string[7][];
        x[0] = new string[2] { "CLPWS1", "dsdfsew" };
        x[1] = new string[2] { "CLPWS2", "dse3DD" };
        x[2] = new string[2] { "CLPWS3", "sf45DWeg" };
        x[3] = new string[2] { "CLPWS4", "gs78fDS" };
        x[4] = new string[2] { "CLPWS5", "42CDWEGtx" };
        x[5] = new string[2] { "CLPWS6", "dsfd96DW" };
        x[6] = new string[2] { "CLPWS7", "dsdfe99iJU" };

        for (int i = 0; i < 7; i++)
        {
            if (x[i][0].ToString() == ID && x[i][1].ToString() == PD)
            {
                return true;
            }
        }
        return false;
    }
    
}
