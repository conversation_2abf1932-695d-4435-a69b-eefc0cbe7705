﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data.SqlClient;
using System.Data;
using Microsoft.Reporting.WebForms;

using System.Configuration;
using System.Xml;
using Common;
using System.IO;

public partial class Certwork_cw_progressdel_rpt : BaseAdminPage
{
    protected void Page_Load(object sender, EventArgs e)
    {
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = '../accountmt/index.aspx';</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" && (GetAgentAccountGrpList().Contains("epa_adm") || GetAgentAccountGrpList().Contains("ac_adm") || GetAgentAccountGrpList().Contains("tc_adm") || GetAgentAccountGrpList().Contains("rv_adm") || GetAgentAccountGrpList().Contains("rs_adm") || GetAgentAccountGrpList().Contains("fis_adm") || GetAgentAccountGrpList().Contains("sys_adm") || GetAgentAccountGrpList().Contains("pg_adm")))
            {
                if (!IsPostBack)
                {
                    //BindData();
                    BindVechileTypeList();
                }
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }
    }

    private void BindVechileTypeList()
    {
        wsCodetbl wsCodeTbl = new wsCodetbl();
        string[] arr_nvc = wsCodetblValidate(getRadNum());
        cbl_vechiletypelist.DataSource = wsCodeTbl.getCarType(arr_nvc[0].ToString(), arr_nvc[1].ToString());
        cbl_vechiletypelist.DataTextField = "ct_ct_desc";
        cbl_vechiletypelist.DataValueField = "ct_ct_id";
        cbl_vechiletypelist.DataBind();

        #region 動力來源
        DataSet dsPowerFrom = wsCodeTbl.getPowerFrom(arr_nvc[0].ToString(), arr_nvc[1].ToString());
        rdBtnLst_powerfrom.DataSource = dsPowerFrom;
        rdBtnLst_powerfrom.DataTextField = "ct_pf_desc";
        rdBtnLst_powerfrom.DataValueField = "ct_pf_id";
        rdBtnLst_powerfrom.DataBind();
        #endregion
    }

    public void BindData()
    {
        Cryptography crypy = new Cryptography();
        wsApplybasedataDetail wsApplyData = new wsApplybasedataDetail();
        int keynum = crypy.getRadNum();
        string[] arr = crypy.wsApplybasedataDetailValid(keynum);
        string sVechiletype = string.Empty;
        foreach (ListItem item in cbl_vechiletypelist.Items)
        {
            if (item.Selected)
            {
                sVechiletype += item.Value + ",";

            }
        }
        #region 去逗號

        sVechiletype = sVechiletype.TrimEnd(',');

        #endregion
        DataTable dt_progresscontrol = wsApplyData.get_Nv_Progresscontrol("", "", "", txt_carcompomodelno.Text.Trim(), sVechiletype, rdBtnLst_powerfrom.SelectedValue.ToString(), cal_applydate_S.GetDate, cal_applydate_E.GetDate, "9", arr[0].ToString(), arr[1].ToString());

        //開啟Viewer顯示   
        rpv_MyData.Visible = true;
        //指定Viewer的rdlc檔   
        rpv_MyData.LocalReport.ReportPath = Server.MapPath("rpt_progressdel.rdlc");
        //清掉之前的狀態，重新把資料丟進去，並做Viewer的Refresh   
        rpv_MyData.LocalReport.DataSources.Clear();
        rpv_MyData.LocalReport.DataSources.Add(new ReportDataSource("DT_progresscontrol", dt_progresscontrol));
        rpv_MyData.LocalReport.Refresh();
    }
    protected void btn_Search_Click(object sender, EventArgs e)
    {
        #region 特殊字元判斷-沒有MasterPage
        foreach (Control ctl in Page.Form.Controls)
        {
            Control content = (Control)ctl;
            foreach (Control c in content.Controls)
            {
                if (c is TextBox)
                {
                    TextBox objTextBox = default(TextBox);
                    objTextBox = (TextBox)c;
                    if ((tbWord(objTextBox.Text)))
                    {
                        MessageBox.Show(objTextBox.Text + ":含有系統不允許的特殊字元");
                        return;
                    }
                }
            }
        }
        #endregion
        #region 特殊字元判斷-日期
        if (tbWord(cal_applydate_S.GetDate) || tbWord(cal_applydate_E.GetDate))
        {
            MessageBox.Show("日期選項含有系統不允許的特殊字元");
            return;
        }
        #endregion
        BindData();
    }
}