﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Services;

using System.Data.SqlClient;
using System.Data;
/// <summary>
/// Summary description for wsApplybasedataDetail
/// </summary>
[WebService(Namespace = "http://tempuri.org/")]
[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
// To allow this Web Service to be called from script, using ASP.NET AJAX, uncomment the following line. 
// [System.Web.Script.Services.ScriptService]
public class wsApplybasedataDetail : System.Web.Services.WebService
{

    public wsApplybasedataDetail()
    {

        //Uncomment the following line if using designed components 
        //InitializeComponent(); 
    }
    [WebMethod]
    public DataTable get_databelongcompname(string applyno, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"SELECT abd_databelongcompname,abd_contactemail,abd_databelongcompidno,abd_enginefamily,abd_contactname
                                                FROM nv_applybasedata                                                                                               
                                                WHERE abd_applyno=@applyno ");
            cmd.Parameters.AddWithValue("@applyno", applyno);

            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;

    }
    [WebMethod]
    public DataTable get_applybasedate_detail(string applyno, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"SELECT ct_sd_desc,ct_aps_desc,ct_ct_desc,ct_twct_desc,ct_pf_desc,nv_applybasedata.* 
                                                FROM nv_applybasedata 
                                                LEFT JOIN nv_codetbl_applystatus ON ct_aps_id = abd_applystatus
                                                LEFT JOIN nv_codetbl_cartype ON ct_ct_id = abd_vechiletype
                                                LEFT JOIN nv_codetbl_standarddate ON ct_sd_id = abd_adaptstandarddate
                                                LEFT JOIN nv_codetbl_twocerttype ON ct_twct_id = abd_twocertmethod 
                                                LEFT JOIN nv_codetbl_powerfrom ON ct_pf_id = abd_powerfrom
                                                WHERE abd_applyno=@applyno ");
            cmd.Parameters.AddWithValue("@applyno", applyno);

            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;

    }
    [WebMethod]
    public DataTable get_paydata(string applyno, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select nv_paydata.*,afi_userfilename,afi_fileid from nv_paydata 
                                               left join nv_attachfileinfo on afi_fileid=pd_attachfileid where pd_applyno=@applyno");
            cmd.Parameters.AddWithValue("@applyno", applyno);

            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;

    }
    [WebMethod]
    public DataTable get_validationprocess(string applyno, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select * from nv_validationprocess where vp_applyno=@applyno ");
            cmd.Parameters.AddWithValue("@applyno", applyno);

            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;

    }
    [WebMethod]
    public DataTable get_carmodeldata(string applyno, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"EXEC pr_get_carcompomodel_qry @applyno");
            cmd.Parameters.AddWithValue("@applyno", applyno);

            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;

    }
    [WebMethod]
    public DataTable get_fix_catalog(string applyno, string carcompomodelno, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"EXEC pr_get_fixcatalog_list @applyno,@carcompomodelno");

            cmd.Parameters.AddWithValue("@applyno", applyno);
            cmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;
    }
    [WebMethod]
    public DataTable get_fix_catalog2(string applyno, string carcompomodelno, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"EXEC pr_get_fixcatalog_list2 @applyno,@carcompomodelno");

            cmd.Parameters.AddWithValue("@applyno", applyno);
            cmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;
    }
    [WebMethod]
    public DataTable get_applyno(string applyno, string carcompomodelno, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"EXEC pr_get_cpm_applytype @applyno,@carcompomodelno");

            cmd.Parameters.AddWithValue("@applyno", applyno);
            cmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;
    }
    [WebMethod]
    public DataTable get_applystatus(string applyno, string carcompomodelno, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"EXEC pr_get_applystatus @applyno,@carcompomodelno");

            cmd.Parameters.AddWithValue("@applyno", applyno);
            cmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;
    }

    //已有50,55資料
    public bool IsApplied(string applyno, string carcompomodelno, string ID, string PD)
    {
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"exec pr_get_unNewApplyData
                        @applyno,
                        @carcompomodelno                                                            
                       ");

            cmd.Parameters.AddWithValue("@applyno", applyno);
            cmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
            DataTable dt = Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
            if (dt.Rows.Count > 0)
                return true;
            else return false;
        } return true;
    }

    public void Insert_Nv_fixcatalog(string applyno, string carcompomodelno, string fixitem, string beforefix, string afterfix, string createaccount, string ID, string PD)
    {

        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"INSERT INTO nv_fixcatalog
           (fc_applyno
           ,fc_carcompomodelno
           ,fc_fixitem
           ,fc_beforefix 
           ,fc_afterfix
           ,fc_createaccount
           ,fc_createdate
           ,fc_modaccount
           ,fc_moddate
           ,fc_oriapplyno
            )
     VALUES
           (@applyno
           ,@carcompomodelno
           ,@fixitem
           ,@beforefix 
           ,@afterfix
           ,@createaccount
           ,GETDATE()
           ,@createaccount
           ,GETDATE()
           ,@applyno)");

            cmd.Parameters.AddWithValue("@applyno", applyno);
            cmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
            cmd.Parameters.AddWithValue("@fixitem", fixitem);
            cmd.Parameters.AddWithValue("@beforefix", beforefix);
            cmd.Parameters.AddWithValue("@afterfix", afterfix);
            cmd.Parameters.AddWithValue("@createaccount", createaccount);
            Common.Data_noisevalidation.runParaCmd1(cmd);
        }
    }
    public void Update_Nv_fixcatalog(string fixid, string fixitem, string beforefix, string afterfix, string modaccount, string ID, string PD)
    {
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"UPDATE  nv_fixcatalog
                            SET fc_fixitem = @fixitem
                               ,fc_beforefix =  @beforefix
                               ,fc_afterfix =  @afterfix
                               ,fc_modaccount =  @modaccount
                               ,fc_moddate = GETDATE() 
                          WHERE fc_fixid=@fixid");

            cmd.Parameters.AddWithValue("@fixitem", fixitem);
            cmd.Parameters.AddWithValue("@beforefix", beforefix);
            cmd.Parameters.AddWithValue("@afterfix", afterfix);
            cmd.Parameters.AddWithValue("@modaccount", modaccount);
            cmd.Parameters.AddWithValue("@fixid", fixid);

            Common.Data_noisevalidation.runParaCmd1(cmd);
        }
    }
    [WebMethod]
    public DataTable get_Nv_fixcataloglist(string fixid, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select * from nv_fixcatalog where fc_fixid=@fixid");

            cmd.Parameters.AddWithValue("@fixid", fixid);

            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;
    }
    [WebMethod]
    public void Insert_toldfixcatalog(string applyno, string carcompomodelno, string ID, string PD)
    {

        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"EXEC pr_insertoldfixcatalog @applyno,@carcompomodelno");

            cmd.Parameters.AddWithValue("@applyno", applyno);
            cmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
            Common.Data_noisevalidation.runParaCmd1(cmd);
        }
    }
    [WebMethod]
    public void Del_Nv_fixcatalog(string fixid, string ID, string PD)
    {
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd_del = new SqlCommand("delete from nv_fixcatalog where fc_fixid=@fixid");
            cmd_del.Parameters.AddWithValue("@fixid", fixid);
            Common.Data_noisevalidation.runParaCmd1(cmd_del);
        }
    }
    [WebMethod]
    public DataTable get_Nv_cardetails(string applyno, string carcompomodelno, string carmodelno, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"EXEC pr_get_cardetails @applyno,@carcompomodelno,@carmodelno");

            cmd.Parameters.AddWithValue("@applyno", applyno);
            cmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
            cmd.Parameters.AddWithValue("@carmodelno", carmodelno);

            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;
    }
    [WebMethod]
    public DataTable get_Nv_carmodelnames(string applyno, string carcompomodelno, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"EXEC pr_get_carmodelnames @applyno,@carcompomodelno");

            cmd.Parameters.AddWithValue("@applyno", applyno);
            cmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);

            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;
    }
    [WebMethod]
    public DataTable get_fenseplanmodadate(string applyno, string carcompomodelno, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"SELECT ndp_modaccount, MAX(ndp_moddate) as ndp_moddate 
                                                FROM nv_noisedefenseplan
                                                where ndp_applyno=@applyno and
                                                      ndp_carcompomodelno=@carcompomodelno
                                                GROUP BY ndp_modaccount ");

            cmd.Parameters.AddWithValue("@applyno", applyno);
            cmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);

            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;
    }
    [WebMethod]
    public void Insert_Nv_noisetestrpt(string carcompomodelno, string carmodelno, string testrptno, string carbodyno, string engineno, string speedupcondi, string stayrpmparam, string stayrpmcondi, string speedupnoise, string staynoise, string rptfileid, string createaccount, string producercountry, string ID, string PD)
    {
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"
           INSERT INTO nv_noisetestrpt
           (ntr_carcompomodelno
           ,ntr_carmodelno
           ,ntr_testrptno
           ,ntr_carbodyno 
           ,ntr_engineno
           ,ntr_speedupcondi
           ,ntr_stayrpmparam
           ,ntr_stayrpmcondi
           ,ntr_speedupnoise
           ,ntr_staynoise
           ,ntr_rptfileid
           ,ntr_createaccount
           ,ntr_createdate
           ,ntr_modaccount
           ,ntr_producercountry
           ,ntr_moddate
            )
     VALUES
           (@carcompomodelno
           ,@carmodelno
           ,@testrptno
           ,@carbodyno 
           ,@engineno
           ,@speedupcondi
           ,@stayrpmparam
           ,@stayrpmcondi
           ,@speedupnoise
           ,@staynoise
           ,@rptfileid
           ,@createaccount
           ,GETDATE()
           ,@createaccount
           ,@producercountry
           ,GETDATE())");

            cmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
            cmd.Parameters.AddWithValue("@carmodelno", carmodelno);
            cmd.Parameters.AddWithValue("@testrptno", testrptno);
            cmd.Parameters.AddWithValue("@carbodyno", carbodyno);
            cmd.Parameters.AddWithValue("@engineno", engineno);
            cmd.Parameters.AddWithValue("@speedupcondi", speedupcondi);
            cmd.Parameters.AddWithValue("@stayrpmparam", stayrpmparam);
            cmd.Parameters.AddWithValue("@stayrpmcondi", stayrpmcondi);
            cmd.Parameters.AddWithValue("@speedupnoise", speedupnoise);
            cmd.Parameters.AddWithValue("@staynoise", staynoise);
            cmd.Parameters.AddWithValue("@rptfileid", rptfileid);
            cmd.Parameters.AddWithValue("@createaccount", createaccount);
            cmd.Parameters.AddWithValue("@producercountry", producercountry);
            Common.Data_noisevalidation.runParaCmd1(cmd);
        }
    }

    public void Update_Nv_noisetestrpt(string carcompomodelno, string carmodelno, string testrptno, string carbodyno, string engineno, string speedupcondi, string stayrpmparam, string stayrpmcondi, string speedupnoise, string staynoise, string rptfileid, string modaccount, string producercountry, string ID, string PD)
    {
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"UPDATE  nv_noisetestrpt
                            SET ntr_testrptno =  @testrptno
                               ,ntr_carbodyno =  @carbodyno
                               ,ntr_engineno  = @engineno
                               ,ntr_speedupcondi = @speedupcondi
                               ,ntr_stayrpmparam = @stayrpmparam
                               ,ntr_stayrpmcondi = @stayrpmcondi
                               ,ntr_speedupnoise = @speedupnoise
                               ,ntr_staynoise = @staynoise
                               ,ntr_rptfileid = @rptfileid
                               ,ntr_modaccount =  @modaccount
                               ,ntr_moddate = GETDATE() 
                               ,ntr_producercountry =  @producercountry
                          WHERE ntr_carcompomodelno=@carcompomodelno and ntr_carmodelno = @carmodelno ");

            cmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
            cmd.Parameters.AddWithValue("@carmodelno", carmodelno);
            cmd.Parameters.AddWithValue("@testrptno", testrptno);
            cmd.Parameters.AddWithValue("@carbodyno", carbodyno);
            cmd.Parameters.AddWithValue("@engineno", engineno);
            cmd.Parameters.AddWithValue("@speedupcondi", speedupcondi);
            cmd.Parameters.AddWithValue("@stayrpmparam", stayrpmparam);
            cmd.Parameters.AddWithValue("@stayrpmcondi", stayrpmcondi);
            cmd.Parameters.AddWithValue("@speedupnoise", speedupnoise);
            cmd.Parameters.AddWithValue("@staynoise", staynoise);
            cmd.Parameters.AddWithValue("@rptfileid", rptfileid);
            cmd.Parameters.AddWithValue("@modaccount", modaccount);
            cmd.Parameters.AddWithValue("@producercountry", producercountry);
            Common.Data_noisevalidation.runParaCmd1(cmd);
        }
    }
    //多加testrptno條件
    public void Update_Nv_noisetestrpt_testrptno(string carcompomodelno, string carmodelno, string testrptno, string oldtestrptno, string carbodyno, string engineno, string speedupcondi, string stayrpmparam, string stayrpmcondi, string speedupnoise, string staynoise, string rptfileid, string modaccount, string producercountry, string ID, string PD)
    {
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"UPDATE  nv_noisetestrpt
                            SET ntr_testrptno =  @testrptno
                               ,ntr_carbodyno =  @carbodyno
                               ,ntr_engineno  = @engineno
                               ,ntr_speedupcondi = @speedupcondi
                               ,ntr_stayrpmparam = @stayrpmparam
                               ,ntr_stayrpmcondi = @stayrpmcondi
                               ,ntr_speedupnoise = @speedupnoise
                               ,ntr_staynoise = @staynoise
                               ,ntr_rptfileid = @rptfileid
                               ,ntr_modaccount =  @modaccount
                               ,ntr_moddate = GETDATE() 
                               ,ntr_producercountry =  @producercountry
                          WHERE ntr_carcompomodelno=@carcompomodelno and ntr_carmodelno = @carmodelno and ntr_testrptno=@oldtestrptno ");

            cmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
            cmd.Parameters.AddWithValue("@carmodelno", carmodelno);
            cmd.Parameters.AddWithValue("@testrptno", testrptno);
            cmd.Parameters.AddWithValue("@oldtestrptno", oldtestrptno);
            cmd.Parameters.AddWithValue("@carbodyno", carbodyno);
            cmd.Parameters.AddWithValue("@engineno", engineno);
            cmd.Parameters.AddWithValue("@speedupcondi", speedupcondi);
            cmd.Parameters.AddWithValue("@stayrpmparam", stayrpmparam);
            cmd.Parameters.AddWithValue("@stayrpmcondi", stayrpmcondi);
            cmd.Parameters.AddWithValue("@speedupnoise", speedupnoise);
            cmd.Parameters.AddWithValue("@staynoise", staynoise);
            cmd.Parameters.AddWithValue("@rptfileid", rptfileid);
            cmd.Parameters.AddWithValue("@modaccount", modaccount);
            cmd.Parameters.AddWithValue("@producercountry", producercountry);
            Common.Data_noisevalidation.runParaCmd1(cmd);

        }
    }


    [WebMethod]
    public void Del_Nv_noisetestrpt(string carcompomodelno, string carmodelno, string remarkno, string ID, string PD)
    {
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd_del = new SqlCommand(@"                                 
            if (@remarkno !='99')
                delete from nv_noisetestrpt_remark where ntrr_carcompomodelno=@carcompomodelno and ntrr_remarkno != '99' and ntrr_carmodelno=@carmodelno           
            else
                delete from nv_noisetestrpt_remark where ntrr_carcompomodelno=@carcompomodelno and ntrr_remarkno = '99' and ntrr_carmodelno=@carmodelno
          
                ");

            cmd_del.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
            cmd_del.Parameters.AddWithValue("@carmodelno", carmodelno);
            cmd_del.Parameters.AddWithValue("@remarkno", remarkno);
            Common.Data_noisevalidation.runParaCmd1(cmd_del);
        }
    }
    [WebMethod]
    public void Insert_Nv_testrptspeedupinfo(string carcompomodelno, string carmodelno, string testrptno, string speedupgear, string speed, string ID, string PD)
    {
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"INSERT INTO nv_testrptspeedupinfo
           (trsui_carcompomodelno
           ,trsui_carmodelno
           ,trsui_testrptno
           ,trsui_speedupgear 
           ,trsui_speed
            )
     VALUES
           (@carcompomodelno
           ,@carmodelno
           ,@testrptno
           ,@speedupgear 
           ,@speed)");

            cmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
            cmd.Parameters.AddWithValue("@carmodelno", carmodelno);
            cmd.Parameters.AddWithValue("@testrptno", testrptno);
            cmd.Parameters.AddWithValue("@speedupgear", speedupgear);
            cmd.Parameters.AddWithValue("@speed", speed);

            Common.Data_noisevalidation.runParaCmd1(cmd);
        }
    }
    [WebMethod]
    public void Del_Nv_testrptspeedupinfo(string id, string ID, string PD)
    {
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd_del = new SqlCommand("delete from nv_testrptspeedupinfo where trsui_id=@id");
            cmd_del.Parameters.AddWithValue("@id", id);
            Common.Data_noisevalidation.runParaCmd1(cmd_del);
        }
    }
    [WebMethod]
    public void Insert_Nv_noisetestrpt_remark(string carcompomodelno, string carmodelno, string testrptno, string remarkno, string ID, string PD)
    {
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"EXEC pr_insert_noisetestrptremark @carcompomodelno,@carmodelno,@testrptno,@remarkno");

            cmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
            cmd.Parameters.AddWithValue("@carmodelno", carmodelno);
            cmd.Parameters.AddWithValue("@testrptno", testrptno);
            cmd.Parameters.AddWithValue("@remarkno", remarkno);


            Common.Data_noisevalidation.runParaCmd1(cmd);
        }
    }
    [WebMethod]
    public void Update_Nv_noisetestrpt_remark(string carcompomodelno, string carmodelno, string testrptno, string remarkno, string ID, string PD)
    {
        if (wsApplybasedataDetailValid(ID, PD))
        {

            SqlCommand cmd_update = new SqlCommand(@"
                            EXEC pr_insert_noisetestrptremark @carcompomodelno,@carmodelno,@testrptno,@remarkno
                           ");

            cmd_update.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
            cmd_update.Parameters.AddWithValue("@carmodelno", carmodelno);
            cmd_update.Parameters.AddWithValue("@testrptno", testrptno);
            cmd_update.Parameters.AddWithValue("@remarkno", remarkno); ;

            Common.Data_noisevalidation.runParaCmd1(cmd_update);
        }
    }
    [WebMethod]
    public void Update_Nv_testrptspeedupinfo(string speedupgear, string speed, string id, string ID, string PD)
    {
        if (wsApplybasedataDetailValid(ID, PD))
        {

            SqlCommand cmd_update = new SqlCommand(@"UPDATE  nv_testrptspeedupinfo
                            SET trsui_speedupgear = @speedupgear
                               ,trsui_speed =  @speed
                          WHERE trsui_id=@id");

            cmd_update.Parameters.AddWithValue("@speedupgear", speedupgear);
            cmd_update.Parameters.AddWithValue("@speed", speed);
            cmd_update.Parameters.AddWithValue("@id", id);

            Common.Data_noisevalidation.runParaCmd1(cmd_update);
        }
    }
    [WebMethod]
    public void Update_Nv_testrptspeeduptrsui_testrptno(string carcompomodelno, string carmodelno, string testrptno, string ID, string PD)
    {
        if (wsApplybasedataDetailValid(ID, PD))
        {

            SqlCommand cmd_update = new SqlCommand(@"UPDATE  nv_testrptspeedupinfo
                            SET trsui_testrptno =  @testrptno
                          WHERE trsui_carcompomodelno=@carcompomodelno AND trsui_carmodelno=@carmodelno");
            cmd_update.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
            cmd_update.Parameters.AddWithValue("@carmodelno", carmodelno);
            cmd_update.Parameters.AddWithValue("@testrptno", testrptno);

            Common.Data_noisevalidation.runParaCmd1(cmd_update);
        }
    }
    [WebMethod]
    public void CheakDel_Nv_testrptspeedupinfo(string carcompomodelno, string testrptno, string ID, string PD)
    {
        if (wsApplybasedataDetailValid(ID, PD))
        {

            SqlCommand cmd_update = new SqlCommand(@"exec pr_check_testrptspeedupinfo @carcompomodelno,@testrptno ");

            cmd_update.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
            cmd_update.Parameters.AddWithValue("@testrptno", testrptno);


            Common.Data_noisevalidation.runParaCmd1(cmd_update);
        }
    }
    public void Insert_Nv_remark99(string carcompomodelno, string carmodelno, string testrptno, string remarkno, string desc, string ID, string PD)
    {
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"                                                   
                            INSERT INTO nv_noisetestrpt_remark
                                       (ntrr_carcompomodelno
                                       ,ntrr_carmodelno
                                       ,ntrr_testrptno
                                       ,ntrr_remarkno 
                                       ,ntrr_remark_desc)
                                 VALUES
                                       (@carcompomodelno
                                       ,@carmodelno
                                       ,@testrptno
                                       ,@remarkno 
                                       ,@desc)");

            cmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
            cmd.Parameters.AddWithValue("@carmodelno", carmodelno);
            cmd.Parameters.AddWithValue("@testrptno", testrptno);
            cmd.Parameters.AddWithValue("@remarkno", remarkno);
            cmd.Parameters.AddWithValue("@desc", desc);


            Common.Data_noisevalidation.runParaCmd1(cmd);
        }
    }

    [WebMethod]
    public DataTable get_Nv_noisestandarddata(string applyno, string carcompomodelno, string carmodelno, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"EXEC pr_get_noisestandarddata @applyno,@carcompomodelno,@carmodelno");

            cmd.Parameters.AddWithValue("@applyno", applyno);
            cmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
            cmd.Parameters.AddWithValue("@carmodelno", carmodelno);

            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;
    }
    [WebMethod]
    public DataTable get_Nv_speedupinfo(string carcompomodelno, string carmodelno, string testrptno, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select * from nv_testrptspeedupinfo where trsui_carcompomodelno=@carcompomodelno and trsui_carmodelno=@carmodelno and trsui_testrptno=@testrptno");

            cmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
            cmd.Parameters.AddWithValue("@carmodelno", carmodelno);
            cmd.Parameters.AddWithValue("@testrptno", testrptno);
            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;
    }
    [WebMethod]
    public DataTable get_Nv_noisetestrpt(string carcompomodelno, string carmodelno, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"

                declare @sub10_carcompomodelno varchar(12) --取前10(電動車11)碼比對 (包括 % 符號)
                declare @powerfrom varchar(1)
                declare @applyno varchar(9)

                SELECT top 1 @powerfrom = abd_powerfrom from nv_carmodeldata
                left join nv_applybasedata on abd_applyno = cmdg_applyno
                where cmdg_carmodelno =@carmodelno

                
                if (@powerfrom ='3') --電動車
                begin
                        set @sub10_carcompomodelno = left(@carcompomodelno,11) + '%'
                end
                else
                begin
                        set @sub10_carcompomodelno = left(@carcompomodelno,10) + '%'
                end

                select * from nv_noisetestrpt where ntr_carcompomodelno like @sub10_carcompomodelno and ntr_carmodelno=@carmodelno");

            cmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
            cmd.Parameters.AddWithValue("@carmodelno", carmodelno);

            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;
    }
    [WebMethod]
    public DataTable get_Nv_noisetestrpt_remark(string carcompomodelno, string carmodelno, string no, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select ROW_NUMBER() over ( order by ntrr_id ) as ntrr_no,* from nv_noisetestrpt_remark where ntrr_testrptno=@no and ntrr_carcompomodelno=@carcompomodelno and ntrr_carmodelno=@carmodelno");
            cmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
            cmd.Parameters.AddWithValue("@carmodelno", carmodelno);
            cmd.Parameters.AddWithValue("@no", no);
            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;
    }
    [WebMethod]
    public DataTable get_Nv_rptfilename(string filenameno, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"EXEC pr_get_filenamenotouserfilename @filenameno");

            cmd.Parameters.AddWithValue("@filenameno", filenameno);


            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;
    }
    [WebMethod]
    public DataSet get_Nv_speedupinfo_DataSet(string carcompomodelno, string carmodelno, string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select trsui_speedupgear,trsui_speed from nv_testrptspeedupinfo where trsui_carcompomodelno=@carcompomodelno and trsui_carmodelno=@carmodelno");

            cmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
            cmd.Parameters.AddWithValue("@carmodelno", carmodelno);
            ds = Common.Data_noisevalidation.runParaCmdDS(cmd);
        }
        return ds;
    }

    [WebMethod]
    public DataTable get_Nv_combinecarlist(string applyno, string carcompomodelno, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"EXEC pr_get_combinecarlist @applyno,@carcompomodelno");

            cmd.Parameters.AddWithValue("@applyno", applyno);
            cmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);

            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;
    }

    [WebMethod]
    public DataTable get_Nv_carmodeldatarpt(string applyno, string carcompomodelno, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"EXEC pr_get_carmodeldatarpt @applyno,@carcompomodelno");

            cmd.Parameters.AddWithValue("@applyno", applyno);
            cmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);

            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;
    }

    [WebMethod]
    public DataTable get_Nv_latestcarbodyno(string applyno, string carcompomodelno, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"EXEC pr_get_latestcarbodyno @applyno,@carcompomodelno");

            cmd.Parameters.AddWithValue("@applyno", applyno);
            cmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);

            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;
    }

    [WebMethod]
    public DataTable get_Nv_representativevehiclerpt(string applyno, string carcompomodelno, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"EXEC pr_get_representativevehiclerpt @applyno,@carcompomodelno");

            cmd.Parameters.AddWithValue("@applyno", applyno);
            cmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);

            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;
    }
    [WebMethod]
    public DataTable get_Nv_vehiclespeedrpt(string rptno, string carmodelno, string ID, string PD)
    {

        DataTable dt = new DataTable();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"EXEC pr_get_vehiclespeedrpt @rptno,@carmodelno");

            cmd.Parameters.AddWithValue("@rptno", rptno);
            cmd.Parameters.AddWithValue("@carmodelno", carmodelno);

            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;
    }
    [WebMethod]
    public DataTable get_Nv_noisedefenseplan_lists(string applyno, string carcompomodelno, string ID, string PD)
    {

        DataTable dt = new DataTable();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select * from nv_noisedefenseplan where ndp_applyno=@applyno and ndp_carcompomodelno=@carcompomodelno ");

            cmd.Parameters.AddWithValue("@applyno", applyno);
            cmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;
    }

    [WebMethod]
    public void Insert_Nv_noisedefenseplan(string applyno, string carcompomodelno, string position, string defenseeqpname, string eqpmaterial, string eqpheight, string equremark, string account, string ID, string PD)
    {
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"INSERT INTO nv_noisedefenseplan
           (ndp_applyno
           ,ndp_carcompomodelno
           ,ndp_position
           ,ndp_defenseeqpname
           ,ndp_eqpmaterial
           ,ndp_eqpheight
           ,ndp_equremark
           ,ndp_createaccount
           ,ndp_createdate
           ,ndp_modaccount
           ,ndp_moddate)
     VALUES
           (@applyno
           ,@carcompomodelno
           ,@position
           ,@defenseeqpname  
           ,@eqpmaterial
           ,@eqpheight
           ,@equremark
           ,@account
           ,GETDATE()
           ,@account
           ,GETDATE())");

            cmd.Parameters.AddWithValue("@applyno", applyno);
            cmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
            cmd.Parameters.AddWithValue("@position", position);
            cmd.Parameters.AddWithValue("@defenseeqpname", defenseeqpname);
            cmd.Parameters.AddWithValue("@eqpmaterial", eqpmaterial);
            cmd.Parameters.AddWithValue("@eqpheight", eqpheight);
            cmd.Parameters.AddWithValue("@equremark", equremark);
            cmd.Parameters.AddWithValue("@account", account);

            Common.Data_noisevalidation.runParaCmd1(cmd);
        }
    }
    [WebMethod]
    public void Update_Nv_noisedefenseplan(string defenseplanid, string position, string defenseeqpname, string eqpmaterial, string eqpheight, string equremark, string modaccount, string ID, string PD)
    {
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"UPDATE  nv_noisedefenseplan
                            SET ndp_position = @position
                               ,ndp_defenseeqpname = @defenseeqpname
                               ,ndp_eqpmaterial = @eqpmaterial
                               ,ndp_eqpheight = @eqpheight
                               ,ndp_equremark = @equremark 
                               ,ndp_modaccount=@modaccount
                               ,ndp_moddate =GETDATE() 
                          WHERE ndp_defenseplanid=@defenseplanid");


            cmd.Parameters.AddWithValue("@position", position);
            cmd.Parameters.AddWithValue("@defenseeqpname", defenseeqpname);
            cmd.Parameters.AddWithValue("@eqpmaterial", eqpmaterial);
            cmd.Parameters.AddWithValue("@eqpheight", eqpheight);
            cmd.Parameters.AddWithValue("@equremark", equremark);
            cmd.Parameters.AddWithValue("@modaccount", modaccount);
            cmd.Parameters.AddWithValue("@defenseplanid", defenseplanid);

            Common.Data_noisevalidation.runParaCmd1(cmd);
        }
    }
    [WebMethod]
    public void Del_Nv_noisedefenseplan(string id, string ID, string PD)
    {
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd_del = new SqlCommand("delete from nv_noisedefenseplan where ndp_defenseplanid=@id");
            cmd_del.Parameters.AddWithValue("@id", id);
            Common.Data_noisevalidation.runParaCmd1(cmd_del);
        }
    }
    [WebMethod]
    public DataTable get_carmodeldataForReadnoly(string applyno, string carcompomodelno, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"exec pr_get_representativevehiclenames @applyno,@carcompomodelno");

            cmd.Parameters.AddWithValue("@applyno", applyno);
            cmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);          
            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;

    }
    [WebMethod]
    public string Insert_Nv_validationprocess(string applyno, string applystatus, string validationstatus, string applyopinion, string account, string ID, string PD)
    {
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"
                if exists(select abd_applyno from nv_applybasedata where abd_applyno=@applyno and abd_applystatus in ('20','21'))
                begin
                    SET XACT_ABORT ON
                    begin transaction
                
                    exec pr_insert_validationprocess @applyno,@validationstatus,@applyopinion,@account
            
                    UPDATE  nv_applybasedata
                                SET abd_applystatus = @applystatus                             
                              WHERE abd_applyno=@applyno
                    update nv_validationchecknotefornotpass 
                            set vcnnp_receivedate=GETDATE(),vcnnp_receiveaccount=@account

                    if (@@ERROR<>0)
                    begin
                        rollback tran;
                        select '收件失敗，請檢查狀態是否仍為送件' 
                    end
                    else
                    begin
                        commit tran;
                        select '收件完成'    
                    end
                end
                        ");

            cmd.Parameters.AddWithValue("@applyno", applyno);
            cmd.Parameters.AddWithValue("@applystatus", applystatus);
            cmd.Parameters.AddWithValue("@validationstatus", validationstatus);
            cmd.Parameters.AddWithValue("@applyopinion", applyopinion);
            cmd.Parameters.AddWithValue("@account", account);

            object obj_return = Common.Data_noisevalidation.runScalar(cmd);//   .runParaCmd1(cmd);
            return (string)obj_return;
        }
        else
        {
            return "收件失敗，請檢查狀態是否仍為送件";
        }
    }

    [WebMethod]
    public void Update_Nv_applystatus(string applyno, string applystatus, string ID, string PD)
    {
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"UPDATE  nv_applybasedata
                            SET abd_applystatus = @applystatus                               
                          WHERE abd_applyno=@applyno");

            cmd.Parameters.AddWithValue("@applyno", applyno);
            cmd.Parameters.AddWithValue("@applystatus", applystatus);

            Common.Data_noisevalidation.runParaCmd1(cmd);
        }
    }

    [WebMethod]
    public string submit_datacheck(string applyno, string ID, string PD)
    {
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"exec pr_check_submit_applydata @applyno");

            cmd.Parameters.AddWithValue("@applyno", applyno);
            object obj_execresult = Common.Data_noisevalidation.runScalar(cmd);
            return (string)obj_execresult;
        }
        return "無法使用";
    }

    public string submit_applydata(string applyno, string account, string ID, string PD)
    {
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"exec pr_applysubmit_datahandle @applyno,@account");

            cmd.Parameters.AddWithValue("@applyno", applyno);
            cmd.Parameters.AddWithValue("@account", account);
            object obj_execresult = Common.Data_noisevalidation.runScalar(cmd);
            return (string)obj_execresult;
        }
        return "無法使用";
    }

    public bool IsNoisetestrptExist(string carcompomodelno, string carmodelno, string ID, string PD)
    {
        DataSet ds_Noisetestrpt = new DataSet();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd_Applyno = new SqlCommand(@"
                                    select count(ntr_carcompomodelno) as ntrno from nv_noisetestrpt where ntr_carcompomodelno=@carcompomodelno and ntr_carmodelno=@carmodelno ");
            cmd_Applyno.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
            cmd_Applyno.Parameters.AddWithValue("@carmodelno", carmodelno);
            ds_Noisetestrpt = Common.Data_noisevalidation.runParaCmdDS(cmd_Applyno);
            if (!(ds_Noisetestrpt.Tables[0].Rows[0]["ntrno"].Equals(0)))
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        else
        {
            return false;
        }
    }
    //判斷是否已申請過沿用
    public bool IsFollow(string carcompomodelno, string ID, string PD)
    {
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select COUNT(*) as num from nv_applybasedata where abd_applyno in  (select cpm_applyno from nv_carcompomodel where cpm_applytype in ('2','5') and cpm_carcompomodelno=@carcompomodelno) and abd_applystatus in ('35','50','55')");//2沿用,5沿用及修改;35核閱完成,50已上傳平台,55歸檔
            cmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
            DataView dv_Follow = Common.Data_noisevalidation.runParaCmd(cmd);
            if (dv_Follow[0]["num"].Equals(0))
            {
                return false;
            }
            else
            {
                return true;
            }
        }
        else return false;
    }
    //判斷是否已有重覆的車型代碼
    [WebMethod]
    public bool CheckhReCarcompomodelno(string carcompomodelno, string ID, string PD)
    {
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd_check = new SqlCommand(@"select cpm_applyno,cpm_carcompomodelno from nv_carcompomodel where cpm_carcompomodelno=@carcompomodelno");
            cmd_check.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
            DataView dv_check = Common.Data_noisevalidation.runParaCmd(cmd_check);
            if (dv_check.Count > 0)
                return true;
            else return false;
        }
        else return false;
    }
    [WebMethod]
    public bool AlterCarcompomodelNo(string applyno, string carcompomodelno, string new_carcompomodelno, string applytype, string ID, string PD)
    {
        if (wsApplybasedataDetailValid(ID, PD))
        {
           
            string compidnoE = Session["compidno"].ToString(); //公司統編
            DataView dv = new MailParaControl().GetApplyBaseData(applyno);            
            string EngineFamily = dv[0]["EngineFamily"].ToString(); //引擎族
            string sIsDateoneExist = "";

            if (!(IsDateoneExist(compidnoE, carcompomodelno, EngineFamily, ID, PD)))
            {
                sIsDateoneExist = "0";
            }
            
            SqlCommand cmd = new SqlCommand(@"
                        SET XACT_ABORT ON
                        begin transaction
                                               
                        UPDATE nv_applydatafiles        SET adf_carcompomodelno=@new_carcompomodelno WHERE adf_applyno=@applyno and adf_carcompomodelno=@carcompomodelno
                        UPDATE nv_carcompomodel         SET cpm_carcompomodelno=@new_carcompomodelno WHERE cpm_applyno=@applyno and cpm_carcompomodelno=@carcompomodelno
                        UPDATE nv_carmodeldata          SET cmdg_carcompomodelno=@new_carcompomodelno WHERE cmdg_applyno=@applyno and cmdg_carcompomodelno=@carcompomodelno
                        UPDATE nv_fixcatalog            SET fc_carcompomodelno=@new_carcompomodelno WHERE fc_applyno=@applyno and fc_carcompomodelno=@carcompomodelno
                        UPDATE nv_gearratio             SET gr_carcompomodelno=@new_carcompomodelno WHERE gr_applyno=@applyno and gr_carcompomodelno=@carcompomodelno
                        UPDATE nv_noisedefenseplan      SET ndp_carcompomodelno=@new_carcompomodelno WHERE ndp_applyno=@applyno and ndp_carcompomodelno=@carcompomodelno
                        UPDATE nv_noisedefenseplanfiles SET ndpf_carcompomodelno=@new_carcompomodelno WHERE ndpf_applyno=@applyno and ndpf_carcompomodelno=@carcompomodelno
                        
                        if(@sIsDateoneExist='0')
                        begin
                            UPDATE nv_noisetestrpt          SET ntr_carcompomodelno=@new_carcompomodelno WHERE ntr_carcompomodelno=@carcompomodelno
                            UPDATE nv_noisetestrpt_remark   SET ntrr_carcompomodelno=@new_carcompomodelno WHERE ntrr_carcompomodelno=@carcompomodelno
                            UPDATE nv_testrptspeedupinfo    SET trsui_carcompomodelno=@new_carcompomodelno WHERE trsui_carcompomodelno=@carcompomodelno
                        end

                        UPDATE nv_validationchecknote   SET vcn_carcompomodelno=@new_carcompomodelno WHERE vcn_applyno=@applyno and vcn_carcompomodelno=@carcompomodelno
                        UPDATE nv_validationchecknotefornotpassdetail SET vcnnpd_carcompomodelno=@new_carcompomodelno WHERE vcnnpd_applyno=@applyno and vcnnpd_carcompomodelno=@carcompomodelno

                        if (@@ERROR<>0)
                        begin
                            rollback tran;
                            select 'false'  
                        end
                        else
                        begin
                            commit tran;
                            select 'true'    
                        end
                        ");

            cmd.Parameters.AddWithValue("@applyno", applyno);
            cmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
            cmd.Parameters.AddWithValue("@new_carcompomodelno", new_carcompomodelno);
            cmd.Parameters.AddWithValue("@applytype", applytype);
            cmd.Parameters.AddWithValue("@sIsDateoneExist", sIsDateoneExist);

            object obj_return = Common.Data_noisevalidation.runScalar(cmd);//   .runParaCmd1(cmd);            
            if ((obj_return.Equals("true")))
            {
                return true;
            }
            else
            {
                return false;
            }

        }
        else
        {
            return false;
        }
    }
    [WebMethod]
    public DataTable get_Nv_Applybasebarcode(string applyno, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"SELECT abd_barcodeno, abd_barcodeimg, abd_databelongcompidno, abd_applyno
                                                FROM nv_applybasedata
                                                WHERE abd_applyno = @applyno");

            cmd.Parameters.AddWithValue("@applyno", applyno);

            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;
    }

    //更新barcode
    [WebMethod]
    public void Update_Nv_applybasedataBarcode(string applyno, string barcodeno, byte[] barcodeimg, string ID, string PD)
    {
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"UPDATE nv_applybasedata
                            SET abd_barcodeno = @barcodeno  
                               ,abd_barcodeimg = @barcodeimg                            
                          WHERE abd_applyno=@applyno");

            cmd.Parameters.AddWithValue("@applyno", applyno);
            cmd.Parameters.AddWithValue("@barcodeno", barcodeno);
            cmd.Parameters.AddWithValue("@barcodeimg", barcodeimg);

            Common.Data_noisevalidation.runParaCmd1(cmd);
        }
    }
    /// <summary>
    /// 取得新的BARCODE編號
    /// </summary>
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    /// <param name="barcodetype"></param>
    /// <returns></returns>
    public string getNewBarCodeNo(string ID, string PD)
    {

        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"select top 1 * from vw_addrbarcode order by abd_barcodeno desc");
            DataTable dtBarCode = Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
            if (dtBarCode.Rows[0]["barcodeMax"].ToString() == "")
            {
                return "A000000001";
            }
            else
            {
                int nowNo = Convert.ToInt32(dtBarCode.Rows[0]["barcodeMax"].ToString());
                string barcodeno = "A" + string.Format("{0:000000000}", (nowNo + 1));
                return barcodeno;
            }
        } return "";
    }

    [WebMethod]
    public DataTable get_Nv_additionalbodyno(string applyno, string carcompomodelno, string carmodelno, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"SELECT TOP 1 abn_bodyno,abn_engineno 
                                                FROM nv_additionalbodyno 
                                               WHERE abn_applyno=@applyno 
                                                 AND abn_carcompomodelno=@carcompomodelno 
                                                 AND abn_carmodelno=@carmodelno 
                                            ORDER BY abn_moddate DESC ");

            cmd.Parameters.AddWithValue("@applyno", applyno);
            cmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
            cmd.Parameters.AddWithValue("@carmodelno", carmodelno);

            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;
    }

    [WebMethod]
    public DataTable get_Nv_validationprocess(string applyno, string applystatus, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"SELECT TOP 1 vp_applyopinion 
                                              FROM nv_validationprocess 
                                              WHERE vp_applyno=@applyno   
                                              AND vp_applystatus=@applystatus 
                                              ORDER BY vp_execdate DESC");

            cmd.Parameters.AddWithValue("@applyno", applyno);
            cmd.Parameters.AddWithValue("@applystatus", applystatus);
            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;
    }
    //更新意見欄位 複審、二級主管意見
    [WebMethod]
    public string UpdateValidationChecknote(string applyno, string opinion, string validationitemno, string ID, string PD)
    {
        if (wsApplybasedataDetailValid(ID, PD))
        {
            try
            {
                SqlCommand cmd_upd = new SqlCommand(@"update nv_validationchecknote set vcn_opinion=@opinion where vcn_applyno=@applyno and vcn_validationitemno=@validationitemno select '意見修改完成' ");
                cmd_upd.Parameters.AddWithValue("@applyno", applyno);
                cmd_upd.Parameters.AddWithValue("@opinion", opinion);
                cmd_upd.Parameters.AddWithValue("@validationitemno", validationitemno);

                object obj_return = Common.Data_noisevalidation.runScalar(cmd_upd);
                return (string)obj_return;
            }
            catch (Exception ex)
            {
                return ex.ToString().Substring(0, 200);
                //if (ex.ToString().IndexOf("Base") > -1)
                //    return "Base-64字串中的無效字元";
                //else
                //    return "帳號狀態以更改失敗";
            }
        }
        else
        {
            return "驗證失敗";
        }
    }
    //更新意見欄位 複審、二級主管意見+carcompomodelno
    [WebMethod]
    public string UpdateValidationChecknote(string applyno, string carcompomodelno, string opinion, string validationitemno, string ID, string PD)
    {
        if (wsApplybasedataDetailValid(ID, PD))
        {
            try
            {
                SqlCommand cmd_upd = new SqlCommand(@"update nv_validationchecknote set vcn_opinion=@opinion where vcn_applyno=@applyno and vcn_carcompomodelno=@carcompomodelno and vcn_validationitemno=@validationitemno select '意見修改完成' ");
                cmd_upd.Parameters.AddWithValue("@applyno", applyno);
                cmd_upd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
                cmd_upd.Parameters.AddWithValue("@opinion", opinion);
                cmd_upd.Parameters.AddWithValue("@validationitemno", validationitemno);

                object obj_return = Common.Data_noisevalidation.runScalar(cmd_upd);
                return (string)obj_return;
            }
            catch (Exception ex)
            {
                return ex.ToString().Substring(0, 200);
                //if (ex.ToString().IndexOf("Base") > -1)
                //    return "Base-64字串中的無效字元";
                //else
                //    return "帳號狀態以更改失敗";
            }
        }
        else
        {
            return "驗證失敗";
        }
    }

    /// <summary>
    /// 供污染複至資料-機踏車
    /// </summary>
    /// <param name="databelongcompidno"></param>公司統編 加密
    /// <param name="vechiletype"></param>char(1) M
    /// <param name="beimport"></param>char(1) 1進口/2國產
    /// <param name="carstyleyear"></param>varchar(4) EX:2012
    /// <param name="engineamily"></param>varchar(30) 引擎族
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    [WebMethod]
    public DataSet CommonDataForPopulation_M(string databelongcompidno, string vechiletype, string beimport, string carstyleyear, string engineamily, string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsApplybasedataDetailValid(ID, PD))
        {

            SqlCommand cmd_upd = new SqlCommand(@"exec pr_provide_common_data_for_population_M @databelongcompidno,@vechiletype,@beimport,@carstyleyear,@engineamily ");
            cmd_upd.Parameters.AddWithValue("@databelongcompidno", databelongcompidno);
            cmd_upd.Parameters.AddWithValue("@vechiletype", vechiletype);
            cmd_upd.Parameters.AddWithValue("@beimport", beimport);
            cmd_upd.Parameters.AddWithValue("@carstyleyear", carstyleyear);
            cmd_upd.Parameters.AddWithValue("@engineamily", engineamily);
            return Common.Data_noisevalidation.runParaCmdDS(cmd_upd);
        }
        return ds;
    }
    [WebMethod]
    public DataTable get_rejectapplydata(string applyno, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"EXEC pr_rejectapply @applyno");
            cmd.Parameters.AddWithValue("@applyno", applyno);

            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;

    }
    [WebMethod]
    public DataSet get_rejectapplyItlist(string applyno, string ID, string PD)
    {
        DataSet ds_carmodeno = new DataSet();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"EXEC pr_rejectapplyItemlist @applyno");
            cmd.Parameters.AddWithValue("@applyno", applyno);

            return Common.Data_noisevalidation.runParaCmdDS(cmd);
        }
        else return ds_carmodeno;

    }
    [WebMethod]
    public DataSet get_carcompomodeno(string applyno, string ID, string PD)
    {
        DataSet ds_carmodeno = new DataSet();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            //compidno為加密過
            SqlCommand cmd_carmodeno = new SqlCommand(@"select 
            cpm_carcompomodelno CarCompoModel
            from dbo.nv_carcompomodel where cpm_applyno=@applyno and cpm_applytype not in ('6','9') -- 不異動,撤銷 不顯示");
            cmd_carmodeno.Parameters.AddWithValue("@applyno", applyno);
            return Common.Data_noisevalidation.runParaCmdDS(cmd_carmodeno);
        }
        else return ds_carmodeno;
    }
    /// <summary>
    /// 供污染複至資料-汽車
    /// </summary>
    /// <param name="databelongcompidno"></param>公司統編 加密
    /// <param name="vechiletype"></param>char(1) G
    /// <param name="beimport"></param>char(1) 1進口/2國產
    /// <param name="carstyleyear"></param>varchar(4) EX:2012
    /// <param name="engineamily"></param>varchar(30) 引擎族
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    [WebMethod]
    public DataSet CommonDataForPopulation_G(string databelongcompidno, string vechiletype, string beimport, string carstyleyear, string engineamily, string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsApplybasedataDetailValid(ID, PD))
        {

            SqlCommand cmd_upd = new SqlCommand(@"exec pr_provide_common_data_for_population_G @databelongcompidno,@vechiletype,@beimport,@carstyleyear,@engineamily ");
            cmd_upd.Parameters.AddWithValue("@databelongcompidno", databelongcompidno);
            cmd_upd.Parameters.AddWithValue("@vechiletype", vechiletype);
            cmd_upd.Parameters.AddWithValue("@beimport", beimport);
            cmd_upd.Parameters.AddWithValue("@carstyleyear", carstyleyear);
            cmd_upd.Parameters.AddWithValue("@engineamily", engineamily);

            return Common.Data_noisevalidation.runParaCmdDS(cmd_upd);
        }
        return ds;
    }
    /// <summary>
    /// 供污染複至資料-機踏車
    /// </summary>
    /// <param name="databelongcompidno"></param>公司統編 加密
    /// <param name="vechiletype"></param>char(1) M
    /// <param name="beimport"></param>char(1) 1進口/2國產
    /// <param name="carstyleyear"></param>varchar(4) EX:2012
    /// <param name="engineamily"></param>varchar(30) 引擎族
    /// <param name="ID"></param>
    /// <param name="PD"></param>
    [WebMethod]
    public DataSet CommonDataForPopulation_D(string databelongcompidno, string vechiletype, string beimport, string carstyleyear, string engineamily, string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsApplybasedataDetailValid(ID, PD))
        {

            SqlCommand cmd_upd = new SqlCommand(@"exec pr_provide_common_data_for_population_D @databelongcompidno,@vechiletype,@beimport,@carstyleyear,@engineamily ");
            cmd_upd.Parameters.AddWithValue("@databelongcompidno", databelongcompidno);
            cmd_upd.Parameters.AddWithValue("@vechiletype", vechiletype);
            cmd_upd.Parameters.AddWithValue("@beimport", beimport);
            cmd_upd.Parameters.AddWithValue("@carstyleyear", carstyleyear);
            cmd_upd.Parameters.AddWithValue("@engineamily", engineamily);
            return Common.Data_noisevalidation.runParaCmdDS(cmd_upd);
        }
        return ds;
    }
    //與污染比對 當次噪音資料
    [WebMethod]
    public DataSet SelfDataForPopulation_G(string applyno, string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsApplybasedataDetailValid(ID, PD))
        {

            SqlCommand cmd_upd = new SqlCommand(@"exec pr_self_data_for_population_G @applyno ");
            cmd_upd.Parameters.AddWithValue("@applyno", applyno);           
            return Common.Data_noisevalidation.runParaCmdDS(cmd_upd);
        }
        return ds;
    }
    [WebMethod]
    public DataSet SelfDataForPopulation_D(string applyno, string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsApplybasedataDetailValid(ID, PD))
        {

            SqlCommand cmd_upd = new SqlCommand(@"exec pr_self_data_for_population_D @applyno ");
            cmd_upd.Parameters.AddWithValue("@applyno", applyno);
            return Common.Data_noisevalidation.runParaCmdDS(cmd_upd);
        }
        return ds;
    }
    [WebMethod]
    public DataSet SelfDataForPopulation_M(string applyno, string ID, string PD)
    {
        DataSet ds = new DataSet();
        if (wsApplybasedataDetailValid(ID, PD))
        {

            SqlCommand cmd_upd = new SqlCommand(@"exec pr_self_data_for_population_M @applyno ");
            cmd_upd.Parameters.AddWithValue("@applyno", applyno);
            return Common.Data_noisevalidation.runParaCmdDS(cmd_upd);
        }
        return ds;
    }
    //延伸、修改 沿用 如果為系統第一筆需調整為可編輯
    [WebMethod]
    public bool IsDateoneExist(string databelongcompidno, string carcompomodelno, string engineamily, string ID, string PD)
    {
        DataSet ds_OneExist = new DataSet();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd_Applyno = new SqlCommand(@"exec pr_check_data_one_exist @databelongcompidno,@carcompomodelno,@engineamily");
            cmd_Applyno.Parameters.AddWithValue("@databelongcompidno", databelongcompidno);
            cmd_Applyno.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
            cmd_Applyno.Parameters.AddWithValue("@engineamily", engineamily);
            ds_OneExist = Common.Data_noisevalidation.runParaCmdDS(cmd_Applyno);
            if (!(ds_OneExist.Tables[0].Rows[0]["OneExist"].Equals(0)))
            {
                return true; //已有資料存在
            }
            else
            {
                return false;
            }
        }
        else
        {
            return false;
        }
    }
    //延伸、修改 沿用 如果為系統第一筆需調整為可編輯(給測試報告用)
    [WebMethod]
    public bool IsDateoneExistForTRP(string databelongcompidno, string carcompomodelno, string engineamily, string ID, string PD)
        {
        DataSet ds_OneExist = new DataSet();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd_Applyno = new SqlCommand(@"exec pr_check_data_one_exist_For_TRP @databelongcompidno,@carcompomodelno,@engineamily");
            cmd_Applyno.Parameters.AddWithValue("@databelongcompidno", databelongcompidno);
            cmd_Applyno.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
            cmd_Applyno.Parameters.AddWithValue("@engineamily", engineamily);
            ds_OneExist = Common.Data_noisevalidation.runParaCmdDS(cmd_Applyno);
            if (!(ds_OneExist.Tables[0].Rows[0]["OneExist"].Equals(0)))
            {
                return true; //已有資料存在
            }
            else
            {
                return false;
            }
        }
        else
        {
            return false;
        }
    }
    public bool IsSpeedupinfoExist(string carcompomodelno, string carmodelno, string testrptno)
    {
        SqlCommand sqlCmd = new SqlCommand(@"
if exists(  select * from nv_testrptspeedupinfo where trsui_carcompomodelno=@carcompomodelno and trsui_carmodelno=@carmodelno and trsui_testrptno=@testrptno)
begin
	select 1
	return
end
select 0
");
        sqlCmd.Parameters.AddWithValue("@testrptno", testrptno);
        sqlCmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
        sqlCmd.Parameters.AddWithValue("@carmodelno", carmodelno);
        object obj = Common.Data_noisevalidation.runScalar(sqlCmd);
        return obj.ToString() == "1" ? true : false;
    }

    //取得合格證統計資料
    [WebMethod]
    public DataTable get_Nv_Statistical(string Year, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"exec pr_statistical_table @Year");

            cmd.Parameters.AddWithValue("@Year", Year);
            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;
    }
    //取得審件進度資料
    [WebMethod]
    public DataTable get_Nv_Progresscontrol(string applystatus, string compname, string account, string sCarcompomodelno, string vechiletype, string powerfrom, string sdate, string edate, string datatype, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"exec pr_static_progresscontrol_table
                        @applystatus 
                       ,@compname
                       ,@account 
                       ,@carcompomodelno
                       ,@vechiletype 
                       ,@powerfrom 
                       ,@validatedsdate
                       ,@validatededate
                       ,@datatype
                       ");
            cmd.Parameters.AddWithValue("@applystatus", applystatus);
            cmd.Parameters.AddWithValue("@compname", compname);
            cmd.Parameters.AddWithValue("@account", account);
            cmd.Parameters.AddWithValue("@carcompomodelno", sCarcompomodelno);
            cmd.Parameters.AddWithValue("@vechiletype", vechiletype);
            cmd.Parameters.AddWithValue("@powerfrom", powerfrom);
            cmd.Parameters.AddWithValue("@validatedsdate", sdate);
            cmd.Parameters.AddWithValue("@validatededate", edate);
            cmd.Parameters.AddWithValue("@datatype", datatype);

            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;
    }
    //取得抽查明細
    [WebMethod]
    public DataTable get_Nv_ProgressSpot(string sdate, string edate, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"exec pr_static_progressspot_table
                        @validatedsdate,
                        @validatededate                      
                       ");

            cmd.Parameters.AddWithValue("@validatedsdate", sdate);
            cmd.Parameters.AddWithValue("@validatededate", edate);

            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;
    }
    //清冊
    [WebMethod]
    public DataTable get_Nv_Inventory(string compname, string isimport, string vechiletype, string cartype, string carcompomodelno, string enginefamily, string powerfrom,string sdate, string edate, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"exec pr_static_inventory_table
                        @compname,
                        @isimport,
                        @vechiletype,
                        @cartype,
                        @carcompomodelno,
                        @enginefamily,
                        @powerfrom,
                        @validatedsdate,
                        @validatededate                      
                       ");
            cmd.Parameters.AddWithValue("@compname", compname);
            cmd.Parameters.AddWithValue("@isimport", isimport);
            cmd.Parameters.AddWithValue("@vechiletype", vechiletype);
            cmd.Parameters.AddWithValue("@cartype", cartype);
            cmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
            cmd.Parameters.AddWithValue("@enginefamily", enginefamily);
            cmd.Parameters.AddWithValue("@powerfrom", powerfrom);
            cmd.Parameters.AddWithValue("@validatedsdate", sdate);
            cmd.Parameters.AddWithValue("@validatededate", edate);

            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;
    }
    //取得初審人員平均審驗天數
    [WebMethod]
    public DataTable get_Nv_AverageValidateddays(string sdate, string edate, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"exec pr_static_average_validateddays
                        @validatedsdate,
                        @validatededate                      
                       ");

            cmd.Parameters.AddWithValue("@validatedsdate", sdate);
            cmd.Parameters.AddWithValue("@validatededate", edate);

            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;
    }
    //取得沒合格證編號的統計表
    [WebMethod]
    public DataTable get_Nv_NonCertdocno(string sdate, string edate, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"exec pr_static_countcertdocno_table
                        @validatedsdate,
                        @validatededate                      
                       ");

            cmd.Parameters.AddWithValue("@validatedsdate", sdate);
            cmd.Parameters.AddWithValue("@validatededate", edate);

            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;
    }
    //彙編
    [WebMethod]
    public DataTable get_Nv_collection(string compname, string enginefamily, string enginesn, string carstyleyear, string revoked, string sdate, string edate, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"exec pr_static_collection_table
                        @compname,
                        @enginefamily,                    
                        @enginesn,
                        @carstyleyear,
                        @revoked,
                        @validatedsdate,
                        @validatededate                      
                       ");
            cmd.Parameters.AddWithValue("@compname", compname);
            cmd.Parameters.AddWithValue("@enginefamily", enginefamily);
            cmd.Parameters.AddWithValue("@enginesn", enginesn);
            cmd.Parameters.AddWithValue("@carstyleyear", carstyleyear);
            cmd.Parameters.AddWithValue("@revoked", revoked);
            cmd.Parameters.AddWithValue("@validatedsdate", sdate);
            cmd.Parameters.AddWithValue("@validatededate", edate);

            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;
    }
    //進出口彙編
    [WebMethod]
    public DataTable get_Nv_Exportcollection(string compname, string isimport, string vechiletype, string powerfrom, string sdate, string edate, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"exec pr_static_exportcollection_table
                        @compname,
                        @isimport,
                        @vechiletype,
                        @powerfrom,
                        @validatedsdate,
                        @validatededate                      
                       ");
            cmd.Parameters.AddWithValue("@compname", compname);
            cmd.Parameters.AddWithValue("@isimport", isimport);
            cmd.Parameters.AddWithValue("@vechiletype", vechiletype);
            cmd.Parameters.AddWithValue("@powerfrom", powerfrom);
            cmd.Parameters.AddWithValue("@validatedsdate", sdate);
            cmd.Parameters.AddWithValue("@validatededate", edate);

            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;
    }
    public void Insert_tmp(string str, string applyno)
    {
        SqlCommand cmd = new SqlCommand(@"INSERT INTO nv_pollutioncomparedapplydata
           (pcad_str,pcad_applyno )
            VALUES
           (@str,@applyno)");

        cmd.Parameters.AddWithValue("@str", str);
        cmd.Parameters.AddWithValue("@applyno", applyno);

        Common.Data_noisevalidation.runParaCmd1(cmd);

    }

    //當申請型式 不為新車型 取得上次申請代表車資料
    [WebMethod]
    public DataTable get_unNewApplyData(string applyno, string carcompomodelno, string ID, string PD)
    {
        DataTable dt = new DataTable();
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"exec pr_get_unNewApplyData
                        @applyno,
                        @carcompomodelno                                                            
                       ");
            cmd.Parameters.AddWithValue("@applyno", applyno);
            cmd.Parameters.AddWithValue("@carcompomodelno", carcompomodelno);
            return Common.Data_noisevalidation.runParaCmdDS(cmd).Tables[0];
        }
        return dt;
    }
    //車型名稱前10碼的比較
    public bool CheckCarnoLeft10(string applyno, string Old_carno, string New_carno, string ID, string PD)
    {
        if (wsApplybasedataDetailValid(ID, PD))
        {
            SqlCommand cmd = new SqlCommand(@"
                            declare @old_carcompomodelno varchar(12) --取前10(電動車11)碼比對
                            declare @new_carcompomodelno varchar(12) --取前10(電動車11)碼比對
                            declare @powerfrom varchar(1)


                            select @powerfrom = abd_powerfrom from nv_applybasedata 
                            where abd_applyno = @applyno

	                            if (@powerfrom ='3') --電動車
	                            begin
	                              set @old_carcompomodelno = left(@Old_carno,11) 
	                              set @new_carcompomodelno = left(@New_carno,11) 
	                            end
	                            else
	                            begin	   
	                                set @old_carcompomodelno = left(@Old_carno,10) 
	                                set @new_carcompomodelno = left(@New_carno,10) 
	                            end
	
	                            if (@old_carcompomodelno <> @new_carcompomodelno)
	                            begin
		                            select 'N'
	                            end
	                            else
	                            begin
		                            select 'Y'
	                            end                                                          
                       ");

            cmd.Parameters.AddWithValue("@applyno", applyno);
            cmd.Parameters.AddWithValue("@Old_carno", Old_carno);
            cmd.Parameters.AddWithValue("@New_carno", New_carno);
            object obj_return = Common.Data_noisevalidation.runScalar(cmd);

            if ((obj_return.Equals("Y")))
            {
                return true;
            }
            else
            {
                return false;
            }
        }
        else
        {
            return false;
        }
    }

    public bool IsPctExist(string CyDesc)
    {
        SqlCommand sqlCmd = new SqlCommand(@"
if exists(  select ct_cy_id,ct_cy_desc from nv_codetbl_country where ct_cy_desc=@CyDesc)
begin
	select 1
	return
end
select 0
");
        sqlCmd.Parameters.AddWithValue("@CyDesc", CyDesc);       
        object obj = Common.Data_noisevalidation.runScalar(sqlCmd);
        return obj.ToString() == "1" ? true : false;
    }

    #region LINQ to DataSet 噪音與污染車資料比對
    public string compareLinqToDS(DataTable sourcedt, string sCCar, string sNCar)
    {//r.IsNull("LDAP群組代號") ? "" : r.Field<string>("LDAP群組代號").Trim()
        IEnumerable<DataRow> myrows = from dt in sourcedt.AsEnumerable()
                                      where (dt.IsNull(sCCar) ? "" : sCCar.Equals("MailAddress") ? dt.Field<string>(sCCar).Replace(" ", "").Trim() : sCCar.Equals("SplyComAddr") ? dt.Field<string>(sCCar).Replace(" ", "").Trim() : dt.Field<string>(sCCar).Trim()) == sNCar.Trim()
                                      select dt;
        if (myrows.Count() == 0)
        {
            return string.Format("{0}", sCCar);
        }
        else
        {
            return string.Format("{0}", myrows.Count().ToString());
        }
    }
    #endregion

    #region 比對不一致欄位字串轉中文
    public string compareEnStrtoCn(string sEnStr)
    {
        string sr_En = sEnStr.TrimEnd(',');
        string sr_Cn = "";
        string[] aryEn = sr_En.Split(',');

        for (int i = 0; i < aryEn.Length; i++)
        {
            switch (aryEn[i])
            {
                case "Manufacturer":
                    sr_Cn += "製造商" + "、";
                    break;
                case "MailAddress":
                case "SplyComAddr":
                    sr_Cn += "合格證地址" + "、";
                    break;
                case "MaxKW":
                    sr_Cn += "馬達最大馬力" + "、";
                    break;
                case "EngineKw":
                    sr_Cn += "最大馬力" + "、";
                    break;
                case "Maxrpm":
                    sr_Cn += "馬達最大馬力轉速" + "、";
                    break;
                case "EngineRpm":
                    sr_Cn += "最大馬力轉速" + "、";
                    break;
                case "BasicEngine":
                case "BasicEngineNo":
                    sr_Cn += "基本引擎" + "、";
                    break;
                case "Transmission":
                case "TSD":
                    sr_Cn += "變速系統" + "、";
                    break;
                case "Coolin":
                case "TypeCoolingSystem":
                    sr_Cn += "冷卻方式" + "、";
                    break;
                case "CylinderNum":
                case "NumberCylinders":
                    sr_Cn += "汽缸數" + "、";
                    break;
                case "Bore":
                    sr_Cn += "缸徑" + "、";
                    break;
                case "Stroke":
                    sr_Cn += "衝程" + "、";
                    break;
                case "Compression":
                case "CompressionRatio_Nominal":
                    sr_Cn += "壓縮比" + "、";
                    break;
                case "AirAspiration":
                case "MethodAirAspiration":
                    sr_Cn += "供氣方式" + "、";
                    break;
                case "DrivenWheels":
                case "DW":
                    sr_Cn += "驅動方式" + "、";
                    break;
                case "FinalDriveRatio":
                case "FDR":
                    sr_Cn += "差速器-齒比" + "、";
                    break;
                case "Brand":
                    sr_Cn += "廠牌" + "、";
                    break;
                case "CCarModel":
                case "CarStyleName_CName":
                    sr_Cn += "中文車型名" + "、";
                    break;
                case "ECarModel":
                case "CarStyleName_E":
                    sr_Cn += "英文車型名" + "、";
                    break;
                case "CarModelType":
                case "CarStyleID":
                    sr_Cn += "車型代碼" + "、";
                    break;
                case "Exhaust":
                case "CC":
                    sr_Cn += "排氣量" + "、";
                    break;
                case "TransmissionType":
                case "Gear":
                    sr_Cn += "排檔方式" + "、";
                    break;
                case "TransmissionNum":
                case "GearCount":
                    sr_Cn += "排檔數" + "、";
                    break;
                case "CarDoorCount":
                case "DoorCount":
                    sr_Cn += "門數" + "、";
                    break;
                case "Others":
                    sr_Cn += "其他" + "、";
                    break;
                case "GearRatios1":
                case "GN":
                    sr_Cn += "齒比" + "、";
                    break;
                default:
                    sr_Cn += aryEn[i].ToString() + "、";
                    break;
            }
        }

        return string.Format("{0}", sr_Cn.TrimEnd('、'));

    }
    #endregion

    private bool wsApplybasedataDetailValid(string ID, string PD)
    {
        string[][] x = new string[7][];
        x[0] = new string[2] { "AOS1", "IJOIJUdAs" };
        x[1] = new string[2] { "AOS2", "NJHUIGi8s" };
        x[2] = new string[2] { "AOS3", "djis7G8DS" };
        x[3] = new string[2] { "AOS4", "nuiEhi97D" };
        x[4] = new string[2] { "AOS5", "njhuV723S" };
        x[5] = new string[2] { "AOS6", "mmmMKDUUs" };
        x[6] = new string[2] { "AOS7", "oko8HGwdS" };

        for (int i = 0; i < 7; i++)
        {
            if (x[i][0].ToString() == ID && x[i][1].ToString() == PD)
            {
                return true;
            }
        }
        return false;
    }
}
