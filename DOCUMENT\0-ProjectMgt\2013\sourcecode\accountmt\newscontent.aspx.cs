﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Configuration;
using System.Data;

public partial class accountmt_newscontent : Cryptography
{
    protected void Page_Load(object sender, EventArgs e)
    {
        if (!IsPostBack)
        {
            if (Request["datatype"] == null || Request["EDID"] == null)
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.參數正確性"].ToString());
                Response.End();
                return;
            }

            string datatype = DeCode(Request["datatype"].ToString());
            if (datatype == "01")
            {

                lbl_PageTitle.Text = "最新消息";
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.參數正確性"].ToString());
                Response.End();
                return;
            }

            BindData();
        }
    }

    private void BindData()
    {
        wsNewsInfo newsinfo = new wsNewsInfo();
        string EDID = DeCode(Request["EDID"].ToString());
        string[] radom_N = wsNewsValidate(getRadNum());
        DataTable dtBase = newsinfo.getnewsdataBase(EDID, radom_N[0].ToString(), radom_N[1].ToString());
        if (dtBase.Rows.Count > 0)
        {
            lbl_title.Text = dtBase.Rows[0]["ed_title"].ToString();
            lbl_content.Text = dtBase.Rows[0]["ed_content"].ToString().Replace("\r\n", "<br>");
        }
    }
    protected void lbtn_TOP_Click(object sender, EventArgs e)
    {
        Response.Redirect("index.aspx", false);
    }
}