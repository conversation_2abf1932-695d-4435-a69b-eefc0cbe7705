﻿<%@ Page Title="" Language="C#" MasterPageFile="~/masterpage/MasterPage.master" AutoEventWireup="true"
    CodeFile="accountprioritygrpmt.aspx.cs" Inherits="systemowner_accountprioritygrpmt" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="fullheader">
        <table width="100%" cellspacing="0" cellpadding="0" class="fullheaderinfo">
            <tr>
                <td valign="top">
                    <b>使用者帳號管理</b>
                </td>
                <td align="right" valign="top" class="font_loginInfo">
                </td>
            </tr>
        </table>
    </div>
    <!--{* fullheader end *}-->
    <div class="fullcontent">
        <div class="formstyle">
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="font_fullcontent">
                <tr>
                    <td align="right">
                        使用者姓名：
                    </td>
                    <td>
                        <asp:Label ID="lbl_username" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        統一編號：
                    </td>
                    <td>
                        <asp:Label ID="lbl_compidno" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        公司名稱：
                    </td>
                    <td>
                        <asp:Label ID="lbl_compname" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        電話：
                    </td>
                    <td>
                        <asp:Label ID="lbl_usertel" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        電子郵件地址：
                    </td>
                    <td colspan="3">
                        <asp:Label ID="lbl_userEmail" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        使用權限：
                    </td>
                    <td colspan="3">
                        <asp:CheckBox ID="cb_usesystemN" runat="server" Checked="true" Enabled="false" Text="噪音" />
                        <asp:CheckBox ID="cb_usersystemP" runat="server" Enabled="false" Text="污染" />
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        系統權限：
                    </td>
                    <td colspan="3">
                        <asp:CheckBoxList ID="cbl_grouplist" runat="server" RepeatDirection="Horizontal"
                            RepeatLayout="Flow" Enabled="False">
                        </asp:CheckBoxList>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        廠商公司帳號管理者：
                    </td>
                    <td colspan="3">
                        <asp:RadioButtonList ID="rbl_isCompMang" runat="server" RepeatDirection="Horizontal" RepeatLayout="Flow">
                            <asp:ListItem Value="1" Text="是"></asp:ListItem>
                            <asp:ListItem Value="0" Text="否"></asp:ListItem>
                        </asp:RadioButtonList>
                    </td>
                </tr>
                                <tr>
                    <td align="right">
                        一般使用者：
                    </td>
                    <td colspan="3">
                        <asp:RadioButtonList ID="rbl_isUser" runat="server" RepeatDirection="Horizontal" RepeatLayout="Flow">
                            <asp:ListItem Value="1" Text="是"></asp:ListItem>                         
                        </asp:RadioButtonList>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        帳號使用狀況：
                    </td>
                    <td>
                        <asp:RadioButtonList ID="rbl_accountstatus" runat="server" RepeatDirection="Horizontal"
                            RepeatLayout="Flow">
                            <asp:ListItem Text="使用中" Value="2" Selected="True"></asp:ListItem>
                            <asp:ListItem Text="停止使用" Value="3"></asp:ListItem>
                        </asp:RadioButtonList>
                    </td>
                </tr>
                <tr>
                    <td colspan="2" align="center">
                        <asp:Button ID="btn_Save" runat="server" Text="存檔" OnClick="btn_Save_Click" CssClass="btn_mouseout"
                            onmouseover="this.className='btn_mouseover'" onmouseout="this.className='btn_mouseout'" />&nbsp;
                        <asp:Button ID="btn_Cancel" runat="server" Text="取消" OnClick="btn_Cancel_Click" CssClass="btn_mouseout"
                            onmouseover="this.className='btn_mouseover'" onmouseout="this.className='btn_mouseout'" />&nbsp;
                        <asp:Button ID="btn_ReSend" runat="server" Text="重發帳號密碼" OnClick="btn_ReSend_Click"
                            CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'" onmouseout="this.className='btn_mouseout'" />
                            <asp:Button ID="btn_Unlock" runat="server" Text="解除鎖定" OnClick="btn_Unlock_Click"
                            CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'" 
                            onmouseout="this.className='btn_mouseout'" Visible="False" />
                    </td>
                </tr>
            </table>
        </div>
    </div>
    <!--{* fullcontent end *}-->
    <div class="fullfooter">
    </div>
    <!--{* content end *}-->
</asp:Content>
