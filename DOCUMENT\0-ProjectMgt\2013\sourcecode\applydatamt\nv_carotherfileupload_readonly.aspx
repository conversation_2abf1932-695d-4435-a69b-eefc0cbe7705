﻿<%@ Page Title="" Language="C#" MasterPageFile="~/masterpage/MasterPage.master" AutoEventWireup="true"
    CodeFile="nv_carotherfileupload_readonly.aspx.cs" Inherits="applydatamt_nv_carotherfileupload_readonly" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="fullheader">
        <table cellpadding="0" cellspacing="0" class="fullheaderinfo" width="100%">
            <tr>
                <td valign="top">
                    <b>檔案唯讀</b>
                </td>
                <td align="right" class="font_loginInfo" valign="top">
                </td>
            </tr>
        </table>
    </div>
    <!--{* fullheader end *}-->
    <div class="fullcontent">
        <table border="0" cellspacing="0" cellpadding="0" width="100%" class="formstyle">
            <tr>
                <td align="right">
                    申請編號：
                </td>
                <td>
                    <asp:Label ID="lab_applyno" runat="server"></asp:Label>
                </td>
                <td align="right">
                    車型組代號：
                </td>
                <td>
                    <asp:Label ID="lab_carcompomodelno" runat="server"></asp:Label>
                </td>
            </tr>
        </table>
        <div class="tableoutcome">
            <asp:GridView ID="gv_data" runat="server" AutoGenerateColumns="False" CellSpacing="0"
                CellPadding="0" BorderWidth="0" GridLines="None" Width="100%" OnRowDataBound="gv_data_RowDataBound"
                CssClass="font_fullcontent" AllowPaging="True" OnPageIndexChanging="gv_data_PageIndexChanging"
                Style="display: none;">
                <Columns>
                    <asp:TemplateField HeaderText="文件名稱" ItemStyle-CssClass="lineleft" HeaderStyle-CssClass="header">
                        <ItemTemplate>
                            <asp:LinkButton ID="lbnt_userfilename" runat="server"></asp:LinkButton>
                        </ItemTemplate>
                        <HeaderStyle CssClass="header"></HeaderStyle>
                        <ItemStyle CssClass="lineleft"></ItemStyle>
                    </asp:TemplateField>
                    <asp:BoundField ItemStyle-CssClass="lineright" HeaderStyle-CssClass="lineright" DataField="afi_uploaddate"
                        HeaderText="上傳日期" DataFormatString="{0:d}" />
                    <asp:TemplateField Visible="false">
                        <ItemTemplate>
                            <asp:Label ID="lbl_afino" runat="server" Text='<%# Eval("afi_fileid").ToString() %>'></asp:Label></ItemTemplate>
                    </asp:TemplateField>
                </Columns>
                <EmptyDataRowStyle ForeColor="Red" />
                <PagerStyle CssClass="S_PageCss" HorizontalAlign="Center" />
                <EmptyDataRowStyle ForeColor="Red" />
                <EmptyDataTemplate>
                    尚未上傳任何檔案
                </EmptyDataTemplate>
                <PagerStyle CssClass="S_PageCss" HorizontalAlign="Center" />
            </asp:GridView>
            <br>
            <asp:GridView ID="gv_paydata" runat="server" AutoGenerateColumns="False" OnRowDataBound="gv_paydata_RowDataBound"
                CellSpacing="0" CellPadding="0" BorderWidth="0" GridLines="None" Width="100%"
                CssClass="font_fullcontent" AllowPaging="True" OnPageIndexChanging="gv_paydata_PageIndexChanging">
                <Columns>
                    <asp:BoundField ItemStyle-CssClass="lineleft" HeaderStyle-CssClass="lineleft" ItemStyle-HorizontalAlign="Center" HeaderStyle-HorizontalAlign="Center" HeaderText="局號" DataField="pd_bankaccount" />
                    <asp:BoundField ItemStyle-CssClass="lineleft" HeaderStyle-CssClass="lineleft" ItemStyle-HorizontalAlign="Center" HeaderStyle-HorizontalAlign="Center" HeaderText="日期" DataField="pd_paydate" DataFormatString="{0:d}" />
                    <asp:BoundField ItemStyle-CssClass="lineleft" HeaderStyle-CssClass="lineleft" ItemStyle-HorizontalAlign="Center" HeaderStyle-HorizontalAlign="Center" HeaderText="序號" DataField="pd_paysn" />
                    <asp:BoundField ItemStyle-CssClass="lineleft" HeaderStyle-CssClass="lineleft" ItemStyle-HorizontalAlign="Center" HeaderStyle-HorizontalAlign="Center" HeaderText="金額" DataField="pd_paymoney" />
                    <asp:TemplateField ItemStyle-CssClass="lineright" HeaderStyle-CssClass="lineright" ItemStyle-HorizontalAlign="Center" HeaderStyle-HorizontalAlign="Center" HeaderText="繳費單" >
                        <ItemTemplate>
                            <asp:LinkButton ID="lbnt_userfilename" runat="server"></asp:LinkButton>
                        </ItemTemplate>
                        <HeaderStyle CssClass="lineright"></HeaderStyle>
                        <ItemStyle CssClass="lineright"></ItemStyle>
                    </asp:TemplateField>
                </Columns>
                <EmptyDataRowStyle ForeColor="Red" />
                <PagerStyle CssClass="S_PageCss" HorizontalAlign="Center" />
            </asp:GridView>
        </div>
    </div>
    <!--{* fullcontent end *}-->
    <div class="fullfooter">
    </div>
    <!--{* content end *}-->
</asp:Content>
