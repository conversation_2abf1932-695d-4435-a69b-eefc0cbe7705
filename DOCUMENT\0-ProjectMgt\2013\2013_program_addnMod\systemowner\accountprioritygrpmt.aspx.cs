﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;

public partial class systemowner_accountprioritygrpmt : BaseAdminPage
{
    wsAccountInfo wsaccount = new wsAccountInfo();
    wsCompany wscompnay = new wsCompany();
    wsGroupInfo wsgroup = new wsGroupInfo();

    public DataSet ds_account
    {
        get { return (DataSet)ViewState["ds_account"]; }
        set { ViewState["ds_account"] = value; }
    }
    protected void Page_Load(object sender, EventArgs e)
    {
        if (Session["account"] == null)
        {
            Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('已閒置過久，請重新登入');
                                                    window.location = '../accountmt/index.aspx';</script>"); return;
        }
        else
        {
            if (GetAgentName() != "" && GetAgentAccountstatus() == "2" && GetAgentAccountGrpList().Contains("sys_adm"))
            {
                if (!IsPostBack)
                {
                    if (Request["account"] == null)
                    {
                        Response.Write(ConfigurationManager.AppSettings["Msg.參數正確性"].ToString());
                        Response.End();
                        return;
                    }

                    BindGroupMainList();

                    BindData();
                    btn_ReSend.Attributes.Add("onclick", "if (confirm('系統將重新設定該帳號的密碼，是否確定要執行?')==false) {return false;}");
                }
            }
            else
            {
                Response.Write(ConfigurationManager.AppSettings["Msg.存取權限"].ToString());
                Response.End();
                return;
            }
        }
    }
    private void BindGroupMainList()
    {
        string[] radom_g = wsGroupValidate(getRadNum());
        cbl_grouplist.DataSource = wsgroup.getGroupMainList(radom_g[0].ToString(), radom_g[1].ToString());
        cbl_grouplist.DataTextField = "ct_gd_grpdesc";
        cbl_grouplist.DataValueField = "ct_gd_grpid";
        cbl_grouplist.DataBind();
    }

    private void BindData()
    {
        string[] radom_A = wsAccountValidate(getRadNum());
        string[] radom_C = wsCompanyValidate(getRadNum());
        string account = Server.UrlDecode(Request["account"].ToString());
        ds_account = wsaccount.getAccountBase(account, radom_A[0].ToString(), radom_A[1].ToString());

        if (ds_account.Tables[0].Rows.Count > 0)
        {
            string compidnoE = ds_account.Tables[0].Rows[0]["ai_accountbelongcomp"].ToString();
            ViewState["compidnoE"] = compidnoE;
            if (DeCode(compidnoE).Equals("********"))
            {
                //可選擇系統權限
                foreach (ListItem item in cbl_grouplist.Items)
                {
                    item.Enabled = true;
                }
            }
            else
            {
                //不可選擇系統權限
                foreach (ListItem item in cbl_grouplist.Items)
                {
                    item.Enabled = false;
                }
            }
            DataSet ds_compnay = wscompnay.getCompanyBase(compidnoE, radom_C[0].ToString(), radom_C[1].ToString());
            string[] radom_G = wsGroupValidate(getRadNum());
            DataTable dt_belgrp = wsgroup.getAccountBelogGrp(Server.UrlDecode(Request["account"].ToString()), radom_G[0].ToString(), radom_G[1].ToString());

            //使用者姓名
            lbl_username.Text = ds_account.Tables[0].Rows[0]["ai_username"].ToString();
            //統一編號
            lbl_compidno.Text = DeCode(compidnoE);
            //公司名稱
            lbl_compname.Text = ds_compnay.Tables[0].Rows[0]["ci_compname"].ToString();
            //電話
            lbl_usertel.Text = ds_account.Tables[0].Rows[0]["ai_usertel"].ToString();
            //電子郵件地址
            lbl_userEmail.Text = ds_account.Tables[0].Rows[0]["ai_email"].ToString();
            //使用權限
            if (ds_account.Tables[0].Rows[0]["ai_use_gm_system"].ToString() == "1")
                cb_usersystemP.Checked = true;
            //系統權限
            foreach (DataRow dr in dt_belgrp.Rows)
            {
                foreach (ListItem item in cbl_grouplist.Items)
                {
                    if (dr["abg_grpid"].ToString() == item.Value)
                        item.Selected = true;
                }
            }

            //帳號使用狀況
            string accountstatus = ds_account.Tables[0].Rows[0]["ai_accountstatus"].ToString();
            switch (accountstatus)
            {
                case "1":
                case "2":
                case "4":
                    rbl_accountstatus.SelectedValue = "2";
                    break;
                default:
                    rbl_accountstatus.SelectedValue = "3";
                    break;
            }

            //******** Gemma 是否為公司帳號管理者
            if (wsgroup.isAccountInCompManage(radom_G[0].ToString(), radom_G[1].ToString(), account))
                rbl_isCompMang.SelectedValue = "1";
            else rbl_isCompMang.SelectedValue = "0";
            //一般使用者
            if (wsgroup.isAccountInCompUser(radom_G[0].ToString(), radom_G[1].ToString(), account))
                rbl_isUser.SelectedValue = "1";

            if (accountstatus == "3")
            {
                cbl_grouplist.Enabled = false;
                rbl_isCompMang.Enabled = false;
                rbl_isUser.Enabled = false;
            }
            else
            {
                cbl_grouplist.Enabled = true;
                rbl_isCompMang.Enabled = true;
                rbl_isUser.Enabled = true;
            }
        }
        else
        {
            Response.Write(ConfigurationManager.AppSettings["Msg.無對應資料"].ToString());
            Response.End();
            return;
        }

        wsLoginCheck wslogin = new wsLoginCheck();
        Cryptography crypt = new Cryptography();
        string[] radom_L = crypt.wsCheckLogin(crypt.getRadNum());
        //當帳號登入失敗次數超過>=4 出現解除鎖定按鈕
        if (!(wslogin.FailAccountLog(account, radom_L[0].ToString(), radom_L[1].ToString())))
        {
            btn_Unlock.Visible = true;
        }

    }
    protected void btn_Save_Click(object sender, EventArgs e)
    {
        string[] radom_G = wsGroupValidate(getRadNum());
        string compidnoE = ViewState["compidnoE"].ToString();
        if (wsgroup.CompIsHaveManage(radom_G[0].ToString(), radom_G[1].ToString(), compidnoE) == "0" && rbl_isCompMang.SelectedValue == "0")
        {
            MessageBox.Show("必須設定一位為廠商公司帳號管理者");
            return;
        }

        int selcount = 0;
        string selgrp = string.Empty;
        string selgrplist = string.Empty;
        string account = Server.UrlDecode(Request["account"].ToString());
        foreach (ListItem item in cbl_grouplist.Items)
        {
            if (item.Selected)
            {
                selcount += 1;
                selgrp += item.Value + ",";
                selgrplist += item.Text + "、";
            }
        }
        if (lbl_compidno.Text == "********")
        {
            if (selcount <= 0)
            {
                MessageBox.Show("“系統權限”至少勾選一個");
            }
            else
            {
                //string account = Server.UrlDecode(Request["account"].ToString());
                //刪除原來權限，新增新的權限
                wsgroup.DelOldGrpInsNewGrp(account, selgrp, radom_G[0].ToString(), radom_G[1].ToString());
                ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('存檔成功');
                                                    window.location = 'accountvalidationlist.aspx';</script>");
            }
        }
        else
        {
            if (selcount > 0)
            {
                MessageBox.Show("“系統權限”僅能設定為ARTC之人員");
            }
            else
            {
                //******** Gemma 設定廠商公司帳號管理者
                if (rbl_isCompMang.SelectedValue == "1")
                {
                    wsgroup.updateCompManage(radom_G[0].ToString(), radom_G[1].ToString(), compidnoE, account);
                }
                if (rbl_isUser.SelectedValue == "1")
                {
                    wsgroup.updateCompUser(radom_G[0].ToString(), radom_G[1].ToString(), compidnoE, account);
                }
                //帳號使用狀況 使用中/停止使用
                string s_accountstatus = "";
                switch (ds_account.Tables[0].Rows[0]["ai_accountstatus"].ToString())
                {
                    case "1":
                    case "2":
                    case "4":
                        s_accountstatus = "2";
                        break;
                    default:
                        s_accountstatus = "3";
                        break;
                }
                //當帳號使用狀況改變時更新資料
                if (rbl_accountstatus.SelectedValue != s_accountstatus)
                {
                    wsgroup.updateAccountStatus(radom_G[0].ToString(), radom_G[1].ToString(), compidnoE, account, rbl_accountstatus.SelectedValue);
                }



                ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('存檔成功');
                                                    window.location = 'accountvalidationlist.aspx';</script>");
            }

        }
    }
    protected void btn_Cancel_Click(object sender, EventArgs e)
    {
        Response.Redirect("accountvalidationlist.aspx", false);
    }
    protected void btn_ReSend_Click(object sender, EventArgs e)
    {
        string PWD = GetRandomPassword(6);
        //email
        string[] _mailTo = new string[1];
        _mailTo[0] = lbl_userEmail.Text;//收件人                
        string[] _mailCc = new string[1];//副本
        _mailCc[0] = "";
        string[] _mailBcc = new string[1];//密件
        _mailBcc[0] = "";
        if (Convert.ToBoolean(ConfigurationManager.AppSettings["DEBUGMode"].ToString()))
        {
            //測試
            _mailTo[0] = ConfigurationManager.AppSettings["MailTestTo"].ToString();
        }

        SendMail mail = new SendMail();
        string account = Server.UrlDecode(Request["account"].ToString());
        if (mail.Mail_ReAccountPWD(_mailTo, _mailCc, _mailBcc, lbl_username.Text.Trim(), account, PWD))
        {
            string[] radom_A = wsAccountValidate(getRadNum());
            wsaccount.UpdataPWD(account, GetPAWDHash(PWD), radom_A[0].ToString(), radom_A[1].ToString());
            MessageBox.Show("已成功寄發通知");
        }
        else
        {
            MessageBox.Show("寄件失敗");
        }
    }
    protected void btn_Unlock_Click(object sender, EventArgs e)
    {
        wsLoginCheck wslogin = new wsLoginCheck();
        Cryptography crypt = new Cryptography();
        string[] radom_L = crypt.wsCheckLogin(crypt.getRadNum());
        string account = Server.UrlDecode(Request["account"].ToString());
        string submit_result = "";
        try
        {
            submit_result = wslogin.RemoveAccountLoginLog(account, radom_L[0].ToString(), radom_L[1].ToString());
            if (submit_result.Equals("解除鎖定完成"))
            {
                Page.ClientScript.RegisterStartupScript(this.GetType(), "", @"<script>alert('" + submit_result + "');window.location = 'accountprioritygrpmt.aspx?account=" + Server.UrlEncode(account) + "';</script>"); return;
            }
            else
            {
                MessageBox.Show(submit_result);
                return;
            }
        }
        catch (Exception ex)
        {
            ex.Message.ToString();
            MessageBox.Show("解除鎖定");
        }
    }
}