﻿<%@ Page Title="" Language="C#" MasterPageFile="~/masterpage/MasterOutset.master"
    AutoEventWireup="true" CodeFile="pollutionfileupload.aspx.cs" Inherits="accountmt_pollutionfileupload" %>

<asp:Content ID="Content1" ContentPlaceHolderID="head" runat="Server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="ContentPlaceHolder1" runat="Server">
    <div class="fullheader">
        <table width="100%" cellspacing="0" cellpadding="0" class="fullheaderinfo">
            <tr>
                <td valign="top">
                    <b>公司基本資料~電子檔上傳</b>
                </td>
                <td align="right" valign="top" class="font_loginInfo">
                </td>
            </tr>
        </table>
    </div>
    <!--{* fullheader end *}-->
    <div class="fullcontent">
        <div class="formstyle">
            <table width="100%" border="0" cellspacing="0" cellpadding="0" class="font_fullcontent">
                <tr>
                    <td align="right">
                        統一編號：
                    </td>
                    <td colspan="3">
                        <asp:Label ID="lbl_compidno" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        公司名稱：
                    </td>
                    <td colspan="3">
                        <asp:Label ID="lbl_compname" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        公司英文名稱：
                    </td>
                    <td>
                        <asp:Label ID="lbl_compename" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        負責人姓名：
                    </td>
                    <td>
                        <asp:Label ID="lbl_chargemane" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        公司電話：
                    </td>
                    <td>
                        <asp:Label ID="lbl_comptel" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        公司傳真：
                    </td>
                    <td>
                        <asp:Label ID="lbl_compfax" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        公司地址：
                    </td>
                    <td colspan="3">
                        <asp:Label ID="lbl_compaddr" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        合格證寄發地址：
                    </td>
                    <td colspan="3">
                        <asp:Label ID="lbl_certaddr" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        相關檔案上傳：
                    <td colspan="3">
                        <table>
                            <tr>
                                <td>
                                    <span style="color: Red;">*</span>公司證明文件：<asp:FileUpload ID="fu_compDoc" runat="server" />
                                </td>
                                <td>
                                    <asp:HyperLink ID="hlk_comDoc" runat="server" Target="_blank"></asp:HyperLink>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    工廠登記證：<asp:FileUpload ID="fu_facCert" runat="server" />
                                </td>
                                <td>
                                    <asp:HyperLink ID="hlk_facCert" runat="server" Target="_blank"></asp:HyperLink>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    代理授權證明函：<asp:FileUpload ID="fu_authLetter" runat="server" />
                                </td>
                                <td>
                                    <asp:HyperLink ID="hlk_authLetter" runat="server" Target="_blank"></asp:HyperLink>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    進口商證明：<asp:FileUpload ID="fu_importCert" runat="server" />
                                </td>
                                <td>
                                    <asp:HyperLink ID="hlk_importCert" runat="server" Target="_blank"></asp:HyperLink>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td align="right">
                        最後修改人：
                    </td>
                    <td>
                        <asp:Label ID="lbl_updateman" runat="server"></asp:Label>
                    </td>
                    <td align="right">
                        最後修改時間：
                    </td>
                    <td>
                        <asp:Label ID="lbl_updatedate" runat="server"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td colspan="4" align="center">
                        <asp:Button ID="btn_tmpSave" runat="server" Text="暫存" CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'"
                            onmouseout="this.className='btn_mouseout'" onclick="btn_tmpSave_Click" />
                        <asp:Button ID="btn_Apply" runat="server" Text="送件" CssClass="btn_mouseout" onmouseover="this.className='btn_mouseover'"
                            onmouseout="this.className='btn_mouseout'" onclick="btn_Apply_Click" />
                    </td>
                </tr>
            </table>
        </div>
    </div>
    <!--{* fullcontent end *}-->
    <div class="fullfooter">
    </div>
    <!--{* content end *}-->
</asp:Content>
